from pyspark.sql.functions import col, udf
from pyspark.sql.types import StringType, ArrayType
from pyspark.sql import functions as F
from rapidfuzz import process, fuzz
import re

from src.sofware_extraction import config
from src.sofware_extraction.constants import UPDATED_AT_TS
from src.sofware_extraction.common_utils import write_table
from src.sofware_extraction.software_extraction_utils import normalize_software_details


def read_table_rapid7(args, spark):
    input_paths=args.inputPath.split(";")

    hv_df = spark.read.table(input_paths[0]).filter(f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid").withColumn(
        UPDATED_AT_TS, F.expr(f"to_timestamp({args.eventTimestampEndEpoch}/1000)"))
    
    nvd_df = (spark.read.table(input_paths[1]).filter(
        f"(parsed_interval_timestamp_ts >= to_timestamp(({args.parsedIntervalStartEpoch}/1000)-(14*86400))) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid and lower(vulnStatus) not  in ('received','rejected','deferred','awaiting analysis')")
              .withColumn("row", F.expr("row_number() over (partition by id order by event_timestamp_epoch desc)"))
              .filter("row=1")
              .select("id", "configurations"))   
    nvd_df = nvd_df.withColumnRenamed("id","cve_id_nvd") 
    
    cisa_df = spark.read.table(input_paths[2]).filter(f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid").withColumn("row", F.expr("row_number() over (partition by cveMetadata.cveId order by event_timestamp_epoch desc)")).filter("row=1").select("containers", "cveMetadata")
    cisa_df = cisa_df.withColumn("cve_id_cisa",F.expr("cveMetadata.cveId"))    
    
    vuln_df = spark.read.table(input_paths[3]).filter(f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid")              
    return hv_df, nvd_df, cisa_df, vuln_df

# Extract all software names from cpe
def cpe_to_string(df, software_update_dict={}):
    df = df.withColumn("cpe_objects",F.when(F.col("cpe_list").isNull(),F.array([])).otherwise(F.expr("split(trim('[]', cast(cpe_list as string)), ', ')")))
    df = df.withColumn("cpe_software_list",F.expr("""transform(cpe_objects, x -> CASE WHEN size(split(x, ':')) > 4 THEN CASE WHEN regexp_extract(split(x, ':')[1], '^([0-9])') != '' THEN concat_ws('|', split(x, ':')[3], split(x, ':')[4]) ELSE concat_ws('|', split(x, ':')[2], split(x, ':')[3]) END ELSE null END)"""))
    for key, value in software_update_dict.items():
        df = df.withColumn("cpe_software_list", F.expr("FILTER(cpe_software_list, x -> x IS NOT NULL)")).withColumn("cpe_software_list",F.expr(f"transform(cpe_software_list, x -> regexp_replace(x, '{key}', '{value}'))"))
    df = df.withColumn("cpe_software_list", F.array_distinct(F.col("cpe_software_list")))
    df = df.drop("cpe_objects")
    return df

def nvd_cisa_processing(nvd_df, cisa_df):
    # derive cpe list
    nvd_df = nvd_df.withColumn("cpe_list_nvd", F.expr("flatten(transform(transform(configurations.nodes, node -> node.cpeMatch), x -> x[0].criteria))"))   
    cisa_df = cisa_df.withColumn("cpe_list_cisa", F.expr("COALESCE(flatten(containers.cna.affected.cpes),flatten(transform(FILTER(containers.adp, x -> x.affected.cpes IS NOT NULL),x -> flatten(x.affected.cpes))))"))
    cisa_df = cisa_df.withColumn("sw_cisa",  F.expr("CASE WHEN size(containers.adp.title)>1 THEN coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE transform(containers.cna.affected, x->(concat_ws('|', regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', '')))) END, transform(filter(containers.adp,x->x.title in ('CISA ADP Vulnrichment'))[0].affected, x->(concat_ws('|', regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', ''))))) ELSE coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE transform(containers.cna.affected, x->(concat_ws('|', regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', '')))) END, transform(containers.adp[0].affected, x->(concat_ws('|', regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', ''))))) END"))
    cisa_df = cisa_df.withColumn("sw_cisa", F.expr("FILTER(sw_cisa, x -> x !='')"))
    
    # nvd_cisa join
    nvd_cisa_join =  nvd_df.join(cisa_df.select("cve_id_cisa", "cpe_list_cisa","sw_cisa"), nvd_df['cve_id_nvd'] == cisa_df['cve_id_cisa'], 'outer')
    nvd_cisa_join = nvd_cisa_join.withColumn("cpe_list",F.when((F.size(F.col("cpe_list_nvd")) >= 1), F.col("cpe_list_nvd")).otherwise(F.col("cpe_list_cisa")))
    nvd_cisa_join = cpe_to_string(nvd_cisa_join,  config.software_update_dict)
    
    # add software list from cisa
    nvd_cisa_join = nvd_cisa_join.withColumn("software_list",F.when((F.size(F.col("cpe_software_list")) >= 1), F.col("cpe_software_list")).otherwise(F.col("sw_cisa")))
    nvd_cisa_join = nvd_cisa_join.withColumn("software_list",F.when(F.col("software_list").isNull(),F.lit(F.array([]))).otherwise(F.col("software_list")))    
    nvd_cisa_join = nvd_cisa_join.withColumn("nvd_cisa_cve_id", F.coalesce(nvd_cisa_join["cve_id_nvd"], nvd_cisa_join["cve_id_cisa"])) 
    return nvd_cisa_join


def preprocessing(hv_df,vuln_df):
    # preprocessing hv_df
    hv_df = hv_df.withColumn("asset_vulns", F.explode(col("asset_vulns")))
    hv_df = hv_df.withColumn("installed_software", F.expr("transform(FILTER(software,x -> regexp_replace(COALESCE(x.vendor, ''), '[^\x00-\x7F]', '') = x.vendor AND regexp_replace(COALESCE(x.product, ''), '[^\x00-\x7F]', '') = x.product AND regexp_replace(COALESCE(x.version, ''), '[^\x00-\x7F]', '') = x.version ),x -> concat_ws('|', regexp_replace(COALESCE(x.vendor, ''), '\\\\|', ''), regexp_replace(COALESCE(x.product, ''), '\\\\|', ''), regexp_replace(COALESCE(x.version, ''), '\\\\|', '') ))"))
    hv_df = hv_df.withColumn("vuln_id", F.expr("asset_vulns.id"))
    hv_df = hv_df.withColumn("proof", F.expr("transform(array_distinct(asset_vulns.results), x -> x.proof)"))
    hv_df = hv_df.withColumn("asset_id", F.expr("id"))
    
    # vuln table
    vuln_df = vuln_df.join(hv_df.select("vuln_id").distinct(),hv_df["vuln_id"]==vuln_df["id"]) # keep only ids present in hv_df
    vuln_df = vuln_df.withColumn("cves", F.explode_outer(col("cves")))
    vuln_df = vuln_df.withColumn('cve_extrated', F.upper(F.regexp_extract('id', r'(cve-\d+-\d+)', 0))) 
    vuln_df = vuln_df.withColumn('cve_extrated', F.coalesce('cves', 'cve_extrated'))    
    return hv_df, vuln_df 

def vulnerability_enrichment(hv_df, vuln_df, nvd_cisa_join):
    vuln_df = vuln_df.join(nvd_cisa_join,vuln_df["cve_extrated"] == nvd_cisa_join["nvd_cisa_cve_id"],"left")    
    grouped_vul = vuln_df.groupBy('id').agg(
        F.expr("array_distinct(flatten(collect_list(software_list)))").alias("software_list"),
        F.first('title').alias('title'),
        F.first('description').alias('description'),
        F.collect_set("cve_extrated").alias("cve")
    )
    hv_df = hv_df.join(grouped_vul, hv_df["vuln_id"]==grouped_vul["id"],"left").drop("grouped_vul.id")
    hv_df = hv_df.select( 'vuln_id', 'proof', 'software_list','installed_software','asset_id', 'title', 'description', 'cve', "updated_at_ts").distinct()   
    return hv_df


def extract_software(title, proof, cpe_list, installed_software):      
    desc = f"{str(title)} {str(proof)}".lower()
    proof_str = str(proof).lower()
    
    # Ensure lists are valid
    cpe_list = cpe_list if isinstance(cpe_list, list) else []
    installed_software = installed_software if isinstance(installed_software, list) else []
    
    # Strategy 1: Check for macOS
    if "os x update" in desc:
        version_match = re.search("apple mac os[ a-z]*([0-9][0-9a-z.]+)", desc)
        if version_match:
            return [f"apple|macos|{version_match.group(1)}"]
        return ["apple|macos|"]
    
    # Strategy 2: CPE matching
    cpe_match = match_cpe(desc, proof_str, cpe_list, installed_software)
    if cpe_match:
        return cpe_match
    
    # Strategy 3: Direct match in installed software
    sw_matches = [sw for sw in installed_software if sw.split("|")[1].lower() in proof_str]
    if sw_matches:
        return sw_matches
    
    # Strategy 4: Regex extraction from proof
    vuln_matches = re.findall("vulnerable software installed: ([^< ]+?) ([^< ]+?) ([0-9][0-9a-z().+-]+)", proof_str)
    if vuln_matches:
        return ["|".join(match) for match in vuln_matches]
    
    # No matches found
    return []


def match_cpe(desc, proof_str, cpe_list, installed_software):
    cleaned_cpes = [cpe.replace("_", " ").replace("-", " ").replace("|", " ") for cpe in cpe_list]
    cleaned_desc = re.sub("[\\/:<>_-]", " ", desc)
    
    # Fuzzy matching 
    potential_match = process.extractOne(cleaned_desc, cleaned_cpes, scorer=fuzz.token_ratio)
    if not potential_match:
        return []
    match_score = potential_match[1]
    cpe_match = cpe_list[cleaned_cpes.index(potential_match[0])]
    single_cpe_match = len(cpe_list) == 1 and any(item in proof_str for item in cpe_list[0].split("|"))    
    if match_score < 85 and not single_cpe_match:
        return []
    
    # Case 1: Try to find version in installed software
    vendor, product = cpe_match.split("|")[0], cpe_match.split("|")[1]
    version_matches = []
    for sw in installed_software:
        sw = sw.lower()
        if vendor in sw and sw.replace("|"," ") in proof_str and len(sw.split("|"))>2:
            version_matches.append(f"{cpe_match}|{sw.split('|')[2]}")
    if version_matches:
        return version_matches
    
    # Case 2: Extract version from proof
    version_pattern = f"vulnerable (?:software installed|os):[^<]*?{re.escape(vendor)}|{re.escape(product)}[^<]*?([0-9][0-9a-z.+-]+)"
    version_matches = re.findall(version_pattern, proof_str)
    if version_matches:
        return [f"{cpe_match}|{ver}" for ver in version_matches]
    
    # Case 3: Return cpe_match with empty version
    return [f"{cpe_match}|"]


def derive_rapid7_software(hv_df, nvd_df, cisa_df, vuln_df, output_path, spark):
    extract_software_udf = udf(extract_software, ArrayType(StringType()))   

    hv_df, vuln_df = preprocessing(hv_df,vuln_df)
    nvd_cisa_join = nvd_cisa_processing(nvd_df, cisa_df)
    hv_df = vulnerability_enrichment(hv_df, vuln_df, nvd_cisa_join)

    # intermediate write 
    write_table(hv_df, output_path + '_intermediate1', None, None, spark)
    hv_df = spark.read.table(output_path + '_intermediate1')
    
    hv_df = hv_df.withColumn("software_derived",extract_software_udf(col("title"),col("proof"),col("software_list"),col("installed_software")))
    hv_df = hv_df.withColumn("software_derived", F.array_distinct(F.col("software_derived")))
     
    # Derive software vendor, product and full name
    hv_df = hv_df.withColumn("software", F.explode_outer(col("software_derived")))
    hv_df = hv_df.withColumn("software_vendor", F.lower(F.split(col("software"), "\\|")[0]))
    hv_df = hv_df.withColumn("software_name", F.lower(F.split(col("software"), "\\|")[1]))
    hv_df = hv_df.withColumn("software_version", F.split(col("software"), "\\|")[2])
    hv_df = normalize_software_details(hv_df)
    
    # Regex match from proof(To increase coverage)
    hv_df = hv_df.withColumn(
    "software_full_name",
    F.when(
        (F.col("software_full_name").isNull()) & 
        (F.col("proof").isNotNull()) &  
        (F.size(F.col("proof")) == 1) &
        (F.size(F.expr("regexp_extract_all(proof[0], 'Vulnerable (OS|software)', 0)"))==1),
        F.regexp_extract(F.col("proof").getItem(0), "Vulnerable (?:OS|software installed): *([^<]*?[0-9][0-9a-zA-Z.+-]+)", 1)
    ).otherwise(F.col("software_full_name")))
    
    hv_df = hv_df.select('asset_id','vuln_id', 'proof',"software_vendor", "software_name","software_version", "software_product", "software_full_name","updated_at_ts").distinct()    
    return hv_df
