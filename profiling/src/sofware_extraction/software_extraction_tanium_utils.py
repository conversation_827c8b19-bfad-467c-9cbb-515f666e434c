from pyspark.sql.functions import col, when, concat_ws, udf
from pyspark.sql.types import StringType, ArrayType
import argparse
from pyspark.sql import functions as F
import re

from src.sofware_extraction import config
from src.sofware_extraction.constants import UPDATED_AT_TS
from src.sofware_extraction.common_utils import write_table
from src.sofware_extraction.software_extraction_utils import normalize_software_details


def read_table_tanium(args, spark):
    input_paths=args.inputPath.split(";")
    hv_df = spark.read.table(input_paths[0]).filter(f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid").withColumn(
        UPDATED_AT_TS, F.expr(f"to_timestamp({args.eventTimestampEndEpoch}/1000)"))
    nvd_df = (spark.read.table(input_paths[1]).filter(
        f"(parsed_interval_timestamp_ts >= to_timestamp(({args.parsedIntervalStartEpoch}/1000)-(14*86400))) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid and lower(vulnStatus) not  in ('received','rejected','deferred','awaiting analysis')")
              .withColumn("row", F.expr("row_number() over (partition by id order by event_timestamp_epoch desc)"))
              .filter("row=1")
              .select("id", "configurations"))   
    nvd_df = nvd_df.withColumnRenamed("id","nvd_cve_id") 
    cisa_df = spark.read.table(input_paths[2]).filter(f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid")
    asset_df = spark.read.table(input_paths[3]).filter(f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid")              
    asset_df = asset_df.withColumn("row", F.expr("row_number() over (partition by id order by event_timestamp_epoch desc)")).filter("row=1").select("id", "OperatingSystemGeneration")
    return hv_df, nvd_df, cisa_df, asset_df

# Extract all software names from cpe
def cpe_to_string(df, software_update_dict):
    df = df.withColumn("cpe_objects",F.when(F.col("cpe_list").isNull(),F.array([])).otherwise(F.expr("split(trim('[]', cast(cpe_list as string)), ', ')")))
    df = df.withColumn("cpe_software_list",F.expr("""transform(cpe_objects, x -> CASE WHEN size(split(x, ':')) > 4 THEN CASE WHEN regexp_extract(split(x, ':')[1], '^([0-9])') != '' THEN concat_ws('|', split(x, ':')[2], split(x, ':')[3], split(x, ':')[4]) ELSE concat_ws('|', split(x, ':')[1], split(x, ':')[2], split(x, ':')[3]) END ELSE null END)"""))
    for key, value in software_update_dict.items():
        df = df.withColumn("cpe_software_list", F.expr("FILTER(cpe_software_list, x -> x IS NOT NULL)")).withColumn("cpe_software_list",F.expr(f"transform(cpe_software_list, x -> regexp_replace(x, '{key}', '{value}'))"))
    df = df.withColumn("cpe_software_list", F.array_distinct(F.col("cpe_software_list")))
    df = df.drop("cpe_objects")
    return df


# Post-processing windows and office products
def extract_software(cpe_software_list, installed_sw_list, affected_sw_list, summary, os, software_type_tag):
    result=[]
    
    if cpe_software_list:        
        # windows case
        if software_type_tag=="o" and "windows" in str(cpe_software_list).lower():
            result = ["microsoft|"+os+"|" if "windows" in str(os).lower() else "microsoft|windows|"]
        
        # hardware case
        elif software_type_tag=="h" and len(cpe_software_list)>1 and all(len(s.split('|')) >= 2 for s in cpe_software_list):
            if len(list(set([sw.split("|")[1] for sw in cpe_software_list])))==1:
                result = [cpe_software_list[0].split("|")[1]+"||"]
            
        # cpe - single software case
        elif len(cpe_software_list)==1 and all(len(s.split('|')) >= 3 for s in cpe_software_list):
            version=""
            cpe_sw = cpe_software_list[0]
            for installed_sw_dict in installed_sw_list:
                ins_name=str(installed_sw_dict["Name"]).lower()
                cpe_full_name = " ".join(cpe_sw.split("|")[1:]).lower().replace("_"," ")
                cpe_product = (cpe_sw.split("|")[2]).lower().replace("_"," ")
                if (ins_name == cpe_full_name) or (ins_name == cpe_product) or (ins_name.startswith(cpe_full_name + " ")) or (ins_name.startswith(cpe_product + " ")):
                    version = installed_sw_dict["Version"]
            result= [(cpe_sw.split("|")[1]+"|"+cpe_sw.split("|")[2])+"|"+version]
        
        # cpe- multiple software case
        elif len(cpe_software_list)>1:
            # check for match with installed applications
            for installed_sw_dict in installed_sw_list:
                product_match=[]
                ins_product=str(installed_sw_dict["Name"]).lower()
                version = str(installed_sw_dict["Version"])
                for cpe_element in cpe_software_list:
                    if (len(cpe_element.split("|"))>=3):
                        cpe_full_name = " ".join(cpe_element.split("|")[1:]).lower().replace("_"," ")
                        cpe_product = (cpe_element.split("|")[2]).lower().replace("_"," ")
                        if (ins_product == cpe_full_name) or (ins_product == cpe_product) or (ins_product.startswith(cpe_full_name + " ")) or (ins_product.startswith(cpe_product + " ")):
                            product_match.append(cpe_element.split("|")[1]+"|"+cpe_element.split("|")[2]+"|"+version)
                if len(product_match)>=1:
                    result.append(max(product_match, key=len))

            if len(result)<1:
                # check for match with summary
                for cpe_element in cpe_software_list:
                    if (len(cpe_element.split("|"))>=3):
                        cpe_product = (cpe_element.split("|")[2]).lower().replace("_"," ")
                        summary_cleaned = str(summary).lower().replace("_"," ")
                        if re.search(rf'\b{re.escape(cpe_product)}\b', summary_cleaned):
                            result.append(cpe_element.split("|")[1]+"|"+cpe_element.split("|")[2]+"|")
    if len(result)>0:
        return result      
    
    # Common products between Affected products and installed applications  
    common_sw=[]
    for affected_sw in affected_sw_list:
        if affected_sw != "":        
            for installed_sw_dict in installed_sw_list:
                ins_product=str(installed_sw_dict["Name"])
                ins_product_cleaned =(ins_product.lower().split('('))[0].strip()
                aff_product_cleaned =(affected_sw.lower().split('('))[0].strip()
                if (ins_product_cleaned == aff_product_cleaned) or (ins_product_cleaned.startswith(aff_product_cleaned + " ")) or (aff_product_cleaned.startswith(ins_product_cleaned + " ")):
                    common_sw.append("|"+installed_sw_dict["Name"]+"|"+installed_sw_dict["Version"])
        
            if len(common_sw)>0:
                common_sw=(list(set(common_sw)))
                return common_sw

            elif len(affected_sw_list)==1:
                return (["|"+affected_sw_list[0]+"|"])
            
    # same vendor in cpe software list
    if cpe_software_list:
        if len(result)<1 and len(cpe_software_list)>1 and all(len(s.split('|')) >= 2 for s in cpe_software_list):
            if len(list(set([sw.split("|")[1] for sw in cpe_software_list])))==1:
                result = [cpe_software_list[0].split("|")[1]+"||"]  
                return result
    
    return None



def derive_tanium_software(hv_df, nvd_df, cisa_df, asset_df, output_path, spark):
    extract_software_udf = udf(extract_software, ArrayType(StringType()))   
     
    hv_df = hv_df.withColumn("cve_id",F.expr("cveFinding.Check__ID"))
    hv_df = hv_df.withColumn("summary",F.expr("cveFinding.Summary"))
    hv_df = hv_df.withColumn("affected_products",F.expr("cveFinding.Products"))
    hv_df = hv_df.withColumn("host_id",F.expr("CONCAT(id,'|',region_tag)"))

    # join with Tanium asset 
    asset_df = asset_df.withColumn("os_generation",F.expr("OperatingSystemGeneration"))
    hv_df = hv_df.join(asset_df,hv_df["id"] == asset_df["id"],"left").drop(asset_df.id)
    
    # cpe enrichment from NVD,CISA
    nvd_df = nvd_df.withColumn("cpe_list_nvd", F.expr("flatten(transform(transform(configurations.nodes, node -> node.cpeMatch), x -> x[0].criteria))"))   
    cisa_df = cisa_df.withColumn("cve_id_cisa",F.expr("cveMetadata.cveId"))
    cisa_df = cisa_df.withColumn("cpe_list_cisa", F.expr("COALESCE(flatten(containers.cna.affected.cpes),flatten(transform(FILTER(containers.adp, x -> x.affected.cpes IS NOT NULL),x -> flatten(x.affected.cpes))))"))
    cisa_df = cisa_df.withColumn("sw_cisa",  F.expr("CASE WHEN size(containers.adp.title)>1 THEN coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE transform(containers.cna.affected, x->(concat_ws('|','cisa',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', '')))) END, transform(filter(containers.adp,x->x.title in ('CISA ADP Vulnrichment'))[0].affected, x->(concat_ws('|','cisa',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', ''))))) ELSE coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE transform(containers.cna.affected, x->(concat_ws('|','cisa',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', '')))) END, transform(containers.adp[0].affected, x->(concat_ws('|','cisa',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', ''))))) END"))
    cisa_df = cisa_df.withColumn("sw_cisa", F.expr("FILTER(sw_cisa, x -> x !='')"))
    cisa_df = cisa_df.withColumn("row", F.expr("row_number() over (partition by cve_id_cisa order by event_timestamp_epoch desc)")).filter("row=1").select("cve_id_cisa", "cpe_list_cisa","sw_cisa")
    
    nvd_cisa_join =  nvd_df.join(cisa_df, nvd_df['nvd_cve_id'] == cisa_df['cve_id_cisa'], 'outer')
    nvd_cisa_join = nvd_cisa_join.withColumn("cpe_list",F.when((F.size(F.col("cpe_list_nvd")) >= 1), F.col("cpe_list_nvd")).otherwise(F.col("cpe_list_cisa")))
    nvd_cisa_join = cpe_to_string(nvd_cisa_join, config.software_update_dict)
    
    # add software list from cisa
    nvd_cisa_join = nvd_cisa_join.withColumn("software_list",F.when((F.size(F.col("cpe_software_list")) >= 1), F.col("cpe_software_list")).otherwise(F.col("sw_cisa")))
    nvd_cisa_join = nvd_cisa_join.withColumn("software_list",F.when(F.col("software_list").isNull(),F.lit(F.array([]))).otherwise(F.col("software_list")))    
    nvd_cisa_join = nvd_cisa_join.withColumn("software_list", F.expr("filter(software_list, x -> size(split(x, '\\\|'))=3)"))   

    # identify type of cpe
    nvd_cisa_join = nvd_cisa_join.withColumn("cpe_type", F.array_distinct(F.expr("""transform(software_list, entry ->split(entry, '|')[0])""")))
    nvd_cisa_join = nvd_cisa_join.withColumn("cpe_type", when(F.size(col("cpe_type")) == 1, col("cpe_type")[0]).otherwise(None))   
    
    nvd_cisa_join = nvd_cisa_join.withColumn("nvd_cisa_cve_id", F.coalesce(nvd_cisa_join["nvd_cve_id"], nvd_cisa_join["cve_id_cisa"]))    

    # Remove duplicates
    hv_distinct = hv_df.select("cve_id","host_id","os_generation","installedapplications","summary","affected_products","updated_at_ts").distinct()
    hv_distinct = hv_distinct.join(nvd_cisa_join,hv_distinct["cve_id"] == nvd_cisa_join["nvd_cisa_cve_id"],"left")

    # intermediate write 
    write_table(hv_distinct, output_path + '_intermediate1', None, None, spark)
    hv_distinct = spark.read.table(output_path + '_intermediate1')
    
    hv_distinct = hv_distinct.withColumn("software_derived",extract_software_udf(F.col("software_list"),F.col("installedapplications"),F.col("affected_products"),F.col("summary"),F.col("os_generation"),F.col("cpe_type")))
    hv_distinct = hv_distinct.withColumn("software_derived", F.array_distinct(F.col("software_derived")))
     
    # Derive software vendor, product and full name
    hv_distinct = hv_distinct.withColumn("software", F.explode_outer(col("software_derived")))
    hv_distinct = hv_distinct.withColumn("software_vendor", F.lower(F.split(col("software"), "\\|")[0]))
    hv_distinct = hv_distinct.withColumn("software_name", F.lower(F.split(col("software"), "\\|")[1]))
    hv_distinct = hv_distinct.withColumn("software_version", F.split(col("software"), "\\|")[2])
    hv_distinct = normalize_software_details(hv_distinct)
    
    
    hv_distinct = hv_distinct.select("cve_id", "host_id","os_generation","summary","affected_products","installedapplications", "software_vendor", "software_name","software_version", "software_product", "software_full_name","updated_at_ts").distinct()
    return hv_distinct
