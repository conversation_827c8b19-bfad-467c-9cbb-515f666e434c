import argparse
import logging
import os
import re
from pyspark.sql import functions as F
from src.utils.custom_exceptions import URLNotFoundException, APIException
import requests


def get_configs(spark, url):
    token = get_token(spark)
    headers = {
        'Content-Type': 'application/json',
        "Authorization": "Bearer {token}".format(token=token)
    }
    config = requests.get(url, headers=headers).json()
    return config


def get_token(spark):
    oidc_url = spark.conf.get("spark.sds.restapi.oidcUrl")
    if oidc_url is None:
        logging.error("OIDC URL not found")
        raise URLNotFoundException("OIDC URL not found!!")
    client_id = os.environ.get("OIDC_CLIENT_ID")
    client_secret = os.environ.get("OIDC_CLIENT_SECRET")
    logging.error(client_id)
    logging.error(client_secret)
    payload = 'grant_type=client_credentials&client_id={client_id}&client_secret={client_secret}'.format(
        client_id=client_id, client_secret=client_secret)
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    logging.error(payload)
    logging.error(oidc_url)
    response = requests.post(oidc_url, headers=headers, data=payload)
    if response.status_code != 200:
        logging.error(response.status_code)
        logging.error(response.json())
        raise APIException("Error while getting access token")
    return response.json().get("access_token")


def read_table(table_name, start_epoch, end_epoch, spark, select_cols=["*"], filter_cond="True"):
    df = spark.read.table(table_name).filter(
        f"updated_at_ts BETWEEN to_timestamp({start_epoch} / 1000) AND to_timestamp({end_epoch} / 1000) AND {filter_cond}").select(
        *select_cols)
    return df


def write_table(output_df, output_path, sort_cols, partition_cols, spark):
    schema_name = ".".join(map(str, output_path.split(".")[:2]))
    spark.sql("CREATE SCHEMA IF NOT EXISTS " + schema_name)
    writer_options = spark.conf.get("spark.sds.table.writer.options", "")
    options_dict = {"write.spark.accept-any-schema": "true"}
    [options_dict.update({prop.split("=")[0]: prop.split("=")[1]}) for prop in writer_options.split(',') if
     writer_options != '']
    writer = output_df.sortWithinPartitions(sort_cols) \
        .writeTo(output_path) \
        .partitionedBy(*partition_cols)
    for key, value in options_dict.items():
        writer = writer.tableProperty(key, value)
    if table_exists(schema_name, output_path.split(".")[2], spark):
        writer.overwritePartitions()
    else:
        writer.createOrReplace()


def table_exists(schema_name, table_name, spark):
    return spark.sql(f"show tables in {schema_name} like '{table_name}'").count() == 1

def view_exists(schema_name, table_name, spark):
    return spark.sql(f"show views in {schema_name} like '{table_name}'").count() == 1


def prepare_derived_columns(df, derived_fields):
    for key, value in derived_fields.items():
        df = df.withColumn(key, F.expr(value))
    return df


def select_and_drop_columns(df, select_fields, drop_fields):
    if drop_fields:
        select_fields = list(set(select_fields) - set(drop_fields))
    df = df.select(*select_fields)
    return df


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--tableName", "-tableName", help="inventory table name")
    parser.add_argument("--startEpoch", "-startEpoch", help="analysis period start date")
    parser.add_argument("--endEpoch", "-endEpoch", help="analysis period end date")
    parser.add_argument("--outputPath", "-outputPath", help="outputPath")
    parser.add_argument("--configURL", "-configURL", help="configURL")
    return parser.parse_args()
