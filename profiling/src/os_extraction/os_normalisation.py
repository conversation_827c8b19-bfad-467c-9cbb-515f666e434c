from pyspark.sql import SparkSession
spark = SparkSession.builder.getOrCreate()
sc = spark.sparkContext
sc.addPyFile(spark.conf.get("spark.sds.dependencies_file"))
from src.utils.common_utils import write_table
from src.os_extraction.constants import UPDATED_AT_TS
from src.os_extraction.os_normalisation_utils import read_table, read_os_lookup_table, derive_os_normalisation,get_args,write_table,clear_temp_tables



from pyspark.sql import functions as F

if __name__ == "__main__":
    args = get_args()
    srdm=read_table(args, spark)
    os_lookup=read_os_lookup_table(args, spark)
    output_df=derive_os_normalisation(srdm, os_lookup, args, spark)
    write_table(output_df, args.outputPath, F.array(UPDATED_AT_TS), [F.days(UPDATED_AT_TS)], spark)
    clear_temp_tables(args,spark)