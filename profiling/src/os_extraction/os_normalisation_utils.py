import argparse
from pyspark.sql import functions as F
from pyspark.sql.functions import col, concat_ws,expr
from src.os_extraction.constants import UPDATED_AT_TS, OS_STRING_COL
from pyspark.sql.types import StringType, StructType, StructField, TimestampType
from os_normaliser import SparkNormalizer
from src.utils.common_utils import write_table, table_exists,view_exists
import logging

logger = logging.getLogger(__name__)

def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--inputPath", "-inputPath", help="Input table name and os column expression if more than 1 table then separate with ';'")
    parser.add_argument("--outputPath", "-outputPath", help="Output eol table name")
    parser.add_argument("--parsedIntervalStartEpoch", "-parsedIntervalStartEpoch", help="parsed interval start epoch")
    parser.add_argument("--parsedIntervalEndEpoch", "-parsedIntervalEndEpoch", help="parsed interval end epoch")
    parser.add_argument("--eventTimestampEndEpoch", "-eventTimestampEndEpoch", help="event timestamp end epoch")
    parser.add_argument("--source", "-source", help="specify source as 'srdm' if input for lookup is srdm",default="loader")
    parser.add_argument("--eolProductLookup", "-eolProductLookup", help="specify the product eol lookup if available if not provided the base lookup from library will be used")
    parser.add_argument("--osColumnName", "-osColumnName", help="OS column name in input data sources")
    return parser.parse_args()



def read_table(args, spark):
    """
    Reads and filters SRDM tables from one or more input paths, extracting the OS string column.
    Returns a DataFrame with a single column: OS_STRING_COL.
    """
    input_paths = args.inputPath.split(";")

    def read_srdm_with_filter(entry):
        table_name, os_column_expr = entry.split(",", 1)
        df = spark.read.table(table_name).filter(
            f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) "
            f"and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) "
            f"and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid"
        ).withColumn(OS_STRING_COL, expr(os_column_expr)).select(OS_STRING_COL)
        return df
    def read_loaders_with_filter(entry):
        table_name, os_column_expr = entry.split(",", 1)
        logger.info(table_name)
        df = spark.read.table(table_name).filter(
            f"{UPDATED_AT_TS} = to_timestamp({args.eventTimestampEndEpoch}/1000)"
        ).withColumn(OS_STRING_COL, expr(os_column_expr)).select(OS_STRING_COL)
        return df
    if args.source=="srdm":
        logger.info(f"Reading SRDM tables")
        final_srdm = read_srdm_with_filter(input_paths[0])

        for entry in input_paths[1:]:
            if table_exists(".".join(entry.split(",")[0].split(".")[:-1]), entry.split(",")[0].split(".")[-1], spark):
                final_srdm = final_srdm.unionByName(read_srdm_with_filter(entry))
        return final_srdm
    else:
        logger.info(f"Reading loader output")
        final_srdm = read_loaders_with_filter(input_paths[0])

        for entry in input_paths[1:]:
            if view_exists(".".join(entry.split(",")[0].split(".")[:-1]), entry.split(",")[0].split(".")[-1], spark):
                final_srdm = final_srdm.unionByName(read_loaders_with_filter(entry))
        return final_srdm



def read_os_lookup_table(args, spark):
    """
    Reads the OS lookup table from args.outputPath if it exists, otherwise returns an empty DataFrame
    with schema: os_string (string), updated_at_ts (timestamp)
    If the table exists, only rows with the latest updated_at_ts are returned.
    The filtered DataFrame is also written to an intermediate table for further use.
    Args:
        args: Arguments with attribute 'outputPath' for table name
        spark: SparkSession
    Returns:
        DataFrame
    """
    table_name = args.outputPath
    logger.info(f"Checking existence of OS lookup table: {table_name}")
    if table_exists(".".join(table_name.split(".")[:-1]), table_name.split(".")[-1], spark):
        logger.info(f"OS lookup table found: {table_name}. Reading table.")
        df = spark.read.table(table_name)
        if df.rdd.isEmpty():
            logger.warning(f"OS lookup table {table_name} is empty.")
            return df
        # Find the latest updated_at_ts value
        max_ts = df.agg({"updated_at_ts": "max"}).collect()[0][0]
        logger.info(f"Latest updated_at_ts in OS lookup table: {max_ts}")
        # Filter to only rows with the latest updated_at_ts
        df = df.filter(df.updated_at_ts == max_ts)
        # Write the filtered DataFrame to an intermediate table for downstream use
        write_table(df, table_name + '_intermediate1',F.array(UPDATED_AT_TS), [F.days(UPDATED_AT_TS)], spark)
        logger.info(f"Wrote filtered OS lookup table to intermediate table: {table_name + '_intermediate1'}")
        # Read back the intermediate table (ensures correct format/partitioning)
        df = spark.read.table(table_name + '_intermediate1')
        logger.info(f"Filtering OS lookup table to latest updated_at_ts: {max_ts}")
        return df
    else:
        logger.warning(f"OS lookup table {table_name} does not exist. Returning empty DataFrame.")
        # Table does not exist or cannot be read; return empty DataFrame with expected schema
        schema = StructType([
            StructField(OS_STRING_COL, StringType(), True),
            StructField(UPDATED_AT_TS, TimestampType(), True)
        ])
        return spark.createDataFrame(spark.sparkContext.emptyRDD(), schema)

def derive_os_normalisation(srdm, os_lookup, args, spark):
    """
    Derives the OS normalisation from the SRDM and OS lookup tables.
    Combines unique OS strings from both sources, normalizes them, and adds updated_at_ts.
    Args:
        srdm: SRDM table (DataFrame)
        os_lookup: OS lookup table (DataFrame)
        args: Arguments (for eventTimestampEndEpoch)
        spark: SparkSession
    Returns:
        DataFrame with normalized OS information and updated_at_ts
    """
    products_df = None
    # 1. Check if eolProductLookup is provided
    if hasattr(args, 'eolProductLookup') and args.eolProductLookup and table_exists(".".join(args.eolProductLookup.split(".")[:-1]), args.eolProductLookup.split(".")[-1], spark):
        df = spark.read.table(args.eolProductLookup)
        columns = set(df.columns)
        required_cols = {'parsed_interval_timestamp_ts', 'event_timestamp_ts', 'isValid'}
        if required_cols.issubset(columns):
            products_df = df.filter(
                f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) "
                f"and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) "
                f"and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid"
            )
        else:
            products_df = df

    # 2. Combine unique OS strings from both SRDM and lookup tables
    input_df = srdm.select(OS_STRING_COL).unionByName(os_lookup.select(OS_STRING_COL)).distinct()
    # 3. Initialize SparkNormalizer
    if products_df is not None:
        spark_normalizer = SparkNormalizer(
            spark_session=spark,
            products_df=products_df
        )
    else:
        spark_normalizer = SparkNormalizer(spark_session=spark)
    logger.info("Normalizing OS strings using SparkNormalizer.")
    # 4. Normalize the DataFrame; adds a struct column 'normalization_result' with normalization details
    result_df = spark_normalizer.normalize_df(input_df, input_col=OS_STRING_COL).withColumn(
        UPDATED_AT_TS, F.expr(f"to_timestamp({args.eventTimestampEndEpoch}/1000)"))
    return result_df


def clear_temp_tables(args, spark):
    table_name = args.outputPath
    spark.sql(f"DROP TABLE IF EXISTS {table_name}_intermediate1")

