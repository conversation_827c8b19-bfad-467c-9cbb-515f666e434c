import logging
from urllib.parse import urljoin
from airflow.providers.http.operators.http import HttpOperator
from airflow.providers.amazon.aws.operators.s3 import S3DeleteObjectsOperator
from typing import List, Dict
from commons.ei.ei_constants.common_constants import DruidApiConstants, CommonConstants
from airflow.models.variable import Variable
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from airflow.operators.python import PythonOperator
sparkOperator = SparkOperatorFactory.get_default()

def get_source_values(source_list):
    for source_name in source_list.values():
        if isinstance(source_name, dict):
            yield from get_source_values(source_name)
        else:
            yield source_name

class EIUtils:
    LOGGER = logging.getLogger('ei_utils')

    @staticmethod
    def delete_s3_sources_tasks(base_path: str, table_sources_key, source_type=None):
        data_sources = Variable.get(table_sources_key, deserialize_json=True)
        delete_bridge_table_list = list(get_source_values(data_sources[source_type] if source_type else data_sources))
        delete_bridge_table_tasks = [S3DeleteObjectsOperator(task_id=f'delete_{table}', bucket='{{ var.value.SDS_EI_INVENTORY_JOB_DATALAKE_BUCKET }}', prefix=f'{{{{ var.value.{base_path} }}}}{table}', aws_conn_id='aws_default') for table in delete_bridge_table_list]
        return [delete_bridge_table_tasks]

    @staticmethod
    def get_druid_delete_task(olap_data_sources):
        druid_delete_tasks = []
        olap_data_sources = Variable.get(olap_data_sources, deserialize_json=True)
        druid_olap_table_name_list = list(get_source_values(olap_data_sources))
        for table in druid_olap_table_name_list:
            sds_ei_druid_delete_tasks = HttpOperator(task_id=f'sds_ei_delete_{table}', retries=2, http_conn_id=DruidApiConstants.DRUID_API_KEY, endpoint=f"{{{{ urljoin(DruidApiConstants.DS_DELETE_URL,'{table}') }}}}", method=CommonConstants.DELETE, headers=CommonConstants.CONTENT_TYPE_HEADER)
            druid_delete_tasks.append(sds_ei_druid_delete_tasks)
        return [druid_delete_tasks]

    @staticmethod
    def group_tasks(source_keys):
        unique_entities = [*set((source_key.split('__')[1] for source_key in source_keys))]
        entity_source_key_comb = dict()
        for entity in unique_entities:
            entity_source_key_comb[entity] = []
            for source_key in source_keys:
                if entity == source_key.split('__')[1]:
                    entity_source_key_comb[entity].append(source_key)
        EIUtils.LOGGER.info(entity_source_key_comb)
        return entity_source_key_comb
    
def create_os_extraction_tasks(config_keys, dag_id, find_task_run_fn):
    task_run_status_list = []
    for task_id in config_keys:
        job_name = task_id.replace('_config', '')
        sds_os_extraction_run_status_task = PythonOperator(task_id=f'find_task_runs_{job_name}', python_callable=find_task_run_fn, op_kwargs={'task_name': f'sds_os_extraction.{job_name}', 'dag_id': dag_id, 'start_epoch_default_key': 'sds_os_extraction_start_epoch_default'}, retries=2)
        sds_os_extraction_task = SparkOperatorFactory.get(task_id=f'{job_name}', from_var=f'{task_id}', retries=2)
        task_run_status_list.append(sds_os_extraction_run_status_task >> sds_os_extraction_task)
    return task_run_status_list