import logging
import re
import requests
import os
from urllib.parse import urljoin
from airflow.models.variable import Variable
from commons.pe.common_utils import Utils
from airflow.models.connection import Connection


conn = Connection.get_connection_from_secrets("management_api")
HOST = os.getenv('MANAGEMENT_API_HOST', conn.host)  
PORT = os.getenv('MANAGEMENT_API_PORT', conn.port)
SCHEMA = os.getenv('MANAGEMENT_API_SCHEMA', conn.schema)

config_manager_base_url = urljoin(f"{SCHEMA}://{HOST}:{PORT}/", "sds_mgmnt/config-manager/api/v1/config-item/")
                   

def get_entities_from_config_manager(**kwargs):
    # Python function to get all entities from config manager API.
    logger = logging.getLogger('get_entities_task')
    logger.info(f"HOST: {HOST}, SCHEMA: {SCHEMA}, PORT:{PORT}")
    logger.info(f"Config Manager Base URL: {config_manager_base_url}")
    # Get config manager endpoint from environment or variables
    config_manager_endpoint = config_manager_base_url + "deployment_config"
    if not config_manager_endpoint:
        raise ValueError("CONFIG_MANAGER_ENDPOINT not configured")

    # Get entities from config manager
    headers = {
        "Authorization": Utils.get_keycloak_token(),
        "Content-Type": "application/json"
    }
    try:
        response = requests.get(
            url=config_manager_endpoint,
            headers=headers,
            verify=Utils.SSL_VERIFY,
            timeout=60
        )
        response.raise_for_status()
        payload = response.json()
        entities = payload.get("config_value").get("spark_job_configs").get("source_models").get("global_entity_config")
        result = [re.match(r'^(.+?)_entity_config$', entity).group(1) for entity in entities if re.match(r'^(.+?)_entity_config$', entity)]
        logger.info(f"Retrieved {len(result)} entities from global entities in deployment config from config manager")
        return result
    except Exception as e:
        logger.error(f"Failed to get deployment config: {str(e)}")
        raise e
    
    

def build_graphql_queries(**kwargs):
    """
    Build one GraphQL query per entity from an Airflow Variable template.
    Returns a list of dictionaries containing queries and task naming info.
    """
    logger = logging.getLogger('build_graphql_queries')
    ti = kwargs['ti']
    entities = ti.xcom_pull(task_ids='get_entities_from_config_manager')
    
    if not entities:
        raise ValueError("No entity names could be derived from deployment config")
    
    graphql_template = Variable.get('SDS_EI_LINEAGE_GRAPHQL_TEMPLATE', default_var=None).encode('utf-8').decode('unicode_escape')
    if not graphql_template:
        raise ValueError("Airflow Variable 'SDS_EI_LINEAGE_GRAPHQL_TEMPLATE' not set")
    
    result = []
    for entity in entities:
        query = graphql_template.replace('ENTITY_NAME', entity.replace('_', ' ').title())
        logger.info(f"Created query for entity {entity}: {query}")
        result.append({
            "query": {"query": query},
            "entity_name": entity
        })
    
    logger.info(f"Prepared {len(result)} GraphQL requests for entities: {entities}")
    return result


def post_lineage_queries(**kwargs):
    """
    Posts a single GraphQL query to the lineage API endpoint
    """
    logger = logging.getLogger('post_lineage_task')
    
    # Get lineage API endpoint from variables
    lineage_endpoint = Variable.get('LINEAGE_API_URI')
    if not lineage_endpoint:
        logger.error("LINEAGE_API_URI not configured")
        raise ValueError("LINEAGE_API_URI not configured")
        
    # Get the query from task instance
    query = kwargs.get('query')
    if not query:
        logger.error("No query provided to post_lineage_queries")
        raise ValueError("No query provided to post_lineage_queries")

    headers = {
        "Authorization": Utils.get_keycloak_token(),
        "Content-Type": "application/json"
    }

    try:
        url = urljoin(lineage_endpoint, '/lineage/graphql')
        response = requests.post(
            url=url,
            json=query,
            params={"uiData":True, "refresh":True},
            headers=headers,
            verify=Utils.SSL_VERIFY,
            timeout=60
        )
        response.raise_for_status()
        logger.info(f"Successfully posted query to lineage API: {query}")
        return response.json()
        
    except Exception as e:
        logger.error(f"Failed to post query to lineage API: {str(e)}")
        raise e
