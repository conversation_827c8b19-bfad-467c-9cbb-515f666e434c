class CommonConstants:
    """Class containing constants common ei module"""

    GET = "GET"
    POST = "POST"
    DELETE = "DELETE"
    CONTENT_TYPE_HEADER = {"Content-Type": "application/json"}


class DruidApiConstants:
    """Class containing constants used for Druid"""

    DRUID_API_KEY = "druid_api"
    DS_DELETE_URL = "/druid/coordinator/v1/datasources/%s"
    DS_DISABLE_URL = "/druid/coordinator/v1/datasources/%s/markUnused"
    DS_INDEXING_URL = "/druid/indexer/v1/task/"
    DS_QUERY_URL = "/druid/v2/sql/"


class OLAPConnConstants:
    SDS_EI_OLAP_QUERY_CONFIG_PATH = "sds-ei-configs/api-configs/sds_ei_insight_api_payload.json"
    SDS_EI_OLAP_QUERY_FILTER_PATH="sds-ei-configs/api-configs/sds_ei_get_date_filter_payload.json"

class DataDictionaryPath:
    SDS_EI_GRAPH_DATA_DICTIONARY_PATH = "sds-ei-configs/ui-configs/sds_ei_graph_data_dictionary.json"
    SDS_EI_DATA_DICTIONARY_PATH = "sds-ei-configs/ui-configs/sds_ei_data_dictionary.json"
    SDS_EI_REFERENCE_DATA_DICTIONARY_PATH = "sds-ei-configs/ui-configs/sds_ei_reference_dd.json"
    SDS_EI_RELATIONSHIP_DATA_DICTIONARY_PATH = "sds-ei-configs/ui-configs/sds_ei_relationship_data_dictionary.json"
    SDS_EI_REFERENCE_RELATIONSHIP_DATA_DICTIONARY_PATH = "sds-ei-configs/ui-configs/sds_ei_reference_relation_dd.json"


class UiApiContants:
    """Class containing constants used for ui mapping"""

    Ui_API_URL = "/sds_mgmnt/api/v1/config/entity_inventory/"
    Ui_API_ENTITY_MIN_MAX_URL = "/sds_mgmnt/api/v1/config/entity-timestamps/"


class DQConnConstants:
    DQ_CONFG_META_ENDPOIN = "/sds_mgmnt/config-manager/api/v1/config-item/list-configs-meta/?config_item_type=data_quality"

