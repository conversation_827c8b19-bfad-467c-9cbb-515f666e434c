"""
This module contains constants used in EI
"""
from plugins.pe.sds_emr_plugin.operators.sds_emr_utils import ClusterType, EMRConstants


class EIConstants:
    """
    Class that contains constants related to EI module
    """

    SDS_EI_MODULE_NAME = "SDS_EI_PIPELINE"

    EMR_CLUSTER_TYPE = ClusterType.LARGE.value

    EI_CLUSTER_NAME = "SDS_EI"
    EI_CLUSTER_ID = "DA_JOBS"
    EI_OLAP_DATASOURCES = "ei_olap_datasources"
    EI_BASE_PATH = "sds_ei_path"
    EI_CLUSTER_TYPE = ClusterType.LARGE.value
    EI_DATA_SOURCES = "ei_data_sources"
    SDS_EI_INVENTORY_MODEL_CONFIG_KEYS ="sds-ei-configs/source_models/inventory_models.json"
    DATA_DICTIONARY_API_ENDPOINT ="/sds_mgmnt/config-manager/api/v1/config-item/"
    LIST_CONFIG_META_API_ENDPOINT ="/sds_mgmnt/config-manager/api/v1/config-item/list-configs-meta/"
    PATCH_STATUS_MANAGER ="/sds_mgmnt/config-manager/api/v1/status-manager/"
    SDS_EI_RELATION_DISAMBIGUATION_CONFIG_KEYS="sds-ei-configs/source_models/relationship_disambiguation.json"
    SDS_EI_INTRA_SOURCE_DISAMBIGUATED_MODEL_CONFIG_KEYS = "sds-ei-configs/source_models/intrasource_disambiguated_models.json"
    SDS_EI_INTER_SOURCE_DISAMBIGUATED_MODEL_CONFIG_KEYS = "sds-ei-configs/source_models/intersource_disambiguated_models.json"
    SDS_EI_RELATION_EXTRACTOR_CONFIG_KEYS ="sds-ei-configs/source_models/relationship_models.json"
    SDS_EI_PUBLISH_MODEL_CONFIG_KEYS = "sds-ei-configs/source_models/publisher.json"
    SDS_EI_OLAP_DAG_FACTORY_CONFIG = "olap/olap_dag_factory.json"
    SDS_EI_ENTITY_ENRICH_CONFIG_KEYS="sds-ei-configs/source_models/entity_rel_enrich.json"
    SDS_EI_DRUID_TABLES_CONFIG_KEYS = "sds-ei-configs/druid_tables.json"
    SDS_EI_DRUID_INDEXING_TASK_GROUP_NAME = "sds_ei_olap_population"
    SDS_EI_PROCESSED_DATA_BACKUP_TASK_GROUP_NAME = "sds_ei_data_backup"
    SDS_EI_PROCESSED_DATA_BACKUP_TASK_PREFIX = "sds_ei"
    SDS_EI_PROCESSED_DATA_BACKUP_CONFIG_KEYS = ["inventory_models"]
    SDS_EI_PROCESSED_DATA_BACKUP_JOB_CONFIG_KEY = "sds_ei_processed_data_backup_config"
    SDS_EI_REDIS_KEY_PREFIX = "sds_ei_redis_clear_key"
    EI_INTRA_RESOLVERS = "entity_intra_resolvers"
    PRE_UPGRADE_CONFIG_SNAPSHOT_VARS = "pre_upgrade_config_snapshot_vars"
    DYNAMIC_DAG_GEN_VARS="sds-ei-configs/source_models/dag_factory_config.json"
    SDS_EI_UPGRADE_VARIABLE_NAME="data_upgrade_spark_job_config"
    LIVY_CONNECTION_URL_KEY = "{}_{}_{}_{}".format(
        EI_CLUSTER_NAME, EI_CLUSTER_ID, EMR_CLUSTER_TYPE, EMRConstants.LIVY_URL_KEY
    )

    SDS_EI_PRE_CHECK_DATA_SOURCES_CONFIG_KEYS = [
        'authentication__active_directory__parser_cdm'
    ]
    SDS_EI_PRE_CHECK_CONFIG = "sds_ei_pre_check_config"
    SDS_OS_EXTRACTION_MODULE_NAME = "SDS_OS_EXTRACTION_PIPELINE"
    SDS_OS_EXTRACTION_CONFIG_PATH = "sds-ei-configs/os_extraction/sds_os_extraction_module.json"
