ARG AWS_KUBE_AZ_BASE_IMAGE="prevalentai/devops-utils:4-1-0-kubectl1.27.0-awscliv2-azcpv10-azcli-bookworm-12.10-20250428-slim"

FROM ${AWS_KUBE_AZ_BASE_IMAGE} AS runnerbase

USER root

ARG DAGS_DIR="/opt/airflow/sds/dags"
ARG COMMONS_DIR="/opt/airflow/sds/commons"
ARG PLUGINS_DIR="/opt/airflow/sds/plugins"

RUN mkdir -p ${DAGS_DIR} ${COMMONS_DIR} ${PLUGINS_DIR}

COPY ./dags/ei ${DAGS_DIR}/ei

COPY ./plugins/ei ${PLUGINS_DIR}/ei

COPY ./commons/ei ${COMMONS_DIR}/ei

RUN cd ${DAGS_DIR} \
    && tar -cvzf ei.tgz ei \
    && rm -rf ei \
    && cd ${COMMONS_DIR} \
    && tar -cvzf ei.tgz ei \
    && rm -rf ei \
    && cd ${PLUGINS_DIR} \
    && tar -cvzf ei.tgz ei \
    && rm -rf ei \
    && chown -R sdsuser /opt/airflow/sds

USER sdsuser

ENTRYPOINT ["/usr/bin/dumb-init", "--", "/entrypoint"]

CMD ["bash"]