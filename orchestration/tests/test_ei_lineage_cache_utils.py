import pytest
from unittest.mock import patch, MagicMock
from commons.ei.lineage_cache_utils import get_entities_from_config_manager, build_graphql_queries, post_lineage_queries

@pytest.fixture
def mock_response():
    mock = MagicMock()
    mock.json.return_value = {
        "config_value": {
            "spark_job_configs": {
                "source_models": {
                    "global_entity_config": [
                        "customer_entity_config",
                        "order_entity_config"
                    ]
                }
            }
        }
    }
    return mock

@pytest.fixture
def mock_variable():
    return 'query { ENTITY_NAME { id name } }'

@patch('commons.pe.common_utils.Utils.get_keycloak_token', return_value='dummy-token')
@patch('requests.get')
def test_get_entities_from_config_manager_success(mock_get, mock_token, mock_response):
    mock_get.return_value = mock_response
    result = get_entities_from_config_manager()
    
    assert result == ['customer', 'order']
    mock_get.assert_called_once()
    assert mock_token.called

@patch('requests.get')
def test_get_entities_from_config_manager_failure(mock_get):
    mock_get.side_effect = Exception("API Error")
    
    with pytest.raises(Exception) as exc_info:
        get_entities_from_config_manager()
    assert str(exc_info.value) == "API Error"

@patch('airflow.models.variable.Variable.get')
def test_build_graphql_queries_success(mock_variable_get, mock_variable):
    mock_variable_get.return_value = mock_variable
    
    context = {
        'ti': MagicMock()
    }
    context['ti'].xcom_pull.return_value = ['customer', 'order']
    
    result = build_graphql_queries(**context)
    
    assert 'entities' in result
    assert 'queries' in result
    assert len(result['queries']) == 2
    assert result['queries'][0]['query'] == 'query { customer { id name } }'
    assert result['queries'][1]['query'] == 'query { order { id name } }'
    assert result['entities'] == ['customer', 'order']

@patch('airflow.models.variable.Variable.get')
def test_build_graphql_queries_no_entities(mock_variable_get):
    context = {
        'ti': MagicMock()
    }
    context['ti'].xcom_pull.return_value = []
    
    with pytest.raises(ValueError) as exc_info:
        build_graphql_queries(**context)
    assert str(exc_info.value) == "No entity names could be derived from deployment config"

@patch('commons.pe.common_utils.Utils.get_keycloak_token', return_value='dummy-token')
@patch('requests.post')
@patch('airflow.models.variable.Variable.get')
def test_post_lineage_queries_success(mock_variable_get, mock_post, mock_token):
    mock_variable_get.return_value = 'https://lineage-api'
    mock_response = MagicMock()
    mock_response.json.return_value = {'data': {'some': 'data'}}
    mock_post.return_value = mock_response
    
    query = {'query': 'test query'}
    result = post_lineage_queries(query=query)
    
    assert result == {'data': {'some': 'data'}}
    mock_post.assert_called_once()
    assert mock_token.called

@patch('airflow.models.variable.Variable.get')
def test_post_lineage_queries_no_endpoint(mock_variable_get):
    mock_variable_get.return_value = None
    
    with pytest.raises(ValueError) as exc_info:
        post_lineage_queries(query={'query': 'test'})
    assert str(exc_info.value) == "LINEAGE_API_URI not configured"

def test_post_lineage_queries_no_query():
    with pytest.raises(ValueError) as exc_info:
        post_lineage_queries()
    assert str(exc_info.value) == "No query provided to post_lineage_queries"
