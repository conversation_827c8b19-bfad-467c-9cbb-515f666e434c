"""
DAG Generator for Data Quality Completeness and Dimension Compaction
Optimized for readability, error handling, and maintainability.
Dummy data generation is retained for now (remove before production).
"""

import json
import requests
from datetime import datetime
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
from commons.pe.common_utils import Utils
from commons.ei.analytical_utils import AnalyticalUtils
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.sds_date_utils import SDSDateUtils
from commons.ei.ei_constants.ei_constants import EIConstants
from commons.ei.ei_constants.common_constants import DQConnConstants
import pendulum
from urllib.parse import urljoin
from airflow.models.connection import Connection
from airflow.models.variable import Variable

# --- Configuration and Constants ---
try:
    conn = Connection.get_connection_from_secrets("management_api")
    HOST = conn.host
    SCHEMA = conn.schema
    PORT = conn.port
    config_url = urljoin(f"{SCHEMA}://{HOST}:{PORT}/", EIConstants.DATA_DICTIONARY_API_ENDPOINT)
    RELATIONSHIP_CONFIG_TYPES = {"relationship_models", "relationship_disambiguation"}
    ENRICH_CONFIG_TYPES = {"entity_rel_enrich"}
    ssl_verify = Variable.get("MANAGEMENT_API_SSL_VERIFY", default_var=True, deserialize_json=True)
    factory_config = Utils.read_data_from_shared_filesystem(EIConstants.DYNAMIC_DAG_GEN_VARS)
except Exception as error:
    raise RuntimeError(f"Failed to load Airflow connection or config: {error}")

# Load DAG factory config from Airflow Variable
try:
    dq_dag_factory_config = Variable.get("DQ_DAG_FACTORY_CONFIG", default_var=None, deserialize_json=True)
except Exception as error:
    raise RuntimeError(f"Failed to load DQ_DAG_FACTORY_CONFIG variable: {error}")

# NOTE: Replace with secure token retrieval in production
HEADERS = {'Content-Type': 'application/json', 'Authorization': Utils.get_keycloak_token()}

# --- Utility Functions ---
def fetch_metadata_by_type(config_item_type):
    """
    Fetch metadata from the config manager for a given config item type.
    Groups the item names by their entity.
    Dummy data is returned for now (replace with API call in production).
    """
    try:
        grouped_metadata = factory_config.get(config_item_type)
        if config_item_type in RELATIONSHIP_CONFIG_TYPES or config_item_type in ENRICH_CONFIG_TYPES:
            modified_grouped_metadata = {}

            for key, items in grouped_metadata.items():
                for item in items:
                    suffix = item.split("__")[-2] if config_item_type in ENRICH_CONFIG_TYPES else item.split("__")[-1]
                    new_key = f"{key}__{suffix}"
                    modified_grouped_metadata.setdefault(new_key, []).append(item)
            return modified_grouped_metadata
        return grouped_metadata
    except Exception as error:
        print(f"Error fetching metadata for type '{config_item_type}': {error}")
        return {}

def fetch_context_variables(required_keys):
    """
    Fetch context variables from the config API and return only the requested subset.
    """
    try:
        response = requests.get(f"{config_url}context", headers=HEADERS, verify=ssl_verify)
        response.raise_for_status()
        data = response.json()
        config_values = data.get("config_value", {})
        return {key: config_values[key] for key in required_keys if key in config_values}
    except requests.RequestException as error:
        print(f"Error fetching context variables: {error}")
        return {}
    except ValueError:
        print("Error parsing context API response.")
        return {}

def fetch_data_quality_config_meta():
    """
    Fetches the list of data quality config items from the management API.
    Returns a dict mapping config item names to their metadata.
    """
    try:
        config_meta_endpoint = urljoin(f"{SCHEMA}://{HOST}:{PORT}/", DQConnConstants.DQ_CONFG_META_ENDPOIN)
        response = requests.get(config_meta_endpoint, headers=HEADERS, verify=ssl_verify)
        response.raise_for_status()
        data_quality_config_items = response.json()
        return {item['name']: item['name'] for item in data_quality_config_items}
    except Exception as error:
        raise RuntimeError(f"Error fetching data quality config meta: {error}")

def create_dynamic_dag(
        template_variable: str,
        dag_name: str,
        task_list: list
) -> SDSPipelineDAG:
    """
    Creates a dynamic DAG for the given template_variable and dag_name keys, using the task list.
    Args:
        template_variable (str): The top-level key in the configuration (e.g., "entity_rel_enrich").
        dag_name (str): The child key in the configuration (e.g., "graph_entity_enrich").
        task_list (list): List of task dicts to include in the DAG.
    Returns:
        SDSPipelineDAG: The dynamically created DAG using the custom SDSDAG class.
    """
    dag_id = f"{dag_name}_dag"
    default_args = {
        "start_date": datetime(2020, 1, 1),
        "catchup": False,
    }
    try:
        with SDSPipelineDAG(
                pipeline_id=EIConstants.SDS_EI_MODULE_NAME,
                dag_id=dag_id,
                default_args=default_args,
                description=f"DAG for {template_variable} - {dag_name}",
                schedule_interval=None,
                tags=[EIConstants.SDS_EI_MODULE_NAME, "DATA_QUALITY", template_variable.upper()],
                user_defined_macros={
                    "render_variable": Utils.render_variable,
                    "get_dq_completeness_job_configs": AnalyticalUtils.get_dq_completeness_job_configs,
                    "json": json,
                    "str": str,
                    "get_start_date": SDSDateUtils.get_start_date,
                    "get_end_date": SDSDateUtils.get_end_date,
                    "pendulum": pendulum,
                    "urljoin": urljoin,
                },
        ) as dag:
            start_task = EmptyOperator(task_id="dq_start")
            end_task = EmptyOperator(task_id="dq_end")
            with TaskGroup(group_id=f"{dag_name}_tasks") as task_group:
                for task in task_list:
                    spark_task = SparkOperatorFactory.get(
                        task_id=task["task_id"],
                        from_conf=f"get_dq_completeness_job_configs('{task['task_id']}','{task['object_type']}','{task['object_name']}','{task['table_info']}','{task['job_type']}', '{task['dq_config']}', '{template_variable}')",
                        retries=2,
                    )
            start_task >> task_group >> end_task
        return dag
    except Exception as error:
        print(f"Error creating dynamic DAG '{dag_name}': {error}")
        return None

def create_dynamic_dimension_compaction_dag(
        template_variable: str,
        dag_name: str,
        task_dict: dict
) -> SDSPipelineDAG:
    """
    Creates a dynamic DAG for dimension compaction jobs.
    Args:
        template_variable (str): The top-level key in the configuration.
        dag_name (str): The DAG name.
        task_dict: Dictionary of task configs.
    Returns:
        SDSPipelineDAG: The dynamically created DAG.
    """
    dag_id = f"{dag_name}_dag"
    default_args = {
        "start_date": datetime(2020, 1, 1),
        "catchup": False,
    }
    try:
        with SDSPipelineDAG(
                pipeline_id=EIConstants.SDS_EI_MODULE_NAME,
                dag_id=dag_id,
                default_args=default_args,
                description=f"DAG for {template_variable} - {dag_name}",
                schedule_interval=None,
                tags=[EIConstants.SDS_EI_MODULE_NAME, "DATA_QUALITY", template_variable.upper()],
                user_defined_macros={
                    "render_variable": Utils.render_variable,
                    "get_dq_dimension_compaction_job_configs": AnalyticalUtils.get_dq_dimension_compaction_job_configs,
                    "json": json,
                    "str": str,
                    "get_start_date": SDSDateUtils.get_start_date,
                    "get_end_date": SDSDateUtils.get_end_date,
                    "pendulum": pendulum,
                    "urljoin": urljoin,
                },
        ) as dag:
            start_task = EmptyOperator(task_id=f"{dag_name}_start")
            end_task = EmptyOperator(task_id=f"end_{dag_name}_end")
            with TaskGroup(group_id=f"{dag_name}_tasks") as task_group:
                for task_name, task_props in task_dict.items():
                    object_type = task_props['object_type']
                    object_name = task_props['object_name']
                    dq_config = task_props['dq_config']
                    spark_task = SparkOperatorFactory.get(
                        task_id=task_name,
                        from_conf=f"get_dq_dimension_compaction_job_configs('{task_name}','{object_type}','{object_name}', '{dq_config}', '{template_variable}')",
                        retries=2,
                    )
            start_task >> task_group >> end_task
        return dag
    except Exception as error:
        print(f"Error creating dimension compaction DAG '{dag_name}': {error}")
        return None

# --- Context and Schema Setup ---
context_variables = fetch_context_variables(["EI_SCHEMA_NAME", "KG_FRAGMENT_SCHEMA", "DQ_DIMENSIONS"])
ei_schema_name = context_variables.get("EI_SCHEMA_NAME")
kg_fragment_schema_name = context_variables.get("KG_FRAGMENT_SCHEMA")
# Fetch config meta for data_quality once per DAG template
data_quality_config_meta = fetch_data_quality_config_meta()

# --- DAG Generation Loop ---
for dag_template_name, job_config_list in dq_dag_factory_config.items():
    if dag_template_name == 'data_quality_dimension_compaction':
        dimension_compaction_tasks = {}
        for job_config in job_config_list:
            config_item_type = job_config.get("config_item_type")
            if not config_item_type:
                print(f"Missing config_item_type in job: {job_config}")
                continue
            metadata_by_type = fetch_metadata_by_type(config_item_type)
            is_relationship = config_item_type in RELATIONSHIP_CONFIG_TYPES

            for dag_name, table_names in metadata_by_type.items():
                if dag_name.lower().startswith("sds_em"):
                    continue
                object_type = "relationship" if is_relationship or (
                        dag_name.split("__")[-2] == "relationship" and config_item_type == "entity_rel_enrich"
                ) else "entity"
                object_name = dag_name.split("__")[-1]
                task_key = f"sds_ei__dq_dimension_compaction_{object_name}"
                config_prefix = "relationship" if is_relationship else "entity"
                dq_config_name = f"sds_ei__{config_prefix}_data_quality__{object_name}"
                dq_config_value = data_quality_config_meta.get(dq_config_name, None)
                dimension_compaction_tasks[task_key] = {
                    "object_type": object_type,
                    "object_name": object_name,
                    "dq_config": dq_config_value
                }
        dag_name = f"sds_ei__dq__dimension_compaction"
        dag_name2 = "sds_ei__dq__dimension_compaction_final"
        create_dynamic_dimension_compaction_dag(dag_template_name, dag_name, dimension_compaction_tasks)
        create_dynamic_dimension_compaction_dag(dag_template_name, dag_name2, dimension_compaction_tasks)
    else:
        for job_config in job_config_list:
            config_item_type = job_config.get("config_item_type")
            if not config_item_type:
                print(f"Missing config_item_type in job: {job_config}")
                continue
            metadata_by_type = fetch_metadata_by_type(config_item_type)
            tasks_by_dag = {}
            is_relationship = config_item_type in RELATIONSHIP_CONFIG_TYPES
            for dag_name, table_names in metadata_by_type.items():
                if dag_name.lower().startswith("sds_em"):
                    continue
                object_type = "relationship" if is_relationship or (
                        dag_name.split("__")[-2] == "relationship" and config_item_type == "entity_rel_enrich"
                ) else "entity"
                resolver_template = job_config.get("resolver", "")
                object_name = dag_name.split("__")[-1]
                resolver = None
                dag_name_full = f"{dag_name}__dq_completeness"
                if resolver_template:
                    try:
                        resolver = resolver_template.format(
                            OBJECT_NAME=object_name,
                            EI_SCHEMA_NAME=ei_schema_name,
                            KG_FRAGMENT_SCHEMA=kg_fragment_schema_name
                        )
                    except KeyError as error:
                        print(f"Missing context variable for resolver formatting: {error}")
                table_info = []
                for table in table_names:
                    entry = {"table_name": f"{ei_schema_name}.{table}"}
                    if resolver is not None:
                        entry["resolver_table"] = resolver
                    table_info.append(entry)
                if is_relationship or config_item_type in ENRICH_CONFIG_TYPES:
                    task_id = dag_name_full
                    dag_name_key = dag_name_full.replace(f"__{object_name}", "")
                else:
                    task_id = dag_name_full
                    dag_name_key = dag_name_full
                config_prefix = "relationship" if is_relationship else "entity"
                dq_config_name = f"sds_ei__{config_prefix}_data_quality__{object_name}"
                dq_config = data_quality_config_meta.get(dq_config_name, None)
                task_properties = {
                    "task_id": task_id,
                    "table_info": json.dumps(table_info),
                    "object_type": object_type,
                    "object_name": object_name,
                    "job_type": config_item_type,
                    "dq_config": dq_config
                }
                tasks_by_dag.setdefault(dag_name_key, []).append(task_properties)
            for dag_name, task_properties_list in tasks_by_dag.items():
                create_dynamic_dag(
                    dag_template_name,
                    dag_name,
                    task_properties_list
                )
