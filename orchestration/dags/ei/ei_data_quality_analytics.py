import json
import logging
from datetime import datetime
from urllib.parse import urljoin
import pendulum
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.sds_date_utils import SDSDateUtils
from commons.pe.common_utils import Utils
from commons.ei.analytical_utils import AnalyticalUtils
from commons.ei.ei_constants.common_constants import DruidApiConstants
from commons.ei.ei_constants.ei_constants import EIConstants
from commons.ei.ei_utils import EIUtils
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG, SDSHeadDAG
from commons.ei.analytical_utils import find_task_run

def create_dag(task_dicts):
    with SDSPipelineDAG(pipeline_id=EIConstants.SDS_EI_MODULE_NAME, dag_id='sds_ei_data_quality_jobs_dag', description='SDS EI Data Quality', start_date=datetime(2000, 1, 1), render_template_as_native_obj=True, catchup=False, concurrency=6, tags=[EIConstants.SDS_EI_MODULE_NAME, 'DATA_ANALYTICS'], user_defined_macros={'render_variable': Utils.render_variable, 'get_start_date': SDSDateUtils.get_start_date, 'find_task_run': find_task_run, 'get_end_date': SDSDateUtils.get_end_date, 'get_data_quality_job_configs': AnalyticalUtils.get_data_quality_job_configs, 'json': json, 'str': str, 'pendulum': pendulum, 'urljoin': urljoin, 'DruidApiConstants': DruidApiConstants}) as sds_ei_analytic_jobs:
        sds_ei_analytic_jobs_start = EmptyOperator(task_id='sds_ei_analytic_jobs_start', wait_for_downstream=True)
        sds_ei_analytic_jobs_end = EmptyOperator(task_id='sds_ei_analytic_jobs_end')
        for entity in task_dicts:
            entity_grp = f'{entity}'.split('__')[-1]
            entity_job_start = EmptyOperator(task_id=f'{entity}_job_start')
            entity_job_end = EmptyOperator(task_id=f'{entity}_job_end')
            with TaskGroup(group_id=entity_grp) as var_grp:
                for task in ['pattern_outlier', 'regex_pattern_analysis', 'temporal_analysis']:
                    loader_job_task = SparkOperatorFactory.get(task_id=f'{task}', from_conf=f"get_data_quality_job_configs('{task}','{entity_grp}')", retries=2)
                    entity_job_start >> loader_job_task >> entity_job_end
                sds_ei_analytic_jobs_start >> entity_job_start >> var_grp >> entity_job_end >> sds_ei_analytic_jobs_end
dag_gen_config = Utils.read_data_from_shared_filesystem(relative_file_path='sds-ei-configs/source_models/sds_ei__entity_data_quality.json')
create_dag(dag_gen_config)