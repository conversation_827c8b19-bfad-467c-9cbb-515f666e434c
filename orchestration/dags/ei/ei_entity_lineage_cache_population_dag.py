import json
from datetime import datetime
from urllib.parse import urljoin
import pendulum
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.models.xcom_arg import XComArg
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.sds_date_utils import SDSDateUtils
from commons.pe.common_utils import Utils
from commons.ei.ei_constants.ei_constants import EIConstants
from commons.ei.lineage_cache_utils import get_entities_from_config_manager, build_graphql_queries, post_lineage_queries

# Create the DAG
with SDSPipelineDAG(
    pipeline_id=EIConstants.SDS_EI_MODULE_NAME,
    dag_id='sds_ei_entity_in_depth_lineage_cache_dag',
    description='SDS EI Entity Lineage Cache population DAG is used for caching entity lineage data for In-Depth UI page queries',
    start_date=datetime(2000, 1, 1),
    schedule_interval=None,
    render_template_as_native_obj=True,
    catchup=False,
    concurrency=1,
    tags=[EIConstants.SDS_EI_MODULE_NAME, 'LINEAGE', 'API'],
    user_defined_macros={
        'render_variable': Utils.render_variable,
        'get_start_date': SDSDateUtils.get_start_date,
        'get_end_date': SDSDateUtils.get_end_date,
        'json': json,
        'str': str,
        'pendulum': pendulum,
        'urljoin': urljoin,
        'get_keycloak_token': Utils.get_keycloak_token
    }
) as entity_lineage_dag:
    
    # Start and end tasks
    start_task = EmptyOperator(task_id='start_entity_lineage_processing', wait_for_downstream=True)
    end_task = EmptyOperator(task_id='end_entity_lineage_processing')
    
    # Task 1: Get entities from config manager API
    get_entities_task = PythonOperator(
        task_id='get_entities_from_config_manager',
        python_callable=get_entities_from_config_manager,
        retries=2,
        retry_delay=pendulum.duration(minutes=1),
        execution_timeout=pendulum.duration(minutes=30)
    )
    
    # Task 2: Build GraphQL queries (one per entity) from Airflow Variable template
    build_queries_task = PythonOperator(
        task_id='build_graphql_queries',
        python_callable=build_graphql_queries,
        retries=2,
        retry_delay=pendulum.duration(minutes=1),
        execution_timeout=pendulum.duration(minutes=30)
    )
    
    # Task 3: Post GraphQL queries to lineage API in parallel (mapped tasks) 
    post_lineage_tasks = PythonOperator.partial(
        task_id='post_lineage_graphql',
        python_callable=post_lineage_queries,
        retries=2,
        map_index_template = "{{task.op_kwargs['entity_name']}}",
        retry_delay=pendulum.duration(minutes=1)
    ).expand(
        op_kwargs=XComArg(build_queries_task).map(
            lambda x: {
                "query": x["query"],
                "entity_name": x["entity_name"]
            }
        )
    )

    start_task >> get_entities_task >> build_queries_task >> post_lineage_tasks >> end_task
