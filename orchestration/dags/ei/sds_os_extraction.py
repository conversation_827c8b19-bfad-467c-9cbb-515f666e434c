import json
from datetime import datetime
from airflow.operators.dummy import Dummy<PERSON>perator
from airflow.utils.task_group import TaskGroup
from commons.pe.common_utils import Utils
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.sds_date_utils import SDSDateUtils
from commons.ei.ei_constants.ei_constants import EIConstants
import pendulum
from urllib.parse import urljoin
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from airflow.operators.python import PythonOperator
from airflow.models.variable import Variable
import copy

from commons.ei.vra.vra_analytics_jobs import find_task_run

# Load factory config from shared filesystem
factory_config = Utils.read_data_from_shared_filesystem(relative_file_path=EIConstants.DYNAMIC_DAG_GEN_VARS)


def get_host_inventory_models(factory_config, filter_block):
    """
    Extract all host-related inventory models from the factory config.

    Args:
        factory_config (dict): Factory configuration containing inventory models
        filter_block (str): Key to filter inventory models (e.g., 'sds_ei__loader__host')

    Returns:
        list: List of models that start with "sds_ei__"
    """
    try:
        inventory_models = factory_config.get("inventory_models", {}).get(filter_block, {})
        host_models = [model for model in inventory_models if model.startswith("sds_ei__")]
        return host_models
    except Exception as error:
        print(f"Error extracting host inventory models: {error}")
        return []


def build_dynamic_input_path(host_models, input_path, os_column_name):
    """
    Build the dynamic inputPath argument from host models for Spark job configuration.

    Args:
        host_models (list): List of host inventory model names
        input_path (str): Base input path prefix
        os_column_name (str): Name of the OS column to append

    Returns:
        str: Formatted input path string for Spark job (semicolon-separated)
    """
    if not host_models:
        return ""

    # Build the input path list with format: base_path.model,os_column
    input_paths = []
    for model in host_models:
        path = f"{input_path}.{model},{os_column_name}"
        input_paths.append(path)

    return ";".join(input_paths)


def get_os_extraction_job_config(task_id, host_models):
    """
    Generate dynamic job configuration for OS extraction jobs.

    This function retrieves the base configuration from Airflow Variables and
    dynamically updates the inputPath argument based on the provided host models.

    Args:
        task_id (str): Task identifier (Airflow Variable name)
        host_models (list): List of host inventory model names

    Returns:
        str: JSON string of the updated job configuration
    """
    # Get base configuration from Airflow Variable
    base_config = Variable.get(task_id, deserialize_json=True)

    # Create a deep copy to avoid modifying the original configuration
    job_config = copy.deepcopy(base_config)

    # Extract existing input path and OS column name from current config
    args = job_config.get('args', [])
    existing_input_path = None
    os_column_name = "os"  # Default OS column name

    # Parse existing arguments to find inputPath and osColumnName values
    for i, arg in enumerate(args):
        if arg == "--inputPath" and i + 1 < len(args):
            existing_input_path = args[i + 1]
        elif arg == "--osColumnName" and i + 1 < len(args):
            os_column_name = args[i + 1]

    # Build dynamic input path by combining base path with host models
    dynamic_input_path = build_dynamic_input_path(host_models, existing_input_path, os_column_name)

    # Update the args list with the new dynamic inputPath
    updated_args = []
    i = 0
    while i < len(args):
        if args[i] == "--inputPath" and i + 1 < len(args):
            # Replace the inputPath value with the dynamic one
            updated_args.append(args[i])
            updated_args.append(dynamic_input_path)
            i += 2  # Skip the next element as it's the old inputPath value
        else:
            updated_args.append(args[i])
            i += 1

    job_config['args'] = updated_args
    return json.dumps(job_config)


def create_os_extraction_tasks(config_keys, dag_id, find_task_run_fn):
    """
    Create OS extraction tasks based on configuration keys.

    This function dynamically creates tasks for each configuration entry,
    building the appropriate task names and configurations based on the
    entity name and filter block specified in the config.

    Args:
        config_keys (list): List of configuration dictionaries containing entityName and filterBlock
        dag_id (str): DAG identifier
        find_task_run_fn: Function to find task runs

    Returns:
        list: List of task dependencies (start_task >> main_task)
    """
    task_run_status_list = []

    for config_key in config_keys:
        # Build the configuration name for Airflow Variable lookup
        config_name = "sds_os_extraction_" + config_key.get("entityName") + "__job_config"
        filter_block = config_key.get("filterBlock")

        # Create job name by removing '_config' suffix
        job_name = config_name.replace('_config', '')

        # Create task to find previous task runs
        sds_os_extraction_run_status_task = PythonOperator(
            task_id=f'find_task_runs_{job_name}',
            python_callable=find_task_run_fn,
            op_kwargs={
                'task_name': f'sds_os_extraction.{job_name}',
                'dag_id': dag_id,
                'start_epoch_default_key': 'sds_os_extraction_start_epoch_default'
            },
            retries=2
        )

        # Get host models based on the filter block
        host_models = get_host_inventory_models(factory_config, filter_block)

        # Create the main OS extraction Spark task
        sds_os_extraction_task = SparkOperatorFactory.get(
            task_id=f'{job_name}',
            from_conf=f"get_os_extraction_job_config('{config_name}',{host_models})",
            retries=2
        )

        # Add task dependency: find_task_runs >> main_task
        task_run_status_list.append(sds_os_extraction_run_status_task >> sds_os_extraction_task)

    return task_run_status_list


# =============================================================================
# DAG Definition
# =============================================================================
# This DAG dynamically creates OS extraction tasks based on configuration
# from the shared filesystem. It supports multiple entity types and filter blocks
# defined in the configuration file.
# =============================================================================

with SDSPipelineDAG(
        pipeline_id=EIConstants.SDS_OS_EXTRACTION_MODULE_NAME,
        dag_id="sds_os_extraction_dag",
        description="SDS OS EXTRACTION - Dynamic OS extraction pipeline for host entities",
        start_date=datetime(2000, 1, 1),
        render_template_as_native_obj=True,
        catchup=False,
        concurrency=6,
        tags=["SDS OS EXTRACTION", "DATA_ANALYTICS", "SDM_ENRICHMENT"],
        user_defined_macros={
            "render_variable": Utils.render_variable,
            "get_os_extraction_job_config": get_os_extraction_job_config,
            "get_start_date": SDSDateUtils.get_start_date,
            "get_end_date": SDSDateUtils.get_end_date,
            "json": json,
            "str": str,
            "pendulum": pendulum,
            "urljoin": urljoin
        },
) as sds_os_extraction_dag:
    # Start and end tasks for the DAG
    sds_os_extraction_start = DummyOperator(
        task_id="sds_os_extraction_start",
        wait_for_downstream=True
    )
    sds_os_extraction_end = DummyOperator(task_id="sds_os_extraction_end")

    # Load configuration from shared filesystem
    sds_os_extraction_config_keys = Utils.read_data_from_shared_filesystem(
        relative_file_path=EIConstants.SDS_OS_EXTRACTION_CONFIG_PATH
    )["os_extraction_config"]

    # Create task group for OS extraction jobs
    with TaskGroup(group_id="sds_os_extraction") as sds_os_extraction_jobs:
        create_os_extraction_tasks(
            sds_os_extraction_config_keys,
            sds_os_extraction_dag.dag_id,
            find_task_run
        )

    # Define task dependencies: start >> jobs >> end
    sds_os_extraction_start >> sds_os_extraction_jobs >> sds_os_extraction_end
