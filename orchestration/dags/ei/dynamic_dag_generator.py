import json
import os
from datetime import datetime
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from airflow.models.variable import Variable
from commons.pe.common_utils import Utils
from commons.ei.analytical_utils import AnalyticalUtils
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.sds_date_utils import SDSDateUtils
from commons.ei.ei_constants.ei_constants import EIConstants
import pendulum
from urllib.parse import urljoin
import requests
from airflow.exceptions import AirflowSkipException

from commons.ei.ei_utils import EIUtils

dag_factory_config = Utils.read_data_from_shared_filesystem(EIConstants.DYNAMIC_DAG_GEN_VARS)
valid_dag_config = {
    key: {k: v for k, v in value.items() if isinstance(v, list) and v}
    for key, value in dag_factory_config.items()
    if isinstance(value, dict) and any(isinstance(v, list) and v for v in value.values())
}



def pre_execute_task_check(context, task_id: str, template_variable: str, dag_name: str):
    """
    Pre-execute function to check if task should be skipped based on API response.

    Args:
        context: Airflow context
        task_id: The task identifier
        template_variable: The template variable (e.g., "entity_rel_enrich")
        dag_name: The DAG name
    """
    print(f"Running pre_execute check for task: {task_id}")

    # Check if KG_CDS_ENABLED feature flag is enabled

    task_instance = context['task_instance']
    existing_response = task_instance.xcom_pull(key=f"cds_api_response_{task_id}")

    if existing_response is not None:
        print(f"✓ Pre-execute check already completed for task {task_id}, using existing result")

        # Re-add the rerun-info-json argument if it's not already there
        task = task_instance.task
        if hasattr(task, 'args') and isinstance(task.args, list):
            if '--rerun-info-json' not in task.args:
                task.args.extend(['--rerun-info-json', json.dumps(existing_response)])
                print(f"✓ Re-added --rerun-info-json argument to task {task_id}")

        # Check if we should skip based on existing response
        if existing_response.get("hasChanges") == False:
            print(f"✓ No changes detected for task {task_id} (from previous check), skipping execution")
            raise AirflowSkipException(f"Task {task_id} skipped - no changes detected")
        else:
            print(f"✓ Changes detected for task {task_id} (from previous check), proceeding with execution")
        return

    kg_cds_enabled = Variable.get("KG_CDS_ENABLED", default_var=False,deserialize_json=True)

    if not kg_cds_enabled:
        print(f"✓ KG_CDS_ENABLED is False, skipping pre-execute check for task {task_id}")
        return  # Exit early, don't run the API check
    else:
        print(f"✓ KG_CDS_ENABLED is True, proceeding with pre-execute check for task {task_id}")

    template_url_mapping = {
        "inventory_models": "inventorymodel"
    }
    validator_endpoint = os.environ.get('VALIDATOR_SVC_ENDPOINT')
    if not validator_endpoint:
        print(f"✗ VALIDATOR_SVC_ENDPOINT environment variable not set, proceeding with task execution")
        return
    url_segment = template_url_mapping.get(template_variable)
    if not url_segment:
        print(f"✗ Unknown template variable '{template_variable}', no URL mapping found. Proceeding with task execution")
        return
    url = f"{validator_endpoint.rstrip('/')}/v1/{url_segment}/execute/cds"
    print(f"Using API endpoint for template '{template_variable}': {url}")

    headers = EIUtils.get_api_headers()

    # Get task configuration to extract configPath and other values
    try:
        # Get the task configuration using the same method as get_dynamic_job_configs
        base_orchestration_config = Variable.get(
            f"sds_ei_{template_variable}_orchestration_config_template", deserialize_json=True)

        # Get DAG level override
        try:
            dag_override_config = Variable.get(f"{dag_name}_dag", deserialize_json=True)
        except KeyError:
            dag_override_config = {}

        # Get task level override
        try:
            task_override_config = Variable.get(task_id, deserialize_json=True)
        except KeyError:
            task_override_config = {}

        # Build config path
        pre_base_path = dag_override_config.get('base_config_path', '') or base_orchestration_config.get(
            'base_config_path', '')
        base_path = task_override_config.get('base_config_path', '') or pre_base_path
        full_path = f"{base_path}{task_id}"
        config_path = full_path if full_path.lower().startswith(('http', 'https')) else f"{full_path}.json"

        # Get JarLocation from application_file
        jar_location = task_override_config.get('application_file') or dag_override_config.get(
            'application_file') or base_orchestration_config.get('application_file', '/path/to/jar')

        # Get currentUpdateDate using the same logic as get_dynamic_job_configs
        # This should match the EVENT_TIMESTAMP from AnalyticalUtils
        current_update_date_template = "{{ get_end_date(data_interval_start, 'day','utc') }}"
        current_update_date = int(Utils.render_variable(current_update_date_template, context['task_instance']))

        # Get parsedIntervalEndEpoch using the same logic as get_dynamic_job_configs
        parsed_interval_end_epoch_template = "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}"
        parsed_interval_end_epoch = int(Utils.render_variable(parsed_interval_end_epoch_template, context['task_instance']))
    except Exception as e:
        print(f"✗ Error determining configuration for {task_id}: {e}")
        print("Skipping API call due to configuration error")
        return

    # Build payload with the correct values
    payload = {
        "EIJobArgs": {
            "configPath": config_path,
            "parsedIntervalEndEpoch": parsed_interval_end_epoch,
            "currentUpdateDate": current_update_date
        },
        "JarLocation": jar_location
    }

    try:
        print(f"Making POST request to API: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")

        response = requests.post(
            url,
            headers=headers,
            data=json.dumps(payload),
            timeout=200
        )

        print(f"Response status code: {response.status_code}")
        print(f"Response text: {response.text}")

        if response.status_code == 200:
            response_data = response.json()
            print(f"API Response: {json.dumps(response_data, indent=2)}")

            context['task_instance'].xcom_push(
                key=f"cds_api_response_{task_id}",
                value=response_data
            )
            print(f"✓ Stored API response in XCom with key: cds_api_response_{task_id}")

            task_instance = context['task_instance']
            task = task_instance.task

            # Add the rerun-info-json argument to the task's args
            if hasattr(task, 'args') and isinstance(task.args, list):
                # Check if --rerun-info-json is already in args
                if '--rerun-info-json' not in task.args:
                    task.args.extend(['--rerun-info-json', json.dumps(response_data)])
                    print(f"✓ Added --rerun-info-json argument directly to task {task_id}")

            # Check if hasChanges is false
            if response_data.get("hasChanges") == False:
                print(f"✓ No changes detected for task {task_id}, skipping execution")
                raise AirflowSkipException(f"Task {task_id} skipped - no changes detected")
            else:
                print(f"✓ Changes detected for task {task_id}, proceeding with execution")
        else:
            print(f"⚠ API call returned status code: {response.status_code}")
            # Continue execution even if API call fails
            context['task_instance'].xcom_push(
                key=f"cds_api_response_{task_id}",
                value={"error": f"API returned status {response.status_code}", "attempted": True}
            )
            print(f"✓ Stored API error status in XCom to prevent re-attempts")

    except requests.exceptions.ConnectionError as e:
        print(f"✗ Connection Error: {e}")
        print("API service not accessible, proceeding with task execution")
        context['task_instance'].xcom_push(
            key=f"cds_api_response_{task_id}",
            value={"error": "Connection error", "attempted": True}
        )
        print(f"✓ Stored connection error status in XCom to prevent re-attempts")

    except requests.exceptions.Timeout as e:
        print(f"✗ Timeout Error: {e}")
        print("API service timeout, proceeding with task execution")
        context['task_instance'].xcom_push(
            key=f"cds_api_response_{task_id}",
            value={"error": "Timeout error", "attempted": True}
        )
        print(f"✓ Stored timeout error status in XCom to prevent re-attempts")

    except AirflowSkipException:
        # Re-raise AirflowSkipException to properly skip the task
        raise

    except Exception as e:
        print(f"✗ Unexpected Error: {e}")
        print("Proceeding with task execution despite API error")
        context['task_instance'].xcom_push(
            key=f"cds_api_response_{task_id}",
            value={"error": f"Unexpected error: {str(e)}", "attempted": True}
        )
        print(f"✓ Stored unexpected error status in XCom to prevent re-attempts")


def merge_prefixed_keys(config):
    prefix_mapping = {"sds_ei__inventory_models__": "sds_ei__loader__"}
    models = config.get("inventory_models", {})
    merged = {}
    for key, val in models.items():
        new_key = next((key.replace(old, new, 1) for old, new in prefix_mapping.items() if key.startswith(old)), key)
        merged[new_key] = list(set(merged.get(new_key, []) + val))
    config["inventory_models"] = merged
    return config


def create_dynamic_dag(template_variable: str, dag_name: str, tasks: list) -> SDSPipelineDAG:
    """
    Creates a dynamic DAG for the given template_variable and dag_name keys, using the task list.

    Args:
        template_variable (str): The top-level key in the configuration (e.g., "entity_rel_enrich").
        dag_name (str): The child key in the configuration (e.g., "graph_entity_enrich").
        tasks (list): List of tasks to include in the DAG.

    Returns:
        SDSPipelineDAG: The dynamically created DAG using the custom SDSDAG class.
    """
    dag_id = f"{dag_name}_dag"

    default_args = {
        "start_date": datetime(2020, 1, 1),
        "catchup": False,
    }

    with SDSPipelineDAG(
            pipeline_id=EIConstants.SDS_EI_MODULE_NAME,
            dag_id=dag_id,
            default_args=default_args,
            description=f"DAG for {template_variable} - {dag_name}",
            schedule_interval=None,
            tags=[EIConstants.SDS_EI_MODULE_NAME, "DATA_ANALYTICS", template_variable.upper()],
            user_defined_macros={
                "render_variable": Utils.render_variable,
                "get_dynamic_job_configs": AnalyticalUtils.get_dynamic_job_configs,
                "json": json,
                "str": str,
                "get_start_date": SDSDateUtils.get_start_date,
                "get_end_date": SDSDateUtils.get_end_date,
                "pendulum": pendulum,
                "urljoin": urljoin,
            },
    ) as dag:
        start_task = EmptyOperator(task_id=f"{dag_name}_start")
        end_task = EmptyOperator(task_id=f"end_{dag_name}_end")

        with TaskGroup(group_id=f"{dag_name}_tasks") as task_group:
            for task in tasks:
                # Create a lambda function that captures the current task parameters
                def create_pre_execute_func(current_task, current_template_variable, current_dag_name):
                    return lambda context: pre_execute_task_check(
                        context, current_task, current_template_variable, current_dag_name
                    )

                pre_execute_func = create_pre_execute_func(task, template_variable, dag_name)

                spark_task = SparkOperatorFactory.get(
                    task_id=task,
                    from_conf=f"get_dynamic_job_configs(dag_run,'{task}', '{template_variable}', '{dag_name}')",
                    retries=2,
                    pre_execute=pre_execute_func
                )
        start_task >> task_group >> end_task

    return dag


# Validate and dynamically generate DAGs
for template_variable, dag_details in merge_prefixed_keys(valid_dag_config).items():
    for dag_name, tasks in dag_details.items():
        if not tasks or not isinstance(tasks, list):
            raise ValueError(f"Invalid tasks for {template_variable}.{dag_name}")
        globals()[f"{dag_name}_dag"] = create_dynamic_dag(template_variable, dag_name, tasks)