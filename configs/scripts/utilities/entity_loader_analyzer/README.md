# Entity Loader Analyzer

## Overview

The Entity Loader Analyzer is a utility script that analyzes JSON configuration files for entity loaders in the SDS Entity Inventory system. It extracts key information from these configuration files, including entity names, loader names, primary keys, and filter conditions, and generates a consolidated CSV report.

This tool is particularly useful for:
- Understanding the relationship between entities and their loaders
- Identifying primary keys used by different loaders
- Analyzing filter conditions applied to different loaders
- Getting a comprehensive view of the entity-loader ecosystem

## Prerequisites

- Python 3.6+
- Access to the SDS Entity Inventory configuration files

## Usage

```bash
# Process a specific entity
python3 entity_loader_analyzer.py <entity_name>

# Process all entities
python3 entity_loader_analyzer.py
```

### Input Parameters

The script accepts the following input parameter:

- **Entity Name** (optional): The name of the entity to analyze.
  - If provided, the script will only process files for that specific entity
  - If not provided, the script will process all entity files in the inventory_models directory

### Examples

```bash
# Process the 'account' entity
python3 entity_loader_analyzer.py account

# Process all entities
python3 entity_loader_analyzer.py
```

## Output

The script generates a CSV file at `.outputs/entity_loader_details.csv` (in the same directory as the script) with the following columns:

1. **entity_name**: The name of the entity (extracted from the filename)
2. **loader_name**: The name of the loader (extracted from the filename)
3. **primary_key**: The primary key defined in the configuration file
4. **filter**: The filter condition defined in the configuration file

## File Naming Convention

The script expects files to follow this naming convention:
```
sds_ei__<entity_name>__<loader_name>__job_config.json
```

For example:
```
sds_ei__cloud_container__ms_azure_resource_details__id__job_config.json
```

From this filename, the script extracts:
- entity_name: cloud_container
- loader_name: ms_azure_resource_details

## Notes

- The script analyzes JSON configuration files located in `configs/spark_job_configs/source_models/inventory_models`
- When no entity is specified, the script processes all folders that match the pattern 'sds_ei__loader*'
- The script extracts the 'primaryKey' and 'filterBy' fields from each JSON configuration file
- If a file cannot be processed (e.g., invalid JSON), the script will log an error and continue with the next file
- The script provides verbose output to help with debugging and understanding the processing flow
