import json
import csv
from pathlib import Path
import sys

def extract_entity_and_loader(filename):
    """Extract entity name and loader name from the filename"""
    print(f"Attempting to match filename: {filename}")

    # The filename format is: sds_ei__cloud_container__ms_azure_resource_details__id__job_config.json
    # We want to extract:
    # - entity_name: cloud_container
    # - loader_name: ms_azure_resource_details

    parts = filename.split('__')
    if len(parts) >= 4 and parts[0] == 'sds_ei':
        entity_name = parts[1]
        loader_name = parts[2]
        print(f"Matched! entity_name: {entity_name}, loader_name: {loader_name}")
        return entity_name, loader_name

    print(f"No match found for filename: {filename}")
    return None, None

def process_json_file(file_path):
    """Process a single JSON file and return required details"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
            return {
                'primary_key': data.get('primaryKey', ''),
                'filter': data.get('filterBy', '')
            }
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in file {file_path}")
        return {'primary_key': '', 'filter': ''}
    except Exception as e:
        print(f"Error processing file {file_path}: {str(e)}")
        return {'primary_key': '', 'filter': ''}

def main():
    # Get script directory and project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent.parent.parent.parent  # Go up four levels from the script to reach project root

    # Define base directory using absolute path
    base_dir = project_root / 'configs/spark_job_configs/source_models/inventory_models'
    print(f"Base directory: {base_dir}")
    print(f"Base directory exists: {base_dir.exists()}")

    # Get input folder if provided
    if len(sys.argv) > 1:
        input_folder = sys.argv[1]
        # Try both patterns for the folder name
        target_dir = base_dir / f"sds_ei__loader__{input_folder}"
        print(f"Looking for directory: {target_dir}")
        print(f"Directory exists: {target_dir.exists()}")

        if not target_dir.exists():
            # List all directories in base_dir to see what's actually there
            print("\nAvailable directories in base_dir:")
            for d in base_dir.iterdir():
                if d.is_dir():
                    print(f"- {d.name}")

            print(f"\nError: Folder 'sds_ei__loader__{input_folder}' does not exist in {base_dir}")
            sys.exit(1)
    else:
        print("No input folder specified. Processing all files in inventory_models...")
        target_dir = base_dir

    # Define output file in a hidden directory within the same directory as the script
    output_dir = script_dir / '.outputs'
    output_dir.mkdir(parents=True, exist_ok=True)
    output_file = output_dir / 'entity_loader_details.csv'

    # Process files and collect results
    results = []

    # If processing specific folder
    if target_dir != base_dir:
        json_files = list(target_dir.glob('*.json'))  # Convert to list to check length
        print(f"\nFound {len(json_files)} JSON files in {target_dir}")
        for json_file in json_files:
            print(f"\nProcessing file: {json_file.name}")
            entity_name, loader_name = extract_entity_and_loader(json_file.name)
            print(f"Extracted entity_name: {entity_name}, loader_name: {loader_name}")
            if entity_name and loader_name:
                details = process_json_file(json_file)
                results.append({
                    'entity_name': entity_name,
                    'loader_name': loader_name,
                    'primary_key': details['primary_key'],
                    'filter': details['filter']
                })
            else:
                print(f"Warning: Could not extract entity and loader names from {json_file.name}")
    else:
        # If processing all folders
        json_files = []
        for folder in target_dir.glob('sds_ei__loader*'):  # Changed pattern to match both formats
            if folder.is_dir():
                json_files.extend(folder.glob('*.json'))

    for json_file in json_files:
        entity_name, loader_name = extract_entity_and_loader(json_file.name)
        if entity_name and loader_name:
            details = process_json_file(json_file)
            results.append({
                'entity_name': entity_name,
                'loader_name': loader_name,
                'primary_key': details['primary_key'],
                'filter': details['filter']
            })

    # Write results to CSV
    if results:
        with open(output_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=['entity_name', 'loader_name', 'primary_key', 'filter'])
            writer.writeheader()
            writer.writerows(results)
            print(f"CSV file generated successfully at: {output_file}")
    else:
        print("No files were processed. No output generated.")

if __name__ == '__main__':
    main()
