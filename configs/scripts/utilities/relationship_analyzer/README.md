# Relationship Analyzer Utility

This utility analyzes relationship data dictionary JSON files and extracts relationship information between entities in the SDS Entity Inventory system.

## Purpose

The Relationship Analyzer processes all JSON files in the relationship data dictionary directory and generates a comprehensive CSV report showing:

- Relationship names
- Source entity types
- Target entity types
- Statistical analysis of entity relationships

## Directory Structure

```
relationship_analyzer/
├── .outputs/                     # Hidden directory for output files
├── relationship_analyzer.py      # Main script
└── README.md                     # This documentation file
```

## Input

The utility analyzes JSON files from:
```
configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_relationship_data_dictionary/
```

Each JSON file contains relationship configuration with `source_entity` and `target_entity` fields.

## Output

### CSV Report
- **Location**: `.outputs/relationship_analysis_YYYYMMDD_HHMMSS.csv`
- **Format**: CSV with headers: `relationship_name`, `source_entity`, `target_entity`
- **Content**: All relationships sorted alphabetically by relationship name

### Console Output
- Processing status for each file
- Summary statistics including:
  - Total relationships analyzed
  - Unique source and target entities
  - Entity distribution analysis
  - Top 10 most connected entities
  - Sample relationship listings

## Usage

### Basic Usage

Navigate to the utility directory and run the script:

```bash
cd configs/scripts/utilities/relationship_analyzer
python3 relationship_analyzer.py
```

### Example Output

```
Starting Relationship Analyzer...
Analyzing relationships in: /path/to/sds_relationship_data_dictionary
Found 88 JSON files to process...
Processed: account_associated_with_identity
Processed: application_has_vulnerability_finding
...

CSV output written to: .outputs/relationship_analysis_20250127_143022.csv

==================================================
RELATIONSHIP ANALYSIS SUMMARY
==================================================
Total relationships analyzed: 88
Unique source entities: 17
Unique target entities: 17
Total unique entities: 17

Processing Statistics:
  Files found: 88
  Files processed: 88
  Files with errors: 0

Top 10 Source Entities (most outgoing relationships):
  Network                   : 17 relationships
  Storage                   : 11 relationships
  Cloud Account             :  9 relationships
  ...
```

## Features

### Robust Error Handling
- Graceful handling of malformed JSON files
- Clear error messages for debugging
- Continues processing even if individual files fail

### Comprehensive Statistics
- Entity relationship distribution analysis
- Processing success/failure tracking
- Top entity rankings by connection count

### Timestamped Outputs
- Output files include timestamp for version tracking
- Prevents overwriting previous analysis results

### Path Resolution
- Automatically resolves paths relative to script location
- Works from any directory when script is called

## Requirements

- Python 3.6+
- Access to SDS Entity Inventory configuration files
- Read permissions for the relationship data dictionary directory

## Error Handling

The utility handles several error conditions:

1. **Missing Directory**: Exits with error if relationship dictionary directory not found
2. **Malformed JSON**: Logs error but continues processing other files
3. **Missing Fields**: Uses "unknown" for missing source_entity or target_entity fields
4. **File Access Issues**: Reports specific file access errors

## Output File Format

The generated CSV file follows this format:

```csv
relationship_name,source_entity,target_entity
account_associated_with_identity,Account,Identity
application_has_vulnerability_finding,Application,Vulnerability
application_running_on_host,Application,Host
...
```

## Integration

This utility can be integrated into:

- CI/CD pipelines for relationship validation
- Documentation generation workflows
- Data governance reporting
- Entity relationship mapping processes

## Maintenance

- Update this README when functionality changes
- Ensure compatibility with new relationship file formats
- Monitor for changes in directory structure
- Test with new entity types as they are added

## Troubleshooting

### Common Issues

1. **"Directory not found" error**
   - Verify the script is run from the correct location
   - Check that the relationship data dictionary directory exists

2. **Permission errors**
   - Ensure read access to input directory
   - Ensure write access to .outputs directory

3. **No output generated**
   - Check if JSON files exist in the input directory
   - Verify JSON files contain required fields

### Debug Mode

For additional debugging information, modify the script to include verbose logging or run with Python's `-v` flag.

## Version History

- **v1.0** (2025-01-27): Initial implementation
  - Basic relationship extraction
  - CSV output generation
  - Statistical analysis
  - Error handling and logging
