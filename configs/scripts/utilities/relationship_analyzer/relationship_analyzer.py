#!/usr/bin/env python3
"""
Relationship Analyzer Utility

This utility analyzes relationship data dictionary JSON files and extracts relationship 
information between entities in the SDS Entity Inventory system.

Author: SDS Entity Inventory Team
Created: 2025-01-27
"""

import json
import os
import csv
import sys
from pathlib import Path
from datetime import datetime
from collections import Counter


class RelationshipAnalyzer:
    """
    A utility class for analyzing relationship configurations between entities.
    
    This class processes JSON files from the relationship data dictionary directory
    and extracts source entity, target entity, and relationship name information.
    """
    
    def __init__(self):
        """Initialize the RelationshipAnalyzer."""
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent.parent.parent.parent
        self.output_dir = self.script_dir / '.outputs'
        self.relationship_dict_dir = (
            self.project_root / 
            'configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_relationship_data_dictionary'
        )
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Statistics tracking
        self.total_files = 0
        self.processed_files = 0
        self.error_files = 0
        self.relationships = []
    
    def extract_relationship_info(self, file_path):
        """
        Extract relationship information from a JSON file.
        
        Args:
            file_path (Path): Path to the JSON file
            
        Returns:
            tuple: (relationship_name, source_entity, target_entity)
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract relationship name from filename (remove .json extension)
            relationship_name = file_path.stem
            
            # Extract source and target entities
            source_entity = data.get('source_entity', 'unknown')
            target_entity = data.get('target_entity', 'unknown')
            
            self.processed_files += 1
            return relationship_name, source_entity, target_entity
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            self.error_files += 1
            relationship_name = file_path.stem
            return relationship_name, 'unknown', 'unknown'
    
    def analyze_relationships(self):
        """
        Analyze all JSON files in the relationship data dictionary directory.
        
        Returns:
            list: List of tuples containing (relationship_name, source_entity, target_entity)
        """
        print(f"Analyzing relationships in: {self.relationship_dict_dir}")
        
        # Check if directory exists
        if not self.relationship_dict_dir.exists():
            print(f"Error: Directory not found: {self.relationship_dict_dir}")
            sys.exit(1)
        
        # Get all JSON files in the directory
        json_files = list(self.relationship_dict_dir.glob('*.json'))
        self.total_files = len(json_files)
        
        print(f"Found {self.total_files} JSON files to process...")
        
        for json_file in json_files:
            relationship_info = self.extract_relationship_info(json_file)
            self.relationships.append(relationship_info)
            print(f"Processed: {relationship_info[0]}")
        
        # Sort by relationship name
        self.relationships.sort(key=lambda x: x[0])
        
        return self.relationships
    
    def generate_csv_output(self):
        """
        Generate CSV output with relationship information.
        
        Returns:
            str: Path to the generated CSV file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = self.output_dir / f'relationship_analysis_{timestamp}.csv'
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(['relationship_name', 'source_entity', 'target_entity'])
            
            # Write data
            for relationship in self.relationships:
                writer.writerow(relationship)
        
        print(f"\nCSV output written to: {output_file}")
        return str(output_file)
    
    def generate_statistics(self):
        """
        Generate and display statistics about the relationships.
        
        Returns:
            dict: Dictionary containing analysis statistics
        """
        if not self.relationships:
            return {}
        
        # Count entities
        source_entities = Counter(r[1] for r in self.relationships)
        target_entities = Counter(r[2] for r in self.relationships)
        all_entities = set(source_entities.keys()) | set(target_entities.keys())
        
        stats = {
            'total_relationships': len(self.relationships),
            'unique_source_entities': len(source_entities),
            'unique_target_entities': len(target_entities),
            'total_unique_entities': len(all_entities),
            'source_distribution': source_entities,
            'target_distribution': target_entities,
            'all_entities': sorted(all_entities),
            'processing_stats': {
                'total_files': self.total_files,
                'processed_files': self.processed_files,
                'error_files': self.error_files
            }
        }
        
        return stats
    
    def print_summary(self, stats):
        """
        Print a summary of the analysis results.
        
        Args:
            stats (dict): Statistics dictionary from generate_statistics()
        """
        print(f"\n{'='*50}")
        print("RELATIONSHIP ANALYSIS SUMMARY")
        print(f"{'='*50}")
        
        print(f"Total relationships analyzed: {stats['total_relationships']}")
        print(f"Unique source entities: {stats['unique_source_entities']}")
        print(f"Unique target entities: {stats['unique_target_entities']}")
        print(f"Total unique entities: {stats['total_unique_entities']}")
        
        print(f"\nProcessing Statistics:")
        print(f"  Files found: {stats['processing_stats']['total_files']}")
        print(f"  Files processed: {stats['processing_stats']['processed_files']}")
        print(f"  Files with errors: {stats['processing_stats']['error_files']}")
        
        print(f"\nTop 10 Source Entities (most outgoing relationships):")
        for entity, count in stats['source_distribution'].most_common(10):
            print(f"  {entity:25} : {count:2d} relationships")
        
        print(f"\nTop 10 Target Entities (most incoming relationships):")
        for entity, count in stats['target_distribution'].most_common(10):
            print(f"  {entity:25} : {count:2d} relationships")
        
        print(f"\nFirst 10 relationships:")
        for i, (name, source, target) in enumerate(self.relationships[:10]):
            print(f"  {i+1:2d}. {name} | {source} -> {target}")
    
    def run(self):
        """
        Main execution method that runs the complete analysis.
        
        Returns:
            str: Path to the generated CSV file
        """
        print("Starting Relationship Analyzer...")
        print(f"Script directory: {self.script_dir}")
        print(f"Project root: {self.project_root}")
        print(f"Output directory: {self.output_dir}")
        
        # Analyze relationships
        self.analyze_relationships()
        
        # Generate outputs
        csv_file = self.generate_csv_output()
        stats = self.generate_statistics()
        
        # Print summary
        self.print_summary(stats)
        
        return csv_file


def main():
    """Main function to execute the relationship analysis."""
    try:
        analyzer = RelationshipAnalyzer()
        csv_file = analyzer.run()
        
        print(f"\n{'='*50}")
        print("Analysis completed successfully!")
        print(f"Results saved to: {csv_file}")
        print(f"{'='*50}")
        
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nError during analysis: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
