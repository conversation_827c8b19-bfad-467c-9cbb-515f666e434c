# Entity Dictionary Analyzer

A utility script that analyzes entity data dictionary files and extracts entity descriptions.

## Overview

The Entity Dictionary Analyzer scans through JSON data dictionary files for entities and extracts their descriptions. It then generates a CSV file with entity names and their corresponding descriptions, which can be useful for documentation or reference purposes. The output is saved to a hidden `.outputs` directory within the script's folder.

## Features

- Automatically finds and processes all data dictionary files
- Extracts entity names and descriptions
- Cleans up descriptions by removing extra whitespace
- Outputs results to a CSV file in a hidden `.outputs` directory
- Uses a fixed path to the data dictionary directory

## Usage

```bash
python entity_dictionary_analyzer.py
```

This will process the data dictionary files from the fixed directory (`configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_data_dictionary`) and generate a CSV file in the `.outputs` directory.

## Input

The script expects JSON files with the naming pattern `*__data_dictionary.json` in the specified directory. Each file should contain a JSON object with at least a `description` field.

Example data dictionary file structure:

```json
{
  "description": "This entity represents a cloud compute resource.",
  "other_fields": "..."
}
```

## Output

The script generates a CSV file named `entity_descriptions.csv` in the `.outputs` directory with the following columns:

- `entity_name`: The name of the entity (extracted from the filename)
- `description`: The description of the entity (extracted from the JSON file)

## Directory Structure

```
entity_dictionary_analyzer/
├── .outputs/                # Output directory (hidden)
│   └── entity_descriptions.csv  # Generated output file
├── entity_dictionary_analyzer.py  # Main script
└── README.md                # This file
```

## Error Handling

The script includes error handling for:
- Missing dictionary directory
- Empty dictionary directory
- Invalid JSON files
- Missing description field

## Requirements

- Python 3.6+
- Standard library modules: json, csv, sys, pathlib
