import json
import csv
import sys
from pathlib import Path

class EntityDictionaryAnalyzer:
    def __init__(self, dictionary_dir):
        # Get the absolute path of the script's directory
        self.script_dir = Path(__file__).parent.absolute()

        # Store the dictionary directory path
        self.dictionary_dir = Path(dictionary_dir)

        # Create .outputs directory if it doesn't exist
        self.output_dir = self.script_dir / '.outputs'
        self.output_dir.mkdir(exist_ok=True)

        self.entities = []

    def extract_entity_name(self, filename: str) -> str:
        """Extract entity name from filename by removing __data_dictionary.json suffix"""
        return filename.replace('__data_dictionary.json', '')

    def analyze_dictionaries(self):
        """Analyze all JSON files in the data dictionary directory"""
        # Check if the dictionary directory exists
        if not self.dictionary_dir.exists():
            print(f"Error: Dictionary directory not found at {self.dictionary_dir}")
            return

        # Get all JSON files in the directory
        json_files = list(self.dictionary_dir.glob('*__data_dictionary.json'))

        if not json_files:
            print(f"No data dictionary files found in {self.dictionary_dir}")
            return

        print(f"Found {len(json_files)} data dictionary files")

        for file_path in json_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    entity_name = self.extract_entity_name(file_path.name)
                    description = data.get('description', '').strip()

                    # Clean up description - remove newlines and extra spaces
                    description = ' '.join(description.split())

                    self.entities.append({
                        'entity_name': entity_name,
                        'description': description
                    })
                    print(f"Processed {entity_name}")

            except json.JSONDecodeError:
                print(f"Error reading {file_path}")
                continue
            except Exception as e:
                print(f"Error processing {file_path}: {str(e)}")
                continue

    def save_results(self):
        """Save results as CSV file in the .outputs directory"""
        # Skip if no entities were found
        if not self.entities:
            print("No entities to save")
            return

        # Create the output file path in the .outputs directory
        csv_path = self.output_dir / 'entity_descriptions.csv'

        # Sort the results by entity name
        sorted_entities = sorted(self.entities, key=lambda x: x['entity_name'])

        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['entity_name', 'description'])
            writer.writeheader()
            writer.writerows(sorted_entities)

        print(f"Results saved to {csv_path}")

def main():
    # Use the fixed data dictionary path
    project_root = Path.cwd()
    while project_root.name and not (project_root / 'configs' / 'orchestration_shared_fs').exists():
        if project_root.parent == project_root:  # Reached filesystem root
            break
        project_root = project_root.parent

    dictionary_dir = project_root / "configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_data_dictionary"
    print(f"Using data dictionary directory: {dictionary_dir}")

    try:
        analyzer = EntityDictionaryAnalyzer(dictionary_dir)
        analyzer.analyze_dictionaries()
        analyzer.save_results()
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()