# Utilities

This directory contains utility scripts for the SDS Entity Inventory system. These utilities are designed to help with various tasks related to data analysis, configuration management, and reporting.

## Directory Structure

Each utility follows a standard structure:

```
utilities/
├── README.md                      # This file
├── entity_column_analyser/        # Utility for analyzing entity columns
│   ├── .outputs/                  # Hidden directory for output files
│   ├── entity_column_analyzer.py  # Main script
│   └── README.md                  # Documentation for this utility
├── entity_dictionary_analyzer/    # Utility for analyzing entity dictionaries
│   ├── .outputs/                  # Hidden directory for output files
│   ├── entity_dictionary_analyzer.py  # Main script
│   └── README.md                  # Documentation for this utility
└── entity_loader_analyzer/        # Utility for analyzing entity loaders
    ├── .outputs/                  # Hidden directory for output files
    ├── entity_loader_analyzer.py  # Main script
    └── README.md                  # Documentation for this utility
```

### Standard Structure for Each Utility

1. **Main Script**: The primary Python script that implements the utility's functionality
2. **Hidden `.outputs` Directory**: Where the utility stores its output files
3. **README.md**: Documentation specific to the utility

## Creating New Utilities with Augment

You can use Augment to generate new utilities that follow the same standards. Here's how to prompt Augment effectively:

### Example Prompt for Augment

```
Create a new utility script called "entity_relationship_analyzer" that:

1. Analyzes relationship configurations between entities
2. Follows the standard structure with:
   - A main Python script in its own directory
   - Output saved to a hidden .outputs folder within the same directory
   - A comprehensive README.md file

The script should:
- Find relationship configuration files in the configs directory
- Extract source and target entity information
- Generate a CSV report showing entity relationships
- Save the output to the .outputs directory
```

### Key Elements to Include in Your Prompt

1. **Name and Purpose**: Clearly state the name and purpose of the utility
2. **Structure Requirements**: Mention that it should follow the standard structure
3. **Input/Output Specifications**: Describe what the utility should process and what it should output
4. **Location Details**: Specify where input files are located and where outputs should be saved

## Setup and Usage

### Prerequisites

- Python 3.6+
- Access to the SDS Entity Inventory configuration files

### Running a Utility

Navigate to the utility's directory and run the main script:

```bash
cd configs/scripts/utilities/[utility_name]
python3 [utility_name].py
```

### Output Files

Output files are stored in the `.outputs` directory within each utility's folder. This keeps the repository clean by:

1. Separating generated files from source code
2. Making it easy to find outputs for a specific utility
3. Allowing the `.outputs` directories to be excluded from version control

## Best Practices

1. **Naming Conventions**:
   - Use snake_case for script names (e.g., `entity_column_analyzer.py`)
   - Use descriptive names that indicate the utility's purpose

2. **Documentation**:
   - Each utility should have its own README.md
   - Document the purpose, inputs, outputs, and usage instructions

3. **Error Handling**:
   - Include robust error handling in scripts
   - Provide clear error messages that help diagnose issues

4. **Output Management**:
   - Always save outputs to the `.outputs` directory
   - Use descriptive filenames for outputs
   - Include timestamps in filenames when appropriate

5. **Code Organization**:
   - Use classes to encapsulate functionality
   - Follow PEP 8 style guidelines
   - Include comments explaining complex logic

## Maintenance

- Update utility READMEs when functionality changes
- Keep this main README updated when adding new utilities
- Ensure all utilities follow the standard structure
