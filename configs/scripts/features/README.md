# Feature Tests for SDS Entity Inventory

This directory contains BDD (Behavior-Driven Development) feature tests for the SDS Entity Inventory system using the Behave framework.

## Overview

These tests validate configuration files and other aspects of the SDS Entity Inventory system using a behavior-driven approach. The tests are written in <PERSON>herkin syntax and executed using the Behave framework.

## Directory Structure

```
scripts/
├── .venv/                   # Virtual environment (hidden directory)
├── requirements.txt         # Python dependencies for all scripts
└── features/
    ├── behave.ini               # Behave configuration file
    ├── config_validation.feature # Feature file containing test scenarios
    ├── environment.py           # Environment setup for tests
    ├── README.md                # This file
    ├── reports/                 # Test reports (generated)
    ├── schemas/                 # JSON schemas for validation
    │   └── config_schemas.py    # Schema definitions
    └── steps/                   # Step definitions
        └── config_validation_steps.py # Implementation of test steps
```

## Prerequisites

- Python 3.6+
- Behave
- JSONSchema

## Installation

1. Create a virtual environment and install dependencies:
   ```bash
   # Navigate to the scripts directory
   cd configs/scripts

   # Create the virtual environment
   python -m venv .venv

   # Activate the virtual environment
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate

   # Install the required dependencies
   pip install -r requirements.txt

   # Navigate to the features directory (optional)
   cd features
   ```

## Running Tests

To run all feature tests:

```bash
# Make sure you're in the features directory and the virtual environment is activated
cd configs/scripts/features
source ../.venv/bin/activate  # On Windows: ../.venv\Scripts\activate

behave
```

To run a specific feature file:

```bash
behave config_validation.feature
```

To run a specific scenario (by line number):

```bash
behave config_validation.feature:4
```

To generate JUnit reports:

```bash
behave --junit
```

## Adding New Tests

### Creating a New Feature File

1. Create a new `.feature` file in the `features` directory
2. Write your scenarios using Gherkin syntax
3. Implement the step definitions in the `steps` directory

### Example Feature File

```gherkin
Feature: Configuration File Validation
    Validate that all configuration files meet the required standards

    Scenario: All inventory model configs should have primaryKey attribute
        Given I have access to the inventory models config directory
        When I read all JSON files in the directory
        Then each JSON file should contain a "primaryKey" attribute
```

### Example Step Definition

```python
@given('I have access to the inventory models config directory')
def step_impl(context):
    # Implementation code here
    pass
```

## Using Augment to Create Tests

You can use Augment to help create new feature tests. Here are some example prompts:

1. **Creating a new feature file**:
   ```
   Create a new feature file to validate that all entity model configs have a valid 'dataSource' section with required fields.
   ```

2. **Implementing step definitions**:
   ```
   Implement step definitions for a feature that checks if all JSON files in a directory have valid JSON syntax.
   ```

3. **Creating a schema validation test**:
   ```
   Create a schema validation test for relationship model configs that ensures they have 'sourceEntity' and 'targetEntity' fields.
   ```

## Best Practices

1. **Keep scenarios focused**: Each scenario should test one specific behavior
2. **Use descriptive names**: Feature and scenario names should clearly describe what is being tested
3. **Reuse step definitions**: Create generic step definitions that can be reused across scenarios
4. **Use tags**: Tag scenarios to categorize them (e.g., `@config`, `@schema`, `@critical`)
5. **Maintain schemas**: Keep JSON schemas up-to-date with the expected structure of configuration files

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure your virtual environment (.venv) is activated and all dependencies are installed
2. **Path issues**: If you encounter path-related errors, check the path construction in the step definitions
3. **Schema validation failures**: If schema validation fails, check both the schema definition and the files being validated

### Debugging

To run Behave with more verbose output:

```bash
behave -v
```

To debug a specific scenario:

```bash
behave -v --no-capture config_validation.feature:4
```

## Contributing

When contributing new tests:

1. Follow the existing code style and organization
2. Add appropriate documentation in the README
3. Make sure all existing tests still pass
4. Consider adding tags to categorize your tests
