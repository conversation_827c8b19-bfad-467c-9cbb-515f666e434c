import os
import json
from behave import given, when, then
from jsonschema import validate, ValidationError
# Import the schema from the local schemas directory
import sys
import os.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from schemas.config_schemas import INVENTORY_MODEL_SCHEMA

@given('I have access to the inventory models config directory')
def step_impl(context):
    # Get the path to the features directory
    features_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    # Navigate to the project root (4 levels up from features directory)
    project_root = os.path.abspath(os.path.join(features_dir, '..', '..', '..'))
    # Path to inventory models
    context.config_dir = os.path.join(project_root, 'configs', 'spark_job_configs', 'source_models', 'inventory_models')

    if not os.path.exists(context.config_dir):
        raise AssertionError(f"Config directory not found at: {context.config_dir}")

    print(f"Using config directory: {context.config_dir}")

@when('I read all JSON files in the directory')
def step_impl(context):
    json_files = []
    print(f"Scanning directory and subdirectories: {context.config_dir}")

    for root, dirs, files in os.walk(context.config_dir):
        for file in files:
            if file.endswith('.json'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r') as f:
                        json_content = json.load(f)
                    json_files.append({
                        'path': file_path,
                        'content': json_content
                    })
                except json.JSONDecodeError as e:
                    raise AssertionError(f"Invalid JSON in file {file_path}: {str(e)}")
                except Exception as e:
                    raise AssertionError(f"Error reading file {file_path}: {str(e)}")

    if not json_files:
        print("Directory structure:")
        for root, dirs, files in os.walk(context.config_dir):
            print(f"\nDirectory: {root}")
            if dirs:
                print("Subdirectories:", dirs)
            if files:
                print("Files:", files)
        raise AssertionError("No JSON files found in the config directory or its subdirectories")

    print(f"Found {len(json_files)} JSON files")
    context.json_files = json_files

@then('each JSON file should contain a "primaryKey" attribute')
def step_impl(context):
    missing_primary_key = []

    for json_file in context.json_files:
        file_path = json_file['path']
        content = json_file['content']

        if 'primaryKey' not in content:
            relative_path = os.path.relpath(file_path, context.config_dir)
            missing_primary_key.append(relative_path)

    if missing_primary_key:
        error_message = "The following files are missing the 'primaryKey' attribute:\n" + "\n".join(missing_primary_key)
        raise AssertionError(error_message)

@then('each JSON file should conform to the inventory model schema')
def step_impl(context):
    validation_errors = []

    for json_file in context.json_files:
        file_path = json_file['path']
        try:
            validate(instance=json_file['content'], schema=INVENTORY_MODEL_SCHEMA)
        except ValidationError as e:
            relative_path = os.path.relpath(file_path, context.config_dir)
            validation_errors.append(f"{relative_path}: {e.message}")

    if validation_errors:
        error_message = "Schema validation failed for the following files:\n" + "\n".join(validation_errors)
        raise AssertionError(error_message)
