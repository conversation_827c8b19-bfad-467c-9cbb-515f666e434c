import json
import os
import glob
import argparse

def add_internally_generated_flag(attributes_list, fields_to_mark_true):
    """
    Adds an 'internally_generated' key to each dictionary (attribute) in a list.
    The value is set to True if the attribute's 'name' is in the
    fields_to_mark_true list, otherwise it's False.

    Args:
        attributes_list (list of dict): A list where each dictionary represents
                                        an attribute and must contain a 'name' key.
        fields_to_mark_true (list): A list of attribute names that should be
                                    marked as 'internally_generated': True.

    Returns:
        list: The modified list of dictionaries with the 'internally_generated'
              key added to each attribute.
    """

    fields_set = set(fields_to_mark_true)
    modified_attributes = []
    for attribute in attributes_list:
        attr_copy = attribute.copy()
        if 'name' not in attr_copy:
            print(f"Warning: Attribute missing 'name' key: {attr_copy}. Skipping 'internally_generated' flag for this attribute.")
            modified_attributes.append(attr_copy)
            continue
        if attr_copy['name'] in fields_set:
            attr_copy['internally_generated'] = True
        else:
            attr_copy['internally_generated'] = False
        modified_attributes.append(attr_copy)
    return modified_attributes

def process_dictionaries_in_folder(folder_path, fields_to_mark_true):
    """
    Reads JSON files from a specified folder, updates the 'attributes' section
    of each dictionary with the 'internally_generated' flag, and writes them back.

    Args:
        folder_path (str): The path to the folder containing JSON files.
        fields_to_mark_true (list): A list of attribute names that should be
                                    marked as 'internally_generated': True.
    """
    if not os.path.isdir(folder_path):
        print(f"Error: Folder '{folder_path}' not found or is not a directory.")
        return
    json_files = glob.glob(os.path.join(folder_path, '*.json'))
    if not json_files:
        print(f"No JSON files found in '{folder_path}'.")
        return
    print(f"Found {len(json_files)} JSON files in '{folder_path}'.")
    for file_path in json_files:
        print(f"\nProcessing file: {file_path}")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if 'attributes' in data and isinstance(data['attributes'], dict):
                attributes_from_data_dict = []
                for attr_name, attr_details in data['attributes'].items():
                    attr_details_copy = attr_details.copy()
                    attr_details_copy['name'] = attr_name
                    attributes_from_data_dict.append(attr_details_copy)
                updated_attributes_list = add_internally_generated_flag(
                    attributes_from_data_dict,
                    fields_to_mark_true
                )
                data['attributes'] = {attr['name']: {k: v for k, v in attr.items() if k != 'name'} for attr in updated_attributes_list}
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=4)
                print(f"Successfully updated '{file_path}'.")
            else:
                print(f"Skipping '{file_path}': 'attributes' key not found or not a dictionary.")
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from '{file_path}': {e}")
        except IOError as e:
            print(f"Error reading/writing file '{file_path}': {e}")
        except Exception as e:
            print(f"An unexpected error occurred while processing '{file_path}': {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Add 'internally_generated' flag to attributes in JSON data dictionaries.")
    parser.add_argument("base_path", type=str, help="Base path")
    parser.add_argument("--dictionary_path", type=str, default=None, help="Optional: Absolute path to the folder containing JSON dictionaries. If not provided, uses base_path logic.")
    args = parser.parse_args()
    common_internal_fields = [
        'p_id', 'class', 'count_of_origin', 'data_source_subset_name',
        'first_found_date', 'last_updated_date', 'last_found_date', 'lifetime',
        'recent_activity', 'observed_lifetime', 'recency', 'fragments',
        'last_updated_attrs'
    ]
    if args.dictionary_path:
        abs_path = args.dictionary_path
    else:
        abs_path = os.path.join(args.base_path, "orchestration_shared_fs/sds-ei-configs/ui-configs/sds_data_dictionary")
    process_dictionaries_in_folder(abs_path, common_internal_fields)

    print("\nProcessing complete.")
