{"types": [{"name": "global_entity_config", "display_name": "Global Configuration", "git_path": "sds_ei_configs/spark_job_configs/source_models/global_entity_config/", "deployment_config_available": true}, {"name": "data_quality", "display_name": "Data Quality", "git_path": "sds_ei_configs/spark_job_configs/source_models/data_quality/", "deployment_config_available": true}, {"name": "inventory_models", "display_name": "Entity Source Extraction", "git_path": "sds_ei_configs/spark_job_configs/source_models/inventory_models/", "deployment_config_available": true, "datahub_schema": {"input_keys": ["dataSource.srdm"], "output_keys": ["outputTable"], "className": "entity.name", "displayLabel": "dataSource"}}, {"name": "intersource_disambiguated_models", "display_name": "Entity Resolution", "git_path": "sds_ei_configs/spark_job_configs/source_models/intersource_disambiguated_models/", "deployment_config_available": true, "datahub_schema": {"input_keys": ["inventoryModelInput[].path"], "output_keys": ["output.disambiguatedModelLocation", "output.fragmentLocation", "output.resolverLocation"], "className": "entity.name", "displayLabel": "entity.name"}}, {"name": "intrasource_disambiguated_models", "display_name": "Entity Source Resolution", "git_path": "sds_ei_configs/spark_job_configs/source_models/intrasource_disambiguated_models/", "deployment_config_available": true, "datahub_schema": {"input_keys": ["inventoryModelInput[].path"], "output_keys": ["output.disambiguatedModelLocation", "output.resolverLocation"], "className": "entity.name", "displayLabel": "dataSource.name"}}, {"name": "relationship_models", "display_name": "Relationship Source Extraction", "git_path": "sds_ei_configs/spark_job_configs/source_models/relationship_models/", "deployment_config_available": true, "datahub_schema": {"input_keys": ["inputSourceInfo[].sdmPath"], "output_keys": ["output.outputTable", "output.prevMiniSDM"], "className": "relationship.rel_name", "displayLabel": "relationship.rel_name"}}, {"name": "publisher", "display_name": "Publish", "git_path": "sds_ei_configs/spark_job_configs/source_models/publisher/", "deployment_config_available": true, "datahub_schema": {"input_keys": ["transformSpec.tableInfo.tableName"], "output_keys": ["outputTableInfo.outputTableName"], "className": "entity.name", "isOLAPTable": "isOLAPTable", "displayLabel": "entity.name"}}, {"name": "cei_config", "display_name": "CEI Config", "deployment_config_available": false}, {"name": "general_configs", "display_name": "General Config", "deployment_config_available": false}, {"name": "druid_indexing_configs", "display_name": "Druid Config", "deployment_config_available": false}, {"name": "dremio_config", "display_name": "<PERSON><PERSON><PERSON>", "deployment_config_available": false}, {"name": "data_dictionary_config", "display_name": "Entity", "git_path": "sds_data_dictionary/", "deployment_config_available": false}, {"name": "relationship_data_dictionary_config", "display_name": "Relationship", "git_path": "sds_relationship_data_dictionary/", "deployment_config_available": false}, {"name": "ui_config", "display_name": "UI Config", "git_path": "sds_ei_configs/orchestration_shared_fs/sds-ei-configs/ui-configs/", "deployment_config_available": false}, {"name": "orchestration_variables", "display_name": "Orchestration Variables", "git_path": "sds_ei_configs/orchestration_variables/", "deployment_config_available": false}, {"name": "context_variables", "display_name": "Context Variables", "deployment_config_available": false}, {"name": "autoparser_config", "display_name": "Autoparser Config", "deployment_config_available": false}, {"name": "recon_config", "display_name": "Recon Config", "deployment_config_available": false}, {"name": "entity_rel_enrich", "display_name": "Enrichment", "git_path": "sds_ei_configs/spark_job_configs/source_models/entity_rel_enrich/", "deployment_config_available": true, "datahub_schema": {"input_keys": ["entityTableName"], "output_keys": ["output.outputTableName"], "className": "entity.name", "displayLabel": "entity.name"}}, {"name": "relationship_disambiguation", "display_name": "Relationship Resolution", "git_path": "sds_ei_configs/spark_job_configs/source_models/relationship_disambiguation/", "deployment_config_available": true, "datahub_schema": {"input_keys": ["relationshipModels[].tableName"], "output_keys": ["output.disambiguatedModelLocation"], "className": "entity.name", "displayLabel": "entity.name"}}, {"name": "deployment_config", "display_name": "Deployment Config", "git_path": "sds_ei_configs/", "deployment_config_available": false}, {"name": "data_dictionary_dashboard", "display_name": "Data Dictionary Dashboard Identifier", "deployment_config_available": false}, {"name": "graph_data_dictionary_config", "display_name": "Graph Data Dictionary Config", "git_path": "sds_ei_configs/orchestration_shared_fs/sds-ei-configs/ui-configs/", "deployment_config_available": false}, {"name": "rdm_config", "display_name": "RDM Config", "deployment_config_available": false}, {"name": "olap_configs", "display_name": "<PERSON><PERSON>figs", "deployment_config_available": false}]}