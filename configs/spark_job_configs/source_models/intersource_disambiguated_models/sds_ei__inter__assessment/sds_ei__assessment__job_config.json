{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__ms_azure", "name": "sds_ei__assessment__ms_azure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__aws", "name": "sds_ei__assessment__aws"}], "disambiguation": {"candidateKeys": ["assessment_id"], "confidenceMatrix": ["sds_ei__assessment__ms_azure", "sds_ei__assessment__aws"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "assessment_severity", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "rollingUpFields": ["origin", "associated_standards", "associated_controls"], "aggregation": [{"field": "last_active_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "assessment_id", "colExpr": "UPPER(assessment_id[0])"}, {"colName": "assessment_source", "colExpr": "'Cloud Defined'"}, {"colName": "kg_assessment_weightage", "colExpr": "CASE WHEN LOWER(assessment_severity) = 'critical' THEN 10 WHEN LOWER(assessment_severity) = 'high' THEN 7 WHEN LOWER(assessment_severity) = 'medium' THEN 4 WHEN LOWER(assessment_severity) = 'low' THEN 2 WHEN LOWER(assessment_severity) = 'informational' THEN 1 ELSE 0 END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__assessment", "filter": "finding_id_ref IS NOT NULL"}, "entity": {"name": "Assessment"}}