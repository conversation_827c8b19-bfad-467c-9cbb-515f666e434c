{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid", "name": "sds_ei__host__active_directory__object_guid"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender", "name": "sds_ei__host__ms_defender"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intune", "name": "sds_ei__host__ms_intune"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys", "name": "sds_ei__host__qualys"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn", "name": "sds_ei__host__globalprotect_vpn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__winevents", "name": "sds_ei__host__winevents"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad", "name": "sds_ei__host__ms_azure_ad"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure", "name": "sds_ei__host__ms_azure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_sc", "name": "sds_ei__host__tenable_sc"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__crowdstrike", "name": "sds_ei__host__crowdstrike"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__itop", "name": "sds_ei__host__itop"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws", "name": "sds_ei__host__aws"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__wiz", "name": "sds_ei__host__wiz"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_io", "name": "sds_ei__host__tenable_io"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7", "name": "sds_ei__host__rapid7"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tanium", "name": "sds_ei__host__tanium"}], "disambiguation": {"candidateKeys": [{"name": "aad_device_id", "exceptionFilter": "aad_device_id = '00000000-0000-0000-0000-000000000000'"}, {"name": "host_name", "exceptionFilter": "(lower(host_name) LIKE '%iphone%' OR lower(host_name) LIKE '%android%' OR lower(host_name) LIKE '%ipad%' OR lower(host_name) LIKE '%macbook%' OR host_name RLIKE '(?i)pro([^a-zA-Z0-9]|$)' OR lower(host_name) LIKE '%galaxy%' OR lower(host_name) LIKE '%samsung%') OR lower(host_name) IN ('wrk','user deleted for this device') OR (lower(os) LIKE '%android%' OR lower(os) LIKE '%appleios%' OR lower(os) LIKE '%tizen%')", "matchAttributesList": ["cloud_instance_id", "resource_id"]}, "fqdn", "hardware_serial_number", "resource_id", "cloud_instance_id", "netbios", "cloud_resource_name"], "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_defender", "sds_ei__host__tanium", "sds_ei__host__rapid7", "sds_ei__host__ms_intune", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__crowdstrike", "sds_ei__host__aws", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure", "sds_ei__host__tenable_sc", "sds_ei__host__itop", "sds_ei__host__wiz", "sds_ei__host__tenable_io"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "login_last_user", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "defender_health_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "defender_exposure_level", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "type", "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__tanium", "sds_ei__host__rapid7", "sds_ei__host__ms_intune", "sds_ei__host__ms_defender", "sds_ei__host__crowdstrike", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure", "sds_ei__host__tenable_sc", "sds_ei__host__itop", "sds_ei__host__tenable_io"]}, {"field": "av_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "fw_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "av_block_malicious_code_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "rollingUpFields": ["origin", "defender_threat_name", "defender_action_type", "qualys_id", "defender_id", "qualys_asset_id", "qualys_detection_method", "vm_tracking_method", "vm_product", "defender_detection_method", "win_event_id", "ip", "mac_address", "edr_product", "asset_role", "rapid7_scan_type", "rapid7_site_id", "rapid7_site_name", "rapid7_asset_group_id", "rapid7_asset_group_name", "rapid7_tag", "rapid7_asset_id", "rapid7_tag_id", "tanium_custom_tags"], "aggregation": [{"field": "login_last_date", "function": "max"}, {"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "mdm_enrolled_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "edr_cloud_last_report_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "azure_resource_created_date", "function": "min"}, {"field": "ad_account_disabled_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vulnerability_last_observed_date", "function": "max"}, {"field": "aws_instance_launch_date", "function": "min"}, {"field": "aws_resource_created_date", "function": "min"}, {"field": "tenable_io_last_authenticated_scan_date", "function": "max"}, {"field": "tenable_io_last_scan_date", "function": "max"}, {"field": "tenable_io_asset_updated_at", "function": "max"}, {"field": "tenable_io_asset_aws_terminated_date", "function": "max"}, {"field": "tenable_io_onboarding_date", "function": "min"}, {"field": "rapid7_site_last_scan_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "vm_onboarding_status", "confidenceMatrix": ["true", "false"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "rapid7_onboarding_status", "confidenceMatrix": ["True", "False"]}]}}, "derivedProperties": [{"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}, {"colName": "edr_threat_count", "colExpr": "defender_threat_count"}, {"colName": "asset_role", "colExpr": "case when size(asset_role)>1 and array_contains(asset_role,'Other') then array_except(asset_role,array('Other')) else asset_role end"}, {"colName": "edr_onboarding_status", "colExpr": "CASE WHEN edr_onboarding_status THEN true ELSE false END"}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN vm_onboarding_status THEN true ELSE false END"}, {"colName": "asset_compliance_scope", "colExpr": "cast(null as array<string>)"}, {"colName": "asset_is_inventoried", "colExpr": "arrays_overlap(origin,array('MS Active Directory','MS Azure AD','MS Intune','iTOP','ServiceNow ITSM','AWS','MS Azure'))"}, {"colName": "is_edr_present", "colExpr": "CASE WHEN edr_onboarding_status THEN 1 ELSE 0 END"}, {"colName": "is_vuln_software_present", "colExpr": "CASE WHEN vm_onboarding_status THEN 1 ELSE 0 END"}, {"colName": "asset_security_posture", "colExpr": "ROUND(double((is_edr_present+is_vuln_software_present)/2),2)"}, {"colName": "host_meet_security_posture", "colExpr": "CASE WHEN asset_security_posture=1 THEN TRUE ELSE FALSE END"}, {"colName": "account_id", "colExpr": "ARRAY(account_id)"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.eol_lookup", "preTransform": [{"colName": "eol_status", "colExpr": "normalization_result.is_eol"}, {"colName": "eol_date", "colExpr": "case when normalization_result.eol_date='NO MATCH' then NULL else UNIX_MILLIS(TIMESTAMP(to_timestamp(normalization_result.eol_date))) end"}, {"colName": "normalized_os", "colExpr": "normalization_result.normalised_name"}], "enrichmentColumns": ["eol_status", "eol_date", "normalized_os"]}, "joinCondition": "s.os=e.os_string"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host"}, "entity": {"name": "Host"}}