{"name": "Container Belongs To Container Service", "origin": "AWS", "inverseRelationshipName": "Container Service Has Container", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__ecs_task_containers", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__container__aws_ecs_task_container__containerarn"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cluster__aws_ecs_task_container__cluster_arn"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_task__container_belongs_to_container_service", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_task__container_belongs_to_container_service"}, "relationship": {"rel_name": "Container Belongs To Container Service", "name": "AWS", "feedName": "ECS Task Container"}}