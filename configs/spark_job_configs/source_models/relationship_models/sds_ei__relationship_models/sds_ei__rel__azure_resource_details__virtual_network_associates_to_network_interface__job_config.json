{"name": "Virtual Network Associates To Network Interface", "origin": "MS Azure", "inverseRelationshipName": "Network Interface Has Virtual Network", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__network__ms_azure_resource_details__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__network__ms_azure_resource_details__virtual_network_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_resource_details__subnet_associates_to_virtual_network", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel__mini_sdm_azure_resource_details__subnet_associates_to_virtual_network"}, "relationship": {"rel_name": "Subnet Associates To Virtual Network", "name": "Microsoft Azure", "feedName": "Resource Details"}}