{"name": "Finding Associated With Assessment", "origin": "AWS", "inverseRelationshipName": "Assessment Associated With Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__finding__aws_sh_finding__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__assessment__aws_sh_findings__security_control_id"]}], "optionalAttributes": [{"name": "rel_finding_id", "exp": "Id", "occurrence": "LAST"}, {"name": "assessment_id", "exp": "UPPER(Compliance.SecurityControlId)", "occurrence": "LAST"}, {"name": "assessment_title", "exp": "Title", "occurrence": "LAST"}, {"name": "rel_affected_resource_id", "exp": "cast(Resources.Id as array<string>)[0]", "occurrence": "LAST"}, {"name": "rel_finding_status", "exp": "case when lower(Workflow.Status) in ('suppressed','resolved') then 'Closed' when lower(Workflow.Status) not in ('suppressed','resolved') and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(event_timestamp_epoch/1000))) > 2 then 'Closed' Else 'Open' END", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__finding_associated_with_assessment", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_sh_findings__finding_associated_with_assessment"}, "relationship": {"rel_name": "Finding Associated With Assessment", "name": "AWS", "feedName": "SH Findings"}}