{"name": "Network Interface Associates To Instance", "origin": "AWS", "inverseRelationshipName": "Instance Has Network Interface", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__resource_details", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__network__aws_resource_details__arn"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__network__aws_resource_details__network_interface_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__network_interface_associates_to_instance", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_resource_details__network_interface_associates_to_instance"}, "relationship": {"rel_name": "Network Interface Associates To Instance", "name": "AWS", "feedName": "Resource Details"}}