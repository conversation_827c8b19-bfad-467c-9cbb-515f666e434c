{"name": "Security Group Associates To Network Interface", "origin": "AWS", "inverseRelationshipName": "Network Interface Has Security Group", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__resource_details", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__network__aws_resource_details__arn"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__network__aws_resource_details__security_group_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__security_group_associates_to_network_interface", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel__mini_sdm__aws_resource_details__security_group_associates_to_network_interface"}, "relationship": {"rel_name": "Security Group Associates To Network Interface", "name": "AWS", "feedName": "Resource Details"}}