{"name": "Bucket Belongs To Storage Account", "origin": "MS Azure", "inverseRelationshipName": "Storage Account Has <PERSON>", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__blob_storage_container", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__storage__ms_azure_blob_storage_container__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__storage__ms_azure_blob_storage_container__sa_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_blob_storage__bucket_belongs_to_storage_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__azure_blob_storage__bucket_belongs_to_storage_account"}, "relationship": {"rel_name": "Bucket Belongs To Storage Account", "name": "Microsoft Azure", "feedName": "Blob Storage Container"}}