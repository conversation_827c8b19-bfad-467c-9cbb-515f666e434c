{"name": "Vulnerability Finding On Host", "origin": "Tanium CVE Findings", "inverseRelationshipName": "Host Has Vulnerability Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"origin": "Tanium", "sdmPath": "<%SRDM_SCHEMA_NAME%>.tanium__endpoints_cve_findings_report", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__vulnerability__tanium_cve_findings__cve_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__tanium_cve_findings__id"], "enrichments": [{"lookupInfo": {"tableName": "<%LOOKUP_SCHEMA_NAME%>.tanium__vulnerability_findings_software", "enrichmentColumns": ["software_version", "software_name", "software_vendor", "software_full_name", "software_product"]}, "joinType": "LEFT", "joinCondition": "s.summary<=>e.summary AND s.affected_products<=>e.affected_products AND s.cveFinding.Check__ID <=> e.cve_id AND s.host_id <=> e.host_id", "sourcePreTransform": [{"colName": "cve_id", "colExpr": "cveFinding.Check__ID"}, {"colName": "host_id", "colExpr": "id"}, {"colName": "affected_products", "colExpr": "cveFinding.Products"}, {"colName": "summary", "colExpr": "cveF<PERSON><PERSON>.Su<PERSON>ry"}]}]}], "optionalAttributes": [{"name": "relationship_first_seen_date", "exp": "CAST(unix_millis(timestamp(unix_timestamp( to_date(firstFound , 'yyyyMMdd')))) AS BIGINT)", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "CAST(unix_millis(timestamp(unix_timestamp( to_date(lastFound , 'yyyyMMdd')))) AS BIGINT)", "occurrence": "LAST"}, {"name": "first_seen_date", "exp": "CAST(unix_millis(timestamp(unix_timestamp( to_date(firstFound , 'yyyyMMdd')))) AS BIGINT)", "occurrence": "FIRST"}, {"name": "last_seen_date", "exp": "CAST(unix_millis(timestamp(unix_timestamp( to_date(lastFound , 'yyyyMMdd')))) AS BIGINT)", "occurrence": "LAST"}, {"name": "software_name", "exp": "software_name", "occurrence": "LAST"}, {"name": "software_full_name", "exp": "software_full_name", "occurrence": "LAST"}, {"name": "software_product", "exp": "software_product", "occurrence": "LAST"}, {"name": "software_vendor", "exp": "software_vendor", "occurrence": "LAST"}, {"name": "software_version", "exp": "software_version", "occurrence": "LAST"}, {"name": "initial_status", "exp": "'Open'", "occurrence": "LAST"}, {"name": "last_scan_date_temp", "exp": "CAST( unix_millis( timestamp( MAX(unix_timestamp( to_date(lastScanDate , 'yyyyMMdd'))) OVER ( PARTITION BY id, region_tag ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING ) ) ) AS BIGINT )", "occurrence": "LAST"}, {"name": "lastFound_date_temp", "exp": "CAST( unix_millis( timestamp( MAX(unix_timestamp( to_date(lastFound , 'yyyyMMdd'))) OVER ( PARTITION BY target_p_id, source_p_id, software_vendor,software_product,software_version ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING )) ) AS BIGINT )", "occurrence": "LAST"}, {"name": "current_status", "exp": "CASE WHEN ( ( (last_scan_date_temp) - (lastFound_date_temp) ) >= 86400000 or COALESCE(datediff( date(to_timestamp(updated_at / 1000)), date(to_timestamp(relationship_last_seen_date / 1000)) ), 0) > 180 ) THEN 'Closed' ELSE 'Open' END", "occurrence": "LAST"}, {"name": "current_status_per_location", "exp": "current_status", "occurrence": "LAST"}, {"name": "findings_description", "exp": "cveF<PERSON><PERSON>.Su<PERSON>ry", "occurrence": "LAST"}, {"name": "recommendation", "exp": "cveFinding.Remediation", "occurrence": "Last"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id", "firstFound", "software_full_name"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__tanium__vulnerability_finding_on_host", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__tanium__vulnerability_finding_on_host"}, "relationship": {"rel_name": "Vulnerability Finding On Host", "name": "Tanium", "feedName": "CVE Findings"}}