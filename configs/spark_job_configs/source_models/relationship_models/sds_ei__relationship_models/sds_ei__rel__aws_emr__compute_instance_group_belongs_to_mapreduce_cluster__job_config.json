{"name": "Compute Instance Group Belongs To MapReduce Cluster", "origin": "AWS", "inverseRelationshipName": "MapReduce Cluster Has Compute Instance Group", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__emr_ec2_fleet", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cluster__aws_emr_ec2_fleet__resource_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cluster__aws_emr_ec2_fleet__cluster_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_emr__compute_instance_group_belongs_to_mapreduce_cluster", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_emr__compute_instance_group_belongs_to_mapreduce_cluster"}, "relationship": {"rel_name": "Compute Instance Group Belongs To Mapreduce Cluster", "name": "AWS", "feedName": "EMR EC2 Fleet"}}