{"name": "Person Has Identity", "inverseRelationshipName": "Identity Associated With Person", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.saviynt__account", "origin": "Saviynt IGA", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__person__saviynt_iga_accounts__user_name"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__identity__saviynt_iga_accounts__name"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__saviynt_iga_accounts__person_has_identity", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__saviynt_iga_accounts__person_has_identity"}, "relationship": {"rel_name": "Person Has Identity", "name": "Saviynt IGA", "feedName": "Accounts"}}