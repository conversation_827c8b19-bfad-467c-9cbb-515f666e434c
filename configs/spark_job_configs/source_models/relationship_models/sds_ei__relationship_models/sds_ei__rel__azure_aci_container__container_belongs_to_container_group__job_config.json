{"name": "Container Belongs To Container Group", "origin": "MS Azure", "inverseRelationshipName": "Container Group Has Container", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.azure__aci_containers", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__container__ms_azure_aci_container__resource_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cluster__ms_azure_aci_container__cluster_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_aci_container__container_belongs_to_container_group", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__azure_aci_container__container_belongs_to_container_group"}, "relationship": {"rel_name": "Container Belongs To Container Group", "name": "Microsoft Azure", "feedName": "ACI Container"}}