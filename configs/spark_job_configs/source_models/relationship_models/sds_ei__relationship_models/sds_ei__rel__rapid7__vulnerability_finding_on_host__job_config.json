{"name": "Vulnerability Finding On Host", "inverseRelationshipName": "Host Has Vulnerability Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"origin": "Rapid7 InsightVM", "sdmPath": "<%SRDM_SCHEMA_NAME%>.rapid7_assets", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__vulnerability__rapid7_assets__assetvulns_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__rapid7_assets__id"], "enrichments": [{"joinType": "LEFT", "lookupInfo": {"tableName": "<%LOOKUP_SCHEMA_NAME%>.rapid7__vulnerability_findings_software", "enrichmentColumns": ["software_version", "software_name", "software_vendor", "software_full_name", "software_product"]}, "joinCondition": "s.id<=>e.asset_id AND s.vuln_id<=>e.vuln_id AND s.proof <=> e.proof", "sourcePreTransform": [{"colExpr": "EXPLODE_OUTER(asset_vulns)", "colName": "temp_asset_vulns"}, {"colExpr": "temp_asset_vulns.id", "colName": "vuln_id"}, {"colExpr": "transform(array_distinct(temp_asset_vulns.results), x -> x.proof)", "colName": "proof"}]}]}], "optionalAttributes": [{"name": "last_scan_date_temp_date", "exp": "ARRAY_MAX(TRANSFORM( FILTER(history, x -> (x.type = 'AGENT-IMPORT' OR x.type = 'SCAN' OR x.type = 'SCAN-LOG-IMPORT')), x -> unix_timestamp(x.date, \"yyyy-MM-dd'T'HH:mm:ss.SSSX\") * 1000 ))", "occurrence": "LAST"}, {"name": "last_scan_date_temp", "exp": "MAX(last_scan_date_temp_date) OVER ( PARTITION BY target_p_id ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING )", "occurrence": "LAST"}, {"name": "relationship_first_seen_date", "exp": "unix_millis(to_timestamp(date_format(to_timestamp(temp_asset_vulns.since), \"yyyy-MM-dd'T'HH:mm:ssX\")))", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "last_scan_date_temp_date", "occurrence": "LAST"}, {"name": "first_seen_date", "exp": "unix_millis(to_timestamp(date_format(to_timestamp(temp_asset_vulns.since), \"yyyy-MM-dd'T'HH:mm:ssX\")))", "occurrence": "FIRST"}, {"name": "last_seen_date", "exp": "last_scan_date_temp_date", "occurrence": "LAST"}, {"name": "last_reopened_date", "exp": "unix_millis(to_timestamp(date_format(to_timestamp(temp_asset_vulns.results.reintroduced[0]),\"yyyy-MM-dd'T'HH:mm:ss.SSSX\")))", "occurrence": "LAST"}, {"name": "software_name", "exp": "software_name", "occurrence": "LAST"}, {"name": "software_full_name", "exp": "software_full_name", "occurrence": "LAST"}, {"name": "software_product", "exp": "software_product", "occurrence": "LAST"}, {"name": "software_vendor", "exp": "software_vendor", "occurrence": "LAST"}, {"name": "software_version", "exp": "software_version", "occurrence": "LAST"}, {"name": "initial_status", "exp": "'Open'", "occurrence": "LAST"}, {"name": "current_status", "exp": "CASE WHEN ( last_scan_date_temp - relationship_last_seen_date ) > 86400001 or COALESCE(datediff( date(to_timestamp(updated_at / 1000)), date(to_timestamp(relationship_last_seen_date / 1000)) ), 0) > 180 THEN 'Closed' ELSE 'Open' END", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id", "temp_asset_vulns.since", "software_full_name"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__rapid7__vulnerability_finding_on_host", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__rapid7__vulnerability_finding_on_host"}, "relationship": {"rel_name": "Vulnerability Finding On Host", "name": "Rapid7 InsightVM", "feedName": "Assets"}}