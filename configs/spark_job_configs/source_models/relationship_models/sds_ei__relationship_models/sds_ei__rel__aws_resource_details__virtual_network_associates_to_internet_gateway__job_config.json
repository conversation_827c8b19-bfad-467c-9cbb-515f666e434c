{"name": "Virtual Network Associates To Internet Gateway", "origin": "AWS", "inverseRelationshipName": "Internet Gateway Has Virtual Network", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__resource_details", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__network__aws_resource_details__arn"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__network__aws_resource_details__gateway_vpc_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__virtual_network_associates_to_internet_gateway", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_resource_details__virtual_network_associates_to_internet_gateway"}, "relationship": {"rel_name": "Virtual Network Associates To Internet Gateway", "name": "AWS", "feedName": "Resource Details"}}