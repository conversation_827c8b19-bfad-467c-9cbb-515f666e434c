{"name": "Finding Associated With Network", "origin": "AWS", "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_finding__finding_associated_with_network", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_sh_finding__finding_associated_with_network"}, "inverseRelationshipName": "Network Has Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__finding__aws_sh_finding__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__network__aws_sh_findings__resource_id"]}], "optionalAttributes": [{"name": "relationship_first_seen_date", "exp": "case when FirstObservedAt is null then UNIX_MILLIS(TIMESTAMP(to_timestamp(event_timestamp_ts))) else UNIX_MILLIS(TIMESTAMP(to_timestamp(FirstObservedAt))) end", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "case when LastObservedAt is null then UNIX_MILLIS(TIMESTAMP(to_timestamp(event_timestamp_ts))) else UNIX_MILLIS(TIMESTAMP(to_timestamp(LastObservedAt))) end", "occurrence": "LAST"}, {"name": "rel_finding_status", "exp": "case when lower(Workflow.Status) in ('suppressed','resolved') then 'Closed' when lower(Workflow.Status) not in ('suppressed','resolved') and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(event_timestamp_epoch/1000))) > 2 then 'Closed' Else 'Open' END", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "relationship": {"rel_name": "Finding Associated With Network", "name": "AWS", "feedName": "SH Findings"}}