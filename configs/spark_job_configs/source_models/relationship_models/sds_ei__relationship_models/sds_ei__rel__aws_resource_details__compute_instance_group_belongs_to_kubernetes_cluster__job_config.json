{"name": "Compute Instance Group Belongs To Kubernetes Cluster", "origin": "AWS", "inverseRelationshipName": "Kubernetes Cluster Has Compute Instance Group", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__resource_details", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cluster__aws_resource_details__arn"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cluster__aws_resource_details__aws_eks_cluster_key"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__compute_instance_group_belongs_to_kubernetes_cluster", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_resource_details__compute_instance_group_belongs_to_kubernetes_cluster"}, "relationship": {"rel_name": "Compute Instance Group Belongs To Kubernetes Cluster", "name": "AWS", "feedName": "Resource Details"}}