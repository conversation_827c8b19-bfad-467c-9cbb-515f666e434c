{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__assessment", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__enrich"}, "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_assessment", "alias": "finding_table", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_cluster", "alias": "finding_cloud1", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__cluster", "alias": "cluster", "colEnrichments": [{"colName": "scope_entity_cluster", "aggExpr": "cluster.class", "aggFunction": "last"}, {"colName": "contributing_module_cluster", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_container", "alias": "finding_cloud2", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__container", "alias": "container", "colEnrichments": [{"colName": "scope_entity_container", "aggExpr": "container.class", "aggFunction": "last"}, {"colName": "contributing_module_container", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_storage", "alias": "finding_cloud3", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__storage", "alias": "storage", "colEnrichments": [{"colName": "scope_entity_storage", "aggExpr": "storage.class", "aggFunction": "last"}, {"colName": "contributing_module_storage", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_network", "alias": "finding_cloud4", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__network", "alias": "network", "colEnrichments": [{"colName": "scope_entity_network", "aggExpr": "network.class", "aggFunction": "last"}, {"colName": "contributing_module_network", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_network_services", "alias": "finding_cloud6", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__network_services", "alias": "network_services", "colEnrichments": [{"colName": "scope_entity_network_services", "aggExpr": "network_services.class", "aggFunction": "last"}, {"colName": "contributing_module_network_services", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_cloud_account", "alias": "finding_cloud7", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account", "alias": "cloud_account", "colEnrichments": [{"colName": "scope_entity_cloud_account", "aggExpr": "cloud_account.class", "aggFunction": "last"}, {"colName": "contributing_module_cloud_account", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_host", "alias": "finding_cloud8", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__host", "alias": "host", "colEnrichments": [{"colName": "scope_entity_host", "aggExpr": "host.class", "aggFunction": "last"}, {"colName": "contributing_module_host", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}]}], "derivedProperties": [{"colName": "scope_entity", "colExpr": "COALESCE(scope_entity_cluster,scope_entity_container,scope_entity_storage,scope_entity_network,scope_entity_network_services,scope_entity_cloud_account,scope_entity_host)"}, {"colName": "contributing_module", "colExpr": "COALESCE(contributing_module_cluster,contributing_module_container,contributing_module_storage,contributing_module_network,contributing_module_network_services,contributing_module_host,contributing_module_cloud_account)"}], "entity": {"name": "Assessment"}}