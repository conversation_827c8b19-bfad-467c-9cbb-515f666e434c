{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__finding", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__finding__enrich"}, "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_cluster", "alias": "finding_cloud1", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__cluster", "alias": "cluster", "colEnrichments": [{"colName": "affected_asset_cluster", "aggExpr": "ARRAY(STRUCT(cluster.p_id,cluster.class,cluster.display_label,cluster.activity_status))", "aggFunction": "last"}, {"colName": "scope_entity_cluster", "aggExpr": "cluster.class", "aggFunction": "last"}, {"colName": "contributing_module_cluster", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_container", "alias": "finding_cloud2", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__container", "alias": "container", "colEnrichments": [{"colName": "affected_asset_container", "aggExpr": "ARRAY(STRUCT(container.p_id,container.class,container.display_label,container.activity_status))", "aggFunction": "last"}, {"colName": "scope_entity_container", "aggExpr": "container.class", "aggFunction": "last"}, {"colName": "contributing_module_container", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_storage", "alias": "finding_cloud3", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__storage", "alias": "storage", "colEnrichments": [{"colName": "affected_asset_storage", "aggExpr": "ARRAY(STRUCT(storage.p_id,storage.class,storage.display_label,storage.activity_status))", "aggFunction": "last"}, {"colName": "scope_entity_storage", "aggExpr": "storage.class", "aggFunction": "last"}, {"colName": "contributing_module_storage", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_network", "alias": "finding_cloud4", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__network", "alias": "network", "colEnrichments": [{"colName": "affected_asset_network", "aggExpr": "ARRAY(STRUCT(network.p_id,network.class,network.display_label,network.activity_status))", "aggFunction": "last"}, {"colName": "scope_entity_network", "aggExpr": "network.class", "aggFunction": "last"}, {"colName": "contributing_module_network", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_network_services", "alias": "finding_cloud6", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__network_services", "alias": "network_services", "colEnrichments": [{"colName": "affected_asset_network_services", "aggExpr": "ARRAY(STRUCT(network_services.p_id,network_services.class,network_services.display_label,network_services.activity_status))", "aggFunction": "last"}, {"colName": "scope_entity_network_services", "aggExpr": "network_services.class", "aggFunction": "last"}, {"colName": "contributing_module_network_services", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_host", "alias": "finding_cloud5", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__host", "alias": "host", "colEnrichments": [{"colName": "affected_asset_host", "aggExpr": "ARRAY(STRUCT(host.p_id,host.class,host.display_label,host.activity_status))", "aggFunction": "last"}, {"colName": "scope_entity_host", "aggExpr": "host.class", "aggFunction": "last"}, {"colName": "contributing_module_host", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_cloud_account", "alias": "finding_cloud7", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account", "alias": "cloud_account", "colEnrichments": [{"colName": "affected_asset_cloud_account", "aggExpr": "ARRAY(STRUCT(cloud_account.p_id,cloud_account.class,cloud_account.display_label,cloud_account.activity_status))", "aggFunction": "last"}, {"colName": "scope_entity_cloud_account", "aggExpr": "cloud_account.class", "aggFunction": "last"}, {"colName": "contributing_module_cloud_account", "aggExpr": "ARRAY('Reporting','Exposure')", "aggFunction": "last"}]}]}], "derivedProperties": [{"colName": "affected_asset", "colExpr": "COALESCE(affected_asset_cluster,affected_asset_container,affected_asset_storage,affected_asset_network,affected_asset_host,affected_asset_cloud_account,affected_asset_network_services)"}, {"colName": "scope_entity", "colExpr": "COALESCE(scope_entity_cluster,scope_entity_container,scope_entity_storage,scope_entity_network,scope_entity_host,scope_entity_cloud_account,scope_entity_network_services)"}, {"colName": "contributing_module", "colExpr": "COALESCE(contributing_module_cluster,contributing_module_container,contributing_module_storage,contributing_module_network,contributing_module_network_services,contributing_module_host,contributing_module_cloud_account)"}], "entity": {"name": "Finding"}}