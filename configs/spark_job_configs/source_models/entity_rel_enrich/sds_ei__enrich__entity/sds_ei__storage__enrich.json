{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__storage", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__storage__enrich"}, "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__bucket_belongs_to_storage_account", "alias": "bk_bl_stg1", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "associated_storage_account_has_bucket_count", "aggExpr": "bk_bl_stg1.target_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__bucket_belongs_to_storage_account", "alias": "bk_bl_stg2", "leftCol": "p_id", "rightCol": "target_p_id", "colEnrichments": [{"colName": "associated_bucket_count", "aggExpr": "bk_bl_stg2.source_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__file_system_service_belongs_to_storage_account", "alias": "fss_bl_stg1", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "associated_storage_account_has_file_system_service_count", "aggExpr": "fss_bl_stg1.target_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__file_system_service_belongs_to_storage_account", "alias": "fss_bl_stg2", "leftCol": "p_id", "rightCol": "target_p_id", "colEnrichments": [{"colName": "associated_file_system_service_count", "aggExpr": "fss_bl_stg2.source_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__queue_service_belongs_to_storage_account", "alias": "q_bl_stg1", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "associated_storage_account_has_queue_service_count", "aggExpr": "q_bl_stg1.target_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__queue_service_belongs_to_storage_account", "alias": "q_bl_stg2", "leftCol": "p_id", "rightCol": "target_p_id", "colEnrichments": [{"colName": "associated_queue_service_count", "aggExpr": "q_bl_stg2.source_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__table_service_belongs_to_storage_account", "alias": "ts_bl_stg1", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "associated_storage_account_has_table_service_count", "aggExpr": "ts_bl_stg1.target_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__table_service_belongs_to_storage_account", "alias": "ts_bl_stg2", "leftCol": "p_id", "rightCol": "target_p_id", "colEnrichments": [{"colName": "associated_table_service_count", "aggExpr": "ts_bl_stg2.source_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_storage", "alias": "finding_associated_with_storage", "filter": "lower(rel_finding_status)='open'", "colEnrichments": [{"colName": "active_storage_finding_count", "aggExpr": "finding_associated_with_storage.source_p_id", "aggFunction": "count"}]}], "countEnriches": [{"colName": "associated_cloud_account_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__storage_resource_belongs_to_cloud_account"}], "entity": {"name": "Storage"}}