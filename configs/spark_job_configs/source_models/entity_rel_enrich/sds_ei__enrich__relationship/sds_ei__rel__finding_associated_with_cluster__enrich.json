{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_cluster", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_cluster__enrich"}, "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__finding", "alias": "sds_ei__finding", "colEnrichments": [{"colName": "finding_severity_latest", "aggExpr": "sds_ei__finding.vendor_severity_normalised", "aggFunction": "latest"}, {"colName": "finding_resolved_date_latest", "aggExpr": "sds_ei__finding.finding_resolved_date", "aggFunction": "latest"}, {"colName": "reopened_date_latest", "aggExpr": "sds_ei__finding.reopened_date", "aggFunction": "latest"}]}], "derivedProperties": [{"colName": "temp_sla_condition", "colExpr": "CASE WHEN finding_severity_latest = 'Critical' THEN 3 WHEN finding_severity_latest = 'High' THEN 5 WHEN finding_severity_latest = 'Medium' THEN 14 WHEN finding_severity_latest = 'Low' THEN 30 ELSE NULL END"}, {"colName": "cluster_finding_sla_duration", "colExpr": "CASE WHEN finding_resolved_date_latest IS NOT NULL THEN datediff(from_unixtime(coalesce(finding_resolved_date_latest,updated_at)/1000, 'yyyy-MM-dd'), from_unixtime(reopened_date_latest/1000, 'yyyy-MM-dd')) END"}, {"colName": "cluster_finding_sla_breach_duration", "colExpr": "CASE WHEN COALESCE(cluster_finding_sla_duration, 0) - COALESCE(temp_sla_condition, 0) > 0 THEN COALESCE(cluster_finding_sla_duration, 0) - COALESCE(temp_sla_condition, 0) ELSE 0 END"}, {"colName": "cluster_finding_sla_resolve_status", "colExpr": "CASE WHEN cluster_finding_sla_breach_duration>0 THEN false ELSE true END"}], "relationship": {"name": "Finding Associated With Cluster"}}