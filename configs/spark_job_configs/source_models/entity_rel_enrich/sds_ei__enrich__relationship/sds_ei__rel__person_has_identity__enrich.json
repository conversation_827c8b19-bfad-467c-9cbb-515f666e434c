{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__person_has_identity", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__person_has_identity__enrich"}, "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__person", "alias": "sds_ei__person", "colEnrichments": [{"colName": "person_termination_date", "aggExpr": "sds_ei__person.termination_date", "aggFunction": "latest"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__identity", "alias": "sds_ei__identity", "colEnrichments": [{"colName": "identity_login_last_date", "aggExpr": "sds_ei__identity.login_last_date", "aggFunction": "latest"}, {"colName": "identity_last_signin_attempt", "aggExpr": "sds_ei__identity.last_signin_attempt", "aggFunction": "latest"}]}], "derivedProperties": [{"colName": "post_termination_login_status", "colExpr": "CASE WHEN datediff(from_unixtime(identity_login_last_date/1000,'yyyy-MM-dd'),from_unixtime(person_termination_date/1000,'yyyy-MM-dd'))>0 then true else false END"}, {"colName": "inactive_user_signin_attempt_status", "colExpr": "CASE WHEN datediff(from_unixtime(identity_last_signin_attempt / 1000, 'yyyy-MM-dd'), from_unixtime(person_termination_date/1000, 'yyyy-MM-dd')) > 0 THEN true ELSE false END"}], "relationship": {"name": "Person Has Identity"}}