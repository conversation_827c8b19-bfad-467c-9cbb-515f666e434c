{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host__enrich"}, "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability", "alias": "sds_ei__vulnerability", "colEnrichments": [{"colName": "vulnerability_severity", "aggExpr": "sds_ei__vulnerability.vulnerability_severity", "aggFunction": "latest"}, {"colName": "exploitability", "aggExpr": "sds_ei__vulnerability.exploitability", "aggFunction": "latest"}]}], "derivedProperties": [{"colName": "temp_sla_condition", "colExpr": "CASE WHEN vulnerability_severity = 'High' and exploitability = 'Exploitable' THEN 5 WHEN vulnerability_severity = 'Critical' THEN 3 WHEN vulnerability_severity = 'High' and exploitability != 'Exploitable' THEN 7 WHEN vulnerability_severity = 'Medium' and exploitability = 'Exploitable' THEN 14 WHEN vulnerability_severity = 'Medium' and exploitability != 'Exploitable' THEN 27 WHEN vulnerability_severity = 'Low' and exploitability = 'Exploitable' THEN 30 WHEN vulnerability_severity = 'Low' and exploitability != 'Exploitable' THEN 45 ELSE NULL END"}, {"colName": "vulnerability_breach_duration", "colExpr": "CASE WHEN COALESCE(sla_duration, 0) - COALESCE(temp_sla_condition, 0) > 0 THEN COALESCE(sla_duration, 0) - COALESCE(temp_sla_condition, 0) ELSE 0 END"}, {"colName": "vulnerability_patch_sla_breach_status", "colExpr": "CASE WHEN vulnerability_breach_duration>0 THEN true ELSE false END"}], "relationship": {"name": "Vulnerability Finding On Host"}}