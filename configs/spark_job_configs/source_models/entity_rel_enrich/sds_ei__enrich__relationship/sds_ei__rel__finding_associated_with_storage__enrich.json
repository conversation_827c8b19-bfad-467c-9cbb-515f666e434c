{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_storage", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_storage__enrich"}, "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__finding", "alias": "sds_ei__finding", "colEnrichments": [{"colName": "finding_severity_latest", "aggExpr": "sds_ei__finding.vendor_severity_normalised", "aggFunction": "latest"}, {"colName": "finding_resolved_date_latest", "aggExpr": "sds_ei__finding.finding_resolved_date", "aggFunction": "latest"}, {"colName": "reopened_date_latest", "aggExpr": "sds_ei__finding.reopened_date", "aggFunction": "latest"}, {"colName": "first_found_date_latest", "aggExpr": "sds_ei__finding.first_found_date", "aggFunction": "latest"}]}], "derivedProperties": [{"colName": "temp_sla_condition", "colExpr": "CASE WHEN finding_severity_latest = 'Critical' THEN 3 WHEN finding_severity_latest = 'High' THEN 5 WHEN finding_severity_latest = 'Medium' THEN 14 WHEN finding_severity_latest = 'Low' THEN 30 ELSE NULL END"}, {"colName": "storage_finding_sla_duration", "colExpr": "datediff(from_unixtime(coalesce(finding_resolved_date_latest,updated_at)/1000, 'yyyy-MM-dd'), from_unixtime(coalesce(reopened_date_latest,first_found_date_latest)/1000, 'yyyy-MM-dd'))"}, {"colName": "storage_finding_sla_breach_duration", "colExpr": "CASE WHEN COALESCE(storage_finding_sla_duration, 0) - COALESCE(temp_sla_condition, 0) > 0 THEN COALESCE(storage_finding_sla_duration, 0) - COALESCE(temp_sla_condition, 0) ELSE 0 END"}, {"colName": "storage_finding_sla_resolve_status", "colExpr": "CASE WHEN storage_finding_sla_breach_duration>0 THEN false ELSE true END"}], "relationship": {"name": "Finding Associated With Storage"}}