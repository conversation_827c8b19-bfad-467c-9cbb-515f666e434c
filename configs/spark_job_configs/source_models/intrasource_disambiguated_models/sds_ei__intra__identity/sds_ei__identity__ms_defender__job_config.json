{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_defender_device_list__aad_device_id", "name": "sds_ei__identity__ms_defender_device_list__aad_device_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__identity__ms_defender_device_list__aad_device_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": [], "aggregation": [{"field": "first_seen_date", "function": "min"}], "valueConfidence": []}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Defender'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_defender", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Ms Defender"}, "entity": {"name": "Identity"}}