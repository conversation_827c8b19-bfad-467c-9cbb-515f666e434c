{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__saviynt_iga_users__email", "name": "sds_ei__identity__saviynt_iga_users__email"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__saviynt_iga_users__secondary_email", "name": "sds_ei__identity__saviynt_iga_users__secondary_email"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__saviynt_iga_accounts__name", "name": "sds_ei__identity__saviynt_iga_accounts__name"}], "disambiguation": {"candidateKeys": [{"name": "iga_username", "matchAttributesList": ["email_id"]}], "confidenceMatrix": ["sds_ei__identity__saviynt_iga_users__email", "sds_ei__identity__saviynt_iga_users__secondary_email", "sds_ei__identity__saviynt_iga_accounts__name"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": ["identity_format", "employee_id"], "aggregation": [{"field": "first_seen_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Saviynt IGA'"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__identity__saviynt_iga", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Saviynt Iga"}, "entity": {"name": "Identity"}}