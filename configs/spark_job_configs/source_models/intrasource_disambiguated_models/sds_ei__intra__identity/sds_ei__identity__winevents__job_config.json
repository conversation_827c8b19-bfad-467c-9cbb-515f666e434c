{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4624__subject_account_name", "name": "sds_ei__identity__winevents_4624__subject_account_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4624__object_account_name", "name": "sds_ei__identity__winevents_4624__object_account_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4725__subject_account_name", "name": "sds_ei__identity__winevents_4725__subject_account_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4725__object_account_name", "name": "sds_ei__identity__winevents_4725__object_account_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4726__subject_account_name", "name": "sds_ei__identity__winevents_4726__subject_account_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4726__object_account_name", "name": "sds_ei__identity__winevents_4726__object_account_name"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__identity__winevents_4624__subject_account_name", "sds_ei__identity__winevents_4624__object_account_name", "sds_ei__identity__winevents_4725__subject_account_name", "sds_ei__identity__winevents_4725__object_account_name", "sds_ei__identity__winevents_4726__subject_account_name", "sds_ei__identity__winevents_4726__object_account_name"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "operational_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "login_last_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Windows Security Logs'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Winevents"}, "entity": {"name": "Identity"}}