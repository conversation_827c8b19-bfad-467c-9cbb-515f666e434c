{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__globalprotect_vpn__user_id", "name": "sds_ei__identity__globalprotect_vpn__user_id"}], "disambiguation": {"candidateKeys": ["primary_key", "ad_sam_account_name"], "confidenceMatrix": ["sds_ei__identity__globalprotect_vpn__user_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": [], "aggregation": [{"field": "first_seen_date", "function": "min"}, {"field": "sf_created_date", "function": "min"}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'GlobalProtect'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__identity__global_protect", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Global Protect"}, "entity": {"name": "Identity"}}