{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__successfactors_hr__work_email", "name": "sds_ei__identity__successfactors_hr__work_email"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__successfactors_hr__external_email_id", "name": "sds_ei__identity__successfactors_hr__external_email_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__identity__successfactors_hr__work_email", "sds_ei__identity__successfactors_hr__external_email_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": ["employee_id"], "aggregation": [{"field": "first_seen_date", "function": "min"}, {"field": "sf_created_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'SuccessFactors'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__identity__successfactors_hr", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Successfactors Hr"}, "entity": {"name": "Identity"}}