{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_intunes__email", "name": "sds_ei__identity__ms_intunes__email"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_intunes__user_principal_name", "name": "sds_ei__identity__ms_intunes__user_principal_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_intunes__azuread_device_id", "name": "sds_ei__identity__ms_intunes__azuread_device_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__identity__ms_intunes__azuread_device_id", "sds_ei__identity__ms_intunes__email", "sds_ei__identity__ms_intunes__user_principal_name"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": [], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "login_last_date", "function": "max"}, {"field": "mdm_last_sync_date", "function": "max"}, {"field": "mdm_enrolled_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Intune'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_intune", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Ms Intune"}, "entity": {"name": "Identity"}}