{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__rapid7_assets__assetvulns_id", "name": "rapid7_insightvm_assets"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__rapid7_vulnerabilities__cveid_id", "name": "rapid7_insightvm_vulnerabilities"}], "disambiguation": {"candidateKeys": ["vendor_id"], "confidenceMatrix": ["rapid7_insightvm_assets", "rapid7_insightvm_vulnerabilities"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "CASE WHEN (ARRAY_CONTAINS(data_source_subset_name,'Rapid7 InsightVM Vulnerabilities') AND SIZE(data_source_subset_name)>1 AND found_in_organisation = TRUE) OR ARRAY_CONTAINS(data_source_subset_name,'Rapid7 InsightVM Assets') THEN 'Rapid7 InsightVM' ELSE 'Rapid7 InsightVM Vulnerabilities' END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__rapid7", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "filter": "origin = 'Rapid7 InsightVM'"}, "dataSource": {"name": "Rapid7 InsightVM"}, "entity": {"name": "Vulnerability"}}