{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_resource_details__arn", "name": "sds_ei__network__aws_resource_details__arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_resource_details__gateway_vpc_id", "name": "sds_ei__network__aws_resource_details__gateway_vpc_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_resource_details__lb_subnet_id", "name": "sds_ei__network__aws_resource_details__lb_subnet_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_resource_details__nic_subnet_id", "name": "sds_ei__network__aws_resource_details__nic_subnet_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_resource_details__subnet_vpc_id", "name": "sds_ei__network__aws_resource_details__subnet_vpc_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_sh_findings__resource_id", "name": "sds_ei__network__aws_sh_findings__resource_id"}], "disambiguation": {"candidateKeys": ["primary_key", "resource_id", "resource_name"], "confidenceMatrix": ["sds_ei__network__aws_resource_details__arn", "sds_ei__network__aws_resource_details__gateway_vpc_id", "sds_ei__network__aws_resource_details__lb_subnet_id", "sds_ei__network__aws_resource_details__nic_subnet_id", "sds_ei__network__aws_resource_details__subnet_vpc_id", "sds_ei__network__aws_sh_findings__resource_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "aws_resource_created_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'AWS'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "properties", "colExpr": "cast(null as string)"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "AWS"}, "entity": {"name": "Network"}}