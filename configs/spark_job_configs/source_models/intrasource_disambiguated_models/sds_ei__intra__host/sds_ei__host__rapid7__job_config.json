{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7_assets__id", "name": "rapid7_insightvm_assets"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7_asset_group_assets__assetgroupsid_id", "name": "rapid7_insightvm_asset_group_assets"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7_site_assets__sitesid_id", "name": "rapid7_insightvm_site_assets"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7_tag_assets__tagid_id", "name": "rapid7_insightvm_tag_assets"}], "disambiguation": {"candidateKeys": [{"name": "rapid7_asset_id", "keyToKeyMatch": true, "matchAttributesList": ["host_name", "fqdn"]}], "confidenceMatrix": ["rapid7_insightvm_assets", "rapid7_insightvm_site_assets", "rapid7_insightvm_asset_group_assets", "rapid7_insightvm_tag_assets"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": ["rapid7_hostnames", "rapid7_scan_type", "rapid7_site_id", "rapid7_site_name", "rapid7_asset_group_id", "rapid7_asset_group_name", "rapid7_tag", "rapid7_tag_id"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "vm_onboarding_status", "confidenceMatrix": ["True", "False"]}, {"field": "rapid7_onboarding_status", "confidenceMatrix": ["True", "False"]}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}, {"field": "rapid7_site_last_scan_date", "function": "max"}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Rapid7 InsightVM'"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Rapid7 InsightVM"}, "entity": {"name": "Host"}}