{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intunes__device_id", "name": "sds_ei__host__ms_intunes__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intunes_encryption_state__device_id", "name": "sds_ei__host__ms_intunes_encryption_state__device_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__host__ms_intunes__device_id", "sds_ei__host__ms_intunes_encryption_state__device_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": [], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "aggregation": []}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Intune'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intune", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "MS Intune"}, "entity": {"name": "Host"}}