{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tanium_cve_findings__id", "name": "tanium_cve_findings"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tanium_endpoints__id", "name": "tanium_endpoints"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tanium_endpoints_extended_host_list_report__id_region_tag", "name": "tanium_extended_endpoints"}], "disambiguation": {"candidateKeys": [{"name": "primary_key", "keyToKeyMatch": true, "matchAttributesList": ["host_name", "fqdn"]}], "confidenceMatrix": ["tanium_endpoints", "tanium_cve_findings", "tanium_extended_endpoints"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": ["ip", "tanium_custom_tags"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "vm_onboarding_status", "confidenceMatrix": ["True", "False"]}, {"field": "tanium_onboarding_status", "confidenceMatrix": ["True", "False"]}, {"field": "type", "confidenceMatrix": ["Hypervisor", "Server", "Workstation", "Network Device", "Virtual Machine", "Container", "Printer", "Mobile"]}, {"field": "is_cloud_resource", "confidenceMatrix": ["True", "False"]}, {"field": "host_sub_type", "confidenceMatrix": ["Domain Controller", "DHCP Server", "File Server", "Desktop", "Laptop", "ThinClient", "iPhone", "iPad", "iPod", "Tablet", "Android Device"]}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Tanium'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "is_mobile", "colExpr": "CASE WHEN lower(type)='mobile' THEN True ELSE False END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__tanium", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Tanium"}, "entity": {"name": "Host"}}