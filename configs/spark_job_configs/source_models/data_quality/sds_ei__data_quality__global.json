{"dqDimensions": ["completeness"], "enrichFilter": "kg_job_type = 'intersource_disambiguated_models'", "enrichRequiredColumns": ["completeness_quality_score_category", "completeness_quality_score", "aggregated_quality_score"], "completeness": {"noDataIdentifier": "  !", "skipColumns": ["disambiguated_p_id", "table_name"], "qualityBands": [{"label": "Low", "min": 0, "max": 50}, {"label": "Medium", "min": 50, "max": 75}, {"label": "High", "min": 75, "max": 100}]}}