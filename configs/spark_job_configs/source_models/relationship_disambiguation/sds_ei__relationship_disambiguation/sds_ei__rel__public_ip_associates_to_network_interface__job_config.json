{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_resource_details__public_ip_associates_to_network_interface", "name": "sds_ei__rel__azure_resource_details__public_ip_associates_to_network_interface"}], "disambiguation": {"disambiguationGrouping": {"type": "Union"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__public_ip_associates_to_network_interface"}, "entity": {"name": "Public IP Associates To Network Interface"}}