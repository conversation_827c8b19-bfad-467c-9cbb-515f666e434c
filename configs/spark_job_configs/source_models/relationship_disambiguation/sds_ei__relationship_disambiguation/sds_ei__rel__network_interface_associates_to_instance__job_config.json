{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__network_interface_associates_to_instance", "name": "sds_ei__rel__aws_resource_details__network_interface_associates_to_instance"}], "disambiguation": {"disambiguationGrouping": {"type": "Union"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__network_interface_associates_to_instance"}, "entity": {"name": "Network Interface Associates To Instance"}}