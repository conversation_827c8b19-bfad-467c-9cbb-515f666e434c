{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__active_directory__person_has_identity", "name": "sds_ei__rel__active_directory__person_has_identity"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_ad_users__person_has_identity", "name": "sds_ei__rel__ms_azure_ad_users__person_has_identity"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_intunes__person_has_identity", "name": "sds_ei__rel__ms_intunes__person_has_identity"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__successfactors_hr__person_has_identity", "name": "sds_ei__rel__successfactors_hr__person_has_identity"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__saviynt_iga_users__person_has_identity", "name": "sds_ei__rel__saviynt_iga_users__person_has_identity"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__snow_itsm__person_has_identity", "name": "sds_ei__rel__snow_itsm__person_has_identity"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws__iam_list_users__person_has_identity", "name": "sds_ei__rel__aws__iam_list_users__person_has_identity"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws__iam_security_center_permission_set_assignment__person_has_identity", "name": "sds_ei__rel__aws__iam_security_center_permission_set_assignment__person_has_identity"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_ad_sign_in__person_has_identity", "name": "sds_ei__rel__ms_azure_ad_sign_in__person_has_identity"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__person_has_identity"}, "entity": {"name": "Person Has Identity"}}