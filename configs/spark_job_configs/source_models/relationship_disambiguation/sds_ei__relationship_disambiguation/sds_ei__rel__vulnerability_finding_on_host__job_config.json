{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender__vulnerability_finding_on_host", "name": "sds_ei__rel__ms_defender__vulnerability_finding_on_host"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__qualys_host_vulnerability__vulnerability_finding_on_host", "name": "sds_ei__rel__qualys_host_vulnerability__vulnerability_finding_on_host"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__tenable_sc__vulnerability_finding_on_host", "name": "sds_ei__rel__tenable_sc__vulnerability_finding_on_host"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__wiz__vulnerability_finding_on_host", "name": "sds_ei__rel__wiz__vulnerability_finding_on_host"}], "disambiguation": {"disambiguationGrouping": {"type": "VulnerabilityFinding", "blockVariables": ["source_p_id", "target_p_id", "software_full_name"], "statusField": "current_status"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin", "vendor_status", "qualys_vulnerability_id", "qualys_host_id", "ms_recommended_update", "ms_recommended_update_id", "path_details"], "aggregation": [{"field": "last_reopened_date", "function": "max"}]}}, "derivedProperties": [{"colName": "vulnerability_fixed_date", "colExpr": "case when current_status='Open' then null else vulnerability_fixed_date end"}, {"colName": "inactivity_period", "colExpr": "COALESCE(inactivity_period, 180)"}, {"colName": "rel_archival_flag", "colExpr": "CASE WHEN datediff(from_unixtime(updated_at/1000, 'yyyy-MM-dd'), from_unixtime(relationship_last_seen_date/1000, 'yyyy-MM-dd')) > round(CAST(<% ARCHIVAL_THRESHOLD_FACTOR %> AS DOUBLE) * inactivity_period) THEN true ELSE false END"}, {"colName": "sla_duration", "colExpr": "datediff(from_unixtime(relationship_last_seen_date/1000,'yyyy-MM-dd'),from_unixtime(relationship_first_seen_date/1000,'yyyy-MM-dd'))"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host"}, "entity": {"name": "Vulnerability Finding On Host"}}