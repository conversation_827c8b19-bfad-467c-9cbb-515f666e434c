{"transformSpec": {"type": "AttributeWriteBack", "postSchemas": "<%EI_POST_SCHEMA_NAMES%>", "tableInfo": {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__host__enrich", "tableType": "entity", "commonColumns": ["p_id", "updated_at", "updated_at_ts"], "uniqCol": "p_id"}}, "outputTableInfo": {"partitionColumns": ["archival_flag", "class"], "outputTableName": "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__host__publish"}, "entity": {"name": "Host"}}