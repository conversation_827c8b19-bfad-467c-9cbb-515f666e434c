{"transformSpec": {"type": "AttributeWriteBack", "postSchemas": "<%EI_POST_SCHEMA_NAMES%>", "tableInfo": {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__compute_instance_group_belongs_to_kubernetes_cluster", "tableType": "relation", "commonColumns": ["relationship_id", "updated_at_partition", "updated_at_ts"], "uniqCol": "relationship_id"}}, "outputTableInfo": {"partitionColumns": ["relationship_name"], "outputTableName": "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__rel__compute_instance_group_belongs_to_kubernetes_cluster__publish"}, "relationship": {"name": "Compute Instance Group Belongs To Kubernetes Cluster"}}