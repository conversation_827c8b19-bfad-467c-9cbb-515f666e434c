{"transformSpec": {"type": "AttributeWriteBack", "postSchemas": "<%EI_POST_SCHEMA_NAMES%>", "tableInfo": {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_network_interface", "tableType": "relation", "commonColumns": ["relationship_id", "updated_at_partition", "updated_at_ts"], "uniqCol": "relationship_id"}}, "outputTableInfo": {"partitionColumns": ["relationship_name"], "outputTableName": "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_network_interface__publish"}, "relationship": {"name": "Finding Associated With Network Interface"}}