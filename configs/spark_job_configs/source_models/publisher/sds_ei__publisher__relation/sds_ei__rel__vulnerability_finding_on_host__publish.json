{"transformSpec": {"type": "AttributeWriteBack", "postSchemas": "<%EI_POST_SCHEMA_NAMES%>", "tableInfo": {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host", "tableType": "relation", "commonColumns": ["relationship_id", "updated_at_partition", "updated_at_ts"], "uniqCol": "relationship_id"}}, "outputTableInfo": {"partitionColumns": ["rel_archival_flag", "relationship_name"], "outputTableName": "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host__publish"}, "relationship": {"name": "Vulnerability Finding On Host"}}