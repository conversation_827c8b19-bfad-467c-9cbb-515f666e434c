{"entityClass": "Identity", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(identity_display_name,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "inactivity_period", "colExpr": "30", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "active_user_login_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "password_rotation_compliant_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "password_rotation_compliant", "colExpr": "case when datediff(from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(last_password_change_date / 1000, 'yyyy-MM-dd'))<= password_rotation_compliant_duration then true else false end", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "active_user_login_status", "colExpr": "case when datediff(from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(login_last_date / 1000, 'yyyy-MM-dd'))<= active_user_login_duration then true else false end", "fieldsSpec": {"computationPhase": "inter"}}], "entitySpecificProperties": [{"colName": "account_never_expire", "colExpr": "COALESCE(account_never_expire, false)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "is_mfa_enabled", "colExpr": "COALESCE(cast(is_mfa_enabled as boolean), false)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "password_never_expire", "colExpr": "COALESCE(cast(password_never_expire as boolean), false)", "fieldsSpec": {"computationPhase": "inter"}}], "lastUpdateFields": ["type", "first_seen_date", "user_principal_name", "identity_display_name", "email_id", "ownership", "last_password_change_date", "last_logged_in_location", "password_never_expire", "password_not_required", "account_never_expire", "operational_status", "aad_id", "login_last_date", "is_mfa_enabled", "identity_format", "authentication_factors", "location_country"], "entity": {"name": "Identity"}}