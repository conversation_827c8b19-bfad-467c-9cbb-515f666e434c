{"entityClass": "Container", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(resource_name,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "inactivity_period", "colExpr": "180", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "infrastructure_type", "colExpr": "CASE WHEN cloud_provider IS NOT NULL THEN 'Cloud' ELSE 'On-Premise' END", "fieldsSpec": {"computationPhase": "inter"}}], "lastUpdateFields": ["type", "business_unit", "department", "location_country", "billing_tag", "environment", "operational_state", "cloud_provider", "resource_id", "resource_name", "private_ip", "encryption_status"], "entity": {"name": "Container"}}