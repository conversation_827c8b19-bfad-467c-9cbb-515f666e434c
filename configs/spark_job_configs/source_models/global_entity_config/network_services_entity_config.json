{"entityClass": "Network Services", "commonProperties": [{"colName": "display_label", "colExpr": "Coalesce(resource_name,primary_key)", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "inactivity_period", "colExpr": "180", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "CASE WHEN lower(provisioning_state) IN ('succeeded', 'ok') THEN last_found_date ELSE cast(null as bigint) END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_updated_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "infrastructure_type", "colExpr": "Case when cloud_provider is not null then 'Cloud' else 'On-Premise' end", "fieldsSpec": {"isInventoryDerived": true}}], "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "location_country", "resource_name", "provisioning_state", "resource_id", "infrastructure_type", "cloud_provider", "account_id", "region", "project", "mac_address", "native_type", "attachment_status", "operational_state", "ip_forwarding_status", "vpc_ddos_protection_status", "public_ip_address", "public_ip_address_allocation_method", "public_ip_address_version"], "entity": {"name": "Network Services"}}