{"primaryKey": "CASE WHEN temp_primary_explode_new IS NULL THEN pluginID ELSE CONCAT(temp_primary_explode_new,'||',pluginID) END", "origin": "'Tenable.sc'", "temporaryProperties": [{"colName": "temp_primary_explode_new", "colExpr": "EXPLODE_OUTER(FILTER(TRANSFORM(SPLIT(cve,','), x -> REGEXP_EXTRACT(x,'CVE-\\\\d{4}-\\\\d{4,}',0)), x -> x !=''))", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_first_seen", "colExpr": "unix_millis(timestamp(to_timestamp(firstSeen)))"}, {"colName": "temp_last_seen", "colExpr": "unix_millis(timestamp(to_timestamp(lastSeen)))"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN riskFactor  IS NULL THEN 'Informational' ELSE 'Vulnerability' END"}, {"colName": "first_seen_date", "colExpr": "vulnerability_first_observed_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "temp_last_seen", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "description", "colExpr": "regexp_replace(description, '<[^>]++>', ' ')"}], "entitySpecificProperties": [{"colName": "cve_id", "colExpr": "CASE WHEN temp_primary_explode_new='' THEN NULL else temp_primary_explode_new END"}, {"colName": "cpe", "colExpr": "cpe"}, {"colName": "exploit_available", "colExpr": "CASE WHEN exploitAvailable='Yes' THEN TRUE ELSE FALSE END"}, {"colName": "patch_available", "colExpr": "CASE WHEN patchPubDate > 0 THEN TRUE ELSE FALSE END"}, {"colName": "title", "colExpr": "pluginName"}, {"colName": "temporal_cvss_score", "colExpr": "cast(temporalScore as double)"}, {"colName": "vendor_severity", "colExpr": "CASE WHEN LOWER(severity.name) ='info' OR LOWER(severity.name)='low' OR severity.name IS NULL THEN 'Low' ELSE severity.name END"}, {"colName": "vulnerability_first_observed_date", "colExpr": "temp_first_seen", "fieldsSpec": {"aggregateFunction": "min"}}], "sourceSpecificProperties": [{"colName": "vendor_id", "colExpr": "pluginID"}, {"colName": "tenable_vulnerability_mitigation_status", "colExpr": "CASE WHEN hasBeenMitigated==1 THEN True else False END"}, {"colName": "bugtraq_id", "colExpr": "bid"}, {"colName": "found_in_organisation", "colExpr": "true", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tenablesc_accept_risk", "colExpr": "CASE WHEN acceptRisk==1 THEN True else False END"}, {"colName": "tenablesc_recast_risk", "colExpr": "CASE WHEN recastRisk==1 THEN True else False END"}, {"colName": "tenablesc_stig_severity", "colExpr": "stigSeverity"}, {"colName": "tenable_exploit_ease", "colExpr": "exploitEase"}, {"colName": "tenablesc_risk_factor", "colExpr": "riskFactor"}], "dataSource": {"name": "Tenable.sc", "feedName": "Analysis", "srdm": "<%SRDM_SCHEMA_NAME%>.emea_tenable_vuln_tenable_sc_vulns"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__tenable_sc_analysis__cve_pluginid", "entity": {"name": "Vulnerability"}}