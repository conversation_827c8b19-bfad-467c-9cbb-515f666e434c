{"primaryKey": "temp_primary_explode", "origin": "'Rapid7 InsightVM Assets'", "temporaryProperties": [{"colName": "temp_primary_explode", "colExpr": "EXPLODE_OUTER(asset_vulns.id)", "fieldsSpec": {"convertEmptyToNull": false}}], "commonProperties": [{"colName": "type", "colExpr": "'Vulnerability'"}], "entitySpecificProperties": [{"colName": "cve_id", "colExpr": "UPPER(regexp_extract(primary_key, '(CVE|cve|Cve|CvE)-[0-9]{4}-[0-9]{4,7}', 0))", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vendor_id", "colExpr": "temp_primary_explode"}], "sourceSpecificProperties": [{"colName": "found_in_organisation", "colExpr": "true", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Rapid7 InsightVM", "feedName": "Assets", "srdm": "<%SRDM_SCHEMA_NAME%>.rapid7_assets"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__rapid7_assets__assetvulns_id"}