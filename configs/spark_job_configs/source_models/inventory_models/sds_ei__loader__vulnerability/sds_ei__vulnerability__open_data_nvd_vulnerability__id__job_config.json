{"primaryKey": "id", "filterBy": "lower(vulnStatus) NOT IN ('received','rejected','deferred','awaiting analysis')", "origin": "'NVD'", "commonProperties": [{"colName": "description", "colExpr": "regexp_replace(descriptions.value[0], '<[^>]++>', ' ')"}, {"colName": "first_seen_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "title", "colExpr": "cisaVulnerabilityName"}, {"colName": "cve_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v2_score", "colExpr": "metrics.cvssMetricV2[0].cvssData.baseScore", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "v2_vector", "colExpr": "metrics.cvssMetricV2[0].cvssData.vectorString"}, {"colName": "v2_severity", "colExpr": "INITCAP(metrics.cvssMetricV2[0].baseSeverity)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "v2_exploitability", "colExpr": "metrics.cvssMetricV2[0].exploitabilityScore"}, {"colName": "v2_impact_score", "colExpr": "metrics.cvssMetricV2[0].impactScore", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "v30_score", "colExpr": "metrics.cvssMetricV30[0].cvssData.baseScore", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "v30_vector", "colExpr": "metrics.cvssMetricV30[0].cvssData.vectorString"}, {"colName": "v30_severity", "colExpr": "INITCAP(metrics.cvssMetricV30[0].cvssData.baseSeverity)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "v30_exploitability", "colExpr": "metrics.cvssMetricV30[0].exploitabilityScore"}, {"colName": "v30_impact_score", "colExpr": "metrics.cvssMetricV30[0].impactScore", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "v31_score", "colExpr": " metrics.cvssMetricV31[0].cvssData.baseScore", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "v31_vector", "colExpr": " metrics.cvssMetricV31[0].cvssData.vectorString"}, {"colName": "v31_severity", "colExpr": "INITCAP (metrics.cvssMetricV31[0].cvssData.baseSeverity)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "v31_exploitability", "colExpr": "metrics.cvssMetricV31[0].exploitabilityScore"}, {"colName": "v31_impact_score", "colExpr": "metrics.cvssMetricV31[0].impactScore", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "published_date", "colExpr": "CASE WHEN (published !='' AND (published  IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(CONCAT(SUBSTRING(published,1,19), 'Z'))) ELSE NULL END"}, {"colName": "last_modified_date", "colExpr": "CASE WHEN (lastModified  !='' AND (lastModified   IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(CONCAT(SUBSTRING(lastModified ,1,19), 'Z')))  ELSE NULL END"}, {"colName": "cisa_exploit_add_date", "colExpr": "CASE WHEN (cisaExploitAdd!='' AND (cisaExploitAdd    IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(cisaExploitAdd))  ELSE NULL END"}, {"colName": "cisa_action_due_date", "colExpr": "CASE WHEN (cisaActionDue !='' AND (cisaActionDue     IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(cisaActionDue))  ELSE NULL END"}, {"colName": "cisa_required_action", "colExpr": "cisaRequiredAction"}, {"colName": "cwe", "colExpr": "weaknesses.description[0].value"}, {"colName": "cpe", "colExpr": "to_json(configurations)"}], "sourceSpecificProperties": [{"colName": "found_in_organisation", "colExpr": "'false'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "nvd_status", "colExpr": "vulnStatus"}], "dataSource": {"name": "NVD", "feedName": "CVE API", "srdm": "<%SRDM_SCHEMA_NAME%>.open_data__nvd_vulnerability"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__open_data_nvd_vulnerability__id", "entity": {"name": "Vulnerability"}}