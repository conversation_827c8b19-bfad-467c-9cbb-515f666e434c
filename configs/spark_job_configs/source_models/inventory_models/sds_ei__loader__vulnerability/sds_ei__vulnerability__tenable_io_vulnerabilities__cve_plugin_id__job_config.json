{"primaryKey": "CONCAT_ws('||',temp_primary_explode_new,plugin.id)", "filterBy": "plugin.id IS NOT NULL", "origin": "'Tenable.io Vulnerabilities'", "temporaryProperties": [{"colName": "temp_primary_explode_new", "colExpr": "EXPLODE_OUTER(FILTER(TRANSFORM(plugin.cve, x -> REGEXP_EXTRACT(x,'CVE-\\\\d{4}-\\\\d{4,}',0)), x -> x !=''))", "fieldsSpec": {"convertEmptyToNull": false}}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN lower(plugin.risk_factor)= NULL or lower(plugin.risk_factor)= 'info' THEN 'Informational' ELSE 'Vulnerability' END"}, {"colName": "last_active_date", "colExpr": "GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(last_found))), UNIX_MILLIS(TIMESTAMP(to_timestamp(indexed))))"}, {"colName": "description", "colExpr": "plugin.description"}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date, first_found_date, vulnerability_first_observed_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "cve_id", "colExpr": "CASE WHEN temp_primary_explode_new='' THEN NULL else temp_primary_explode_new END"}, {"colName": "cpe", "colExpr": "ARRAY_JOIN(plugin.cpe,' ,')"}, {"colName": "patch_available", "colExpr": "CASE WHEN plugin.has_patch = True OR plugin.patch_publication_date is not null Then true ELSE FALSE END"}, {"colName": "title", "colExpr": "plugin.name"}, {"colName": "vulnerability_first_observed_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(min(first_found) over (partition by primary_key rows between unbounded preceding and unbounded following))))"}, {"colName": "exploit_available", "colExpr": "CASE WHEN tenableio_exploit_available = true  THEN TRUE ELSE FALSE END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vendor_severity", "colExpr": "CASE WHEN lower(severity) = 'info' OR lower(severity) = 'low'  OR severity = NULL  THEN 'Low' ELSE severity END"}], "sourceSpecificProperties": [{"colName": "bugtraq_id", "colExpr": "CAST(plugin.bid[0] AS STRING)"}, {"colName": "vendor_id", "colExpr": "CAST(plugin.id AS STRING)"}, {"colName": "tenableio_exploit_available", "colExpr": "plugin.exploit_available"}, {"colName": "tenableio_unsupported_by_vendor", "colExpr": "plugin.unsupported_by_vendor"}], "dataSource": {"name": "Tenable.io", "feedName": "Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.tenable_io_Vulnerabilities"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id", "entity": {"name": "Vulnerability"}}