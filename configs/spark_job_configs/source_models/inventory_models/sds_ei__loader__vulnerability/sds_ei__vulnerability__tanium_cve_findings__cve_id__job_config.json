{"primaryKey": "cveFinding.Check__ID", "filterBy": " cveFinding.Check__ID IS NOT NULL AND cveFinding.Check__ID != '[no results]' AND cveFinding.Check__ID != 'ERROR: command is not registered' AND cveFinding.Check__ID != '[current result unavailable]'", "origin": "'Tanium CVE Findings'", "commonProperties": [{"colName": "type", "colExpr": "'Vulnerability'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "unix_timestamp(to_date( max(lastFound) over (partition by primary_key rows between unbounded preceding and unbounded following), 'yyyyMMdd'))*1000"}, {"colName": "description", "colExpr": "cveF<PERSON><PERSON>.Su<PERSON>ry", "fieldsSpec": {"persistNonNullValue": false}}], "entitySpecificProperties": [{"colName": "cve_id", "colExpr": "UPPER(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "title", "colExpr": "description", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v2_score", "colExpr": "CAST(cveFinding.CVSS__Score AS FLOAT)"}, {"colName": "v2_severity", "colExpr": "CASE WHEN LOWER(cveFinding.Severity) = '[no results]' OR LOWER(cveFinding.Severity) = 'unscored' OR cveFinding.Severity IS NULL THEN 'Low' ELSE cveFinding.Severity END"}, {"colName": "v31_score", "colExpr": "cveFinding.CVSS__v3__Base__Score"}, {"colName": "v31_severity", "colExpr": "CASE WHEN LOWER(cveFinding.CVSS__v3__Severity) = '[no results]' OR LOWER(cveFinding.CVSS__v3__Severity) = 'unscored' OR cveFinding.CVSS__v3__Severity IS NULL THEN 'Low' ELSE cveFinding.CVSS__v3__Severity END"}, {"colName": "exploit_available", "colExpr": "is_end_of_life", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vendor_severity", "colExpr": "v31_severity", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vulnerability_first_observed_date", "colExpr": "MIN( COALESCE( UNIX_TIMESTAMP(to_date(absoluteFirstFoundDate, 'yyyyMMdd')), UNIX_TIMESTAMP(to_date(firstFound, 'yyyyMMdd')) ) ) OVER ( PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING ) * 1000"}, {"colName": "is_end_of_life", "colExpr": "CASE WHEN LOWER(description) LIKE '%eol%' OR <PERSON>OWER(description) LIKE '%eol.%' OR LOWER(description) LIKE '%unsupported when assigned%' OR LOWER(description) LIKE '%unsupported_when_assigned%' OR LOWER(description) LIKE '%end of life%' OR LOWER(description) LIKE '%end-of-life%'  OR LOWER(description) LIKE '%end_of_life%'  THEN True ELSE False END", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "found_in_organisation", "colExpr": "cast(True as boolean)"}], "dataSource": {"name": "Tanium", "feedName": "CVE Findings", "srdm": "<%SRDM_SCHEMA_NAME%>.tanium__endpoints_cve_findings_report"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__tanium_cve_findings__cve_id"}