{"primaryKey": "CONCAT_WS('|',temp_cve_extracted, id)", "origin": "'Rapid7 InsightVM Vulnerabilities'", "temporaryProperties": [{"colName": "temp_primary_explode_new", "colExpr": "EXPLODE_OUTER(cves)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_cve_extracted", "colExpr": "UPPER(CASE WHEN temp_primary_explode_new IS NULL THEN regexp_extract(id, '(CVE-\\\\d{4}-\\\\d{4,7})', 1) ELSE temp_primary_explode_new END)", "fieldsSpec": {"convertEmptyToNull": false}}], "commonProperties": [{"colName": "type", "colExpr": "'Vulnerability'"}, {"colName": "description", "colExpr": "description.text"}], "entitySpecificProperties": [{"colName": "cve_id", "colExpr": "temp_cve_extracted", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "is_end_of_life", "colExpr": "CASE WHEN ( LOWER(cast(description.text as string)) LIKE '%end of life%' OR <PERSON>OWER(cast(description.text as string)) LIKE '%end-of-life%' OR LOWER(cast(description.text as string)) LIKE '%end_of_life%' OR LOWER(cast(description.text as string)) LIKE '% eol %' OR LOWER(cast(description.text as string)) LIKE '% eol.%' ) OR ( LOWER(cast(description.text as string)) LIKE '% seol%' OR LOWER(cast(description.text as string)) LIKE '%unsupported%' ) OR ( LOWER(cast(description.text as string)) LIKE '%lack of support%' OR LOWER(cast(description.text as string)) LIKE '%no longer maintained by its vendor%' ) THEN TRUE ELSE FALSE END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "title", "colExpr": "title"}, {"colName": "vendor_id", "colExpr": "id"}, {"colName": "vendor_severity", "colExpr": "CASE WHEN lower(severity) LIKE '%moderate%' THEN 'Low' WHEN lower(severity) LIKE '%severe%' THEN 'Medium' WHEN lower(severity) LIKE '%critical%' THEN 'High' END"}, {"colName": "exploit_available", "colExpr": "CASE WHEN exploits = '0' THEN false ELSE true END"}, {"colName": "is_malwarekit_available", "colExpr": "CASE WHEN malwarekits = '0' THEN false ELSE true END"}], "sourceSpecificProperties": [{"colName": "rapid7_vulnerability_risk_score", "colExpr": "riskscore"}, {"colName": "found_in_organisation", "colExpr": "cast(<PERSON><PERSON><PERSON> as boolean)"}], "dataSource": {"name": "Rapid7 InsightVM", "feedName": "Vulnerabilities", "srdm": "<%SRDM_SCHEMA_NAME%>.rapid7_vulnerabilities"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__rapid7_vulnerabilities__cveid_id"}