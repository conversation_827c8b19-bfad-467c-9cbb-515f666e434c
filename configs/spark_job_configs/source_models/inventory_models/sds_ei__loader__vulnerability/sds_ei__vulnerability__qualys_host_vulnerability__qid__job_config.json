{"primaryKey": "qid", "origin": "'Qualys Host Vulnerability'", "temporaryProperties": [{"colName": "first_found_datetime_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(first_found_datetime)))"}], "commonProperties": [{"colName": "type", "colExpr": "case when LOWER(type)  LIKE '%info%' then 'Informational' when LOWER(type)  LIKE '%confirmed%' then 'Vulnerability' when LOWER(type)  LIKE '%potential%' then 'Weakness' end"}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,vulnerability_first_observed_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "CASE WHEN (last_found_datetime != '[]' AND last_found_datetime != '(never)') THEN   UNIX_MILLIS(TIMESTAMP(to_timestamp(last_found_datetime))) ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}], "entitySpecificProperties": [{"colName": "vendor_severity", "colExpr": "severity"}, {"colName": "vulnerability_first_observed_date", "colExpr": "CASE WHEN (first_found_datetime != '[]' AND first_found_datetime != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(first_found_datetime)))ELSE NULL END", "fieldsSpec": {"aggregateFunction": "min"}}], "sourceSpecificProperties": [{"colName": "vendor_id", "colExpr": "qid"}, {"colName": "found_in_organisation", "colExpr": "true", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Qualys", "feedName": "Host Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.qualys__host_vulnerability"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__qualys_host_vulnerability__qid", "entity": {"name": "Vulnerability"}}