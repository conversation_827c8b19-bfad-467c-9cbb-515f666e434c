{"primaryKey": "lower(name)", "origin": "'MS Azure Security Resources'", "filterBy": "lower(type) IN ('microsoft.security/assessments')", "commonProperties": [{"colName": "type", "colExpr": "'Cloud Security'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "display_label", "colExpr": "coalesce(assessment_title,primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "regexp_replace(properties.metadata.description, '<[^>]++>', ' ')"}], "entitySpecificProperties": [{"colName": "assessment_id", "colExpr": "UPPER(name)"}, {"colName": "assessment_title", "colExpr": "LTRIM(case when regexp_like(properties.displayName,'\\\\[.*\\\\](.*)') then REGEXP_EXTRACT(properties.displayName, '\\\\[.*\\\\](.*)',1) else properties.displayName end)"}, {"colName": "assessment_severity", "colExpr": "case when lower(properties.metadata.severity) in ('informational','low','medium') then INITCAP(properties.metadata.severity) when lower(properties.metadata.severity) = 'high' then 'Critical' else 'Other' end"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "assessment_status", "colExpr": "case when lower(properties.status.code)= 'notapplicable' and lower(properties.status.cause)= 'offbypolicy' then 'Disabled' else 'Enabled' end"}, {"colName": "affected_resource_type", "colExpr": "case when properties.resourceDetails.ResourceType IS NOT NULL THEN REGEXP_REPLACE(REGEXP_EXTRACT(properties.resourceDetails.ResourceType, '([^/]+)$'), '[.]', '') WHEN REGEXP_EXTRACT(COALESCE(properties.resourceDetails.ResourceId,properties.resourceDetails.Id),'\\\\/providers\\\\/[^\\\\/]+\\\\/([^\\\\/]+)\\\\/') = '' THEN REGEXP_EXTRACT(COALESCE(properties.resourceDetails.ResourceId,properties.resourceDetails.Id),'\\\\/([^\\\\/]+)\\\\/') ELSE REGEXP_EXTRACT(COALESCE(properties.resourceDetails.ResourceId,properties.resourceDetails.Id),'\\\\/providers\\\\/[^\\\\/]+\\\\/([^\\\\/]+)\\\\/') END"}, {"colName": "scope_validation_steps", "colExpr": "ARRAY(CASE WHEN lower(affected_resource_type) LIKE '%mapreducecluster%' THEN 'MapReduce Cluster' WHEN lower(affected_resource_type) LIKE '%awsec2instance%' THEN 'Virtual Machine' WHEN lower(affected_resource_type) LIKE '%awsec2volume%' THEN 'Volume' WHEN lower(affected_resource_type) LIKE '%disks%' THEN 'Volume' WHEN lower(affected_resource_type) LIKE '%awslogsloggroup%' THEN 'Logging' WHEN lower(affected_resource_type) LIKE '%awsiampolicy%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) LIKE '%awsecsservice%' THEN 'Container Service' WHEN lower(affected_resource_type) LIKE '%awsrdsdbinstance%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awss3bucket%' THEN 'Bucket' WHEN lower(affected_resource_type) LIKE '%containergroups%' THEN 'Container Groups' WHEN lower(affected_resource_type) LIKE '%awssecretsmanagersecret%' THEN 'Secrets Management' WHEN lower(affected_resource_type) LIKE '%awselbv2loadbalancer%' THEN 'Load Balancer' WHEN lower(affected_resource_type) LIKE '%awslambdafunction%' THEN 'Serverless' WHEN lower(affected_resource_type) LIKE '%awsec2securitygroup%' THEN 'Firewall' WHEN lower(affected_resource_type) LIKE '%awsrdsdbsnapshot%' THEN 'Database Snapshot' WHEN lower(affected_resource_type) LIKE '%awsaccount%' THEN 'Cloud Account' WHEN lower(affected_resource_type) LIKE '%awskmskey%' THEN 'Key Management' WHEN lower(affected_resource_type) LIKE '%awsiamrole%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) LIKE '%awscertificatemanagercertificate%' THEN 'Certificate Management' WHEN lower(affected_resource_type) LIKE '%awsec2subnet%' THEN 'Subnet' WHEN lower(affected_resource_type) LIKE '%awsec2launchtemplate%' THEN 'Virtual Machine Configuration' WHEN lower(affected_resource_type) LIKE '%awsautoscalingautoscalinggroup%' or lower(affected_resource_type) LIKE '%awsec2ec2fleet%' THEN 'Compute Instance Group' WHEN lower(affected_resource_type) LIKE '%awselasticachereplicationgroup%' THEN 'Caching' WHEN lower(affected_resource_type) LIKE '%awsecstaskdefinition%' THEN 'Container Service' WHEN lower(affected_resource_type) LIKE '%awselasticachecachecluster%' THEN 'Caching' WHEN lower(affected_resource_type) LIKE '%awsecscluster%' THEN 'ECS Cluster' WHEN lower(affected_resource_type) LIKE '%awsefsaccesspoint%' THEN 'File System Service' WHEN lower(affected_resource_type) LIKE '%awsstepfunctionsstatemachine%' THEN 'Workflow Orchestration' WHEN lower(affected_resource_type) LIKE '%awsec2eip%' THEN 'Network Address' WHEN lower(affected_resource_type) LIKE '%awsiamuser%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) LIKE '%awsec2networkacl%' THEN 'Firewall' WHEN lower(affected_resource_type) LIKE '%awsekscluster%' THEN 'Kubernetes Cluster' WHEN lower(affected_resource_type) LIKE '%awscloudformationstack%' THEN 'Infrastructure as Code' WHEN lower(affected_resource_type) LIKE '%awscloudtrailtrail%' THEN 'Auditing and Monitoring' WHEN lower(affected_resource_type) LIKE '%awsefsfilesystem%' THEN 'File System Service' WHEN lower(affected_resource_type) LIKE '%awssnstopic%' THEN 'Messaging' WHEN lower(affected_resource_type) LIKE '%awsec2vpc%' THEN 'Virtual Network' WHEN lower(affected_resource_type) LIKE '%awsec2routetable%' THEN 'Route Table' WHEN lower(affected_resource_type) LIKE '%awsecrrepository%' THEN 'Container Registry' WHEN lower(affected_resource_type) LIKE '%awsec2internetgateway%' THEN 'Gateway' WHEN lower(affected_resource_type) LIKE '%awsecservice%' THEN 'Container Service' WHEN lower(affected_resource_type) LIKE '%virtualmachinescalesets%' THEN 'Compute Instance Group' WHEN lower(affected_resource_type) LIKE '%storageaccounts%' THEN 'Storage Accounts' WHEN lower(affected_resource_type) LIKE '%managedclusters%' THEN 'Kubernetes Cluster' WHEN lower(affected_resource_type) LIKE '%accounts%' THEN 'AI Services' WHEN lower(affected_resource_type) LIKE '%virtualmachines%' THEN 'Virtual Machine' WHEN lower(affected_resource_type) LIKE '%registries%' THEN 'Container Registry' WHEN lower(affected_resource_type) LIKE '%virtualnetworks%' THEN 'Virtual Network' WHEN lower(affected_resource_type) LIKE '%vaults%' THEN 'Vaults' WHEN lower(affected_resource_type) LIKE '%subscriptions%' THEN 'Cloud Account' WHEN lower(affected_resource_type) LIKE '%workspaces%' THEN 'Workspace' WHEN lower(affected_resource_type) LIKE '%applicationgateways%' THEN 'Load Balancer' WHEN lower(affected_resource_type) LIKE '%redis%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awssagemakernotebookinstance%' THEN 'Data Workload' WHEN lower(affected_resource_type) LIKE '%awsdynamodbtable%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awsec2clientvpnendpoint%' THEN 'Virtual Network' WHEN lower(affected_resource_type) LIKE '%awsssmdocument%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awsathenaworkgroup%' THEN 'Workspace' WHEN lower(affected_resource_type) LIKE '%awsiamgroup%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) = 'container' THEN 'Container' WHEN lower(affected_resource_type) = 'subscription' THEN 'Cloud Account' WHEN lower(affected_resource_type) = 'subnets' THEN 'Subnet' WHEN lower(affected_resource_type) LIKE '%acrcontainerimage%' THEN 'Container Image' WHEN lower(affected_resource_type) LIKE '%awscloudwatchalarm%' THEN 'Auditing and Monitoring' WHEN lower(affected_resource_type) LIKE '%awselbloadbalancer%' THEN 'Load Balancer' WHEN lower(affected_resource_type) LIKE '%awsrdsdbcluster%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awsrdsdbclustersnapshot%' THEN 'Database Snapshot' WHEN lower(affected_resource_type) LIKE '%awssagemakerendpointconfig%' THEN 'Data Workload' WHEN lower(affected_resource_type) LIKE '%flexibleservers%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%functionapp%' THEN 'Serverless' WHEN lower(affected_resource_type) LIKE '%onpremisemachines%' THEN 'Virtual Machine' WHEN affected_resource_type is null THEN null ELSE 'Other' END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_category", "colExpr": "'Control Gap'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "policy_definition_id", "colExpr": "properties.metadata.policyDefinitionId"}, {"colName": "finding_id_ref", "colExpr": "id"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__ms_azure_security_assessments__name", "entity": {"name": "Assessment"}}