{"primaryKey": "lower(Compliance.SecurityControlId)", "origin": "'AWS SH Findings'", "temporaryProperties": [{"colName": "temp_stand", "colExpr": "transform(Compliance.AssociatedStandards,x->REGEXP_EXTRACT(upper(x.StandardsId), '\\/(.*)$'))"}], "commonProperties": [{"colName": "type", "colExpr": "'Cloud Security'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "display_label", "colExpr": "coalesce(Title,primary_key)"}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "activity_status", "colExpr": "'Active'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "assessment_id", "colExpr": "lower(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "assessment_status", "colExpr": "'Enabled'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "assessment_title", "colExpr": "Title"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "associated_standards", "colExpr": "transform(temp_stand,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))"}, {"colName": "associated_framework", "colExpr": "Compliance.RelatedRequirements"}, {"colName": "exposure_category", "colExpr": "'Control Gap'"}, {"colName": "affected_resource_type", "colExpr": "cast(Resources.Type[0] as string)"}, {"colName": "scope_validation_steps", "colExpr": "ARRAY(CASE WHEN lower(affected_resource_type) LIKE '%mapreducecluster%' THEN 'MapReduce Cluster' WHEN lower(affected_resource_type) LIKE '%awsec2instance%' THEN 'Virtual Machine' WHEN lower(affected_resource_type) LIKE '%awsec2volume%' THEN 'Volume' WHEN lower(affected_resource_type) LIKE '%disks%' THEN 'Volume' WHEN lower(affected_resource_type) LIKE '%awslogsloggroup%' THEN 'Logging' WHEN lower(affected_resource_type) LIKE '%awsiampolicy%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) LIKE '%awsecsservice%' THEN 'Container Service' WHEN lower(affected_resource_type) LIKE '%awsrdsdbinstance%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awss3bucket%' THEN 'Bucket' WHEN lower(affected_resource_type) LIKE '%containergroups%' THEN 'Container Groups' WHEN lower(affected_resource_type) LIKE '%awssecretsmanagersecret%' THEN 'Secrets Management' WHEN lower(affected_resource_type) LIKE '%awselbv2loadbalancer%' THEN 'Load Balancer' WHEN lower(affected_resource_type) LIKE '%awslambdafunction%' THEN 'Serverless' WHEN lower(affected_resource_type) LIKE '%awsec2securitygroup%' THEN 'Firewall' WHEN lower(affected_resource_type) LIKE '%awsrdsdbsnapshot%' THEN 'Database Snapshot' WHEN lower(affected_resource_type) LIKE '%awsaccount%' THEN 'Cloud Account' WHEN lower(affected_resource_type) LIKE '%awskmskey%' THEN 'Key Management' WHEN lower(affected_resource_type) LIKE '%awsiamrole%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) LIKE '%awscertificatemanagercertificate%' THEN 'Certificate Management' WHEN lower(affected_resource_type) LIKE '%awsec2subnet%' THEN 'Subnet' WHEN lower(affected_resource_type) LIKE '%awsec2launchtemplate%' THEN 'Virtual Machine Configuration' WHEN lower(affected_resource_type) LIKE '%awsautoscalingautoscalinggroup%' or lower(affected_resource_type) LIKE '%awsec2ec2fleet%' THEN 'Compute Instance Group' WHEN lower(affected_resource_type) LIKE '%awselasticachereplicationgroup%' THEN 'Caching' WHEN lower(affected_resource_type) LIKE '%awsecstaskdefinition%' THEN 'Container Service' WHEN lower(affected_resource_type) LIKE '%awselasticachecachecluster%' THEN 'Caching' WHEN lower(affected_resource_type) LIKE '%awsecscluster%' THEN 'ECS Cluster' WHEN lower(affected_resource_type) LIKE '%awsefsaccesspoint%' THEN 'File System Service' WHEN lower(affected_resource_type) LIKE '%awsstepfunctionsstatemachine%' THEN 'Workflow Orchestration' WHEN lower(affected_resource_type) LIKE '%awsec2eip%' THEN 'Network Address' WHEN lower(affected_resource_type) LIKE '%awsiamuser%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) LIKE '%awsec2networkacl%' THEN 'Firewall' WHEN lower(affected_resource_type) LIKE '%awsekscluster%' THEN 'Kubernetes Cluster' WHEN lower(affected_resource_type) LIKE '%awscloudformationstack%' THEN 'Infrastructure as Code' WHEN lower(affected_resource_type) LIKE '%awscloudtrailtrail%' THEN 'Auditing and Monitoring' WHEN lower(affected_resource_type) LIKE '%awsefsfilesystem%' THEN 'File System Service' WHEN lower(affected_resource_type) LIKE '%awssnstopic%' THEN 'Messaging' WHEN lower(affected_resource_type) LIKE '%awsec2vpc%' THEN 'Virtual Network' WHEN lower(affected_resource_type) LIKE '%awsec2routetable%' THEN 'Route Table' WHEN lower(affected_resource_type) LIKE '%awsecrrepository%' THEN 'Container Registry' WHEN lower(affected_resource_type) LIKE '%awsec2internetgateway%' THEN 'Gateway' WHEN lower(affected_resource_type) LIKE '%awsecservice%' THEN 'Container Service' WHEN lower(affected_resource_type) LIKE '%virtualmachinescalesets%' THEN 'Compute Instance Group' WHEN lower(affected_resource_type) LIKE '%storageaccounts%' THEN 'Storage Accounts' WHEN lower(affected_resource_type) LIKE '%managedclusters%' THEN 'Kubernetes Cluster' WHEN lower(affected_resource_type) LIKE '%accounts%' THEN 'AI Services' WHEN lower(affected_resource_type) LIKE '%virtualmachines%' THEN 'Virtual Machine' WHEN lower(affected_resource_type) LIKE '%registries%' THEN 'Container Registry' WHEN lower(affected_resource_type) LIKE '%virtualnetworks%' THEN 'Virtual Network' WHEN lower(affected_resource_type) LIKE '%vaults%' THEN 'Vaults' WHEN lower(affected_resource_type) LIKE '%subscriptions%' THEN 'Cloud Account' WHEN lower(affected_resource_type) LIKE '%workspaces%' THEN 'Workspace' WHEN lower(affected_resource_type) LIKE '%applicationgateways%' THEN 'Load Balancer' WHEN lower(affected_resource_type) LIKE '%redis%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awssagemakernotebookinstance%' THEN 'Data Workload' WHEN lower(affected_resource_type) LIKE '%awsdynamodbtable%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awsec2clientvpnendpoint%' THEN 'Virtual Network' WHEN lower(affected_resource_type) LIKE '%awsssmdocument%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awsathenaworkgroup%' THEN 'Workspace' WHEN lower(affected_resource_type) LIKE '%awsiamgroup%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) = 'container' THEN 'Container' WHEN lower(affected_resource_type) = 'subscription' THEN 'Cloud Account' WHEN lower(affected_resource_type) = 'subnets' THEN 'Subnet' WHEN lower(affected_resource_type) LIKE '%acrcontainerimage%' THEN 'Container Image' WHEN lower(affected_resource_type) LIKE '%awscloudwatchalarm%' THEN 'Auditing and Monitoring' WHEN lower(affected_resource_type) LIKE '%awselbloadbalancer%' THEN 'Load Balancer' WHEN lower(affected_resource_type) LIKE '%awsrdsdbcluster%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awsrdsdbclustersnapshot%' THEN 'Database Snapshot' WHEN lower(affected_resource_type) LIKE '%awssagemakerendpointconfig%' THEN 'Data Workload' WHEN lower(affected_resource_type) LIKE '%flexibleservers%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%functionapp%' THEN 'Serverless' WHEN lower(affected_resource_type) LIKE '%onpremisemachines%' THEN 'Virtual Machine' WHEN affected_resource_type is null THEN null ELSE 'Other' END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "contributing_module", "colExpr": "CASE WHEN lower(affected_resource_type) IN ( 'mapreducecluster', 'awsec2instance', 'awsec2volume', 'disks', 'awsecsservice', 'awss3bucket', 'containergroups', 'awselbv2loadbalancer', 'awslambdafunction', 'awsec2securitygroup', 'awsaccount', 'awsec2subnet', 'awsautoscalingautoscalinggroup', 'awsec2ec2fleet', 'awsecscluster', 'awsekscluster', 'awsefsfilesystem', 'awsec2vpc', 'awsecrrepository', 'awsec2internetgateway', 'virtualmachinescalesets', 'storageaccounts', 'managedclusters', 'virtualmachines', 'registries', 'virtualnetworks', 'subscriptions', 'applicationgateways', 'awssagemakernotebookinstance', 'container', 'subscription', 'subnets', 'awselbloadbalancer', 'awssagemakerendpointconfig' ) THEN ARRAY('Reporting','Exposure') ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "SH Findings", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__aws_sh_findings__security_control_id", "entity": {"name": "Assessment"}}