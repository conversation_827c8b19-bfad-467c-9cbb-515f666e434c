{"primaryKey": "lower(SecurityControlId)", "origin": "'AWS List Security Control Definitions'", "commonProperties": [{"colName": "type", "colExpr": "'Cloud Security'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "display_label", "colExpr": "coalesce(Title,primary_key)"}, {"colName": "description", "colExpr": "Description"}], "entitySpecificProperties": [{"colName": "assessment_id", "colExpr": "lower(SecurityControlId)"}, {"colName": "assessment_title", "colExpr": "Title"}, {"colName": "assessment_status", "colExpr": "'Enabled'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "assessment_severity", "colExpr": "INITCAP(SeverityRating)"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_category", "colExpr": "'Control Gap'"}], "dataSource": {"name": "AWS", "feedName": "List Security Control Definitions", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_list_security_control_definitions"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__aws_list_security_control_definitions__security_control_id", "entity": {"name": "Assessment"}}