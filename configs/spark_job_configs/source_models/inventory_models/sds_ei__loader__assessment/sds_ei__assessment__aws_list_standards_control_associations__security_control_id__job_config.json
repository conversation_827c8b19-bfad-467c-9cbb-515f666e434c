{"primaryKey": "lower(SecurityControlId)", "origin": "'AWS List Standards Control Associations'", "temporaryProperties": [{"colName": "temp_stand", "colExpr": "collect_set(REGEXP_EXTRACT(upper(StandardsArn), '\\/(.*)$')) OVER (partition by SecurityControlId,DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}], "commonProperties": [{"colName": "type", "colExpr": "'Cloud Security'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "display_label", "colExpr": "coalesce(StandardsControlTitle,primary_key)"}, {"colName": "description", "colExpr": "StandardsControlDescription"}], "entitySpecificProperties": [{"colName": "assessment_id", "colExpr": "lower(SecurityControlId)"}, {"colName": "assessment_status", "colExpr": "initcap(AssociationStatus)"}, {"colName": "assessment_title", "colExpr": "StandardsControlTitle"}, {"colName": "associated_standards", "colExpr": "transform(temp_stand,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_category", "colExpr": "'Control Gap'"}, {"colName": "associated_framework", "colExpr": "RelatedRequirements"}], "dataSource": {"name": "AWS", "feedName": "List Standards Control Associations", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_list_standards_control_associations"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__aws_list_standards_control_associations__security_control_id", "entity": {"name": "Assessment"}}