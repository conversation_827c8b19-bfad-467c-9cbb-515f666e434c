{"primaryKey": "lower(SecurityControlId)", "origin": "'AWS List Security Control Definitions'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Compliance Control'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "Description"}], "entitySpecificProperties": [{"colName": "id", "colExpr": "upper(SecurityControlId)"}, {"colName": "title", "colExpr": "Title"}, {"colName": "status", "colExpr": "'Enabled'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "severity", "colExpr": "INITCAP(SeverityRating)"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "List Security Control Definitions", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_list_security_control_definitions"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__aws_list_security_control_definitions__security_control_id", "entity": {"name": "Security Control"}}