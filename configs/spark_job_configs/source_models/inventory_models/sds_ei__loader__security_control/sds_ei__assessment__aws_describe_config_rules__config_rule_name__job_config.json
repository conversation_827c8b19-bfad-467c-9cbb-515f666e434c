{"primaryKey": "lower(ConfigRuleName)", "origin": "'AWS Describe Config Rules'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Compliance Assessment'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "Description"}], "entitySpecificProperties": [{"colName": "id", "colExpr": "ConfigRuleName"}, {"colName": "title", "colExpr": "Description"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Describe Config Rules", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__describe_config_rules"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__aws_describe_config_rules__config_rule_name", "entity": {"name": "Assessment"}}