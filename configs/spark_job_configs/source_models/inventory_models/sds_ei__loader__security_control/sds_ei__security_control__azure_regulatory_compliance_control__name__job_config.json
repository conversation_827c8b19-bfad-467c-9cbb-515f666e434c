{"primaryKey": "lower(name)", "origin": "'MS Azure Security Resources'", "filterBy": "lower(properties.state) IN ('passed','failed','skipped') AND lower(type) IN ('microsoft.security/regulatorycompliancestandards/regulatorycompliancecontrols')", "temporaryProperties": [{"colName": "temp_statuses", "colExpr": "collect_set(properties.state) over (partition by lower(name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000)) ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_id", "colExpr": "id"}, {"colName": "temp_standard", "colExpr": "upper(REGEXP_EXTRACT(temp_id, 'regulatoryComplianceStandards/([^/]+)/'))"}], "commonProperties": [{"colName": "type", "colExpr": "'MS Azure Regulatory Compliance Control'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "upper(name)"}, {"colName": "title", "colExpr": "properties.description"}, {"colName": "status", "colExpr": "case when array_contains(temp_statuses, 'Passed') or array_contains(temp_statuses, 'Failed') then 'Enabled' else 'Disabled' end"}, {"colName": "standard_name", "colExpr": "collect_set(temp_standard) OVER (partition by lower(name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "associated_standards", "colExpr": "transform(standard_name,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__azure_regulatory_compliance_control__name", "entity": {"name": "Security Control"}}