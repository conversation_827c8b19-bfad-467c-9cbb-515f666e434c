{"primaryKey": "lower(REGEXP_EXTRACT(id,'regulatoryComplianceControls/([^/]+)/'))", "origin": "'MS Azure Security Resources'", "filterBy": "lower(properties.state) IN ('passed','failed','skipped') AND lower(type) IN ('microsoft.security/regulatorycompliancestandards/regulatorycompliancecontrols/regulatorycomplianceassessments')", "commonProperties": [{"colName": "type", "colExpr": "'MS Azure Regulatory Compliance Control'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "upper(REGEXP_EXTRACT(id,'regulatoryComplianceControls/([^/]+)/'))"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__azure_regulatory_compliance_assessments__control_id", "entity": {"name": "Security Control"}}