{"primaryKey": "lower(ProductFields.RelatedAWSResources__0__name)", "origin": "'AWS SH Findings'", "temporaryProperties": [{"colName": "temp_stand", "colExpr": "transform(Compliance.AssociatedStandards,x->REGEXP_EXTRACT(upper(x.StandardsId), '\\/(.*)$'))"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Compliance Assessment'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "ProductFields.RelatedAWSResources__0__name"}, {"colName": "associated_standards", "colExpr": "transform(temp_stand,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))"}, {"colName": "associated_controls", "colExpr": "collect_set(Compliance.SecurityControlId) OVER (partition by lower(ProductFields.RelatedAWSResources__0__name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.lookup_config_rule", "enrichmentColumns": ["title"]}, "joinCondition": "s.id = e.id", "sourcePreTransform": [{"colName": "id", "colExpr": "lower(ProductFields.RelatedAWSResources__0__name)"}]}], "dataSource": {"name": "AWS", "feedName": "SH Findings", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__aws_sh_findings__config_rule_name", "entity": {"name": "Assessment"}}