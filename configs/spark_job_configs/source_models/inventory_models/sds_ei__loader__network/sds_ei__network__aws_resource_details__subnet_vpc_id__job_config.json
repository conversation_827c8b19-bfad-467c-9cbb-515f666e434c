{"primaryKey": "configuration.vpcId", "filterBy": " resourceType IN ('AWS::EC2::Subnet')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "'Virtual Network'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 VPC'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "native_type", "colExpr": "'AWS EC2 VPC'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_resource_details__subnet_vpc_id", "entity": {"name": "Network"}}