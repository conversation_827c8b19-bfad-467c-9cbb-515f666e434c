{"primaryKey": "lower(explode_id)", "filterBy": "LOWER(type) IN ('microsoft.network/virtualnetworks')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "subnet_info", "colExpr": "explode_outer(properties.subnets)"}, {"colName": "explode_id", "colExpr": "subnet_info.id"}, {"colName": "explode_resource_name", "colExpr": "subnet_info.name"}, {"colName": "temp_cidr_block", "colExpr": "subnet_info.properties.addressPrefix"}], "commonProperties": [{"colName": "type", "colExpr": "'Subnet'"}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(explode_resource_name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'MS Azure'"}, {"colName": "environment", "colExpr": "lower(azure_tags.Environment)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "subnet_info.properties.provisioningState"}, {"colName": "region", "colExpr": "location"}, {"colName": "project", "colExpr": "azure_tags.Project", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cidr_block", "colExpr": "subnet_info.properties.addressPrefix"}, {"colName": "native_type", "colExpr": "'Azure Subnet'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_tags", "colExpr": "tags"}], "sourceSpecificProperties": [], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network__ms_azure_resource_details__vpc_subnet_id", "entity": {"name": "Network"}}