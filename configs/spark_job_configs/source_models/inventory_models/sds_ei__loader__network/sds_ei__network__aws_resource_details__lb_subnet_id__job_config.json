{"primaryKey": "lower(subnet_id)", "filterBy": "resourceType IN ('AWS::ElasticLoadBalancingV2::LoadBalancer')", "origin": "'AWS Resource Details'", "temporaryProperties": [{"colName": "subnet_id", "colExpr": "explode_outer(configuration.availabilityZones.subnetId)"}], "commonProperties": [{"colName": "type", "colExpr": "'Subnet'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 Subnet'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_resource_details__lb_subnet_id", "entity": {"name": "Network"}}