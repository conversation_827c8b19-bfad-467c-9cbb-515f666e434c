{"primaryKey": "lower(subnet_id)", "filterBy": "lower(type) IN ('microsoft.network/networkinterfaces')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "subnet_config", "colExpr": "explode_outer(properties.ipConfigurations)"}, {"colName": "subnet_id", "colExpr": "subnet_config.properties.subnet.id"}], "commonProperties": [{"colName": "type", "colExpr": "'Subnet'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Subnet'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network__ms_azure_resource_details__nic_subnet_id", "entity": {"name": "network"}}