{"primaryKey": "lower(arn)", "filterBy": "arn IS NOT NULL AND arn NOT IN ('', ' ') AND resourceType IN ('AWS::EC2::Subnet', 'AWS::EC2::VPC')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN resourceType IN ('AWS::EC2::VPC') THEN 'Virtual Network' WHEN resourceType IN ('AWS::EC2::Subnet') THEN 'Subnet' END"}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date,aws_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_resource_configuration_change_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(status) IN ('pending','available') THEN 'Active' WHEN LOWER(status) IN ('unavailable','failed','failed-insufficient-capacity') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mac_address", "colExpr": "configuration.macAddress"}, {"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Environment' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Project' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "res_tag_name", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'name' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountId"}, {"colName": "region", "colExpr": "awsRegion"}, {"colName": "zone_availability", "colExpr": "CASE WHEN availabilityZone = 'Multiple Availability Zones' THEN 'Multiple'  WHEN availabilityZone= 'Not Applicable'  THEN 'Not Applicable' WHEN availabilityZone= 'Regional'  THEN 'Regional' WHEN availabilityZone IS NULL THEN NULL ELSE 'Single' END"}, {"colName": "cidr_block", "colExpr": "CASE WHEN resourceType IN ('AWS::EC2::VPC', 'AWS::EC2::Subnet') THEN configuration.cidrBlock ELSE 'Not Applicable' END"}, {"colName": "available_address_count", "colExpr": "configuration.availableIpAddressCount"}, {"colName": "default_for_availability_zone", "colExpr": "configuration.defaultForAz"}, {"colName": "map_public_ip_on_launch", "colExpr": "configuration.mapPublicIpOnLaunch"}, {"colName": "map_customer_owned_ip_on_launch", "colExpr": "configuration.mapCustomerOwnedIpOnLaunch"}, {"colName": "status", "colExpr": "configuration.state.value"}, {"colName": "vpc_id", "colExpr": "configuration.vpcId"}, {"colName": "subnet_id", "colExpr": "configuration.subnetId"}, {"colName": "assign_ipv6_on_creation", "colExpr": "configuration.assignIpv6AddressOnCreation"}, {"colName": "ipv6_native", "colExpr": "configuration.ipv6Native"}, {"colName": "block_public_access_state", "colExpr": "configuration.BlockPublicAccessStates.InternetGatewayBlockMode"}, {"colName": "native_type", "colExpr": "CASE WHEN resourceType IN ('AWS::EC2::VPC') THEN 'AWS EC2 VPC' WHEN resourceType IN ('AWS::EC2::Subnet') THEN 'AWS EC2 Subnet' END"}, {"colName": "aws_tags", "colExpr": "tags"}, {"colName": "aws_resource_configuration_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime)))"}, {"colName": "aws_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(resourceCreationTime)))"}], "sourceSpecificProperties": [], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_resource_details__arn", "entity": {"name": "Network"}}