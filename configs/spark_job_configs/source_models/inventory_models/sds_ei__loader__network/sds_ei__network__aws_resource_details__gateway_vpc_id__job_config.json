{"primaryKey": "lower(vpc_id)", "filterBy": "resourceType IN ('AWS::EC2::InternetGateway')", "origin": "'AWS Resource Details'", "temporaryProperties": [{"colName": "vpc_id", "colExpr": "explode_outer(configuration.attachments.vpcId)"}], "commonProperties": [{"colName": "type", "colExpr": "'Virtual Network'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_resource_details__gateway_vpc_id", "entity": {"name": "Network"}}