{"primaryKey": "lower(temp_resource_id)", "filterBy": "lower(temp_resource_type) IN ('awsec2vpc','awsec2subnet')", "origin": "'AWS SH Findings'", "temporaryProperties": [{"colName": "temp_resource", "colExpr": "explode_outer(Resources)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_resource_id", "colExpr": "temp_resource.Id"}, {"colName": "temp_resource_type", "colExpr": "temp_resource.Type"}, {"colName": "temp_resource_details", "colExpr": "temp_resource.Details"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN temp_resource_type IN ('awsec2vpc') THEN 'Virtual Network' WHEN temp_resource_type IN ('awsec2subnet') THEN 'Subnet' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN temp_resource_type IN ('awsec2vpc') THEN 'AWS EC2 VPC' WHEN temp_resource_type IN ('awsec2subnet') THEN 'AWS EC2 Subnet' END"}, {"colName": "environment", "colExpr": "Resources.Tags.Environment[0]"}, {"colName": "account_id", "colExpr": "CAST(AwsAccountId as string)"}], "sourceSpecificProperties": [{"colName": "aws_region", "colExpr": "Region"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.Region = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network__aws_sh_findings__resource_id", "entity": {"name": "Network"}}