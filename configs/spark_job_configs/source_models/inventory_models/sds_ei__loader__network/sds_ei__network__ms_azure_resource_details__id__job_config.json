{"primaryKey": "lower(id)", "filterBy": "LOWER(type) IN ('microsoft.network/virtualnetworks')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [], "commonProperties": [{"colName": "type", "colExpr": "'Virtual Network'"}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "tags.BusinessUnit"}, {"colName": "department", "colExpr": "tags.Department"}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "lower(tags.Environment)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "properties.provisioningState"}, {"colName": "region", "colExpr": "location"}, {"colName": "project", "colExpr": "tags.Project"}, {"colName": "mac_address", "colExpr": "properties.macAddress"}, {"colName": "native_type", "colExpr": "'Azure Virtual Network'"}, {"colName": "ddos_protection_status", "colExpr": "CAST(properties.enableDdosProtection AS STRING)"}], "sourceSpecificProperties": [], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network__ms_azure_resource_details__id", "entity": {"name": "Network"}}