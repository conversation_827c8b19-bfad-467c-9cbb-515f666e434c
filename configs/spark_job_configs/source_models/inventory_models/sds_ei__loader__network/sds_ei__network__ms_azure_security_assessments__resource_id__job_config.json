{"primaryKey": "lower(properties.resourceDetails.Id)", "filterBy": "lower(type) IN ('microsoft.security/assessments') and ( case when lower(regexp_extract(properties.resourceDetails.Id,'/providers/((?:[^/]++/){2}(?:[^/]++/)subnets|(?:[^/]++/){1}(?:[^/]++))'))  like '%subnets%' then 'Subnets' when lower(regexp_extract(properties.resourceDetails.Id,'/providers/((?:[^/]++/){2}(?:[^/]++/)subnets|(?:[^/]++/){1}(?:[^/]++))'))  in ('microsoft.network/virtualnetworks') then 'Virtual Network' else null end) is not null", "origin": "'MS Azure Security Resources'", "commonProperties": [{"colName": "type", "colExpr": "case when lower(regexp_extract(properties.resourceDetails.Id,'/providers/((?:[^/]++/){2}(?:[^/]++/)subnets|(?:[^/]++/){1}(?:[^/]++))'))  like '%subnets%' then 'Subnets' when lower(regexp_extract(properties.resourceDetails.Id,'/providers/((?:[^/]++/){2}(?:[^/]++/)subnets|(?:[^/]++/){1}(?:[^/]++))'))  in ('microsoft.network/virtualnetworks') then 'Virtual Network' else null end"}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(regexp_extract(resource_id,'\\/([^\\/]+)$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "case when lower(regexp_extract(properties.resourceDetails.Id,'/providers/((?:[^/]++/){2}(?:[^/]++/)subnets|(?:[^/]++/){1}(?:[^/]++))'))  like '%subnets%' then 'Azure Subnet' when lower(regexp_extract(properties.resourceDetails.Id,'/providers/((?:[^/]++/){2}(?:[^/]++/)subnets|(?:[^/]++/){1}(?:[^/]++))'))  in ('microsoft.network/virtualnetworks') then 'Azure Virtual Network' else null end"}, {"colName": "account_id", "colExpr": "regexp_extract(id, '/subscriptions/([^/]+)/', 1)"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network__ms_azure_security_assessments__resource_id", "entity": {"name": "Network"}}