{"primaryKey": "lower(Id)", "filterBy": "Compliance.SecurityControlId IS NOT NULL", "origin": "'AWS SH Findings'", "temporaryProperties": [{"colName": "temp_stand", "colExpr": "transform(Compliance.AssociatedStandards,x->REGEXP_EXTRACT(upper(x.StandardsId), '\\/(.*)$'))"}, {"colName": "temp_affected_resource_id", "colExpr": "cast(Resources.Id as array<string>)[0]"}], "commonProperties": [{"colName": "type", "colExpr": "'Cloud Security'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "display_label", "colExpr": "finding_title", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "case when firstobservedat is null then UNIX_MILLIS(TIMESTAMP(to_timestamp(createdat))) else UNIX_MILLIS(TIMESTAMP(to_timestamp(firstobservedat))) end"}, {"colName": "last_active_date", "colExpr": "case when lastobservedat is null then UNIX_MILLIS(TIMESTAMP(to_timestamp(event_timestamp_ts))) else UNIX_MILLIS(TIMESTAMP(to_timestamp(lastobservedat))) end"}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_description", "colExpr": "Description"}], "entitySpecificProperties": [{"colName": "finding_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_title", "colExpr": "title"}, {"colName": "assessment_title", "colExpr": "finding_title", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "assessment_id", "colExpr": "UPPER(Compliance.SecurityControlId)"}, {"colName": "finding_sub_type", "colExpr": "REGEXP_EXTRACT(CONCAT_WS(', ', types), '\\/(.*)$')"}, {"colName": "status", "colExpr": "case when lower(workflow_status) in ('suppressed','resolved') then 'Closed' when lower(workflow_status) not in ('suppressed','resolved') and activity_status='Inactive' then 'Closed' Else 'Open' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vendor_severity_normalised", "colExpr": "INITCAP(Severity.Label)"}, {"colName": "finding_evidence", "colExpr": "finding_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "affected_asset_origin", "colExpr": "'AWS'"}, {"colName": "reopened_date", "colExpr": "CASE WHEN status = 'Open' and last_updated_attrs.status.prev.value='Closed' THEN last_updated_attrs.status.last_changed.updated_at ELSE reopened_date END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "affected_asset_display_label", "colExpr": "case when length(regexp_extract(temp_affected_resource_id,'[\\/:]+([^\\/:]+)$'))<2 then regexp_extract(temp_affected_resource_id,'[\\/:]+([^\\/]+)$') else regexp_extract(temp_affected_resource_id,'[\\/:]+([^\\/:]+)$') end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_resolved_date", "colExpr": "case when status = 'Closed' then IFNULL(last_updated_attrs.status.last_changed.updated_at,last_active_date) else NULL end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "associated_standard", "colExpr": "transform(temp_stand,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))"}, {"colName": "affected_resource_type", "colExpr": "cast(Resources.Type[0] as string)"}, {"colName": "scope_validation_steps", "colExpr": "ARRAY(CASE WHEN lower(affected_resource_type) LIKE '%mapreducecluster%' THEN 'MapReduce Cluster' WHEN lower(affected_resource_type) LIKE '%awsec2instance%' THEN 'Virtual Machine' WHEN lower(affected_resource_type) LIKE '%awsec2volume%' THEN 'Volume' WHEN lower(affected_resource_type) LIKE '%disks%' THEN 'Volume' WHEN lower(affected_resource_type) LIKE '%awslogsloggroup%' THEN 'Logging' WHEN lower(affected_resource_type) LIKE '%awsiampolicy%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) LIKE '%awsecsservice%' THEN 'Container Service' WHEN lower(affected_resource_type) LIKE '%awsrdsdbinstance%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awss3bucket%' THEN 'Bucket' WHEN lower(affected_resource_type) LIKE '%containergroups%' THEN 'Container Groups' WHEN lower(affected_resource_type) LIKE '%awssecretsmanagersecret%' THEN 'Secrets Management' WHEN lower(affected_resource_type) LIKE '%awslambdafunction%' THEN 'Serverless' WHEN lower(affected_resource_type) LIKE '%awsec2securitygroup%' THEN 'Firewall' WHEN lower(affected_resource_type) LIKE '%awsrdsdbsnapshot%' THEN 'Database Snapshot' WHEN lower(affected_resource_type) LIKE '%awsaccount%' THEN 'Cloud Account' WHEN lower(affected_resource_type) LIKE '%awskmskey%' THEN 'Key Management' WHEN lower(affected_resource_type) LIKE '%awsiamrole%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) LIKE '%awscertificatemanagercertificate%' THEN 'Certificate Management' WHEN lower(affected_resource_type) LIKE '%awsec2subnet%' THEN 'Subnet' WHEN lower(affected_resource_type) LIKE '%awsec2launchtemplate%' THEN 'Virtual Machine Configuration' WHEN lower(affected_resource_type) LIKE '%awsautoscalingautoscalinggroup%' or lower(affected_resource_type) LIKE '%awsec2ec2fleet%' THEN 'Compute Instance Group' WHEN lower(affected_resource_type) LIKE '%awselasticachereplicationgroup%' THEN 'Caching' WHEN lower(affected_resource_type) LIKE '%awsecstaskdefinition%' THEN 'Container Service' WHEN lower(affected_resource_type) LIKE '%awselasticachecachecluster%' THEN 'Caching' WHEN lower(affected_resource_type) LIKE '%awsecscluster%' THEN 'ECS Cluster' WHEN lower(affected_resource_type) LIKE '%awsefsaccesspoint%' THEN 'File System Service' WHEN lower(affected_resource_type) LIKE '%awsstepfunctionsstatemachine%' THEN 'Workflow Orchestration' WHEN lower(affected_resource_type) LIKE '%awsec2eip%' THEN 'Network Address' WHEN lower(affected_resource_type) LIKE '%awsiamuser%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) LIKE '%awsec2networkacl%' THEN 'Firewall' WHEN lower(affected_resource_type) LIKE '%awsekscluster%' THEN 'Kubernetes Cluster' WHEN lower(affected_resource_type) LIKE '%awscloudformationstack%' THEN 'Infrastructure as Code' WHEN lower(affected_resource_type) LIKE '%awscloudtrailtrail%' THEN 'Auditing and Monitoring' WHEN lower(affected_resource_type) LIKE '%awsefsfilesystem%' THEN 'File System Service' WHEN lower(affected_resource_type) LIKE '%awssnstopic%' THEN 'Messaging' WHEN lower(affected_resource_type) LIKE '%awsec2vpc%' THEN 'Virtual Network' WHEN lower(affected_resource_type) LIKE '%awsec2routetable%' THEN 'Route Table' WHEN lower(affected_resource_type) LIKE '%awsecrrepository%' THEN 'Container Registry' WHEN lower(affected_resource_type) LIKE '%awsec2internetgateway%' THEN 'Gateway' WHEN lower(affected_resource_type) LIKE '%awsecservice%' THEN 'Container Service' WHEN lower(affected_resource_type) LIKE '%virtualmachinescalesets%' THEN 'Compute Instance Group' WHEN lower(affected_resource_type) LIKE '%storageaccounts%' THEN 'Storage Accounts' WHEN lower(affected_resource_type) LIKE '%managedclusters%' THEN 'Kubernetes Cluster' WHEN lower(affected_resource_type) LIKE '%accounts%' THEN 'AI Services' WHEN lower(affected_resource_type) LIKE '%virtualmachines%' THEN 'Virtual Machine' WHEN lower(affected_resource_type) LIKE '%registries%' THEN 'Container Registry' WHEN lower(affected_resource_type) LIKE '%virtualnetworks%' THEN 'Virtual Network' WHEN lower(affected_resource_type) LIKE '%vaults%' THEN 'Vaults' WHEN lower(affected_resource_type) LIKE '%subscriptions%' THEN 'Cloud Account' WHEN lower(affected_resource_type) LIKE '%workspaces%' THEN 'Workspace' WHEN lower(affected_resource_type) LIKE '%applicationgateways%' THEN 'Load Balancer' WHEN lower(affected_resource_type) LIKE '%redis%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awssagemakernotebookinstance%' THEN 'Data Workload' WHEN lower(affected_resource_type) LIKE '%awsdynamodbtable%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awsec2clientvpnendpoint%' THEN 'Virtual Network' WHEN lower(affected_resource_type) LIKE '%awsssmdocument%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awsathenaworkgroup%' THEN 'Workspace' WHEN lower(affected_resource_type) LIKE '%awsiamgroup%' THEN 'Identity and Access Management (IAM)' WHEN lower(affected_resource_type) = 'container' THEN 'Container' WHEN lower(affected_resource_type) = 'subscription' THEN 'Cloud Account' WHEN lower(affected_resource_type) = 'subnets' THEN 'Subnet' WHEN lower(affected_resource_type) LIKE '%acrcontainerimage%' THEN 'Container Image' WHEN lower(affected_resource_type) LIKE '%awscloudwatchalarm%' THEN 'Auditing and Monitoring' WHEN lower(affected_resource_type) LIKE '%awselbloadbalancer%' THEN 'Load Balancer' WHEN lower(affected_resource_type) LIKE '%awsrdsdbcluster%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%awsrdsdbclustersnapshot%' THEN 'Database Snapshot' WHEN lower(affected_resource_type) LIKE '%awssagemakerendpointconfig%' THEN 'Data Workload' WHEN lower(affected_resource_type) LIKE '%flexibleservers%' THEN 'Database' WHEN lower(affected_resource_type) LIKE '%functionapp%' THEN 'Serverless' WHEN lower(affected_resource_type) LIKE '%onpremisemachines%' THEN 'Virtual Machine' WHEN affected_resource_type is null THEN null ELSE 'Other' END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_category", "colExpr": "'Control Gap'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "effectiveness_indicator", "colExpr": "CASE WHEN status = 'Open' THEN 0 ELSE 1 END ", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "workflow_status", "colExpr": "lower(Workflow.Status)"}], "dataSource": {"name": "AWS", "feedName": "SH Findings", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__finding__aws_sh_finding__id", "entity": {"name": "Finding"}}