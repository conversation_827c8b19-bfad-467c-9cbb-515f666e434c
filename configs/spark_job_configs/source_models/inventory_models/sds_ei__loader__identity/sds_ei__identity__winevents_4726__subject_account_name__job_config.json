{"origin": "'WinEvents 4726'", "filterBy": "event_code='4726'", "dataSource": {"name": "WinEvents 4726", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__windows_security_logs", "feedName": "WinEvents 4726"}, "primaryKey": "lower(temp_subject_account_name_4726)", "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4726__subject_account_name", "temporaryProperties": [{"colExpr": "regexp_extract(subject_account_domain,'^([^.]++)')", "colName": "domain_temp"}, {"colExpr": "RTRIM('$',subject_account_name)", "colName": "sam_account_name_temp"}, {"colExpr": "CASE WHEN subject_account_domain IS NULL OR subject_account_name IS NULL OR subject_account_domain = '-' OR subject_account_name = '-' THEN NULL ELSE CONCAT_WS('\\\\',domain_temp,sam_account_name_temp) END", "colName": "temp_subject_account_name_4726"}], "commonProperties": [{"colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(epochTimeInSec)))", "colName": "last_active_date", "fieldsSpec": {"aggregateFunction": "max"}}, {"colExpr": "CASE WHEN subject_account_name LIKE '%$' OR lower(subject_account_name) LIKE 'svc_%' or lower(subject_account_name) like '%admin%' OR lower(subject_account_name) like '%defaultuser0%' OR lower(subject_account_name) like '%guest%' OR lower(subject_account_domain) like '%workgroup%' then 'Non-Human' else 'Human' end", "colName": "type"}], "entitySpecificProperties": [{"colName": "identity_provider", "colExpr": "'Active Directory'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_format", "colExpr": "'SAM Account Name'", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "RTRIM('$',subject_account_name)", "colName": "identity_display_name"}, {"colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "colName": "identity_primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "'Active'", "colName": "operational_status", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colExpr": "cast(null as string)", "colName": "ad_sam_account_name", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "cast(null as string)", "colName": "ad_domain", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "CASE WHEN subject_account_name LIKE '%$' THEN temp_subject_account_name_4624 ELSE NULL END", "colName": "ad_sam_account_name_with_domain", "fieldsSpec": {"isInventoryDerived": true}}]}