{"primaryKey": "aadDeviceId", "origin": "'MS Defender Device List'", "temporaryProperties": [{"colName": "temp_first_seen", "colExpr": "CONCAT(SUBSTRING(firstSeen,0,19),'Z')"}, {"colName": "temp_last_seen", "colExpr": "CONCAT(SUBSTRING(lastSeen,0,19),'Z')"}], "commonProperties": [{"colName": "type", "colExpr": "'Non-Human'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(temp_last_seen)))", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "first_seen_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(temp_first_seen)))", "fieldsSpec": {"aggregateFunction": "min"}}], "entitySpecificProperties": [{"colName": "identity_display_name", "colExpr": "UPPER(regexp_extract(computerDnsName,'^((?>(?:[0-9]++[.]){3}[0-9]++|[^.]++))([.][^\\\\r\\\\n]*+)?$'))"}, {"colName": "identity_provider", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_format", "colExpr": "'AAD Device ID'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device List", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_list"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_defender_device_list__aad_device_id"}