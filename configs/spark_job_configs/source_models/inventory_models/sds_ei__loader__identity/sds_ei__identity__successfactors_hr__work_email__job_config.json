{"origin": "'SuccessFactors'", "dataSource": {"name": "SuccessFactors", "srdm": "<%SRDM_SCHEMA_NAME%>.sap__successfactors", "feedName": "HR"}, "primaryKey": "LOWER(Email)", "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__successfactors_hr__work_email", "temporaryProperties": [{"colName": "temp__mail_domain_extracted", "colExpr": "split(<PERSON><PERSON>,'@')[1]"}], "commonProperties": [{"colName": "type", "colExpr": "'Human'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(sf_recruit_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_format", "colExpr": "'Email ID'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_provider", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_display_name", "colExpr": "INITCAP(concat_ws(' ',First__Name,Middle__Name,Last__Name))"}, {"colName": "email_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "employee_id", "colExpr": "cast(User__ID as string)"}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "sf_recruit_date", "colExpr": "to_unix_timestamp(Recruit__date,'d[/][-]M[/][-]y[ HH:mm:ss]')*1000"}]}