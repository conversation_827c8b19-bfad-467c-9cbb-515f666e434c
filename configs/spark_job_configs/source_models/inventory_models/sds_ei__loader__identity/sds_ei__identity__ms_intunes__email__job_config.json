{"origin": "'MS Intune'", "dataSource": {"name": "MS Intune", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__intune", "feedName": "MDM"}, "primaryKey": "emailAddress", "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_intunes__email", "temporaryProperties": [{"colName": "temp_upn_domain_extracted", "colExpr": "split(<PERSON><PERSON><PERSON><PERSON>,'@')[1]"}], "commonProperties": [{"colExpr": "login_last_date", "colName": "last_active_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "first_found_date", "colName": "first_seen_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "case when LOWER(identity_display_name) REGEXP 'monitor|computer|bot\\\\b|\\\\btest\\\\b|_|robot\\\\b' then 'Non-Human' else 'Human' end", "colName": "type", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colExpr": "'Azure AD'", "colName": "identity_provider", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "'Email'", "colName": "identity_format", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "INITCAP(userDisplayName)", "colName": "identity_display_name"}, {"colExpr": "userId", "colName": "aad_id"}, {"colExpr": "userPrincipalName", "colName": "user_principal_name"}, {"colExpr": "lower(primary_key)", "colName": "email_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "colName": "identity_primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "CASE WHEN lastLogOnDateTime is not null THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastLogOnDateTime)))ELSE NULL END ", "colName": "login_last_date", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}]}