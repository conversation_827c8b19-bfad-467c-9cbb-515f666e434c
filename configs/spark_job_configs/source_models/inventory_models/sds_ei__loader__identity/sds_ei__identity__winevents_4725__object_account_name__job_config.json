{"origin": "'WinEvents 4725'", "filterBy": "event_code='4725'", "dataSource": {"name": "WinEvents 4725", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__windows_security_logs", "feedName": "WinEvents 4725"}, "primaryKey": "lower(temp_object_account_name_4725)", "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4725__object_account_name", "temporaryProperties": [{"colExpr": "regexp_extract(object_account_domain,'^([^.]++)')", "colName": "domain_temp"}, {"colExpr": "RTRIM('$',object_account_name)", "colName": "sam_account_name_temp"}, {"colExpr": "CASE WHEN object_account_domain IS NULL OR object_account_name IS NULL OR object_account_domain = '-' OR object_account_name = '-' THEN NULL ELSE CONCAT_WS('\\\\',domain_temp,sam_account_name_temp) END", "colName": "temp_object_account_name_4725"}], "commonProperties": [{"colExpr": "CASE WHEN object_account_name LIKE '%$' OR lower(object_account_name) LIKE 'svc_%' or lower(object_account_name) like '%admin%' OR lower(object_account_name) like '%defaultuser0%' OR lower(object_account_name) like '%guest%' OR lower(object_account_domain) like '%workgroup%' then 'Non-Human' else 'Human' end", "colName": "type"}], "entitySpecificProperties": [{"colName": "identity_provider", "colExpr": "'Active Directory'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_format", "colExpr": "'SAM Account Name'", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "RTRIM('$',object_account_name)", "colName": "identity_display_name"}, {"colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "colName": "identity_primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "'Disabled'", "colName": "operational_status", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colExpr": "CASE WHEN object_account_name LIKE '%$' THEN RTRIM('$',object_account_name) ELSE NULL END", "colName": "ad_sam_account_name", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "case when lower(domain_temp)='corp' then domain_temp else null end", "colName": "ad_domain", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "CASE WHEN object_account_name LIKE '%$' THEN temp_object_account_name_4624 ELSE NULL END", "colName": "ad_sam_account_name_with_domain", "fieldsSpec": {"isInventoryDerived": true}}]}