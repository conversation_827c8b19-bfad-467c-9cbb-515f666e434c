{"origin": "'WinEvents 4624'", "filterBy": "event_code='4624'", "dataSource": {"name": "WinEvents 4624", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__windows_security_logs", "feedName": "WinEvents 4624"}, "primaryKey": "lower(temp_subject_account_name_4624)", "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4624__subject_account_name", "temporaryProperties": [{"colExpr": "regexp_extract(subject_account_domain,'^([^.]++)')", "colName": "domain_temp"}, {"colExpr": "RTRIM('$',subject_account_name)", "colName": "sam_account_name_temp"}, {"colExpr": "CASE WHEN subject_account_domain IS NULL OR subject_account_name IS NULL OR subject_account_domain = '-' OR subject_account_name = '-' OR lower(subject_account_name) = 'cliusr' OR lower(subject_account_domain) IN ('nt authority', 'nt service') THEN NULL ELSE CONCAT_WS('\\\\', domain_temp, sam_account_name_temp) END\n", "colName": "temp_subject_account_name_4624"}], "commonProperties": [{"colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(epochTimeInSec)))", "colName": "last_active_date", "fieldsSpec": {"aggregateFunction": "max"}}, {"colExpr": "CASE WHEN subject_account_name LIKE '%$' OR lower(subject_account_name) LIKE 'svc_%' or lower(subject_account_name) like '%admin%' OR lower(subject_account_name) like '%defaultuser0%' OR lower(subject_account_name) like '%guest%' OR lower(subject_account_domain) like '%workgroup%' then 'Non-Human' else 'Human' end", "colName": "type"}], "entitySpecificProperties": [{"colExpr": "case when lower(domain_temp)='corp' then 'Active Directory' else 'Other' end", "colName": "identity_provider"}, {"colExpr": "cast(null as string)", "colName": "identity_format", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "RTRIM('$',subject_account_name)", "colName": "identity_display_name"}, {"colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "colName": "identity_primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "last_active_date", "colName": "login_last_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "'Active'", "colName": "operational_status", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colExpr": "CASE WHEN subject_account_name LIKE '%$' THEN RTRIM('$',subject_account_name) ELSE NULL END", "colName": "ad_sam_account_name", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "case when lower(domain_temp)='corp' then domain_temp else null end", "colName": "ad_domain", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "CASE WHEN subject_account_name LIKE '%$' THEN temp_subject_account_name_4624 ELSE NULL END", "colName": "ad_sam_account_name_with_domain", "fieldsSpec": {"isInventoryDerived": true}}]}