{"primaryKey": "temp_primary_key", "origin": "'Saviynt IGA Accounts'", "temporaryProperties": [{"colName": "temp_primary_key", "colExpr": "CASE WHEN LOWER(name) LIKE '%deleted%' THEN  regexp_extract(lower(name),'^([^-]++)') ELSE lower(name) END"}, {"colName": "temp_operational_status", "colExpr": "collect_set(lower(status)) OVER (partition by temp_primary_key,event_timestamp_date  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}], "commonProperties": [{"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "ownership", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "company_email_domains", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "CASE WHEN lastlogondate!='' THEN UNIX_TIMESTAMP(lastlogondate,'MM/dd/yyyy')*1000 ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "last_password_change_date", "colExpr": "CASE WHEN lastpasswordchange!='' THEN UNIX_TIMESTAMP(lastpasswordchange,'MM/dd/yyyy')*1000 ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "email_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "employee_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_display_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "iga_username", "colExpr": "username"}, {"colName": "iga_status", "colExpr": "CASE WHEN temp_operational_status IS NOT NULL AND ( ARRAY_CONTAINS(temp_operational_status, '1') OR ARRAY_CONTAINS(temp_operational_status, 'manually provisioned'))THEN 'Active' ELSE 'Inactive' END"}], "dataSource": {"name": "Saviynt IGA", "feedName": "Accounts", "srdm": "<%SRDM_SCHEMA_NAME%>.saviynt__account"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__saviynt_iga_accounts__name"}