{"primaryKey": "lower(secondaryEmail)", "origin": "'Saviynt IGA Users'", "temporaryProperties": [{"colExpr": "CASE WHEN createdate!='' THEN UNIX_TIMESTAMP(createdate,'yyyy-MM-dd HH:mm:ss')*1000 ELSE NULL END", "colName": "temp_created_date"}, {"colExpr": "CASE WHEN startdate!='' THEN UNIX_TIMESTAMP(startdate,'yyyy-MM-dd HH:mm:ss')*1000 ELSE NULL END", "colName": "temp_start_date"}], "commonProperties": [{"colName": "type", "colExpr": "CASE when lower(user_name) RLIKE '.*katest.*|.*charlottest.*|.*annettest.*|.*suzettest.*|.*bernadettest.*|.*tbh-.*|.*repl.*|.*test.*' THEN 'Non-Human' when lower(first_name)  RLIKE '.*test.*|.*connection.*|.*saviyntsupport.*|.*human.*|.*tst.*' THEN 'Non-Human' when lower(last_name) RLIKE '.*test.*' THEN 'Non-Human' when lower(email_id)='<EMAIL>' then 'Non-Human' when lower(email_id)='<EMAIL>' then 'Non-Human' ELSE 'Human' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "country"}, {"colName": "location_city", "colExpr": "city"}, {"colName": "first_seen_date", "colExpr": "least(temp_created_date,temp_start_date)"}, {"colName": "activity_status", "colExpr": "case when statuskey=0 then 'Inactive' when statuskey=1 then 'Active' end"}], "entitySpecificProperties": [{"colName": "identity_format", "colExpr": "'Email'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "company_email_domains", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "employee_id", "colExpr": "collect_set(employeeid) over (partition by primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "email_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_display_name", "colExpr": "concat_ws(\" \", firstname,middlename , lastname)"}, {"colName": "first_name", "colExpr": "firstname"}, {"colName": "last_name", "colExpr": "lastname"}, {"colName": "user_name", "colExpr": "username"}], "sourceSpecificProperties": [{"colName": "iga_username", "colExpr": "collect_set(username) over (partition by primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "iga_created_date", "colExpr": "temp_created_date", "fieldsSpec": {"aggregateFunction": "min"}}], "dataSource": {"name": "Saviynt IGA", "feedName": "Users", "srdm": "<%SRDM_SCHEMA_NAME%>.saviynt__user"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__saviynt_iga_users__secondary_email"}