{"primaryKey": "lower(Username)", "filterBy": "EventName='ConsoleLogin'", "origin": "'AWS Cloudtrail ConsoleLogin'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "first_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "login_last_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_provider", "colExpr": "case when aws_cloudtrail_user_identity_type='AssumedRole' then 'Azure AD' when aws_cloudtrail_user_identity_type='IAM User' then 'AWS' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_display_name", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_service", "colExpr": "'Cloudtrail'", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_cloudtrail_event_name", "colExpr": "EventName"}, {"colName": "aws_cloudtrail_region", "colExpr": "CloudTrailEvent.awsRegion"}, {"colName": "aws_cloudtrail_event_type", "colExpr": "CloudTrailEvent.eventType"}, {"colName": "aws_cloudtrail_event_source", "colExpr": "CloudTrailEvent.eventSource"}, {"colName": "aws_cloudtrail_user_identity_type", "colExpr": "CloudTrailEvent.userIdentity.type"}, {"colName": "aws_cloudtrail_user_identity_arn", "colExpr": "CloudTrailEvent.userIdentity.arn"}], "dataSource": {"name": "AWS", "feedName": "Cloudtrail", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__cloudtrail"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__aws_cloudtrail_console_login__user_name", "entity": {"name": "Identity"}}