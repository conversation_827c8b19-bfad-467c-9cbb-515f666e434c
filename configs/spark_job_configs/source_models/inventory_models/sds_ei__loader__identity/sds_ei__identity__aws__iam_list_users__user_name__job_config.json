{"primaryKey": "lower(UserName)", "origin": "'AWS IAM Users'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "aws_account_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "password_last_used", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "case when password_last_used is null then 'Non-Human' else 'Human' end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_display_name", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "password_last_used", "colExpr": "CASE WHEN (passwordlastused != '' AND (passwordlastused IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(passwordlastused))) ELSE NULL END"}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_service", "colExpr": "'IAM Users'", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_account_created_date", "colExpr": "CASE WHEN (createdate != '' AND (createdate IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(createdate))) ELSE NULL END"}], "dataSource": {"name": "AWS", "feedName": "IAM Users", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__iam_list_users"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__aws__iam_list_users__user_name", "entity": {"name": "Identity"}}