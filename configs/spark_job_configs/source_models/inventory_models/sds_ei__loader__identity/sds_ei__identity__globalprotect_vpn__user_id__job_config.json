{"primaryKey": "user_id", "filterBy": "LOWER(event_label) LIKE '%gateway-auth%' AND LOWER(event_status) LIKE 'success'", "origin": "'GlobalProtect'", "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colExpr": "'Active Directory'", "colName": "identity_provider", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "'SAM Account Name'", "colName": "identity_format", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "user_id", "colName": "identity_display_name"}, {"colName": "auth_methods_registered", "colExpr": "collect_set(auth_method) over (partition by user_id,event_timestamp_date ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "colName": "identity_primary_key", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "ad_sam_account_name", "colExpr": "SUBSTRING_INDEX(user_id, '\\\\', -1)"}, {"colName": "ad_sam_account_name_with_domain", "colExpr": "CASE WHEN INSTR(user_id, '\\\\') > 0 THEN user_id END"}], "dataSource": {"name": "GlobalProtect", "feedName": "GlobalProtect", "srdm": "<%SRDM_SCHEMA_NAME%>.palo_alto__global_protect"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__globalprotect_vpn__user_id"}