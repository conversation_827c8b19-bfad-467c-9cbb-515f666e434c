{"origin": "'WinEvents 4624'", "filterBy": "event_code='4624'", "dataSource": {"name": "WinEvents 4624", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__windows_security_logs", "feedName": "WinEvents 4624"}, "primaryKey": "lower(temp_object_account_name_4624)", "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents_4624__object_account_name", "temporaryProperties": [{"colExpr": "regexp_extract(object_account_domain,'^([^.]++)')", "colName": "domain_temp"}, {"colExpr": "RTRIM('$',object_account_name)", "colName": "sam_account_name_temp"}, {"colExpr": "CASE WHEN object_account_domain IS NULL OR object_account_name IS NULL OR object_account_domain = '-' OR object_account_name = '-' OR lower(object_account_name) = 'cliusr' OR lower(object_account_name) RLIKE '(?i)lenovo_tmp_|oracyber|neoload|iaadmin|ssastelemetry|sqltelemetry|bvssh_virtualusers|diahostservice|svc_atp_sensor|wsiaccount|fdr_fdrm|pslocaladmin|fdrcyber|defaultapppool' OR lower(object_account_domain) IN ('nt authority', 'nt service', 'nt virtual machine', 'font driver host', 'window manager') THEN NULL ELSE CONCAT_WS('\\\\', domain_temp, sam_account_name_temp) END\n", "colName": "temp_object_account_name_4624"}], "commonProperties": [{"colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(epochTimeInSec)))", "colName": "last_active_date", "fieldsSpec": {"aggregateFunction": "max"}}, {"colExpr": "CASE WHEN object_account_name LIKE '%$' OR lower(object_account_name) LIKE 'svc_%' or lower(object_account_name) like '%admin%' OR lower(object_account_name) like '%defaultuser0%' OR lower(object_account_name) like '%guest%' OR lower(object_account_domain) like '%workgroup%' then 'Non-Human' else 'Human' end", "colName": "type"}], "entitySpecificProperties": [{"colExpr": "case when lower(domain_temp)='corp' then 'Active Directory' else 'Other' end", "colName": "identity_provider"}, {"colName": "user_principal_name", "colExpr": "CASE WHEN regexp_like(object_account_name, '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\\\.[a-zA-Z0-9-.]+$') THEN lower(object_account_name) ELSE NULL END"}, {"colExpr": "cast(null as string)", "colName": "identity_format", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "last_active_date", "colName": "login_last_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "RTRIM('$',object_account_name)", "colName": "identity_display_name"}, {"colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "colName": "identity_primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "'Active'", "colName": "operational_status", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colExpr": "CASE WHEN object_account_name LIKE '%$' THEN RTRIM('$',object_account_name) ELSE NULL END", "colName": "ad_sam_account_name"}, {"colExpr": "case when lower(domain_temp)='corp' then domain_temp else null end", "colName": "ad_domain"}, {"colExpr": "CASE WHEN object_account_name LIKE '%$' THEN temp_object_account_name_4624 ELSE NULL END", "colName": "ad_sam_account_name_with_domain"}]}