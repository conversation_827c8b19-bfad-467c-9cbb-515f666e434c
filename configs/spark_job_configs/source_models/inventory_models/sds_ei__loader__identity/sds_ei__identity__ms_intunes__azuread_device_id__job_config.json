{"origin": "'MS Intune'", "dataSource": {"name": "MS Intune", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__intune", "feedName": "MDM"}, "primaryKey": "azureADDeviceId", "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_intunes__azuread_device_id", "commonProperties": [{"colExpr": "GREATEST(login_last_date,mdm_last_sync_date,mdm_enrolled_date)", "colName": "last_active_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "LEAST(mdm_enrolled_date,first_found_date,last_active_date)", "colName": "first_seen_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "'Non-Human'", "colName": "type", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colExpr": "'Azure AD'", "colName": "identity_provider", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "'AAD Device ID'", "colName": "identity_format", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "upper(regexp_extract(deviceName,'^([^\\r\\n]+?)(?>_([0-9]++[-\\/]){2}|\\$|$)'))", "colName": "identity_display_name"}, {"colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "colName": "identity_primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "CASE WHEN lastLogOnDateTime is not null THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastLogOnDateTime)))ELSE NULL END ", "colName": "login_last_date", "fieldsSpec": {"aggregateFunction": "max"}}], "sourceSpecificProperties": [{"colExpr": "CASE WHEN lastSyncDateTime is not null THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSyncDateTime)))ELSE NULL END", "colName": "mdm_last_sync_date", "fieldsSpec": {"aggregateFunction": "max"}}, {"colExpr": "CASE WHEN enrolledDateTime is not null THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(enrolledDateTime))) ELSE NULL END ", "colName": "mdm_enrolled_date", "fieldsSpec": {"aggregateFunction": "min"}}]}