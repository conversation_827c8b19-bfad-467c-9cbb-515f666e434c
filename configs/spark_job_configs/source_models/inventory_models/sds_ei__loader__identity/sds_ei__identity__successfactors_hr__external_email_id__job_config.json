{"origin": "'SuccessFactors'", "dataSource": {"name": "SuccessFactors", "srdm": "<%SRDM_SCHEMA_NAME%>.sap__successfactors", "feedName": "HR"}, "primaryKey": "LOWER(External__Business__Email__Address)", "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__successfactors_hr__external_email_id", "temporaryProperties": [{"colName": "temp_external_mail_domain_extracted", "colExpr": "split(External__Business__Email__Address,'@')[1]"}], "commonProperties": [{"colName": "type", "colExpr": "'Human'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(sf_recruit_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_format", "colExpr": "'External Email'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_display_name", "colExpr": "INITCAP(concat_ws(' ',First__Name,Middle__Name,Last__Name))"}, {"colName": "email_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "employee_id", "colExpr": "cast(User__ID as string)"}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_primary_key", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "sf_recruit_date", "colExpr": "to_unix_timestamp(Recruit__date,'d[/][-]M[/][-]y[ HH:mm:ss]')*1000"}]}