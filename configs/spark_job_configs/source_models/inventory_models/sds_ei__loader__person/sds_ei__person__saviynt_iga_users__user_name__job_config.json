{"primaryKey": "username", "filterBy": "employeeid IS NOT NULL", "origin": "'Saviynt IGA Users'", "commonProperties": [{"colName": "last_active_date", "colExpr": "ad_last_password_change_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "employeeclass"}, {"colName": "department", "colExpr": "departmentname"}, {"colName": "location_country", "colExpr": "location"}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(CONCAT_WS(' ',first_name,middle_name,last_name))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_name", "colExpr": "INITCAP(firstname)"}, {"colName": "last_name", "colExpr": "INITCAP(lastname)"}, {"colName": "middle_name", "colExpr": "middlename"}, {"colName": "email_id", "colExpr": "LOWER(email)"}, {"colName": "manager", "colExpr": "<PERSON><PERSON><PERSON><PERSON>(manager)"}, {"colName": "employee_id", "colExpr": "employeeid"}, {"colName": "company", "colExpr": "companyname"}, {"colName": "external_email_id", "colExpr": "LOWER(secondaryEmail)"}, {"colName": "job_title", "colExpr": "title"}, {"colName": "legal_entity", "colExpr": "entity"}, {"colName": "organisation_unit_id", "colExpr": "orgunitid"}, {"colName": "cost_center", "colExpr": "costcenter"}, {"colName": "job_function"}, {"colName": "phone_number", "colExpr": "phonenumber"}, {"colName": "address", "colExpr": "street"}], "sourceSpecificProperties": [{"colName": "iga_operational_status", "colExpr": "CASE WHEN statuskey=1 then 'Active' else 'Disabled' END"}, {"colName": "ad_last_password_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(lastPasswordUpdateDate)))"}], "dataSource": {"name": "Saviynt IGA", "feedName": "Users", "srdm": "<%SRDM_SCHEMA_NAME%>.saviynt__user"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__saviynt_iga_users__user_name", "entity": {"name": "Person"}}