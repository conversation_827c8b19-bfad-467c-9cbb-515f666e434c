{"primaryKey": "lower(UserName)", "origin": "'AWS IAM Center'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "aws_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(DisplayName)"}, {"colName": "first_name", "colExpr": "INITCAP(Name.<PERSON>N<PERSON>)"}, {"colName": "email_id", "colExpr": "LOWER(UserName)"}], "sourceSpecificProperties": [{"colName": "aws_iam_center_user_id", "colExpr": "UserId"}, {"colName": "aws_created_date", "colExpr": "CASE WHEN (CreateDate != '' AND (CreateDate IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(CreateDate))) ELSE NULL END"}], "dataSource": {"name": "AWS", "feedName": "IAM Center", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__iam_security_center_permission_set_assignment"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name", "entity": {"name": "Person"}}