{"primaryKey": "ObjectGUID", "filterBy": "(sAMAccountType = '*********' OR sAMAccountType='NORMAL_USER_ACCOUNT') AND DistinguishedName like ('%OU=PAI users%') AND lower(SamAccountName) not in ('administrator') AND (not(lower(SamAccountName) like '%test%' OR lower(SamAccountName) like '%demo%'))", "origin": "'MS Active Directory'", "temporaryProperties": [{"colName": "temp_manager", "colExpr": "CASE WHEN manager <> '[]' THEN manager ELSE NULL END"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,ad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(login_last_date,ad_last_password_change_date,ad_created_date,ad_account_disabled_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "CASE WHEN department <> '[]' THEN department ELSE NULL END"}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(CONCAT_WS(' ',first_name,middle_name,last_name))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_name", "colExpr": "INITCAP(CASE WHEN givenName <> '[]' THEN givenName ELSE NULL END)"}, {"colName": "middle_name", "colExpr": "CASE WHEN initials <> '[]' THEN initials ELSE NULL END"}, {"colName": "last_name", "colExpr": "INITCAP(CASE WHEN Surname <> '[]' THEN Surname ELSE NULL END)"}, {"colName": "manager", "colExpr": "INITCAP(CASE WHEN regexp_replace(regexp_extract(temp_manager,'CN=(.*)'), ',\\\\w.*', '') != '' THEN regexp_replace(regexp_extract(temp_manager, 'CN=(.*)'), ',\\\\w.*', '') ELSE NULL END)"}, {"colName": "company", "colExpr": "CASE WHEN company <> '[]' THEN trim(company) ELSE NULL END"}, {"colName": "job_title", "colExpr": "CASE WHEN title <> '[]' THEN title ELSE NULL END"}, {"colName": "login_last_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(LastLogonDate, 'M/d/yyyy h:mm:ss a')))"}, {"colName": "employee_id", "colExpr": "EmployeeID"}], "sourceSpecificProperties": [{"colName": "ad_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(whenCreated, 'M/d/yyyy h:mm:ss a')))"}, {"colName": "ad_last_password_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(PasswordLastSet, 'M/d/yyyy h:mm:ss a')))"}, {"colName": "account_enabled_status", "colExpr": "lower(TRIM(Enabled))"}, {"colName": "earliest_ad_account_disabled_date", "colExpr": "CASE WHEN lower(TRIM(Enabled)) = 'false' THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "min"}}, {"colName": "ad_account_disabled_date", "colExpr": "CASE WHEN account_enabled_status = 'false' and last_updated_attrs.account_enabled_status.last_changed.updated_at is not null then last_updated_attrs.account_enabled_status.last_changed.updated_at WHEN account_enabled_status = 'false' THEN earliest_ad_account_disabled_date ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ad_operational_status", "colExpr": "CASE WHEN lower(TRIM(Enabled)) = 'false' THEN 'Disabled' WHEN lower(TRIM(Enabled)) = 'true' THEN 'Active' ELSE NULL END", "fieldsSpec": {"persistNonNullValue": false}}], "dataSource": {"name": "MS Active Directory", "feedName": "MS Active Directory", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__active_directory"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__active_directory__object_guid", "entity": {"name": "Person"}}