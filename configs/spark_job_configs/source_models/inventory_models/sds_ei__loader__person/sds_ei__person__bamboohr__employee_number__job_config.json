{"primaryKey": "cast(employeeNumber AS string)", "origin": "'BambooHR'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,bamboo_emp_recruit_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "employment_type", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "bamboo_emp_business_unit", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "INITCAP(location)"}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(concat_WS(' ',first_name,middle_name,last_name))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_name", "colExpr": "INITCAP(firstname)"}, {"colName": "last_name", "colExpr": "INITCAP(lastName)"}, {"colName": "middle_name", "colExpr": "INITCAP(middleName)"}, {"colName": "email_id", "colExpr": "LOWER(workEmail)"}, {"colName": "manager", "colExpr": "INITCAP(manager_name)"}, {"colName": "employee_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "recruit_date", "colExpr": "bamboo_emp_recruit_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_known_termination_date", "colExpr": "termination_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "job_function", "colExpr": "bamboo_emp_competency", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "bamboo_emp_project", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "employee_status", "colExpr": "CASE WHEN LOWER(employmenthistorystatus)='terminated' THEN 'Terminated' ELSE status END"}, {"colName": "phone_number", "colExpr": "COALESCE(mobilePhone,workPhone)"}, {"colName": "termination_date", "colExpr": "to_unix_timestamp((CASE WHEN employmenthistorystatus='Terminated' THEN employeeStatusDate ELSE NULL END), 'yyyy-MM-dd')*1000", "fieldsSpec": {"persistNonNullValue": false, "replaceExpression": false}}, {"colName": "employment_type", "colExpr": "customEmploymentType"}, {"colName": "employee_level", "colExpr": "customLevel"}, {"colName": "job_title", "colExpr": "jobTitle"}], "sourceSpecificProperties": [{"colName": "bamboo_emp_recruit_date", "colExpr": "to_unix_timestamp(hireDate, 'yyyy-MM-dd')*1000"}, {"colName": "bamboo_emp_business_unit", "colExpr": "division"}, {"colName": "bamboo_emp_competency", "colExpr": "customCompetency"}, {"colName": "bamboo_emp_project", "colExpr": "customSubFunction"}], "dataSource": {"name": "BambooHR", "feedName": "HR Report Pull", "srdm": "<%SRDM_SCHEMA_NAME%>.bamboohr__hr_report_pull"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__bamboohr__employee_number", "entity": {"name": "Person"}}