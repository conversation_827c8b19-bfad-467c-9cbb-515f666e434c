{"primaryKey": "lower(userId)", "filterBy": "not(lower(userDisplayName) like '%account%' or lower(userDisplayName) like '%support%' or lower(userDisplayName) like '%events%' or lower(userDisplayName) like '%training%' or lower(userDisplayName) like '%service%' or lower(userDisplayName) like '%aws%' or lower(userDisplayName) like '%dast%' or lower(userDisplayName) like '%enquiries%' or lower(userDisplayName) like '%team%' or lower(userDisplayName) like '%l&d%'  or lower(userDisplayName) like '%forum%' or lower(userDisplayName) like '%learning%'  or lower(userDisplayName) like '%admin%'  or lower(userDisplayName) like '%payroll%'  or lower(userDisplayName) like '%notifications%'  or lower(userDisplayName) like '%posh%' or lower(userDisplayName) like '%prevalent%' or lower(userDisplayName) like '%skype%' or lower(userDisplayName) like '%system%' or lower(userDisplayName) like '%talent%' or lower(userDisplayName) like '%jira%' or lower(userDisplayName) like '%monitor%' or lower(userDisplayName) like '%test%' or lower(userDisplayName) like '%platform%' or lower(userDisplayName) like '%contact%' or lower(userDisplayName) like '%info%' or lower(userDisplayName) like '%pai%')", "origin": "'MS Azure AD Sign-in Logs'", "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(userDisplayName)"}, {"colName": "email_id", "colExpr": "LOWER(userPrincipalName)"}], "sourceSpecificProperties": [{"colName": "aad_user_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "MS Azure AD", "feedName": "Sign-in Logs", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_user_sign_in"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad_sign_in__user_id", "entity": {"name": "Person"}}