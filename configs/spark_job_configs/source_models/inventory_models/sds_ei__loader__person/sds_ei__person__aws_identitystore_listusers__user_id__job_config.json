{"primaryKey": "UserId", "origin": "'AWS IAM'", "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(DisplayName)"}, {"colName": "first_name", "colExpr": "INITCAP(Name.<PERSON>N<PERSON>)"}, {"colName": "last_name", "colExpr": "INITCAP(Name.FamilyName)"}, {"colName": "email_id", "colExpr": "lower(Emails[0].Value)"}, {"colName": "company", "colExpr": "REGEXP_EXTRACT(email_id,'@([^.]+)\\.')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "job_title", "colExpr": "Title"}], "sourceSpecificProperties": [{"colName": "aws_identity_store_id", "colExpr": "IdentityStoreId"}, {"colName": "aws_identity_store_user_id", "colExpr": "UserId"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country"]}, "joinCondition": "s.country_name = e.region", "sourcePreTransform": [{"colName": "country_name", "colExpr": "s.Addresses[0].Country"}]}], "dataSource": {"name": "AWS", "feedName": "IAM", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__identity_store_list_users"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__aws_identitystore_listusers__user_id", "entity": {"name": "Person"}}