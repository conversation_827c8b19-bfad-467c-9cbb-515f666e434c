{"primaryKey": "id", "filterBy": "id is not null and TRIM(id)!='' and not(lower(displayName) like '%account%' or lower(displayName) like '%support%' or lower(displayName) like '%events%' or lower(displayName) like '%training%' or lower(displayName) like '%service%' or lower(displayName) like '%aws%' or lower(displayName) like '%dast%' or lower(displayName) like '%enquiries%' or lower(displayName) like '%team%' or lower(displayName) like '%l&d%'  or lower(displayName) like '%forum%' or lower(displayName) like '%learning%'  or lower(displayName) like '%admin%'  or lower(displayName) like '%payroll%'  or lower(displayName) like '%notifications%'  or lower(displayName) like '%posh%' or lower(displayName) like '%prevalent%' or lower(displayName) like '%skype%' or lower(displayName) like '%system%' or lower(displayName) like '%talent%' or lower(displayName) like '%jira%' or lower(displayName) like '%monitor%' or lower(displayName) like '%test%' or lower(displayName) like '%platform%' or lower(displayName) like '%contact%' or lower(displayName) like '%info%' or lower(displayName) like '%pai%')", "origin": "'MS Azure AD Registered Users'", "commonProperties": [{"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(displayName)"}, {"colName": "first_name", "colExpr": "INITCAP(givenName)"}, {"colName": "last_name", "colExpr": "<PERSON><PERSON><PERSON><PERSON>(surname)"}, {"colName": "email_id", "colExpr": "LOWER(mail)"}, {"colName": "job_title", "colExpr": "jobTitle"}, {"colName": "phone_number", "colExpr": "mobilePhone"}], "sourceSpecificProperties": [{"colName": "aad_user_id", "colExpr": "id"}], "dataSource": {"name": "MS Azure AD", "feedName": "Registered Users", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_registered_user"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad_registered_users__aad_user_id", "entity": {"name": "Person"}}