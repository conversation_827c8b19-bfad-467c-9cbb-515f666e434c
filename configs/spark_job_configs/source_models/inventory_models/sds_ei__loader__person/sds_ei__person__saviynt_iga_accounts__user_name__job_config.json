{"primaryKey": "username", "origin": "'Saviynt IGA Accounts'", "commonProperties": [{"colName": "last_active_date", "colExpr": "login_last_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(coalesce(displayName,username))"}, {"colName": "login_last_date", "colExpr": "CASE WHEN lastlogondate!='' THEN UNIX_TIMESTAMP(lastlogondate,'dd/MM/yyyy')*1000 ELSE NULL END"}], "dataSource": {"name": "Saviynt IGA", "feedName": "Accounts", "srdm": "<%SRDM_SCHEMA_NAME%>.saviynt__account"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__saviynt_iga_accounts__user_name", "entity": {"name": "Person"}}