{"primaryKey": "lower(properties.resourceDetails.Id)", "filterBy": "lower(type) IN ('microsoft.security/assessments') and (lower(regexp_extract(properties.resourceDetails.Id,'\\\\/providers\\\\/([^\\\\/]+\\\\/[^\\\\/]+)\\\\/[^\\\\/]+$')) IN ('microsoft.network/networkinterfaces'))", "origin": "'MS Azure Security Resources'", "commonProperties": [{"colName": "type", "colExpr": "'Network Interface'"}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(regexp_extract(resource_id,'\\/([^\\/]+)$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Network Interface'"}, {"colName": "account_id", "colExpr": "regexp_extract(id, '/subscriptions/([^/]+)/', 1)"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_interface__ms_azure_security_assessments__resource_id", "entity": {"name": "Network Interface"}}