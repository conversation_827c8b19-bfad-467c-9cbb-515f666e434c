{"primaryKey": "lower(arn)", "filterBy": "arn IS NOT NULL AND arn NOT IN ('', ' ') AND resourceType IN ('AWS::EC2::NetworkInterface')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "'Network Interface'"}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date,aws_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_resource_configuration_change_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "res_tag_name", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'name' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mac_address", "colExpr": "configuration.macAddress"}, {"colName": "attach_time", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.attachment.attachTime)))"}, {"colName": "delete_on_termination", "colExpr": "configuration.attachment.deleteOnTermination"}, {"colName": "cloud_instance_id", "colExpr": "configuration.attachment.instanceId"}, {"colName": "interface_type", "colExpr": "configuration.interfaceType"}, {"colName": "private_dns_name", "colExpr": "configuration.privateDnsName"}, {"colName": "private_ip", "colExpr": "configuration.privateIpAddress"}, {"colName": "public_dns_name", "colExpr": "configuration.publicDnsName"}, {"colName": "public_ip", "colExpr": "configuration.publicIpAddress"}, {"colName": "requested_by", "colExpr": "configuration.requesterId"}, {"colName": "source_destination_check", "colExpr": "configuration.sourceDestCheck"}, {"colName": "subnet_id", "colExpr": "configuration.subnetId"}, {"colName": "status", "colExpr": "configuration.status"}, {"colName": "vpc_id", "colExpr": "configuration.vpcId"}, {"colName": "deny_all_igw_traffic", "colExpr": "configuration.denyAllIgwTraffic"}, {"colName": "ipv6_address", "colExpr": "configuration.ipv6Address"}, {"colName": "security_group_id", "colExpr": "configuration.groups.groupId"}, {"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Environment' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_ip_address_version", "colExpr": "CASE WHEN resourceType = 'AWS::EC2::NetworkInterface' AND configuration.PrivateIpAddress IS NOT NULL THEN 'ipv4' WHEN resourceType = 'AWS::EC2::NetworkInterface' AND configuration.Ipv6Addresses IS NOT NULL AND size(configuration.Ipv6Addresses) > 0 THEN 'ipv6' else null END"}, {"colName": "project", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Project' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountId"}, {"colName": "region", "colExpr": "awsRegion", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN availabilityZone = 'Multiple Availability Zones' THEN 'Multiple'  WHEN availabilityZone= 'Not Applicable'  THEN 'Not Applicable' WHEN availabilityZone= 'Regional'  THEN 'Regional' WHEN availabilityZone IS NULL THEN NULL ELSE 'Single' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_public_ip_address_mapped", "colExpr": "CASE WHEN configuration.association.publicIp IS NOT NULL THEN true ELSE false END"}, {"colName": "native_type", "colExpr": "'AWS Network Interface'"}, {"colName": "attachment_status", "colExpr": "configuration.attachment.status"}, {"colName": "operational_state", "colExpr": "CASE WHEN lower(status) IN ('available','associated','attaching','in-use') THEN 'Active' WHEN lower(status) IN ('detaching') THEN 'Inactive' else null END"}, {"colName": "aws_tags", "colExpr": "tags"}, {"colName": "aws_resource_configuration_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime)))"}, {"colName": "aws_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(resourceCreationTime)))"}], "sourceSpecificProperties": [], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_interface__aws_resource_details__arn", "entity": {"name": "Network Interface"}}