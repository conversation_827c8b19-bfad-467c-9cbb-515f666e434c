{"primaryKey": "lower(nic_id)", "filterBy": "lower(type) IN ('microsoft.compute/virtualmachines')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "nic_info", "colExpr": "explode_outer(properties.networkProfile.networkInterfaces)"}, {"colName": "nic_id", "colExpr": "nic_info.id"}], "commonProperties": [{"colName": "type", "colExpr": "'Network Interface'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Network interface'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_interface__ms_azure_resource_details__network_interface_id", "entity": {"name": "Network Interface"}}