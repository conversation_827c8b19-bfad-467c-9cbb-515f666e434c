{"primaryKey": "lower(id)", "filterBy": "LOWER(type) IN ('microsoft.network/networkinterfaces')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "LOWER(type)"}, {"colName": "temp_trans_public_ip_map", "colExpr": "SIZE(FILTER(properties.ipConfigurations, x -> x.properties.publicIPAddress.id IS NOT NULL))"}, {"colName": "temp_trans_private_ip_alloc", "colExpr": "ARRAY_JOIN( TRANSFORM( FILTER(properties.ipConfigurations, config -> config.properties.primary = true), config -> config.properties.privateIPAllocationMethod ), '' )"}, {"colName": "temp_trans_private_ip_address", "colExpr": "ARRAY_JOIN( TRANSFORM( FILTER(properties.ipConfigurations, config -> config.properties.primary = true), config -> config.properties.privateIPAddress ), '' )"}, {"colName": "temp_trans_private_ip_version", "colExpr": "ARRAY_JOIN( TRANSFORM( FILTER(properties.ipConfigurations, config -> config.properties.primary = true), config -> config.properties.privateIPAddressVersion ), '' )"}], "commonProperties": [{"colName": "type", "colExpr": "'Network Interface'"}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'MS Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "lower(tags.Environment)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "properties.provisioningState"}, {"colName": "region", "colExpr": "location"}, {"colName": "project", "colExpr": "tags.Project"}, {"colName": "mac_address", "colExpr": "properties.macAddress"}, {"colName": "interface_type", "colExpr": "properties.nicType"}, {"colName": "default_outbound_connectivity", "colExpr": "properties.defaultOutboundConnectivityEnabled"}, {"colName": "vnet_encryption_supported", "colExpr": "properties.vnetEncryptionSupported"}, {"colName": "accelerated_networking", "colExpr": "properties.enableAcceleratedNetworking"}, {"colName": "ip_forwarding", "colExpr": "properties.enableIPForwarding"}, {"colName": "security_group_id", "colExpr": "properties.networkSecurityGroup.id"}, {"colName": "cloud_instance_id", "colExpr": "properties.virtualMachine.id"}, {"colName": "private_ip_address_version", "colExpr": "CASE WHEN temp_type = 'microsoft.network/networkinterfaces' THEN temp_trans_private_ip_version ELSE 'Not Applicable' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_ip", "colExpr": "CASE WHEN temp_type = 'microsoft.network/networkinterfaces' THEN temp_trans_private_ip_address ELSE 'Not Applicable' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_ip_allocation_method", "colExpr": "CASE WHEN temp_type = 'microsoft.network/networkinterfaces' THEN temp_trans_private_ip_alloc ELSE 'Not Applicable' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_public_ip_address_mapped", "colExpr": "CASE WHEN temp_type = 'microsoft.network/networkinterfaces' AND temp_trans_public_ip_map > 0 THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Network Interface'"}, {"colName": "attachment_status", "colExpr": "CASE WHEN cloud_instance_id is not null THEN 'Attached' ELSE 'Detached' END", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_interface__ms_azure_resource_details__id", "entity": {"name": "Network Interface"}}