{"primaryKey": "lower(nic_id)", "filterBy": "resourceType IN ('AWS::EC2::Instance')", "origin": "'AWS Resource Details'", "temporaryProperties": [{"colName": "nic_id", "colExpr": "explode_outer(configuration.networkInterfaces.networkInterfaceId)"}], "commonProperties": [{"colName": "type", "colExpr": "'Network Interface'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_interface__aws_resource_details__network_interface_id", "entity": {"name": "Network Interface"}}