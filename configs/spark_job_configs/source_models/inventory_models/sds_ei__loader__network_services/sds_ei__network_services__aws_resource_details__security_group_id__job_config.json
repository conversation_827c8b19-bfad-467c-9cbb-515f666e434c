{"primaryKey": "lower(group_id)", "filterBy": "resourceType IN ('AWS::EC2::NetworkInterface')", "origin": "'AWS Resource Details'", "temporaryProperties": [{"colName": "group_id", "colExpr": "explode_outer(configuration.groups.groupId)"}], "commonProperties": [{"colName": "type", "colExpr": "'Security Group'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 Security Group'"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_services__aws_resource_details__security_group_id", "entity": {"name": "Network Services"}}