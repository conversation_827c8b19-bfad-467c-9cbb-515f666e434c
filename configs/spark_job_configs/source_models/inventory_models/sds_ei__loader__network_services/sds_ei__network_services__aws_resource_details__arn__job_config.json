{"primaryKey": "lower(arn)", "filterBy": "arn IS NOT NULL AND arn NOT IN ('', ' ') AND resourceType IN ('AWS::EC2::InternetGateway','AWS::EC2::SecurityGroup')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN resourceType IN ('AWS::EC2::InternetGateway') THEN 'Internet Gateway' WHEN resourceType IN ('AWS::EC2::SecurityGroup') THEN 'Security Group' END"}, {"colName": "first_seen_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.createdTime)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_resource_configuration_change_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Environment' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Project' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountId"}, {"colName": "region", "colExpr": "awsRegion", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN resourceType IN ('AWS::EC2::InternetGateway') THEN 'AWS EC2 InternetGateway' WHEN resourceType IN ('AWS::EC2::SecurityGroup') THEN 'AWS EC2 Security Group' END"}, {"colName": "attachment_status", "colExpr": "CASE WHEN resourceType = 'AWS::EC2::InternetGateway' THEN configuration.attachment.status END"}, {"colName": "has_insecurerules", "colExpr": "CASE WHEN fromPort = 0 AND toPort = 65535 THEN true WHEN (22 BETWEEN fromPort AND toPort OR 3389 BETWEEN fromPort AND toPort) AND size(ipPermissions.ipRanges) > 5 THEN true ELSE false END"}, {"colName": "aws_tags", "colExpr": "tags"}, {"colName": "aws_resource_configuration_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime)))"}, {"colName": "operational_state", "colExpr": "CASE WHEN relationships IS NULL OR size(relationships) = 0 THEN 'Inactive' ELSE 'Active' END"}], "sourceSpecificProperties": [], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_services__aws_resource_details__arn", "entity": {"name": "Network Services"}}