{"primaryKey": "lower(properties.networkSecurityGroup.id)", "filterBy": "lower(type) IN ('microsoft.network/networkinterfaces')", "origin": "'MS Azure Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "'Security Group'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Network Security Group'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_services__ms_azure_resource_details__network_security_group_id", "entity": {"name": "Network Services"}}