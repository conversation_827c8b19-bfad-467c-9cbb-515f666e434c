{"primaryKey": "lower(id)", "filterBy": "LOWER(type) IN ('microsoft.network/networksecuritygroups','microsoft.network/publicipaddresses')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "LOWER(type)"}, {"colName": "temp_trans_public_ip_map", "colExpr": "SIZE(FILTER(properties.ipConfigurations, x -> x.properties.publicIPAddress.id IS NOT NULL))"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN temp_type IN ('microsoft.network/publicipaddresses') THEN 'Public IP' WHEN temp_type IN ('microsoft.network/networksecuritygroups') THEN 'Security Group' ELSE NULL END"}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "lower(tags.Environment)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "properties.provisioningState"}, {"colName": "region", "colExpr": "location"}, {"colName": "project", "colExpr": "tags.Project", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN temp_type IN ('microsoft.network/networksecuritygroups') THEN 'Azure Security Group' WHEN temp_type IN ('microsoft.network/publicipaddresses') THEN 'Azure Public IP Address' END"}, {"colName": "ddos_protection_status", "colExpr": "CASE WHEN temp_type = 'microsoft.network/publicipaddresses' THEN CASE properties.ddosSettings.protectionMode WHEN 'Disabled' THEN 'False' WHEN 'Enabled' THEN 'True' WHEN 'VirtualNetworkInherited' THEN 'True' ELSE NULL END WHEN LOWER(type) = 'microsoft.network/virtualnetworks' THEN CAST(properties.enableDdosProtection AS STRING) ELSE 'Not Applicable' END"}, {"colName": "public_ip_address", "colExpr": "CASE WHEN temp_type = 'microsoft.network/publicipaddresses' THEN properties.ipAddress ELSE 'Not Applicable' END"}, {"colName": "public_ip_address_allocation_method", "colExpr": "CASE WHEN temp_type = 'microsoft.network/publicipaddresses' THEN properties.publicIPAllocationMethod else 'Not Applicable' END"}, {"colName": "public_ip_address_version", "colExpr": "CASE WHEN temp_type = 'microsoft.network/publicipaddresses' THEN properties.publicIPAddressVersion ELSE 'Not Applicable' END"}, {"colName": "tags", "colExpr": "tags"}, {"colName": "zone", "colExpr": "zones"}], "sourceSpecificProperties": [], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_services__ms_azure_resource_details__id", "entity": {"name": "Network Services"}}