{"primaryKey": "lower(public_ip_id)", "filterBy": "lower(type) IN ('microsoft.network/networkinterfaces')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "ip_config", "colExpr": "explode_outer(properties.ipConfigurations)"}, {"colName": "public_ip_id", "colExpr": "ip_config.properties.publicIPAddress.id"}], "commonProperties": [{"colName": "type", "colExpr": "'Public IP'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Public IP Address'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__network_services__ms_azure_resource_details__public_ip_address_id", "entity": {"name": "Network Services"}}