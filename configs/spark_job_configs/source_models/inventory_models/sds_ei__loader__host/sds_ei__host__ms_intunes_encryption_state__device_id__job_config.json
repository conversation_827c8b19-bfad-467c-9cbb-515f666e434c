{"primaryKey": "id", "filterBy": "id is not null", "origin": "'MS Intune Managed Device Encryption States'", "commonProperties": [], "temporaryProperties": [], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "upper(CASE WHEN intune_device_name = '' THEN NULL ELSE regexp_extract(intune_device_name,'^([^\\\\r\\\\n]+?)(?>_([0-9]++[-\\\\/]){2}|\\\\$|$)') END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_version", "colExpr": "osVersion"}, {"colName": "bitlocker_protection_status", "colExpr": "CASE WHEN advancedBitLockerStates IS NULL THEN NULL WHEN lower(advancedBitLockerStates)='success' THEN 'Protection On' ELSE 'Protection Off' END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "bitlocker_conversion_status", "colExpr": "CASE WHEN advancedBitLockerStates IS NOT NULL AND lower(encryptionState)='encrypted' THEN 'Fully Encrypted' WHEN advancedBitLockerStates IS NOT NULL AND lower(encryptionState)='notencrypted' THEN 'Unencrypted' ELSE NULL END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "file_vault_protection_status", "colExpr": "CASE WHEN fileVaultStates IS NULL THEN NULL WHEN lower(fileVaultStates)='success' THEN 'Protection On' ELSE 'Protection Off' END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "file_vault_conversion_status", "colExpr": "CASE WHEN fileVaultStates IS NOT NULL AND lower(encryptionState)='encrypted' THEN 'Fully Encrypted' WHEN fileVaultStates IS NOT NULL AND lower(encryptionState)='notencrypted' THEN 'Unencrypted' ELSE NULL END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "hardware_encryption_status", "colExpr": "CASE WHEN lower(encryptionState)='encrypted' THEN TRUE ELSE False END", "fieldsSpec": {"persistNonNullValue": false}}], "sourceSpecificProperties": [{"colName": "intune_device_name", "colExpr": "deviceName"}, {"colName": "intune_device_type", "colExpr": "deviceType"}], "dataSource": {"name": "MS Intune", "feedName": "Managed Device Encryption States", "srdm": "<%SRDM_SCHEMA_NAME%>.ms_intune__managed_device_encryption_states"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intunes_encryption_state__device_id"}