{"primaryKey": "ID", "origin": "'Tanium Endpoints Extended Host List Report'", "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(CASE WHEN element_at(Computer__Name,1) REGEXP '^((25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)\\\\.){3}(25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)$' THEN NULL WHEN element_at(Computer__Name,1) REGEXP '^((25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)\\\\-){3}(25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)$' THEN element_at(Computer__Name,1) WHEN element_at(Computer__Name,1) LIKE '%.%' THEN SUBSTRING_INDEX(element_at(Computer__Name,1), '.', 1) ELSE element_at(Computer__Name,1) END)"}, {"colName": "cloud_provider", "colExpr": "CASE WHEN LOWER(element_at(Cloud__Instance__Provider, 1)) LIKE '%microsoft azure%' THEN 'Azure' WHEN LOWER(element_at(Cloud__Instance__Provider, 1)) LIKE '%amazon ec2%' THEN 'AWS' WHEN LOWER(element_at(Cloud__Instance__Provider, 1)) LIKE '%google cloud%' THEN 'GCP' ELSE NULL END"}, {"colName": "file_vault_protection_status", "colExpr": "CASE WHEN LOWER(element_at(FileVault__Details.Fully__Secure, 1))='yes' THEN 'Protection On' WHEN LOWER(element_at(FileVault__Details.Fully__Secure, 1))='no' THEN 'Protection Off' ELSE NULL END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "file_vault_conversion_status", "colExpr": "CASE WHEN LOWER(element_at(FileVault__Details.Status, 1))='encryption complete' THEN 'Fully Encrypted' WHEN LOWER(element_at(FileVault__Details.Status, 1))='none' THEN 'Unencrypted' ELSE NULL END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "hardware_encryption_status", "colExpr": "CASE WHEN LOWER(file_vault_conversion_status)='fully encrypted' THEN TRUE WHEN LOWER(file_vault_conversion_status)='unencrypted' THEN FALSE ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider_unique_id", "colExpr": "Case WHEN element_at(cloud__instance__id, 1) RLIKE '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^i-[0-9a-f]{17}$' Then element_at(cloud__instance__id, 1) ELSE NULL END"}, {"colName": "cloud_instance_type", "colExpr": "CASE WHEN LOWER(element_at(Cloud__Instance__Provider, 1)) LIKE '%microsoft azure%' OR LOWER(element_at(Cloud__Instance__Provider, 1)) LIKE '%amazon ec2%' THEN element_at(Cloud__Instance__Type, 1) ELSE NULL END"}, {"colName": "cloud_subscription_id", "colExpr": "Case WHEN element_at(Cloud__Instance__Account, 1) RLIKE '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^[0-9]{12}$' Then element_at(Cloud__Instance__Account, 1) ELSE NULL END"}, {"colName": "is_cloud_resource", "colExpr": "CASE WHEN cloud_provider IS NOT NULL OR cloud_provider_unique_id IS NOT NULL OR cloud_instance_type IS NOT NULL OR cloud_subscription_id IS NOT NULL THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ad_group", "colExpr": "TRANSFORM(tanium_ad_groups, x -> regexp_extract(x, 'CN=([^,]+)', 1))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "windows_account_name", "colExpr": "CASE WHEN element_at(LAPS__Policy__Configuration______Windows.AccountName, 1) LIKE '[no results]' OR element_at(LAPS__Policy__Configuration______Windows.AccountName, 1) LIKE 'TSE-Error%' THEN NULL ELSE element_at(LAPS__Policy__Configuration______Windows.AccountName, 1) END"}, {"colName": "windows_password_length", "colExpr": "CAST(CASE WHEN trim(lower(element_at(LAPS__Policy__Configuration______Windows.PasswordLength,1))) rlike 'no results' OR trim(element_at(LAPS__Policy__Configuration______Windows.PasswordLength, 1)) = '' THEN NULL ELSE element_at(LAPS__Policy__Configuration______Windows.PasswordLength,1) END AS BIGINT)"}, {"colName": "windows_password_age_day", "colExpr": "element_at(LAPS__Policy__Configuration______Windows.PasswordAgeDays,1)"}, {"colName": "windows_password_complexity", "colExpr": "element_at(LAPS__Policy__Configuration______Windows.PasswordComplexity,1)"}, {"colName": "windows_password_last_set", "colExpr": "CAST(ARRAY_JOIN(TRANSFORM(regexp_extract_all(element_at(LAPS__Policy__Configuration______Windows.PasswordLastSet, 1), '(\\\\d{1,2}/\\\\d{1,2}/\\\\d{4} \\\\d{1,2}:\\\\d{2}:\\\\d{2}(?: [APap][Mm])?)'), x -> CAST(unix_timestamp(x, CASE WHEN x LIKE '%AM' OR x LIKE '%PM' THEN 'M/d/yyyy h:mm:ss a' ELSE 'd/M/yyyy H:mm:ss' END) * 1000 AS BIGINT)), ',') AS BIGINT)"}, {"colName": "tags", "colExpr": "tanium_custom_tags", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "tanium_custom_tags", "colExpr": "Custom__Tags"}, {"colName": "tanium_ad_short_domain", "colExpr": "element_at(AD__Short__Domain,1)"}, {"colName": "tanium_domain_role", "colExpr": "element_at(Domain__Role,1)"}, {"colName": "tanium_ad_groups", "colExpr": "AD__Query______Computer__Groups"}, {"colName": "tanium_ad_primary_user_country", "colExpr": "AD__Query______Primary__User__Details.Country"}], "dataSource": {"name": "Tanium", "feedName": "Endpoints Extended Host List Report", "srdm": "<%SRDM_SCHEMA_NAME%>.tanium__endpoints_extended_host_list_report"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__tanium_endpoints_extended_host_list_report__id_region_tag"}