{"primaryKey": "id", "origin": "'Tanium CVE Findings'", "commonProperties": [{"colName": "type", "colExpr": "'Unknown'"}, {"colName": "last_active_date", "colExpr": "tanium_last_scan_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(CASE WHEN tanium_hostname REGEXP '^[0-9]{1,3}(\\\\.[0-9]{1,3}){3}$' THEN NULL WHEN tanium_hostname LIKE '%.%' THEN SUBSTRING_INDEX(tanium_hostname, '.', 1) ELSE tanium_hostname END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_family", "colExpr": "'Unknown'"}, {"colName": "vm_product", "colExpr": "CASE WHEN tanium_onboarding_status is True THEN 'Tanium' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN tanium_onboarding_status is True THEN True ELSE False END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "tanium_last_scan_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_scan_method", "colExpr": "'Agent'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cmdb_product", "colExpr": "CASE WHEN tanium_onboarding_status is True THEN 'Tanium' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cmdb_onboarding_status", "colExpr": "CASE WHEN tanium_onboarding_status is True THEN True ELSE False END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "'Unknown'"}], "sourceSpecificProperties": [{"colName": "tanium_hostname", "colExpr": "name"}, {"colName": "tanium_id", "colExpr": "id"}, {"colName": "tanium_onboarding_status", "colExpr": "cast(True as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tanium_last_scan_date", "colExpr": "unix_timestamp( to_date( max(lastScanDate) over (partition by primary_key rows between unbounded preceding and unbounded following), 'yyyyMMdd' ))*1000"}], "dataSource": {"name": "Tanium", "feedName": "CVE Findings", "srdm": "<%SRDM_SCHEMA_NAME%>.tanium__endpoints_cve_findings_report"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__tanium_cve_findings__id"}