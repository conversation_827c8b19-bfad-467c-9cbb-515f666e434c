{"primaryKey": "dId", "origin": "'MS Azure AD Registered Users'", "commonProperties": [{"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "login_last_user", "colExpr": "displayName"}], "sourceSpecificProperties": [{"colName": "aad_device_id", "colExpr": "deviceId"}, {"colName": "aad_id", "colExpr": "dId"}], "dataSource": {"name": "MS Azure AD", "feedName": "Registered Users", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_registered_user"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_registered_users__id", "entity": {"name": "Host"}}