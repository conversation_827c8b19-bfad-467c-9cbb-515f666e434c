{"primaryKey": "id", "origin": "'Rapid7 InsightVM Assets'", "commonProperties": [{"colExpr": "UPPER(coalesce(host_name, netbios[0], fqdn , dns_name, primary_key))", "colName": "display_label", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "rapid7_first_history_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "rapid7_last_active_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER( CASE WHEN hostName RLIKE '^(\\\\d{1,3}\\\\.){3}\\\\d{1,3}$' THEN NULL WHEN hostName RLIKE '\\\\.' THEN SPLIT(hostName, '\\\\.')[0] ELSE hostName END)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "os", "colExpr": "os"}, {"colName": "os_version", "colExpr": "osFingerprint.version"}, {"colName": "domain", "colExpr": "UPPER( CASE WHEN dns_name REGEXP '^[0-9]{1,3}(\\\\.[0-9]{1,3}){3}$' THEN NULL WHEN dns_name LIKE '%.%' THEN SUBSTRING(dns_name, INSTR(dns_name, '.') + 1) ELSE NULL END )", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fqdn", "colExpr": "CASE WHEN lower(temp_rapid7_hostname) = lower(dns_name) THEN UPPER(dns_name) ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "netbios", "colExpr": "FLATTEN(COLLECT_SET(FILTER(hostNames, h -> h.source = 'netbios').name) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING))"}, {"colName": "dns_name", "colExpr": "CASE WHEN array_contains(temp_rapid7_source_dns, lower(temp_rapid7_dns)) THEN temp_rapid7_dns ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "CASE WHEN rapid7_onboarding_status = TRUE THEN 'Rapid7 InsightVM' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN rapid7_onboarding_status is True THEN True ELSE False END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_scan_method", "colExpr": "CASE WHEN EXISTS( rapid7_scan_type, x -> lower(x) LIKE '%agent%' ) THEN 'Agent' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "rapid7_last_history_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "addresses.ip", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "mac_address", "colExpr": "addresses.mac"}], "sourceSpecificProperties": [{"colName": "rapid7_asset_id", "colExpr": "id", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_last_history_date", "colExpr": "ARRAY_MAX(TRANSFORM( FILTER(history, x -> (x.type = 'AGENT-IMPORT' OR x.type = 'SCAN' OR x.type = 'SCAN-LOG-IMPORT')), x -> CAST(to_timestamp(x.date, \"yyyy-MM-dd'T'HH:mm:ss.SSSX\") AS BIGINT) * 1000 ))"}, {"colName": "rapid7_first_history_date", "colExpr": "ARRAY_MIN(TRANSFORM( FILTER(history, x -> (x.type = 'AGENT-IMPORT' OR x.type = 'SCAN' OR x.type = 'SCAN-LOG-IMPORT')), x -> CAST(to_timestamp(x.date, \"yyyy-MM-dd'T'HH:mm:ss.SSSX\") AS BIGINT) * 1000 ))"}, {"colName": "rapid7_last_active_date", "colExpr": "ARRAY_MAX(TRANSFORM( FILTER(history, x -> (x.type = 'ASSET-IMPORT' OR x.type = 'AGENT-IMPORT' OR x.type = 'EXTERNAL-IMPORT' OR x.type = 'SCAN' OR x.type = 'SCAN-LOG-IMPORT' OR x.type = 'ACTIVE-SYNC')), x -> CAST(to_timestamp(x.date, \"yyyy-MM-dd'T'HH:mm:ss.SSSX\") AS BIGINT) * 1000 ))"}, {"colName": "rapid7_hostnames", "colExpr": "hostNames.name", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_onboarding_status", "colExpr": "cast(True as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "rapid7_asset_type_classification", "colExpr": "CASE WHEN lower(type) LIKE '%hypervisor%' THEN 'Hypervisor' WHEN lower(type) LIKE '%guest%' THEN 'Guest' WHEN lower(type) LIKE '%physical%' THEN 'Physical' WHEN lower(type) LIKE '%mobile%' THEN 'Mobile' ELSE 'Unknown' END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_os_type_classification", "colExpr": "osFingerprint.type"}, {"colName": "rapid7_os_family_classification", "colExpr": "osFingerprint.family", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_scan_type", "colExpr": "ids.source", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "temp_rapid7_hostname", "colExpr": "UPPER( CASE WHEN hostName RLIKE '^(\\\\d{1,3}\\\\.){3}\\\\d{1,3}$' THEN NULL WHEN hostName RLIKE '^\\\\d+$' THEN NULL  ELSE hostName END)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "temp_rapid7_source_dns", "colExpr": "TRANSFORM(FLATTEN(COLLECT_SET(FILTER(hostNames, h -> h.source = 'dns').name) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)),x -> lower(x))", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "temp_rapid7_dns", "colExpr": "hostName", "fieldsSpec": {"persistNonNullValue": false}}], "dataSource": {"name": "Rapid7 InsightVM", "feedName": "Assets", "srdm": "<%SRDM_SCHEMA_NAME%>.rapid7_assets"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7_assets__id"}