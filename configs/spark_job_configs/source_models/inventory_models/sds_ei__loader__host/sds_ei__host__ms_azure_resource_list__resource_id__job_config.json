{"primaryKey": "id", "filterBy": "lower(type) IN ('microsoft.compute/virtualmachines')", "origin": "'MS Azure Resource List'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "type"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "first_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(name)"}, {"colName": "os", "colExpr": "CASE WHEN details.properties.storageProfile.osDisk.osType IS NULL THEN NULL\nWHEN LOWER(details.properties.storageProfile.osDisk.osType) LIKE '%unknown%' THEN NULL\nWHEN LOWER(details.properties.storageProfile.osDisk.osType) LIKE '%other%' THEN 'Other'\nELSE CONCAT_WS(' ',details.properties.storageProfile.osDisk.osType,CASE WHEN NOT regexp_like(details.properties.storageProfile.imageReference.exactVersion,'(?i)unknown|other') THEN details.properties.storageProfile.imageReference.exactVersion END)\nEND"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "location"}, {"colName": "account_id", "colExpr": "(SPLIT(id,'/')[2])"}, {"colName": "provisioning_state", "colExpr": "details.properties.provisioningState"}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN size(zones) = 1 THEN 'Single' WHEN size(zones) > 1 THEN 'Multiple' ELSE null END"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource List", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_list"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_resource_list__resource_id", "entity": {"name": "Host"}}