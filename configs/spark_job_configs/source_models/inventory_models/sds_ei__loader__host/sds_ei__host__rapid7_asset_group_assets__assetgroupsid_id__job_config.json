{"primaryKey": "CONCAT(assetGroupsID,'|',id)", "origin": "'Rapid7 InsightVM Asset Group Assets'", "entitySpecificProperties": [{"colName": "vm_product", "colExpr": "'Rapid7 InsightVM'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "cast(True as boolean)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "rapid7_onboarding_status", "colExpr": "cast(True as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "rapid7_asset_id", "colExpr": "id", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_asset_group_id", "colExpr": "assetGroupsID", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_asset_group_name", "colExpr": "name", "fieldsSpec": {"persistNonNullValue": false}}], "dataSource": {"name": "Rapid7 InsightVM", "feedName": "Asset Group Assets", "srdm": "<%SRDM_SCHEMA_NAME%>.rapid7_asset_group_assets"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7_asset_group_assets__assetgroupsid_id"}