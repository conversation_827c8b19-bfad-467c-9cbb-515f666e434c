{"primaryKey": "cast(ID as string)", "origin": "'Qualys Host List'", "commonProperties": [{"colName": "last_active_date", "colExpr": "vm_last_scan_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(DNS_DATA.HOSTNAME)"}, {"colName": "os", "colExpr": "OS"}, {"colName": "ip", "colExpr": "IP"}, {"colName": "dns_name", "colExpr": "CASE WHEN DNS RLIKE '^([a-zA-Z0-9][-a-zA-Z0-9]*\\\\.)+[a-zA-Z]{2,}$' THEN DNS else null end"}, {"colName": "netbios", "colExpr": "NETBIOS"}, {"colName": "accessibility", "colExpr": "CASE WHEN LOWER(qualys_tags) LIKE '%internal%' THEN 'Internal' WHEN LOWER(qualys_tags) LIKE '%external%' THEN 'External' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "CLOUD_PROVIDER"}, {"colName": "cloud_instance_id", "colExpr": "CLOUD_RESOURCE_ID"}, {"colName": "cloud_resource_type", "colExpr": "case when cloud_instance_id IS NOT NULL then 'Virtual Machine' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "CASE WHEN lower(qualys_detection_method) LIKE '%agent%' THEN 'Qualys' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN LOWER(qualys_detection_method) LIKE '%agent%' THEN true when datediff(from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(vm_last_scan_date / 1000, 'yyyy-MM-dd')) <= 30 THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_tracking_method", "colExpr": "qualys_detection_method", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(GREATEST (CASE WHEN LAST_VM_SCANNED_DATE != '[]' AND LAST_VM_SCANNED_DATE != '(never)' AND LAST_VM_SCANNED_DATE != 'NULL' THEN LAST_VM_SCANNED_DATE end,CASE WHEN LAST_VULN_SCAN_DATETIME != '[]' AND LAST_VULN_SCAN_DATETIME != '(never)' AND LAST_VULN_SCAN_DATETIME != 'NULL' THEN LAST_VULN_SCAN_DATETIME end,case WHEN LAST_VM_AUTH_SCANNED_DATE != '[]' AND LAST_VM_AUTH_SCANNED_DATE != '(never)' AND LAST_VM_AUTH_SCANNED_DATE != 'NULL' THEN LAST_VM_AUTH_SCANNED_DATE end,case WHEN LAST_COMPLIANCE_SCAN_DATETIME != '[]' AND LAST_COMPLIANCE_SCAN_DATETIME != '(never)' AND LAST_COMPLIANCE_SCAN_DATETIME != 'NULL' THEN LAST_COMPLIANCE_SCAN_DATETIME end,case WHEN LAST_SCAP_SCAN_DATETIME != '[]' AND LAST_SCAP_SCAN_DATETIME != '(never)' AND LAST_SCAP_SCAN_DATETIME != 'NULL' THEN LAST_SCAP_SCAN_DATETIME end))))  ", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "native_type", "colExpr": "CASE WHEN (CLOUD_SERVICE) LIKE '%EC2%' THEN 'AWS EC2 Instance'  WHEN (CLOUD_SERVICE) LIKE '%VM%' THEN 'Azure Virtual Machine' WHEN (CLOUD_SERVICE) LIKE '%Compute Engine%' THEN 'GCP Compute Engine' ELSE NULL END"}], "sourceSpecificProperties": [{"colName": "qualys_id", "colExpr": "ID"}, {"colName": "qualys_tags", "colExpr": "array_join(TAGS.TAG.NAME, ',')"}, {"colName": "qualys_groups", "colExpr": "ASSET_GROUP_IDS"}, {"colName": "qualys_asset_id", "colExpr": "CAST(ASSET_ID as string)"}, {"colName": "qualys_detection_method", "colExpr": "CONCAT_WS(' ','Qualys',TRACKING_METHOD)"}], "dataSource": {"name": "Qualys", "feedName": "Host List", "srdm": "<%SRDM_SCHEMA_NAME%>.qualys__host_list"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys_host_list__host_id", "entity": {"name": "Host"}}