{"primaryKey": "CONCAT(tagID,'|',id)", "origin": "'Rapid7 InsightVM Tag Assets'", "entitySpecificProperties": [{"colName": "vm_product", "colExpr": "CASE WHEN rapid7_onboarding_status is True THEN 'Rapid7 InsightVM' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN rapid7_onboarding_status is True THEN True ELSE False END", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "rapid7_asset_id", "colExpr": "id", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_onboarding_status", "colExpr": "cast(True as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "rapid7_tag_id", "colExpr": "tagID", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_tag", "colExpr": "name", "fieldsSpec": {"persistNonNullValue": false}}], "dataSource": {"name": "Rapid7 InsightVM", "feedName": "Tag Assets", "srdm": "<%SRDM_SCHEMA_NAME%>.rapid7_tag_assets"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7_tag_assets__tagid_id"}