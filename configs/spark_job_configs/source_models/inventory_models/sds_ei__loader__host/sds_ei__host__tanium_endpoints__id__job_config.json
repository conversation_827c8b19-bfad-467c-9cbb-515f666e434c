{"primaryKey": "id", "origin": "'Tanium Endpoints'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "tanium_first_seen", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "tanium_last_seen", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "display_label", "colExpr": "UPPER(coalesce(host_name,hardware_serial_number,primary_key))", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(CASE WHEN tanium_hostname REGEXP '^[0-9]{1,3}(\\\\.[0-9]{1,3}){3}$' THEN NULL WHEN tanium_hostname LIKE '%.%' THEN SUBSTRING_INDEX(tanium_hostname, '.', 1) ELSE tanium_hostname END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "domain", "colExpr": "lower(domainName)"}, {"colName": "hardware_manufacturer", "colExpr": "lower(manufacturer)"}, {"colName": "hardware_model", "colExpr": "model"}, {"colName": "hardware_serial_number", "colExpr": "UPPER(serialNumber)"}, {"colName": "hardware_chassis_type", "colExpr": "tanium_chassis_type", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_encryption_status", "colExpr": "CASE WHEN LOWER(bitlocker_conversion_status)='fully encrypted' THEN TRUE WHEN LOWER(bitlocker_conversion_status)='unencrypted' THEN FALSE ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "CASE WHEN tanium_onboarding_status is True THEN 'Tanium' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN tanium_onboarding_status is True THEN True ELSE False END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_scan_method", "colExpr": "'Agent'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cmdb_product", "colExpr": "CASE WHEN tanium_onboarding_status is True THEN 'Tanium' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cmdb_onboarding_status", "colExpr": "CASE WHEN tanium_onboarding_status is True THEN True ELSE False END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mac_address", "colExpr": "array_except(macAddresses,array(NULL))"}, {"colName": "operational_state", "colExpr": "'Unknown'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "host_sub_type", "colExpr": "CASE WHEN lower(hardware_chassis_type) LIKE '%desktop%' OR lower(hardware_chassis_type) LIKE '%mini%' THEN 'Desktop' WHEN lower(hardware_chassis_type) LIKE '%laptop%' OR lower(hardware_chassis_type) LIKE '%notebook%' THEN 'Laptop' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bitlocker_protection_status", "colExpr": "CASE WHEN lower(filter(BitlockerDetails, bd -> bd.Drive = 'C:') [0].Protection__Status) IS NULL THEN NULL WHEN lower(filter(BitlockerDetails, bd -> bd.Drive = 'C:') [0].Protection__Status)= 'protection on' THEN 'Protection On' ELSE 'Protection Off' END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "bitlocker_conversion_status", "colExpr": "CASE WHEN lower(filter(BitlockerDetails, bd -> bd.Drive = 'C:')[0].Conversion__Status) IS NULL THEN NULL WHEN lower(filter(BitlockerDetails, bd -> bd.Drive = 'C:')[0].Conversion__Status)= 'fully encrypted' THEN 'Fully Encrypted' ELSE 'Unencrypted' END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "ip", "colExpr": "array_distinct(concat(tanium_nat_ip_address,tanium_ip_addresses))", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "tanium_hostname", "colExpr": "name"}, {"colName": "tanium_computer_id", "colExpr": "computerID"}, {"colName": "tanium_first_seen", "colExpr": "unix_timestamp(substring(eidFirstSeen, 6), 'dd MMM yyyy HH:mm:ss Z') * 1000"}, {"colName": "tanium_last_seen", "colExpr": "unix_timestamp(substring(e<PERSON><PERSON><PERSON><PERSON>, 6), 'dd MMM yyyy HH:mm:ss Z') * 1000"}, {"colName": "tanium_id", "colExpr": "id"}, {"colName": "tanium_onboarding_status", "colExpr": "cast(True as boolean)"}, {"colName": "tanium_system_uuid", "colExpr": "systemUUID"}, {"colName": "tanium_ip_addresses", "colExpr": "ipAddresses", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "tanium_nat_ip_address", "colExpr": "ARRAY(natIpaddress)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "tanium_chassis_type", "colExpr": "chassisType", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "tanium_windows_os", "colExpr": "os_windows_type"}, {"colName": "aad_organizational_unit", "colExpr": "ADOrganizationalUnit"}, {"colName": "tanium_operating_system_generation", "colExpr": "OperatingSystemGeneration"}, {"colName": "tanium_os", "colExpr": "os_name"}, {"colName": "tanium_os_platform", "colExpr": "osPlatform"}], "dataSource": {"name": "Tanium", "feedName": "Endpoints", "srdm": "<%SRDM_SCHEMA_NAME%>.tanium__endpoints_host_list_report"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__tanium_endpoints__id"}