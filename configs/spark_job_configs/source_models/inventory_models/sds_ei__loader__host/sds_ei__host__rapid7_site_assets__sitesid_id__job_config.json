{"primaryKey": "CONCAT(sitesID,'|',id)", "origin": "'Rapid7 InsightVM Site Assets'", "commonProperties": [{"colExpr": "UPPER(coalesce(host_name, netbios[0], fqdn , dns_name, primary_key))", "colName": "display_label", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER( CASE WHEN hostName RLIKE '^(\\\\d{1,3}\\\\.){3}\\\\d{1,3}$' THEN NULL WHEN hostName RLIKE '\\\\.' THEN SPLIT(hostName, '\\\\.')[0] ELSE hostName END )"}, {"colName": "fqdn", "colExpr": "UPPER(CASE WHEN hostName NOT RLIKE '(\\\\d{1,3}\\\\.){3}\\\\d{1,3}' AND hostName RLIKE '\\\\.' THEN hostName ELSE NULL END)"}, {"colName": "netbios", "colExpr": "FLATTEN(COLLECT_SET(FILTER(hostNames, h -> h.source = 'netbios').name) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING))"}, {"colName": "dns_name", "colExpr": "CASE WHEN array_contains(temp_rapid7_source_dns, lower(temp_rapid7_dns)) THEN temp_rapid7_dns ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "CASE WHEN rapid7_onboarding_status is True THEN 'Rapid7 InsightVM' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN rapid7_onboarding_status is True THEN True ELSE False END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "ip"}], "sourceSpecificProperties": [{"colName": "rapid7_asset_id", "colExpr": "id", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_hostnames", "colExpr": "hostNames.name", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_onboarding_status", "colExpr": "cast(True as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "rapid7_site_last_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(lastScanTime)))"}, {"colName": "rapid7_site_id", "colExpr": "sitesID", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_site_name", "colExpr": "name", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "rapid7_scan_template", "colExpr": "scanTemplate", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "temp_rapid7_source_dns", "colExpr": "TRANSFORM(FLATTEN(COLLECT_SET(FILTER(hostNames, h -> h.source = 'dns').name) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)),x -> lower(x))", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "temp_rapid7_dns", "colExpr": "hostName", "fieldsSpec": {"persistNonNullValue": false}}], "dataSource": {"name": "Rapid7 InsightVM", "feedName": "Site Assets", "srdm": "<%SRDM_SCHEMA_NAME%>.rapid7_site_assets"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__rapid7_site_assets__sitesid_id"}