{"primaryKey": "id", "filterBy": "id is not null AND id!='' AND id!=' '", "origin": "'MS Azure AD Device Groups'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,aad_created_date,aad_enrolled_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aad_created_date,  aad_enrolled_date, ad_last_sync_date, login_last_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(displayName,'^([^\\\\r\\\\n]+?)(?>_([0-9]++[-\\\\/]){2}|\\\\$|$)'))"}, {"colName": "os", "colExpr": "CASE WHEN operatingSystem IS NULL THEN NULL WHEN LOWER(operatingSystem) LIKE '%unknown%' THEN null WHEN LOWER(operatingSystem) LIKE '%other%' THEN 'Other' ELSE CONCAT_WS(' ',operatingSystem,CASE WHEN NOT regexp_like(operatingSystemVersion,'(?i)unknown|other') THEN operatingSystemVersion END) END"}, {"colName": "os_version", "colExpr": "operatingSystemVersion"}, {"colName": "domain", "colExpr": "lower(domainName)"}, {"colName": "login_last_date", "colExpr": "CASE WHEN (approximateLastSignInDateTime != '' AND approximateLastSignInDateTime IS NOT NULL)THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(approximateLastSignInDateTime))) END"}, {"colName": "hardware_manufacturer", "colExpr": "manufacturer"}, {"colName": "hardware_model", "colExpr": "model"}, {"colName": "intune_ownership_status", "colExpr": "deviceOwnership ", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "resource_id", "colExpr": "UPPER(CASE WHEN physicalIds[0] like '[AzureResourceId]%' THEN split(physicalIds[0], ':')[1] ELSE NULL END)"}, {"colName": "mdm_compliance_state", "colExpr": "CASE WHEN isCompliant = TRUE THEN 'Compliant' WHEN isCompliant = FALSE THEN 'Non Compliant' ELSE NULL END"}], "sourceSpecificProperties": [{"colName": "aad_device_id", "colExpr": "deviceId"}, {"colName": "aad_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(createdDateTime)))"}, {"colName": "aad_enrolled_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(registrationDateTime)))"}, {"colName": "ad_last_sync_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(max(onPremisesLastSyncDateTime) over (partition by primary_key rows between unbounded preceding and unbounded following))))"}, {"colName": "aad_profile_type", "colExpr": "profileType"}, {"colName": "aad_system_label", "colExpr": "systemLabels"}, {"colName": "aad_device_category", "colExpr": "deviceCategory"}, {"colName": "aad_device_group_id", "colExpr": "groupInfo.groupId"}, {"colName": "aad_device_group_names", "colExpr": "collect_set(groupInfo.groupDisplayName) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "aad_operational_status", "colExpr": "CASE WHEN accountEnabled = FALSE THEN 'Disabled' WHEN accountEnabled = TRUE THEN 'Active' ELSE NULL END", "fieldsSpec": {"persistNonNullValue": false}}], "dataSource": {"name": "MS Azure AD", "feedName": "Device Groups", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_device_groups"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_device_groups__id"}