{"primaryKey": "id", "origin": "'MS Intune Managed Device'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,mdm_enrolled_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(login_last_date, mdm_enrolled_date,mdm_last_sync_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "login_last_date", "colExpr": "CASE WHEN lastLogOnDateTime is not null THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastLogOnDateTime))) ELSE NULL END"}, {"colName": "host_name", "colExpr": "upper(regexp_extract(deviceName,'^([^\\\\r\\\\n]+?)(?>_([0-9]++[-\\\\/]){2}|\\\\$|$)'))"}, {"colExpr": "case when wifi_mac_address is not null and ethernet_mac_address is not null then (array_union(wifi_mac_address,ethernet_mac_address)) else coalesce(wifi_mac_address,ethernet_mac_address) end", "colName": "mdm_mac_address", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "CASE WHEN operatingSystem IS NULL THEN NULL WHEN LOWER(operatingSystem) LIKE '%unknown%' THEN NULL \nWHEN LOWER(operatingSystem) LIKE '%other%' THEN 'Other' \nELSE CONCAT_WS(' ', operatingSystem,CASE WHEN NOT regexp_like(osVersion,'(?i)unknown|other') THEN osVersion END) \nEND"}, {"colName": "hardware_manufacturer", "colExpr": "manufacturer"}, {"colName": "hardware_model", "colExpr": "model"}, {"colName": "hardware_serial_number", "colExpr": "serialNumber"}, {"colName": "hardware_imei", "colExpr": "imei"}, {"colName": "mdm_product", "colExpr": "'MS Intune'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_onboarding_status", "colExpr": "case when lower(deviceRegistrationState)='registered' then true else false end"}, {"colName": "mdm_compliance_state", "colExpr": "CASE WHEN lower(complianceState)='compliant' THEN 'Compliant'\nWHEN lower(complianceState)='noncompliant' THEN 'Non Compliant'\nWHEN lower(complianceState)='ingraceperiod' THEN 'In Grace Period'\nWHEN lower(complianceState)='configmanager' THEN 'Config Manager'\nELSE NULL\nEND"}, {"colName": "mdm_enrolled_date", "colExpr": "CASE WHEN enrolledDateTime is not null then UNIX_MILLIS(TIMESTAMP(to_timestamp(enrolledDateTime))) ELSE NULL END"}, {"colName": "mdm_last_sync_date", "colExpr": "CASE WHEN lastSyncDateTime is not null THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSyncDateTime)))ELSE NULL END"}, {"colName": "encryption_status", "colExpr": "mdm_encryption_status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "full_disk_encryption_status", "colExpr": "mdm_encryption_status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "compliance_state", "colExpr": "mdm_compliance_state", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aad_device_id", "colExpr": "azureADDeviceId"}, {"colName": "intune_ownership_status", "colExpr": "managedDeviceOwnerType"}, {"colName": "mdm_encryption_status", "colExpr": "isEncrypted"}, {"colName": "intune_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "intune_management_service", "colExpr": "managementAgent"}, {"colName": "wifi_mac_address", "colExpr": "collect_set(wifiMacAddress) OVER  (partition by id  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "ethernet_mac_address", "colExpr": "collect_set(ethernetMacAddress) OVER  (partition by id  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "fieldsSpec": {"persistNonNullValue": false}}], "dataSource": {"name": "MS Intune", "feedName": "Managed Device", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__intune"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intunes__device_id", "entity": {"name": "Host"}}