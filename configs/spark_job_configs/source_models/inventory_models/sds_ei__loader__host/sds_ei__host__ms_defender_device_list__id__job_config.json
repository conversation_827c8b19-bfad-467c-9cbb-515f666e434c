{"primaryKey": "id", "origin": "'MS Defender Device List'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,defender_onboarding_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(defender_onboarding_date,edr_last_scan_date,edr_discovered_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(dns_name,'^((?>(?:[0-9]++[.]){3}[0-9]++|[^.]++))([.][^\\\\r\\\\n]*+)?$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "CASE WHEN osPlatform IS NULL OR lower(osPlatform) like '%unknown%' THEN NULL \nWHEN regexp_like(osPlatform,'(?i)other') THEN 'Other' \nELSE concat_ws(' ',osPlatform,CASE WHEN NOT regexp_like(osVersion,'(?i)other|unknown') THEN osVersion END,CASE WHEN NOT regexp_like(osBuild,'(?i)other|unknown') THEN osBuild END,CASE WHEN NOT regexp_like(osArchitecture,'(?i)other|unknown') THEN osArchitecture END,CASE WHEN NOT regexp_like(osProcessor,'(?i)other|unknown') THEN osProcessor END)\nEND"}, {"colName": "ip", "colExpr": "lastIpAddress"}, {"colName": "dns_name", "colExpr": "computerDnsName"}, {"colName": "cloud_resource_name", "colExpr": "lower(computerDnsName)"}, {"colName": "os_version", "colExpr": "osVersion"}, {"colName": "os_architecture", "colExpr": "osArchitecture"}, {"colName": "os_build", "colExpr": "osBuild"}, {"colName": "cloud_provider", "colExpr": "vmMetadata.cloudProvider"}, {"colName": "resource_id", "colExpr": "vmMetadata.resourceId"}, {"colName": "account_id", "colExpr": "vmMetadata.subscriptionId"}, {"colName": "cloud_instance_id", "colExpr": "lower(vmMetadata.vmId)"}, {"colName": "cloud_resource_type", "colExpr": "case when cloud_instance_id IS NOT NULL then 'Virtual Machine' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "case when lower(cloud_provider)='azure' and cloud_instance_id is not null then 'Azure Virtual Machine' when lower(cloud_provider)='aws' and cloud_instance_id is not null then 'AWS EC2 Instance' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_product", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' THEN 'MS Defender' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_onboarding_status", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_last_scan_date", "colExpr": "case when  onboardingStatus='Onboarded' then UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeen))) else null end"}, {"colName": "edr_discovered_date", "colExpr": "case when  onboardingStatus!='Onboarded' then UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeen))) else null end"}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_fully_functional", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' and lower(defender_health_status)='active' THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_cloud_last_report_date", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' and lower(defender_health_status)='active' THEN edr_last_scan_date ELSE null END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_tracking_method", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' THEN 'MS Defender Agent' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' THEN 'MS Defender' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fqdn", "colExpr": "LOWER(CASE WHEN regexp_like(dns_name,'^(?!:\\\\/\\\\/)(?=.{1,255}$)((.{1,63}[.]){1,127}(?![0-9]*$)[a-z0-9-A-Z]+[.]?)$') THEN dns_name END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "domain", "colExpr": "regexp_extract(fqdn,'^(?:[^.]++[.])((local|corp|[^.]++[^\\\\r\\\\n]++))$')", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aad_device_id", "colExpr": "aadDeviceId"}, {"colName": "defender_id", "colExpr": "id"}, {"colName": "defender_health_status", "colExpr": "healthStatus"}, {"colName": "defender_detection_method", "colExpr": "case when lastExternalIpAddress is null then 'Network Scan' else 'Defender Agent' end"}, {"colName": "defender_tags", "colExpr": "CAST(machineTags AS STRING)"}, {"colName": "defender_risk_score", "colExpr": "riskScore"}, {"colName": "defender_exposure_level", "colExpr": "exposureLevel"}, {"colName": "defender_onboarding_status", "colExpr": "onboardingStatus"}, {"colName": "defender_management_service", "colExpr": "managedBy"}, {"colName": "defender_onboarding_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstSeen)))"}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device List", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_list"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_list__id", "entity": {"name": "Host"}}