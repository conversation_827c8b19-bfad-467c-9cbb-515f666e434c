{"caption": "Network Services", "entity_classification": "Entity", "description": "A collection of interconnected systems and devices through which data is transmitted, and where we have visibility or control over traffic flow.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data", "group": "common", "type": "string", "category": "General Information"}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.\nFor Host Entity instance name, DNS name ,host name etc are some of the attributes that contribute to display label.  ", "group": "common", "type": "string", "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host, Person, Vulnerability etc.", "group": "common", "type": "string", "category": "General Information"}, "type": {"caption": "Type", "description": "The specific role of the entity based on its functionality within an organisation.\n\nPossible distinct values includes Server,Workstation,Network,Printer,Hypervisor,Mobile and Other.If Type information is not available, this field is marked as No Data.", "group": "common", "type": "string", "category": "General Information"}, "origin": {"caption": "Origin", "description": "Data source(s) from which the entity has been extracted.\nFor example AWS, Qualys etc.", "group": "common", "type": "string", "data_structure": "list", "category": "General Information"}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information"}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.\nIt is the actual api name from which the data is ingested.\n Some of the examples for data feed in Host entity are Qualys Host List, MS Azure AD Devices etc.", "group": "common", "ui_visibility": true, "type": "string", "data_structure": "list", "category": "General Information"}, "first_found_date": {"caption": "First Found", "description": "The date at which the entity was first observed in the ingested data.This will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "category": "General Information"}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.\n By default first seen date date is calculated based on the minimum value between the last active date and the first found date of the entity.\nIf the data sources provides information regarding first seen activity of the entity for example AD created date in Host, they take precedence over the default logic.", "group": "common", "type": "timestamp", "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\nDefaults to First Found.\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "category": "General Information"}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "category": "General Information"}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which entity was active as inferred from available data sources.\nThis date is determined by considering the maximum value of dates contributed by each data source for the entity in question.\nThis includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "timestamp", "category": "General Information"}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \nIf the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. When there is no information available to determine a host's activity, its status is recorded as No Data.\nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "group": "common", "type": "string", "category": "General Information"}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "type": "integer", "category": "General Information", "range_selection": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source. ", "group": "common", "type": "integer", "category": "General Information", "range_selection": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "type": "integer", "category": "General Information", "range_selection": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "group": "common", "category": "General Information", "type": "integer", "range_selection": true}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets. If no information is available, this field is marked as No Data.", "group": "common", "ui_visibility": true, "type": "string", "category": "General Information"}, "location_country": {"caption": "Location Country", "description": "Specifies the country where the asset is located.\nFor example 'South Africa'.If the country information is not available, this field is marked as No Data.", "group": "common", "ui_visibility": true, "type": "string", "category": "General Information"}, "location_city": {"caption": "Location City", "description": "Specifies the city where the asset is located.\nFor example 'Sydney'.", "group": "common", "ui_visibility": true, "category": "General Information", "type": "string"}, "department": {"caption": "Department", "description": "Name of the department within the business unit.", "group": "common", "ui_visibility": true, "category": "General Information", "type": "string"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "range_selection": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.", "group": "common", "type": "string", "data_structure": "struct", "ui_visibility": false}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "ui_visibility": false, "type": "integer"}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive.\nFor example 2 days.", "group": "common", "type": "integer", "ui_visibility": false}, "resource_name": {"caption": "Resource Name", "description": "Name of the network resource. ", "group": "entity_specific", "type": "string", "ui_visibility": true}, "provisioning_state": {"caption": "Resource State", "description": "State of the network resource.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "resource_id": {"caption": "Resource Id", "description": "Unique identifier assigned to a specific resource", "group": "entity_specific", "type": "string", "ui_visibility": true}, "infrastructure_type": {"caption": "Deployment Type", "description": "Type of the network Infrastructure where network resources are hosted", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Asset Identification"}, "environment": {"caption": "Environment", "description": "Type of environment of the resource", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Resource Location and Availability"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud provider. For On-premises network this field might not be polulated", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "account_id": {"caption": "Account ID", "description": "The account id that the resource belongs to. For Azure this refers to the subscription ID.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "region": {"caption": "Region", "description": "Geographic location where a cloud provider establishes data centers and infrastructure to offer its services.\nThese regions are essentially physical locations with clusters of servers, storage systems, networking equipment, and other resources that power the cloud platform.\nFor example 'eu-west-1', 'ca-central-1'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Resource Location and Availability"}, "project": {"caption": "Project", "description": "Identifies the specific project or initiative to which a resource belongs. It is derived from the Tags attribute.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "is_public_ip_address_mapped": {"caption": "Public IP Address Mapped on Launch", "description": "Indicates if the resource has a public IP mapped to it. Helps in understanding network exposure and connectivity, particularly for resources that need to be accessible from the internet.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "associated_open_finding_count": {"caption": "Count of Open Findings", "description": "Number of open findings associated with Network Services resource.", "group": "enrichment", "type": "integer", "range_selection": true}, "native_type": {"caption": "Native Type", "description": "It specifies the exact cloud service named by the Cloud Provider.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource Identification"}, "operational_state": {"caption": "Operational State", "description": "Shows the operational state of the network interface.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource State and Security"}, "public_ip_address": {"caption": "Public IP Address", "description": "Displays the assigned Public IP of the network interface.", "group": "entity_specific", "type": "string", "category": "IP Configuration", "ui_visibility": true}, "public_ip_address_allocation_method": {"caption": "Public IP Address Allocation Method", "description": "Indicates if the Public IP is statically or dynamically assigned.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "IP Configuration"}, "public_ip_address_version": {"caption": "Public IP Address Version", "description": "Specifies the type of IP address associated with a resource. In AWS and Azure, resources can be assigned either IPv4 or IPv6 addresses.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "IP Configuration"}, "inbound_access": {"caption": "Inbound Public Access", "description": "Indicates if the security group has a rule which allows public access to internal resources.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "has_insecurerules": {"caption": "Insecure Rules", "description": "Indicates if there are overly permissive rules in a security group.For eg. rules with wide port ranges (e.g., 0-65535), rules allowing sensitive ports (e.g., 22, 3389) from broad IP ranges", "group": "entity_specific", "type": "string", "ui_visibility": true}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "type": "string", "navigator_attribute_description": "Origin can be either unique or corroborated.", "navigator_attribute_distinct_values": ["Corroborated", "Unique"], "navigator_distinct_values_enabled": true}, "completeness_quality_score": {"caption": "Completeness Quality Score", "description": "A metric representing the extent to which required data attributes for the entity are populated across ingested data sources. This score helps evaluate the completeness of the entity's data profile.", "group": "enrichment", "examples": "1", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "aggregated_quality_score": {"caption": "Aggregated Quality Score", "description": "A composite score derived from various quality metrics (such as completeness, correctness, and consistency) to assess the overall data quality of the entity.", "examples": "1", "group": "enrichment", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score_category": {"caption": "Completeness Quality Score Category", "description": "A categorical interpretation of the Completeness Quality Score that classifies the entity's data completeness into levels such as High, Medium, or Low (configurable). This helps in quickly identifying the priority or severity of incomplete data across entities, enabling better triaging and quality improvement decisions.", "examples": "High", "group": "enrichment", "type": "string", "range_selection": true, "category": "General Information", "internally_generated": true}}, "dashboard_identifier": "EI"}