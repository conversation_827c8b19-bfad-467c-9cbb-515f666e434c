{"caption": "Cluster", "entity_classification": "Entity", "navigator_enabled": true, "navigator_entity_description": "# Cluster\n\nA Cluster is a logical grouping of computing resources that function as a unified system within cloud computing environments. Unlike individual hosts or containers, clusters aggregate multiple computing instances to provide enhanced capabilities such as high availability, load balancing, distributed processing, and scalability. This entity represents specialized computing infrastructures including Kubernetes clusters, EMR (Elastic MapReduce) clusters, container groups, and auto-scaling instance collections.\n\n## Key Characteristics\n\nClusters are distinguished by their orchestration of multiple computing resources working together as a cohesive unit. They can exist in various deployment environments (cloud or on-premise) and serve specialized computing purposes. Unlike standalone computing resources, clusters provide management frameworks for resource allocation, workload distribution, and service resilience.\n\n## Infrastructure and Architecture\n\nClusters are categorized by their infrastructure type (cloud or on-premise) and specific implementation technologies. Cloud-based clusters include AWS EKS (Elastic Kubernetes Service), AWS EMR, Azure AKS (Azure Kubernetes Service), and Azure Container Instances. Each cluster type maintains distinct attributes tracking its architecture, such as kubernetes_version for Kubernetes clusters or emr_version for MapReduce implementations.\n\nClusters typically comprise multiple node groups or instance fleets that determine their computing capacity. These are tracked through attributes like kubernetes_node_pool_vm_count, scaling_instance_count, and aws_ec2fleet_total_target_capacity. The composition of these resources is captured in attributes like azure_aks_node_pool_profiles and aws_autoscaling_instance_types.\n\n## Networking and Security\n\nClusters maintain complex networking configurations to enable both internal communication between components and external access. This is reflected in attributes like private_ip, public_dns_name, aws_eks_endpoint_private_access, and azure_aks_private_fqdn. Security configurations are tracked through security group identifiers (aws_emr_master_security_group_id), network access controls (azure_public_network_access), and authentication mechanisms (azure_aks_enable_rbac).\n\n## Operational Characteristics\n\nThe operational state of clusters is tracked through attributes like operational_state, provisioning_state, and activity_status. Clusters have distinct lifecycle management features captured in attributes like aws_emr_auto_terminate and azure_aks_auto_upgrade_type. Their operational capabilities are enhanced through integrations with monitoring systems (azure_aks_monitor_enabled), security services (azure_aks_defender_status), and policy frameworks (azure_aks_azure_policy_status).\n\n## Distinguishing Features from Other Entities\n\nUnlike individual Hosts which represent single computing instances, Clusters orchestrate multiple computing resources. While Containers represent isolated application environments, Clusters provide the management framework within which containers operate. Networks define communication pathways, whereas Clusters represent the computing resources that utilize these pathways.\n\nClusters are distinct from Cloud Accounts which represent administrative boundaries for cloud resources. While Applications represent software systems, Clusters provide the computing infrastructure on which applications run. Storage entities focus on data persistence, whereas Clusters emphasize computing power and processing capabilities.\n\n## Security and Compliance\n\nClusters maintain security configurations through attributes tracking antivirus status (av_status), firewall configurations (fw_status), and vulnerability management (vm_onboarding_status). Their security posture is assessed through exposure_score and asset_criticality metrics. Security monitoring integrations are tracked through attributes like edr_threat_count and active_cluster_finding_count.\n\n## Relationships and Associations\n\nClusters maintain relationships with other entities through attributes like associated_kubernetes_cluster_count and compute_instance_group_has_mapreduce_count. They are associated with cloud accounts (account_id) and may be linked to specific business units, departments, and projects for organizational context. Their resource utilization is tracked through relationships with compute instance groups and other infrastructure components.\n\nThrough these comprehensive attributes and relationships, the Cluster entity provides a holistic view of aggregated computing resources, enabling effective management, security monitoring, and operational optimization across cloud and on-premise environments.", "navigator_graph_node_description": "A Cluster entity represents a group of interconnected computing resources that function as a unified system within cloud or on-premise environments. It encompasses various types including Kubernetes clusters, AWS EMR clusters, Azure AKS clusters, and compute instance groups, each identified by unique attributes such as cluster name, resource ID, and cloud provider details. Key technical characteristics include infrastructure type (cloud/on-premise), operational state, scaling capabilities, node configurations, and network accessibility settings. Clusters maintain relationships with cloud accounts and compute resources while tracking security posture through vulnerability management metrics, firewall status, and active finding counts. This entity enables comprehensive monitoring of distributed computing environments, supporting both operational management and security assessment across an organization's infrastructure landscape.", "navigator_examples": ["User Query: 'Show me all clusters with public IP addresses that are open to all internet traffic' Output: 'cluster'", "User Query: 'Which clusters have high privileges or admin-level permissions?' Output: 'cluster'"], "description": "Cluster entity encompasses both compute resources and its services within the broader cloud computing paradigm.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data", "group": "common", "type": "string", "category": "General Information", "internally_generated": true}, "display_label": {"caption": "Display Label", "description": "The derived and \"best known\"identifier or name, based on the attribute that best uniquely identifies it.For Cluster instance name,resource name etc are some of the attributes that contributes to display label.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host, Person,Cluster etc", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "type": {"caption": "Type", "description": "The specific category to which the cloud resources are classified based on its purpose. E.g Virtual Machine,Compute Instance Group.", "group": "common", "type": "string", "ui_visibility": true, "navigator_is_full_value_required": true, "category": "General Information"}, "origin": {"caption": "Origin", "description": "Data source in which the entity was extracted (found or derived).Examples are AWS,Azure.", "group": "common", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "General Information"}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity is extracted.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.Examples are AWS Resource Details,Azure Resource Details etc.", "group": "common", "examples": "", "ui_visibility": true, "category": "General Information", "type": "string", "data_structure": "list", "internally_generated": true}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.\nThis will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "The initial observation date of the cloud resource as inferred from available data sources.Defaults to least of first found date,last active date if no information is obtained from the data source. AWS/Azure resource created date,instance launch time etc can be considered as some of the attributes that contribute to the first seen date.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\\nDefaults to First Found.\\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "The latest date on which the cloud resource has been found active as inferred from the data sources. ", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information"}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. The logic is as follows: If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. For Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score": {"caption": "Completeness Quality Score", "description": "A metric representing the extent to which required data attributes for the entity are populated across ingested data sources. This score helps evaluate the completeness of the entity's data profile.", "group": "enrichment", "examples": "1", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "aggregated_quality_score": {"caption": "Aggregated Quality Score", "description": "A composite score derived from various quality metrics (such as completeness, correctness, and consistency) to assess the overall data quality of the entity.", "examples": "1", "group": "enrichment", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score_category": {"caption": "Completeness Quality Score Category", "description": "A categorical interpretation of the Completeness Quality Score that classifies the entity's data completeness into levels such as High, Medium, or Low (configurable). This helps in quickly identifying the priority or severity of incomplete data across entities, enabling better triaging and quality improvement decisions.", "examples": "High", "group": "enrichment", "type": "string", "range_selection": true, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "description": {"caption": "Description", "description": "A detailed information associated with the the cloud resource", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "type": "string", "category": "General Information", "ui_visibility": true}, "location_country": {"caption": "Location Country", "description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard.", "group": "common", "examples": "", "category": "General Information", "ui_visibility": true, "navigator_is_full_value_required": true, "type": "string"}, "location_city": {"caption": "Location City", "description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard.", "group": "common", "examples": "", "category": "General Information", "ui_visibility": true, "navigator_is_full_value_required": true, "type": "string"}, "department": {"caption": "Department", "description": "Tag used to identify or categorize the resource based on its association with a specific business department within an organization", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "The key fields that are considering for updating the last updated date of an entity.", "group": "common", "type": "string", "ui_visibility": false, "data_structure": "struct", "internally_generated": true}, "infrastructure_type": {"caption": "Deployment Type", "description": "Indicates the deployment environment of a Cluster, specifying whether it is hosted on a cloud platform (e.g., AWS, Azure, Google Cloud) or located on-premise within an organization's data center. Examples include 'Cloud' and 'On-Premise'.\n", "type": "string", "group": "entity_specific", "navigator_is_full_value_required": true, "category": "Asset Identification"}, "container_has_container_service_count": {"caption": "Count of Containers Belonging to Container Services", "description": "Number of Containers belonging to Container Services.", "group": "enrichment", "type": "integer", "range_selection": true}, "container_has_container_group_count": {"caption": "Count of Containers Belonging to Container Groups", "description": "Number of Containers belonging to Container Groups.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_compute_instance_group_count": {"caption": "Count of Compute Instance Groups having Virtual Machines", "description": "Number of Compute Instance Groups having Virtual Machines.", "group": "enrichment", "type": "integer", "range_selection": true}, "virtual_machine_has_compute_instance_group_count": {"caption": "Count of Virtual Machines Belonging to Compute Instance Groups", "description": "Number of Virtual Machines belonging To Compute Instance Groups.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_mapreduce_cluster_count": {"caption": "Count of MapReduce Clusters having Compute Instance Groups", "description": "Number of MapReduce Clusters having Compute Instance Groups.", "group": "enrichment", "type": "integer", "range_selection": true}, "compute_instance_group_has_mapreduce_count": {"caption": "Count of Compute Instance Groups Belonging to Mapreduce Clusters", "description": "Number of Compute Instance Groups belonging to Mapreduce Clusters.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_kubernetes_cluster_count": {"caption": "Count of Kubernetes Clusters having Compute Instance Groups", "description": "Number of Kubernetes Clusters having Compute Instance Groups.", "group": "enrichment", "type": "integer", "range_selection": true}, "compute_instance_group_has_kubernetes_count": {"caption": "Count of Compute Instance Groups Belonging To Kubernetes Clusters", "description": "Number of Compute Instance Groups belonging to Kubernetes Clusters.", "group": "enrichment", "type": "integer", "range_selection": true}, "active_cluster_finding_count": {"caption": "Active Cluster Finding Count", "description": "Number of active findings related to cluster.", "group": "enrichment", "type": "integer", "range_selection": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "type": "integer", "ui_visibility": false, "range_selection": true}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive.", "group": "common", "type": "integer", "ui_visibility": false}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "category": "General Information", "type": "string"}, "associated_host_count": {"caption": "Count of Corresponding Host", "description": "Number of host corresponding to the cloud resource.", "group": "enrichment", "type": "integer", "range_selection": true}, "cloud_provider": {"caption": "Cloud Provider", "description": "Identifies the service responsible for hosting and managing the cloud resource.Common cloud providers are AWS,Azure,GCP. ", "group": "entity_specific", "type": "string", "ui_visibility": true, "navigator_is_full_value_required": true, "category": "General Information"}, "account_id": {"caption": "Cloud Account ID", "description": "Unique identifier associated with a specific account or subscription within a cloud provider's infrastructure.\nThis ID is typically used to manage billing, access, and administrative tasks related to resources provisioned in that account.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "region": {"caption": "Cloud Region", "description": "Geographical location where the data centers of cloud providers are located.Examples are ap-south-1,centralindia etc.", "group": "entity_specific", "type": "string", "ui_visibility": true, "navigator_is_full_value_required": true, "category": "Resource Location and Availability"}, "resource_id": {"caption": "Cloud Resource ID", "description": "Unique identifier assigned to each resource within a cloud environment. Resource ID provides the full path to the resource and typically includes information about the resource type, resource group, subscription, and other details.Example arn:aws:ec2:ap-south-1:************:volume/vol-0040d3fe7d57777.", "group": "entity_specific", "type": "string", "ui_visibility": true, "candidate_key": true, "data_structure": "list", "category": "Cloud Resource Identification"}, "resource_name": {"caption": "Cloud Resource Name", "description": "A specific, user-defined name assigned to a resource within a cloud environment.\n It uniquely identifies that particular resource within its scope and makes it easier to reference, manage, and organize resources.", "group": "entity_specific", "type": "string", "ui_visibility": true, "candidate_key": true, "data_structure": "list", "category": "Cloud Resource Identification"}, "native_type": {"caption": "Cloud Native Type", "description": "It specifies the exact Cloud Service type of the cloud resource prefixed by the cloud provider name.Examples are AWS EC2 Instance,Azure Virtual Machine", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "operational_state": {"caption": "Cloud Last Known Operational State", "description": "Last known operational status of the cloud resource as inferred from data source. The possible distinct values are 'Active' and 'Inactive'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "navigator_is_full_value_required": true, "category": "Operational and Lifecycle State"}, "environment": {"caption": "Environment", "description": "Tag that defines the environment in which cloud resources are deployed and used.Values typically include dev,staging,prod etc.", "group": "entity_specific", "type": "string", "ui_visibility": true, "navigator_is_full_value_required": true, "category": "Resource Location and Availability"}, "provisioning_state": {"caption": "Provisioning State", "description": "Refers to the current status of a resource during its creation or deployment process. The possible distinct values are 'Creating', 'Updating', 'Deleting', 'Succeeded', 'Failed' and 'Cancelled'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Operational and Lifecycle State"}, "zone_availability": {"caption": "Cloud Zone Availability", "description": "Specifies whether the resource is created in single or multiple availability zones within the region. The possible distinct values are 'Single', 'Multiple','Regional' and 'Not Applicable'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Resource Location and Availability"}, "accessibility": {"caption": "Accessibility", "description": "Refers to the ability of the resource to be accessible over the public internet, allowing external users or systems to connect and interact with it. The possible distinct values are 'Internal' and 'External'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Network"}, "os_family": {"caption": "OS Family", "description": "Underlying family of OS that powers the cloud resource.Example values are Windows, Linux, macOS, Android, iOS etc.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "kubernetes_cluster_id": {"caption": "Kubernetes Cluster ID", "description": "Unique identifier assigned to a Kubernetes cluster within a cloud platform.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "kubernetes_cluster_name": {"caption": "Kubernetes Cluster Name", "description": "Unique name assigned to the Kubernetes Cluster within a cloud platform.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "kubernetes_version": {"caption": "Kubernetes Version", "description": "Specific version of the Kubernetes platform that is running within a Kubernetes cluster. \nEach version of Kubernetes includes new features, enhancements, bug fixes, and security updates. \nKnowing the Kubernetes version is essential for compatibility, especially when deploying applications, configuring resources, and using specific Kubernetes features.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "kuberenetes_namespace": {"caption": "Kubernetes Namespace", "description": "Refers to a logical and isolated space within a Kubernetes cluster where you can deploy and manage resources.\n Namespaces are used to organize and partition Kubernetes objects, such as pods, services, and deployments, providing a way to avoid naming conflicts and efficiently manage applications and services in a multi-tenant environment.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "emr_cluster_id": {"caption": "EMR Cluster ID", "description": "Unique identifier assigned to an Amazon Elastic MapReduce (EMR) cluster within AWS. This ID is used to manage, monitor, and interact with the cluster throughout its lifecycle.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource Identification"}, "kubernetes_node_group_name": {"caption": "Kubernetes Node Group Name", "description": "Unique name assigned to the node group belonging to a particular kubernetes cluster.A Kubernetes Node Group refers to a collection of worker nodes within a Kubernetes cluster that share the same configuration and are typically managed as a single unit.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "kubernetes_node_pool_vm_count": {"caption": "Kubernetes Node Group VM Count", "description": "The total number of virtual machines (VMs) that can be provisioned within an Kubernetes cluster.Calculated as the sum of total number of virtual machines hosted within each node group of the kubernetes cluster", "group": "entity_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "ecs_cluster_name": {"caption": "ECS Cluster Name", "description": "User defined name assigned to the Elastic Container Service(ECS) Cluster.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource Identification"}, "scaling_instance_count": {"caption": "Scaling Instance Count", "description": "Indicates the count of virtual machines operational within the Autoscaling group or Virtual Machine Scale Sets.", "group": "entity_specific", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "Scaling and Elasticity"}, "scaling_instance_type": {"caption": "Scaling Instance Type", "description": "Specific configuration or size of a computing instance within an autoscaling group or scalesets.Example m5a.2xlarge.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Scaling and Elasticity"}, "scaling_instance_ids": {"caption": "Scaling Instance IDs", "description": "Unique ID associated with the EC2 Instances launched by the Auto Scaling group.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Scaling and Elasticity"}, "scaling_group_name": {"caption": "Scaling Group Name", "description": "Unique name associated with the scaling group. This name must be unique per Region per account.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Scaling and Elasticity"}, "ec2fleet_id": {"caption": "EC2 Fleet ID", "description": "ID used to uniquely identify and manage individual EC2 Fleet configurations.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource Identification"}, "edr_threat_count": {"caption": "EDR Threat Count", "description": "The number of threats that have been detected by the EDR Product", "group": "entity_specific", "type": "integer", "ui_visibility": true}, "cloud_instance_id": {"caption": "Cloud Instance ID", "description": "Unique identifier assigned to a cloud-based virtual machine (VM) or instance by the cloud provider.\nIt serves as a way to distinguish one instance from others within the cloud infrastructure", "group": "entity_specific", "type": "string", "ui_visibility": true, "candidate_key": true, "data_structure": "list", "category": "Cloud Resource Identification"}, "kubernetes_pod_name": {"caption": "Kubernetes Pod Name", "description": "Refers to the unique identifier assigned to an individual pod within a Kubernetes cluster. Pods are the smallest deployable units in Kubernetes and encapsulate one or more containers.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "aci_container_services_active_containers": {"caption": "ACI Active Containers", "description": "Active containers present within the Azure Container Instance.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "private_ip": {"caption": "Private IP", "description": "The set of private IP addresses that are currently assigned to the cloud resource.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Network"}, "public_ip": {"caption": "Public IP", "description": "The set of public IP addresses that are currently assigned to the cloud resource.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Network"}, "aws_instance_subnet_id": {"caption": "AWS Instance Subnet ID", "description": "Unique identifier assigned to a specific subnet within a Virtual Private Cloud (VPC) of the instance.", "group": "source_specific", "type": "string", "ui_visibility": true}, "private_dns_name": {"caption": "Private DNS Name", "description": "A private DNS name, in the context of cloud computing and networking, refers to a domain name system (DNS) name that resolves to private IP addresses within a specific network or cloud environment. Unlike public DNS names that are accessible over the internet, private DNS names are used within a private network and are not resolvable or accessible from outside that network.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Network"}, "public_dns_name": {"caption": "Public DNS Name", "description": "A Public DNS (Domain Name System) name refers to a domain name that is globally accessible over the internet.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Network"}, "active_operational_date": {"caption": "Active Operational Date", "description": "Date at which the operational state of the resource was found last active.", "group": "entity_specific", "type": "timestamp", "category": "Operational and Lifecycle State"}, "login_last_date": {"caption": "Last Login", "description": "The date at which the device was last logged into the service or source.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true}, "login_last_user": {"caption": "Last logged in User", "description": "The last user logged in to the cloud resource.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "edr_product": {"caption": "EDR Product", "description": "The product used for Endpoint Detection and Response on the host.Examples MS Defender,Qualys etc.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "edr_onboarding_status": {"caption": "EDR Onboarding Status", "description": "Indicates whether the EDR agent is onboarded in the cloud resource or not.Distinct values are true and false.", "examples": true, "group": "entity_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "category": "Security and Compliance"}, "edr_last_scan_date": {"caption": "EDR Last Scan Date", "description": "Last Scanned date of EDR sources.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true}, "av_status": {"caption": "Anti Virus Scan Completed", "description": "Indicates the antivirus scan completion status of a cloud resource.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Compliance"}, "av_last_scan_date": {"caption": "AV Last Scan Date", "description": "Indicates the date by which the antivirus scan completed on the cloud resource.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true}, "av_signature_update_date": {"caption": "AV Signature Update Date", "description": "Refers to the most recent date when the antivirus software installed on the host received an update to its virus definition files.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true}, "av_block_malicious_code_status": {"caption": "AV Block Malicious Code Setting Enabled", "description": "Antivirus Block Malicious Code feature refers to the functionality within the antivirus software that identifies and prevents the execution of code or files that are recognized as malicious.This attribute checks whether the functionality is enabled or disabled.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "av_signature_update_sla_breach_status": {"caption": "AV Signature Update SLA Breach Status", "description": "Indicates whether the antivirus signature is up to date within the allowed sla. Returns true if the signature was updated recently , and false if it is considered outdated based on the defined compliance threshold.", "group": "entity_specific", "type": "string"}, "av_scan_sla_breach_status": {"caption": "AV Scan SLA Breach Status", "description": "Indicates whether the antivirus scan is up to date within the allowed sla. Returns true if the scan was updated recently , and false if it is considered outdated based on the defined compliance threshold.", "group": "entity_specific", "type": "string"}, "av_signature_update_sla_breach_duration": {"caption": "AV Signature Update SLA Breach Duration", "description": "The number of  days between the antivirus signature update date and the last update to the record. This duration helps identify if the update exceeds the allowed SLA period for antivirus signature updates.", "group": "entity_specific", "type": "integer"}, "av_scan_sla_breach_duration": {"caption": "AV Scan SLA Breach Duration", "description": "The number of  days between the antivirus scan  date and the last update to the record. This duration helps identify if the update exceeds the allowed SLA period for antivirus scan date.", "group": "entity_specific", "type": "integer"}, "av_signature_update_sla_duration": {"caption": "AV Signature Update SLA Duration", "description": "The maximum allowable duration  defined by the SLA for updating antivirus signature definitions.", "group": "entity_specific", "type": "integer"}, "av_scan_sla_duration": {"caption": "AV Scan SLA Duration", "description": "The maximum allowable time duration, as defined by the SLA, within which an antivirus scan must be completed on a system or set of systems.", "group": "entity_specific", "type": "integer"}, "fw_status": {"caption": "Firewall Enabled", "description": "Indicates the firewall scan completion status of a host.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "vulnerability_last_observed_date": {"caption": "Vulnerability Last Observed", "description": "The time at which the vulnerability was last observed for a cloud resource.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true, "category": "Security and Compliance"}, "vm_product": {"caption": "VM Product", "description": "The product used for Vulnerability Management on the cloud resource.Example Qualys.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "vm_onboarding_status": {"caption": "VM Onboarding Status", "description": "Indicates whether the VM agent is onboarded on the cloud resource or not.Distinct values are true and false.", "examples": "False", "group": "entity_specific", "type": "string", "category": "Security and Compliance"}, "vm_tracking_method": {"caption": "VM Tracking Method", "description": "Refers to how assets, particularly virtual machines (VMs), are identified, monitored, and tracked across scans to ensure vulnerabilities are properly assessed and reported over time.", "examples": "False", "group": "entity_specific", "type": "string", "data_structure": "list"}, "vm_last_scan_date": {"caption": "VM Last Scan Date", "description": "Last scanned date of the cloud resource by the VM agent.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true}, "purchase_plan_name": {"caption": "Plan Name", "description": "Service plans or resource plans that define the capacity, features, and other settings for various Azure services.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "properties": {"caption": "Properties", "description": "Set of attributes that describe specific details or configurations associated with a cloud resource.", "group": "entity_specific", "type": "string", "ui_visibility": false, "data_structure": "struct"}, "billing_tag": {"caption": "Billing Tag", "description": "Custom tag field to classify the resources based on the billing criteria.Example sol-sds3-perfext.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "is_accessible_from_internet": {"caption": "Internet Exposure", "description": "Indicates whether this resource is accessible from at least one internet address.Distinct values are true and false.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Network"}, "has_high_privileges": {"caption": "Has High Privileges", "description": "This property indicated whether this VM has high privilege.High permissions are derived from workflow disruption - permissions that allow the deletion of resources that might disrupt the workflow.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Compliance"}, "has_admin_privileges": {"caption": "<PERSON> <PERSON><PERSON> Privileges", "description": "This property indicated whether this VM is has admin Privilage.Admin permissions are defined as permissions that can allow an attacker persistence in the environment. These can be IAM permissions to provision, create, delete, or update identities or wild card permissions on the subscription/account/project level.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Compliance"}, "open_to_all_internet": {"caption": "Open To All Internet", "description": "Indicates whether the cloud resource is accessible from the public internet.Distinct values are true and false.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Network"}, "has_sensitive_data": {"caption": "Has Sensitive Info", "description": "This property indicates whether the VM has sensitive data contained within", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Compliance"}, "organisational_unit": {"caption": "Organizational Unit", "description": "Logical grouping or container within Active Directory (on-premises or Azure AD Domain Services) that helps to organize and manage resources, including Azure VM", "group": "entity_specific", "type": "string", "ui_visibility": true}, "project": {"caption": "Project", "description": "Tag to categorize and identify resources associated with a specific project. By applying a project tag to a cloud resource, we can streamline management, cost tracking, and reporting based on the project the cloud resource is part of", "group": "entity_specific", "type": "string", "ui_visibility": true}, "instance_imdsv2_status": {"caption": "Instance Metadata Version", "description": "IMDSv2 uses token-backed sessions. Indicates whether the use of HTTP tokens is optional (in other words, indicates whether the use of IMDSv2 is optional) or required (in other words, indicates whether the use of IMDSv2 is required).", "group": "entity_specific", "type": "string", "ui_visibility": true}, "is_container_host": {"caption": "Is Container Host", "description": "Indicates whether the resource is used to host containers in managed Kubernetes clusters.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Compliance"}, "wiz_id": {"caption": "Wiz ID", "description": "Unique ID assigned by Wiz to its cloud resources", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_resource_created_date": {"caption": "Azure Resource Created Date", "description": "Refers to the timestamp when the Azure resource was initially created.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_vmss_vm_count": {"caption": "Azure VMSS VM Count", "description": "Specifies the number of Azure virtual machines in the virtual machine scale set.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "azure_vmss_vm_size": {"caption": "Azure VMSS VM Size", "description": "Specific configuration or size of an Azure virtual machine within a virtual machine scale sets.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_system_data": {"caption": "Azure System Data", "description": "Metadata associated with a Azure resource that provides information about the resource's lifecycle, status etc.", "group": "source_specific", "type": "string", "ui_visibility": false, "data_structure": "struct"}, "azure_tags": {"caption": "Azure Tags", "description": "Metadata elements associated with Azure resources, represented as key-value pairs.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "azure_public_network_access": {"caption": "Azure Public Network Access", "description": "Azure Public Network Access refers to the ability of resources within the Azure environment to be accessed via the public internet, enabling external connectivity and interaction. The possible distinct values are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_operational_state": {"caption": "Azure Operational State", "description": "Last known operational state of the Azure resource. The possible values are 'Started', 'Connected' and 'Stopped'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_power_state": {"caption": "Azure AKS Power State", "description": "Indicates the current operational status of an Azure Kubernetes Service (AKS) cluster, showing whether it's actively running or in a suspended state. The possible vales are 'Running' and 'Stopped'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_dns_prefix": {"caption": "Azure AKS DNS Prefix", "description": "Azure AKS DNS Prefix is a user-defined label that forms part of the fully qualified domain name (FQDN) for an Azure Kubernetes Service (AKS) cluster. It helps create a unique and identifiable web address for accessing the AKS resources.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_private_fqdn": {"caption": "Azure AKS Private FQDN", "description": "Azure AKS Private FQDN refers to the fully qualified domain name assigned exclusively to an Azure Kubernetes Service (AKS) cluster within a private network, enabling internal access and communication.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_node_pool_profiles": {"caption": "Azure AKS Node Pool Profiles", "description": "The AKS agent pool properties.", "group": "source_specific", "type": "string", "ui_visibility": false, "data_structure": "struct"}, "azure_aks_node_pool_vm_sizes": {"caption": "Azure AKS Node Pool VM Sizes", "description": "Azure AKS Node Pool VM Sizes represent the specific configurations or sizes of virtual machines (VMs) within a designated node pool of an Azure Kubernetes Service (AKS) cluster. These sizes determine the computational capacity and resources allocated to the nodes in that pool.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "azure_aks_node_pool_type": {"caption": "Azure AKS Node Pool Type", "description": "Azure AKS Node Pool Type refers to the categorization or classification of a set of nodes within an Azure Kubernetes Service (AKS) cluster based on specific characteristics or purposes. The type helps define the role and functionality of the nodes within that particular pool.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "azure_aks_node_pool_node_image_version": {"caption": "Azure AKS Node Pool Node Image Version", "description": "The version of the operating system and container runtime image used by the nodes within a specific node pool in an Azure Kubernetes Service (AKS) cluster.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "azure_aks_node_resource_group": {"caption": "Azure AKS Node Resource Group", "description": "The name of the resource group containing agent pool nodes.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_enable_rbac": {"caption": "Azure AKS Enable RBAC", "description": "Azure AKS RBAC is a security feature that enables fine-grained control over permissions within an Azure Kubernetes Service (AKS) cluster. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_max_node_pools": {"caption": "Azure AKS Max Node Pools", "description": "The maximum number of node pools that can be created within an Azure Kubernetes Service (AKS) cluster.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "azure_aks_node_pool_count": {"caption": "Azure AKS Node Pool Count", "description": "The number of node pools configured within an Azure Kubernetes Service (AKS) cluster.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "azure_aks_node_pool_maximum_vm_count": {"caption": "Azure AKS Node Pool max VM Count", "description": "The maximum limit of virtual machines (VMs) that can be provisioned across all the nodepools within an Azure Kubernetes Service (AKS) cluster.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "azure_aks_node_pool_maximum_pod_count": {"caption": "Azure AKS Node Pool Max Pod Count", "description": "The maximum number of pods that can be scheduled across all nodes within an Azure Kubernetes Service (AKS) cluster.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "azure_aks_image_cleaner_enabled_status": {"caption": "Azure AKS Image Cleaner Enabled Status", "description": "Indicates whether the image cleaning feature is enabled or disabled in an Azure Kubernetes Service (AKS) cluster. Image cleaning involves removing unused container images from the nodes, helping to optimise storage and resource usage. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_auto_upgrade_type": {"caption": "Azure AKS Auto Upgrade Type", "description": "Azure Kubernetes Service provides multiple auto-upgrade channels dedicated to timely node-level OS security updates. This channel is different from cluster-level Kubernetes version upgrades and supersedes it. The possible vales are 'node-image', 'none', 'patch', 'rapid' and 'stable'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_storage_snapshot_controller_enabled_status": {"caption": "Azure AKS Snapshot Controller Enabled Status", "description": "Indicates whether the snapshot controller feature is enabled or disabled in an Azure Kubernetes Service (AKS) cluster. The snapshot controller allows for the creation and management of volume snapshots in the context of AKS, enhancing data persistence and resilience. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_defender_status": {"caption": "Azure AKS Defender Status", "description": "Indicates the current operational state of the Azure Defender for Kubernetes within an Azure Kubernetes Service (AKS) cluster, showing whether this security feature is active and providing protection. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_workload_identity_status": {"caption": "Azure AKS Workload Identity Status", "description": "Azure AKS (Azure Kubernetes Service) Workload Identity enables secure access to other Azure resources from within your AKS cluster without the need for explicit credentials. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_support_plan": {"caption": "Azure AKS Support Plan", "description": "A Kubernetes Support Plan refers to a structured service offered by a provider (like cloud service providers or specialized Kubernetes support companies) to assist organizations in managing and operating their Kubernetes environments. These plans typically include technical support, expert guidance, troubleshooting, and sometimes, tools for managing Kubernetes clusters more effectively. The possible values are 'AKSLongTermSupport' and 'KubernetesOfficial'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_aad_profile_admin_group_object_ids": {"caption": "Azure AKS AAD Profile Admin Group Object ID", "description": "The list of AAD group object IDs that will have admin role of the Azure cluster.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "azure_aks_azure_rbac_status": {"caption": "Azure AKS Azure RBAC Status", "description": "Refers to a configuration or setting related to enabling Azure RBAC (Role-Based Access Control) within the AKS cluster. Azure RBAC is a security feature that allows fine-grained control over access to Azure resources, including those within a Kubernetes cluster. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_key_vault_secrets_status": {"caption": "Azure AKS Key Vault Secrets Status", "description": "Indicates the current state of integration between an Azure Kubernetes Service (AKS) cluster and Azure Key Vault for managing secrets. This status reflects whether the AKS cluster is configured to retrieve and use secrets securely stored in Azure Key Vault. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_azure_policy_status": {"caption": "Azure AKS Azure Policy Status", "description": "Denotes the current operational condition of Azure Policy within an Azure Kubernetes Service (AKS) cluster, indicating whether policies for resource compliance and governance are actively enforced and monitored. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_oms_agent_status": {"caption": "Azure AKS OMS Agent Status", "description": "The Azure AKS OMS (Operations Management Suite) Agent is a component used to integrate Azure Kubernetes Service (AKS) clusters with Azure Monitor. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_monitor_enabled": {"caption": "Azure AKS Monitor Enabled", "description": "Field indicates whether to enable or disable the Azure Managed Prometheus addon for Prometheus monitoring.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aci_instance_state": {"caption": "Azure ACI Instance State", "description": "It indicates whether the ACI instance is running, stopped, or in another operational state, providing insight into the current condition of the containerized workload deployed in Azure. The possible values are 'Running' and 'Stopped'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aci_restart_policy": {"caption": "Azure ACI Restart Policy", "description": "Restart policy for all containers within the container group.Always Always restart OnFailure Restart on failure Never Never restart.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aci_container_services_active_containers_count": {"caption": "Azure ACI Active Containers Count", "description": "Count of active containers in the Azure ACI Environment.", "group": "entity_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "azure_resource_last_modified_date": {"caption": "Azure Resource Modified", "description": "Resource last modified time.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_plan": {"caption": "Azure Plan", "description": "Details of Azure subscription plan.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "azure_region": {"caption": "Azure Region", "description": "Specific geographical area where Azure data centers are located.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_availability_zone": {"caption": "Azure Availability Zone", "description": "Specific geographical area where Azure data centers are located within the region.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aci_finish_date": {"caption": "Azure ACI Finish Date", "description": "", "group": "source_specific", "type": "string", "ui_visibility": false}, "aws_operational_state": {"caption": "AWS Operational State", "description": "Last known status of that resource within its lifecycle.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_tags": {"caption": "AWS Tags", "description": "Metadata elements associated with resources, represented as key-value pairs.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "aws_tag": {"caption": "AWS Tag", "description": "Metadata elements associated with resources, represented as key-value pairs.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_resource_created_date": {"caption": "AWS Resource Created Date", "description": "Refers to the timestamp when the resource was initially created.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "aws_resource_configuration_change_date": {"caption": "AWS Resource Configuration Change Date", "description": "The time when the recording of configuration changes was initiated for the resource.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "aws_lambda_memory_size": {"caption": "AWS Lambda Memory Size", "description": "The amount of memory available to the function at runtime.The default value is 128 MB.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_lambda_runtime": {"caption": "AWS Lambda Runtime", "description": "Programming language and version used for the lambda function.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_lambda_last_modified_date": {"caption": "AWS Lambda Last Modified", "description": "The time that specifies any modification done to the Lambda resource.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "aws_lambda_code_size": {"caption": "AWS Lambda Code Size", "description": "The value represents the compressed size of the code and dependencies that are uploaded to AWS Lambda when you create or update the function in bytes.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true, "step_interval": 10000}, "aws_eks_endpoint_private_access": {"caption": "AWS EKS Endpoint Private Access", "description": "Set this value to true to enable private access for your cluster's Kubernetes API server endpoint. If you enable private access, Kubernetes API requests from within your cluster's VPC use the private VPC endpoint.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_eks_endpoint_public_access": {"caption": "AWS EKS Endpoint Public Access", "description": "Set this value to false to disable public access to your cluster's Kubernetes API server endpoint. If you disable public access, your cluster's Kubernetes API server can only receive requests from within the cluster VPC.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_eks_network_ip_family": {"caption": "AWS EKS Network IP Family", "description": "Specify which IP family is used to assign Kubernetes pod and service IP addresses.Possible values are ipv4 and ipv6.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_eks_cluster_security_group_id": {"caption": "AWS EKS Cluster Security Group ID", "description": "Unique identifier associated with the security group of the EKS Cluster.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ecs_cluster_arn": {"caption": "AWS ECS Cluster ARN", "description": "Amazon resource name (ARN) assigned to the ECS Cluster.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ecs_service_name": {"caption": "AWS ECS Service Name", "description": "User-defined identifier for the Elastic Container service. It is specified when the service is created.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ecs_platform_version": {"caption": "AWS ECS Platform Version", "description": "The platform version that tasks in the service are running on.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ecs_scheduling_strategy": {"caption": "AWS ECS Scheduling Strategy", "description": "Refers to how tasks associated with the service are placed on container instances within the ECS cluster.Possible scheduling strategies are REPLICA and DAEMON.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ecs_launch_type": {"caption": "AWS ECS Launch Type", "description": "Property that decides the underlying infrastructure for executing the tasks within the ECS Cluster, determining whether they run on EC2 instances or on AWS Fargate.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ecs_assign_public_ip": {"caption": "AWS ECS Assign Public IP", "description": "Part of the task definition that specifies whether a public IP address should be automatically assigned to the Elastic Network Interface (ENI) of a task.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_autoscaling_instance_max_size": {"caption": "AWS Autoscaling Instance Max Si<PERSON>", "description": "Represents the maximum number of instances that the Auto Scaling group can scale out to. It specifies the upper limit on the number of instances that can be running in the group at any given time.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_autoscaling_instance_types": {"caption": "AWS Autoscaling Instance Types", "description": "Specifies the Instance type for the instances launched by the Auto Scaling group. The instance type defines the hardware of the instances, including the CPU, memory, storage, and network capacity.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "aws_autoscaling_instance_count": {"caption": "AWS Autoscaling Instance Count", "description": "Number of instances running are associated with autoscaling group.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_autoscaling_instance_min_size": {"caption": "AWS Autoscaling Instance Min size", "description": "Represents the minimum number of instances that the Auto Scaling group can scale out to. It specifies the lowerlimit on the number of instances that can be running in the group at any given time.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_ec2fleet_valid_from": {"caption": "AWS EC2 Fleet Valid From", "description": "Time when the EC2 Fleet becomes active or starts launching instances.", "group": "source_specific", "type": "timestamp", "ui_visibility": false}, "aws_ec2fleet_valid_until": {"caption": "AWS EC2 Fleet Valid Until", "description": "Timestamp when the EC2 Fleet's launch activity will expire, and the fleet will no longer launch instances.", "group": "source_specific", "type": "timestamp", "ui_visibility": false}, "aws_ec2fleet_terminate_instances": {"caption": "AWS EC2 Fleet Terminate Instances", "description": "Indicates whether running instances should be terminated when the EC2 Fleet expires.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ec2fleet_replace_unhealthy_instances": {"caption": "AWS EC2 Fleet Replace Unhealthy Instances", "description": "Indicates whether EC2 Fleet should replace unhealthy Spot Instances. Supported only for fleets of type maintain'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ec2fleet_override_instance_type": {"caption": "AWS EC2 Fleet Override Instance Type", "description": "Allows to customize the instance type on a per-instance basis by overriding the default instance type specified in the associated Launch Template.", "group": "source_specific", "type": "string", "data_structure": "list", "ui_visibility": true}, "aws_ec2fleet_total_target_capacity": {"caption": "AWS EC2 Fleet Total Target Capacity", "description": "Represents the desired number of instances across all specified instance types within your EC2 Fleet configuration.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_ec2fleet_on_demand_target_capacity": {"caption": "AWS EC2 Fleet On Demand Target Capacity", "description": "Specifies the target capacity for On-Demand instances within the fleet configuration.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_ec2fleet_spot_target_capacity": {"caption": "AWS EC2 Fleet Spot Target Capacity", "description": "Specify the target capacity for Spot Instances within the fleet configuration.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_ec2fleet_default_target_capacity_type": {"caption": "AWS EC2 Fleet Default Target Capacity type", "description": "This is used to set the default target capacity type for an EC2 Fleet. It determines whether the default capacity type is On-Demand or Spot Instances.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ec2fleet_type": {"caption": "AWS EC2 Fleet Type", "description": "Type of the fleet.Possible values are maintain,request and instant.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ec2fleet_provisioned_on_demand_capacity": {"caption": "AWS EC2 Fleet Provisioned On-Demand Capacity", "description": "The number of On-Demand units that have been provisioned for the instance fleet to fulfill TargetOnDemandCapacity. This provisioned capacity might be less than or greater than TargetOnDemandCapacity.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_ec2fleet_provisioned_spot_capacity": {"caption": "AWS EC2 Fleet Provisioned Spot Capacity", "description": "The number of Spot units that have been provisioned for this instance fleet to fulfill TargetSpotCapacity. This provisioned capacity might be less than or greater than TargetSpotCapacity.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_autoscaling_override_mixed_instance_type": {"caption": "AWS Autoscaling Override Mixed Instance Type", "description": "In AWS Auto Scaling, the term Mixed Instances Override Instance Type refers to a configuration option that allows you to specify different instance types within an Auto Scaling group. An Auto Scaling group dynamically adjusts the number of Amazon EC2 instances to maintain application availability and meet defined capacity requirements.", "group": "source_specific", "type": "string", "data_structure": "list", "ui_visibility": true}, "aws_region": {"caption": "AWS Region", "description": "Specific geographical area where AWS data centers are located.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_availability_zone": {"caption": "AWS Availability Zone", "description": "Availability zones are separated groups of datacenters within an AWS region.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_master_security_group_id": {"caption": "AWS EMR Master Security Group", "description": "The identifier of the Amazon EC2 security group for the master node.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_slave_security_group_id": {"caption": "AWS EMR Slave Security Group", "description": "The identifier of the Amazon EC2 security group for the core and task nodes.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_service_security_group_id": {"caption": "AWS EMR Service Security Group", "description": "The identifier of the Amazon EC2 security group for the Amazon EMR service to access clusters in VPC private subnets.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_instance_collection_type": {"caption": "AWS EMR Instance Collection Type", "description": "The instance group configuration of the cluster. A value of INSTANCE_GROUP indicates a uniform instance group configuration. A value of INSTANCE_FLEET indicates an instance fleets configuration.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_version": {"caption": "AWS EMR Version", "description": "The Amazon EMR release label, which determines the version of open-source application packages installed on the cluster. Release labels are in the form emr-x.x.x, where x.x.x is an Amazon EMR release version such as emr-5.14.0.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_auto_terminate": {"caption": "AWS EMR Auto Terminate", "description": "Specifies whether the AWS EMR Cluster should terminate after completing all steps. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_visible_to_users": {"caption": "AWS EMR Visible to All Users", "description": "Indicates whether the cluster is visible to IAM principals in the Amazon Web Services account associated with the cluster. When true, IAM principals in the Amazon Web Services account can perform Amazon EMR cluster actions on the cluster that their IAM policies allow. When false, only the IAM principal that created the cluster and the Amazon Web Services account root user can perform Amazon EMR actions, regardless of IAM permissions policies attached to other IAM principals.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_applications": {"caption": "AWS EMR Cluster Applications", "description": "The applications installed on this cluster. The possible values are like Hadoop 3.14, Spark 3.34", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_volume": {"caption": "AWS EMR Cluster Volume", "description": "The size, in GiB, of the Amazon EBS root device volume of the Linux AMI that is used for each Amazon EC2 instance.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_emr_cluster_name": {"caption": "AWS EMR Cluster Name", "description": "The name of the AWS EMR Cluster.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_end_date": {"caption": "AWS EMR End Date", "description": "", "group": "source_specific", "type": "string", "ui_visibility": false}, "aws_emr_cluster_arn": {"caption": "AWS EMR Cluster ARN", "description": "An AWS EMR Cluster ARN (Amazon Resource Name) is a unique identifier that represents an Amazon Elastic MapReduce (EMR) cluster in AWS.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_emr_instance_fleet_type": {"caption": "AWS EMR Instance Fleet Type", "description": "The node type that the instance fleet hosts. Valid values are MASTER, CORE, or TASK.", "group": "source_specific", "type": "string", "ui_visibility": true}, "defender_onboarding_date": {"caption": "Defender Onboarding Date", "description": "", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "defender_onboarding_status": {"caption": "Defender Onboarding Status", "description": "Defender onboarding status of host.", "examples": "Onboarded", "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false}, "defender_id": {"caption": "Defender ID", "description": "This ID is used to track and manage individual devices within Microsoft Defender.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "defender_detection_method": {"caption": "Defender Detection Method", "description": "Method of detection in defender.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "aad_device_id": {"caption": "AAD Device ID", "description": "The unique identifier for the device in Azure AD.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "defender_action_type": {"caption": "Defender Action Type", "description": "It determines how the antivirus software responds when it detects a potential threat.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "defender_threat_name": {"caption": "Defender Threat Name", "description": "The threat names found by MS Defender.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "defender_threat_count": {"caption": "Defender Infection Count", "description": "The number of threats that have been detected by Microsoft Defender.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "defender_exposure_level": {"caption": "Defender Exposure Level", "description": "Exposure level as evaluated by Microsoft Defender for Endpoint.", "group": "source_specific", "type": "string", "ui_visibility": true}, "qualys_detection_method": {"caption": "Qualys Detection Method", "description": "Method of detection in qualys.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "aws_sagemaker_configuration_id": {"caption": "AWS Sagemaker Configuration ID", "description": "Identifier associated with a specific instance configuration.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_notebook_instance_name": {"caption": "AWS Sagemaker Notebook Instance Name", "description": "The name of the notebook instance.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_notebook_instance_storage_size": {"caption": "AWS Sagemaker Notebook Instance Storage Size", "description": "The size, in GB, of the ML storage volume to attach to the notebook instance.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_sagemaker_default_code_repository": {"caption": "AWS Sagemaker Default Code Repository", "description": "The Git repository associated with the notebook instance as its default code repository.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_direct_internet_access_status": {"caption": "AWS Sagemaker Direct Internet Access Status", "description": "Sets whether SageMaker provides internet access to the notebook instance.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_role_arn": {"caption": "AWS Sagemaker Role ARN", "description": "The role that <PERSON><PERSON> assumes to perform the tasks when a request is sent to AWS resources fro the notebook instances.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_root_access_status": {"caption": "AWS Sagemaker Root Access Status", "description": "Whether root access is enabled or disabled for users of the notebook instance.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_lifecycle_config_name": {"caption": "AWS Sagemaker Lifecycle Configuration Name", "description": "The name of a lifecycle configuration to associate with the notebook instance.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_model_name": {"caption": "AWS Sagemaker Model Name", "description": "Name of the SageMaker model.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_network_isolation_status": {"caption": "AWS Sagemaker Network Isolation Status", "description": "Indicating whether network isolation is enabled.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_execution_role_arn": {"caption": "AWS Sagemaker Execution Role ARN", "description": "ARN of the IAM role used for execution.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_model_primary_container_image": {"caption": "AWS Sagemaker Model Primary Container Image", "description": "The Docker image URI for the primary container that runs the model.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_model_primary_container_mode": {"caption": "AWS Sagemaker Model Mode", "description": "Specifies the mode used by the model container.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_model_data_url": {"caption": "AWS Sagemaker Model Data URL", "description": "The location of the model artifacts in Amazon S3.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_model_environment_config": {"caption": "AWS Sagemaker Model Environment Configuration", "description": "Environment variables passed to the model container.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "aws_sagemaker_model_available_gpu": {"caption": "AWS Sagemaker Model Available GPU", "description": "Specifies the number of GPUs available for processing.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_model_cache_root": {"caption": "AWS Sagemaker Model Cache Directory Name", "description": "The location where the model files are stored on the container.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_sagemaker_model_datasource_s3_uri": {"caption": "AWS Sagemaker Model Datasource S3 URI", "description": "Specifies the data source for the model which is located in Amazon S3.", "group": "source_specific", "type": "string", "ui_visibility": true}, "wiz_operational_state": {"caption": "Wiz Operational State", "description": "Reflects the operational state of the resource, possible values are Active,Inactive and Error.", "group": "source_specific", "type": "string", "ui_visibility": true}, "wiz_onboarding_date": {"caption": "Wiz Onboarding Date", "description": "On boarding date respective to Wiz.", "group": "source_specific", "type": "timestamp"}, "wiz_is_cloud_managed": {"caption": "Is Managed by Cloud As Per Wiz", "description": "This property indicated whether this VM was launched by a service. This includes: Azure Scale Sets, GCP Instance Groups, and any AWS VM with a non-empty requested ID (for example, AWS Management Console, Auto Scaling, and so on).", "group": "source_specific", "type": "string", "ui_visibility": true}, "wiz_last_scan_date": {"caption": "Wiz Last Scan Date", "description": "Last scan date of a machine by <PERSON><PERSON>.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "wiz_modified_date": {"caption": "Wiz Modified Date", "description": "Modified date of wiz.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "wiz_onboarding_status": {"caption": "Wiz Onboarding Status", "description": "Wiz coverage on a machine.", "group": "source_specific", "type": "string", "ui_visibility": true}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "description": "Number of vulnerability findings associated with cluster.", "group": "enrichment", "type": "integer", "range_selection": true}, "has_open_vulnerability_finding_count": {"caption": "Count of Open Vulnerability Findings", "description": "Number of open vulnerability findings associated with cluster", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_cloud_account_count": {"caption": "Count of Cloud Account", "description": "Number of cloud accounts associated with cluster.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_volume_count": {"caption": "Count of Volume", "description": "Number of volume associated with virtual machine.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_open_finding_count": {"caption": "Count of Open Findings", "description": "Number of open findings associated with Cluster resource.", "group": "enrichment", "type": "integer", "range_selection": true}, "tenable_io_id": {"caption": "Tenable.io ID", "description": "The UUID of the asset in Tenable.io.This value is the unique key for the asset.", "group": "source_specific", "type": "string"}, "tenable_io_onboarding_date": {"caption": "Tenable.io Onboarding Date", "description": "Indicating the onboarding date of the asset within Tenable.io.", "group": "source_specific", "type": "timestamp"}, "tenable_io_asset_agent_status": {"caption": "Tenable.io Asset Agent Status", "description": "Specifies whether a Nessus agent scan identified the asset.", "group": "source_specific", "type": "string"}, "tenable_io_asset_plugin_status": {"caption": "Tenable.io Asset Plugin Status", "description": "Specifies whether the asset has plugin results associated with it.", "group": "source_specific", "type": "string"}, "tenable_io_asset_updated_at": {"caption": "Tenable.io <PERSON> Updated Date", "description": "The time and date when the asset record was last updated.", "group": "source_specific", "type": "timestamp"}, "tenable_io_last_scan_date": {"caption": "Tenable.io <PERSON> Last Scan Date", "description": "The time and date of the last scan run against the asset.", "group": "source_specific", "type": "timestamp"}, "tenable_io_last_authenticated_scan_date": {"caption": "Tenable.io Last Autheticated Scan Date", "description": "The time and date of the last credentialed scan run on the asset.", "group": "source_specific", "type": "timestamp"}, "tenable_io_asset_last_licensed_scan_date": {"caption": "Tenable.io Last Licensed Scan Date", "description": "The time and date of the last scan that identified the asset as licensed. Tenable Vulnerability Management categorizes an asset as licensed if a scan of that asset has returned results from a non-discovery plugin within the last 90 days.", "group": "source_specific", "type": "timestamp"}, "tenable_io_asset_aws_terminated_date": {"caption": "Tenable.io AWS Terminated Date", "description": "The time and date when a user terminated the Amazon Web Service (AWS) virtual machine instance of the asset.", "group": "source_specific", "type": "timestamp"}, "tenable_io_asset_source_name": {"caption": "Tenable.io Asset Source Name", "description": "The name of the entity that reported the asset details. Sources can include sensors, connectors, and API imports.", "group": "source_specific", "type": "string", "data_structure": "list"}, "tenable_io_asset_license_status": {"caption": "Tenable.io Asset License Status", "description": "Indicates whether the asset was licensed at the time of the identified scans.", "group": "source_specific", "type": "string"}, "tenable_io_ipv4_addresses": {"caption": "Tenable.io IPv4 Address", "description": "The IPv4 addresses that scans have associated with the asset record.", "group": "source_specific", "type": "string", "data_structure": "list"}, "tenable_io_ipv6_addresses": {"caption": "Tenable.io IPv6 Address", "description": "The IPv6 addresses that scans have associated with the asset record.", "group": "source_specific", "type": "string", "data_structure": "list"}, "tenable_io_system_type": {"caption": "Tenable.io System Type", "description": "The system types as reported by Plugin ID 54615. Nessus Plugin ID 54615 is associated with the 'System Type Enumeration' plugin. This plugin is designed to determine the type of system based on various characteristics observed during a vulnerability scan.", "group": "source_specific", "type": "string", "data_structure": "list"}, "aws_vpc_id": {"caption": "AWS VPC ID", "description": "The unique identifier for the virtual public cloud that hosts the AWS virtual machine instance.", "group": "source_specific", "type": "string"}, "tenable_io_onboarding_status": {"caption": "Tenable.io Onboarding Status", "description": "Tenable.io coverage of the asset.", "group": "source_specific", "type": "string"}, "tenable_io_agent_uuid": {"caption": "Tenable.io Agent UUID", "description": "The UUID of the agent that performed the scan where the vulnerability was found", "group": "source_specific", "type": "string"}, "archival_flag": {"caption": "Archival Flag", "description": "The Archival Flag indicates whether the Cloud Compute is set to be archived or not.", "group": "enrichment", "examples": "", "type": "string", "dashboard_identifier": {"EI": {}}}}, "dashboard_identifier": "EI"}