{"caption": "Assessment", "entity_classification": "Supporting Entity", "description": "Control assessment involves evaluating the effectiveness and implementation of security controls within an organization.", "navigator_enabled": true, "navigator_description": "\n# Assessment Table Summary\n\n## Overview\nThe Assessment table serves as a structured framework for evaluating and monitoring security controls, compliance requirements, and risk factors across an organization's environment. It provides a systematic approach to identifying vulnerabilities, misconfigurations, and control gaps with detailed information about evaluation criteria, severity classifications, and scope definitions.\n\n## Data Categories\n\n### Identity & Classification\n- **Assessment Identifiers**: Unique IDs and reference codes (e.g., AST-01-01)\n- **Assessment Titles**: Descriptive names identifying each security evaluation\n- **Type Classification**: Categories like Endpoint Exposure, Software Exposure\n- **Origin Details**: Data sources required for conducting assessments (AWS, MS Azure, etc.)\n\n### Descriptive Content\n- **Assessment Description**: Comprehensive explanations of evaluation objectives and benefits\n- **Display Labels**: Derived identifiers for referencing assessments\n- **Scope Validation Steps**: Specifies the validation steps of the assessment that is used to evaluate success/failure\n- **Scope Entity**: Specifies the entity being evaluated in the assessment, helps define the boundaries of evaluation coverage\n\n### Exposure Information\n- **Exposure Category**: Classification of the type of security exposure (Software Vulnerability, Control Gap, Threat Detection)\n- **Assessment Category**: Classification system for organizing types of risks (Endpoint Exposure, Software Exposure)\n- **Assessment Severity**: Importance rating from Critical to Low\n- **Assessment Weightage**: Coefficient (1-10) determining contribution to overall scoring\n\n### Status & Lifecycle\n- **Activity Status**: Current operational state (active/inactive)\n- **Temporal Information**: First found, last updated dates\n- **Lifecycle Metrics**: Lifetime duration and observation timeline\n- **Recent Activity**: Time elapsed since last activity\n\n### Operational Context\n- **Contributing Module**: Whether assessment contributes to Exposure scoring, Reporting, or both\n- **Scope Definition**: Parameters defining what is evaluated for success/failure determination\n- **Assessment Parameters**: Specific criteria and thresholds for evaluation\n\n### Data Provenance\n- **Origin Count**: Number of data sources contributing to the assessment\n- **Data Feed**: Specific API endpoints providing assessment data\n- **Source Attribution**: Original systems used for assessment evaluation\n\n## Key Features\n\n1. **Comprehensive Control Evaluation**: Assesses security measures across various dimensions and control types\n\n2. **Severity-Based Classification**: Categorizes assessments based on their importance to security and compliance\n\n3. **Flexible Evaluation Framework**: Adapts to different security domains and control types\n\n4. **Weighted Scoring System**: Provides configurable impact on overall security scoring\n\n5. **Multi-source Integration**: Incorporates assessment data from various security tools and platforms\n\n6. **Temporal Tracking**: Maintains historical timeline of assessment changes and updates\n\n7. **Modular Contribution**: Designates whether assessments affect exposure scoring, reporting, or both\n\n8. **Scope Definition**: Clearly defines the boundaries and applicability of each assessment\n\nThis Assessment table functions as the foundation for security evaluation processes, providing the structured criteria needed for identifying security gaps, measuring compliance, and tracking control effectiveness across the organization's environment.\n\nIMPORTANT: Query: Show assessments with critical severity, filter for assessment will be 'assessment_severity = Critical' because the filter restricts assessments to only critical ones. End user assessment is a filter.\n", "navigator_graph_node_description": "The Assessment entity represents structured evaluations of security controls, configurations, and vulnerabilities within an organization's IT infrastructure. It captures key attributes such as assessment ID (e.g., AST-01-01), title, description, severity classification, and exposure category. Each assessment defines evaluation criteria for identifying security gaps, measuring compliance, and assessing risk posture across various domains including endpoint security, software vulnerabilities, and control implementation. Assessments maintain lifecycle information from creation through updates and include configurable weightage that determines their contribution to overall security scoring. They define the framework that generates findings when security issues are identified, providing the basis for remediation prioritization and compliance tracking.", "navigator_entity_description": "\nAn Assessment is a structured evaluation framework used to measure the effectiveness, implementation, and compliance of security controls within an organization's IT infrastructure. Assessments define the criteria and methodologies for identifying security gaps, vulnerabilities, misconfigurations, and compliance issues. \n\nFor example, the following data corresponds to the various steps used to validate the assessment.\n\n'assessment title'==>'Steps used to validate the assesssment'                                                                                                                                                                       \n'Devices have endpoint protection signatures frequently updated'==>'Select Active Server and Workstation from Host,Validate that AV Signature Update SLA Breach Status is false'                                                                               \n'Windows devices are configured with right User Account Control (UAC)'==>'Select Active Server and Workstation from Host where OS Family is Windows,Validate that AD UAC Compliance Status is true'                                                                \n'Devices do not have vulnerabilities related to expired SSL certificates'==>'Select Active Mobile, Network Device, Server and Workstation from Host,Validate that SSL Certificate Expired Status is false'                                                             \n'Devices have a single assigned owner'==>'Select Active Workstation from Host,Validate that Active Owner Count is one'                                                                                                               \n'Software vulnerabilities on network devices are remediated within SLA'==>'Select Active Network Device from Host,Use Host Has Vulnerability Finding relationship from Graph filter,Validate that Vulnerability Patch SLA Breach Status is false'                 \n'Software vulnerabilities on external devices are remediated within SLA'==>'Select Active External Server and Workstation from Host,Use Host Has Vulnerability Finding relationship from Graph filter,Validate that Vulnerability Patch SLA Breach Status is false'\n'Devices enforce blocking of known malware'==>'Select Active Server and Workstation from Host,Validate that AV Block Malicious Code Status is true'                                                                                       \n'Devices have firewall protection enabled'==>'Select Active Server and Workstation from Host,Validate that Firewall Status is true'                                                                                                      \n'Devices do not have password related vulnerabilities'==>'Select Active Mobile, Network Device, Server and Workstation from Host,Validate that Password Vulnerability Count is zero'                                                                \n'Devices have an accountable owner'==>'Select Active Device from Host,Validate that Active Owner Count is greater than zero'                                                                                                      \n'Devices have endpoint protection agents onboarded'==>'Select Active Server and Workstation from Host,Validate that EDR Onboarding Status is true'                                                                                                \n'Devices are recorded in the appropriate CMDB'==>'Select Active Mobile, Network Device, Server and Workstation from Host,Validate that CMDB Onboarding Status is true'                                                                      \n'Devices employ full-disk encryption'==>'Select Active Mobile, Server and Workstation from Host,Validate that Full Disk Encryption Status is true'                                                                                  \n'Software vulnerabilities on internal devices are remediated within SLA '==>'Select Active Internal Server and Workstation from Host,Use Host Has Vulnerability Finding relationship from Graph filter,Validate that Vulnerability Patch SLA Breach Status is false'\n'Devices are regularly reconciled in the appropriate CMDB'==>'Select Active Mobile, Network Device, Server and Workstation from Host,Validate that CMDB Last Sync Status is true'                                                                       \n'Devices are scanned for malicious code at the defined frequency'==>'Select Active Server and Workstation from Host,Validate that AV Scan SLA Breach Status is false'                                                                                           \n'Devices follow approved naming conventions'==>'Select Active Mobile, Network Device, Server and Workstation from Host,Validate that Naming Convention Compliance Status is true'    \n\nThe Assessment entity contains essential details about evaluation objectives, criteria, and parameters. It includes attributes such as assessment ID (uniquely formatted as AST-xx-xx), assessment title, detailed description, and classification information. Each assessment is categorized by type and exposure category, providing context about the security domain being evaluated.  \n\nSeverity attributes help prioritize assessment importance. These include assessment severity ratings (Critical to Low) that classify the assessment based on its significance to security and compliance posture. Assessment weightage values (between 1 and 10) determine how much the assessment contributes to overall security scoring, with higher values indicating greater impact.  \n\nAssessments maintain temporal information tracking their lifecycle from creation through updates, including activity status and last active dates. They define specific scopes that establish the boundaries of evaluation of the assessment.  \n\nThe Assessment entity serves as the foundation for generating findings when security issues are identified. It aggregates information from various security tools and data sources, creating a structured approach to security evaluation across the organization.  \n\nIn addition, the Assessment entity includes specific quantitative attributes:  \n\n- Count of Origin: The total count of data sources that contribute to the assessment evaluation process.  \n- Assessment Weightage: Numerical value between 1 and 10 that determines the assessment's contribution to overall security scoring.  \n- Recent Activity: Number of days since the assessment was last updated or evaluated.  \n- Lifetime: Duration in days that the assessment has been active in the system.  \n    - When a user queries for maximum, minimum, highest, lowest, or filtered values (e.g., greater than or less than) related to the above quantitative attributes, results should focus only on the Assessment entity without including other entities for that specific part of the query. However, for other aspects of the query, additional entities may be included as needed. \n    - The above quantitative attributes should not be used when calculating the total count of assessments to avoid redundancy and we dont have *sum* operation support.\n\n", "navigator_examples": ["User Query: 'Show all Assessments' Output: 'assessment'"], "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.", "group": "common", "type": "string", "category": "General Information", "internally_generated": true, "navigator_attribute_description": "Unique identifier that serves as the primary key for each Assessment record."}, "display_label": {"caption": "Display Label", "description": "The derived and \"best known\" identifier or name, based on the attribute that best uniquely identifies it. For assessment entity title, assessmentId will contribute to display label.", "group": "common", "type": "string", "category": "General Information", "navigator_attribute_description": "Human-readable title of assessment shown in the user interface, same as assessment title.", "navigator_is_full_value_required": true}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host,Compliance Standard, Security Control, Assessment etc.", "group": "common", "type": "string", "category": "General Information", "internally_generated": true, "navigator_disabled": true}, "type": {"caption": "Type", "description": "The type field categorizes compliance standards into distinct classifications, specifically focusing on regulatory frameworks associated with cloud service providers. Possible distinct values includes AWS Compliance Assessment, Azure Regulatory Compliance Assessment.", "group": "common", "type": "string", "category": "General Information", "navigator_attribute_description": "The assessment category.", "navigator_deep_thinking_categorical": true, "navigator_is_full_value_required": true}, "origin": {"caption": "Origin", "description": "Data source(s) from which the entity has been extracted. For example AWS, Qualys etc.", "group": "common", "type": "string", "category": "General Information", "data_structure": "list", "navigator_attribute_description": "Required data sources for conducting this assessment.", "navigator_is_full_value_required": true}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity is extracted.", "navigator_attribute_description": "Total number of data sources that contributed to this assessment.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested. It is the actual api name from which the data is ingested. Some of the examples for data feed in Host entity are Qualys Host List, MS Azure AD Devices etc.", "navigator_attribute_description": "Specific API endpoints that provided data for this assessment.", "group": "common", "examples": "", "category": "General Information", "ui_visibility": true, "type": "string", "data_structure": "list", "internally_generated": true}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data. This will be the minimum time at which the entity is observed within the scope of inventory run.", "navigator_attribute_description": "The initial date when this assessment was discovered during data ingestion.", "group": "common", "type": "timestamp", "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources. By default first seen date date is calculated based on the minimum value between the last active date and the first found date of the entity. If the data sources provides information regarding first seen activity of the entity for example AD created date in Host, they take precedence over the default logic.", "navigator_attribute_description": "The earliest date when this assessment was observed across all data sources.", "group": "common", "type": "timestamp", "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data. Defaults to First Found. If any of the relevant attribute changes within an entity then that date is considered as last updated date.", "navigator_attribute_description": "Most recent date when any attribute of this assessment was modified.", "group": "common", "type": "timestamp", "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data. This will be the maximum time at which the entity is observed within the scope of inventory run.", "navigator_attribute_description": "Most recent date when this assessment was observed during data ingestion.", "group": "common", "type": "timestamp", "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "The latest date on which the entity was active.", "navigator_attribute_description": "Most recent date when this assessment was actively run.", "group": "common", "type": "timestamp", "category": "General Information"}, "activity_status": {"caption": "Activity Status", "description": "Latest date on which entity was active as inferred from available data sources. This date is determined by considering the maximum value of dates contributed by each data source for the entity in question. This includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "navigator_attribute_description": "Current state of the assessment - Active if recently executed, Inactive if not.", "group": "common", "type": "string", "category": "General Information"}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "navigator_attribute_description": "Total duration in days between the first_seen_date and last_active_date.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "navigator_attribute_description": "Number of days since the assessment was last executed.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score": {"caption": "Completeness Quality Score", "description": "A metric representing the extent to which required data attributes for the entity are populated across ingested data sources. This score helps evaluate the completeness of the entity's data profile.", "group": "enrichment", "examples": "1", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "aggregated_quality_score": {"caption": "Aggregated Quality Score", "description": "A composite score derived from various quality metrics (such as completeness, correctness, and consistency) to assess the overall data quality of the entity.", "examples": "1", "group": "enrichment", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score_category": {"caption": "Completeness Quality Score Category", "description": "A categorical interpretation of the Completeness Quality Score that classifies the entity's data completeness into levels such as High, Medium, or Low (configurable). This helps in quickly identifying the priority or severity of incomplete data across entities, enabling better triaging and quality improvement decisions.", "examples": "High", "group": "enrichment", "type": "string", "range_selection": true, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "navigator_attribute_description": "Duration in days between first_found_date and last_found_date.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "navigator_attribute_description": "Number of days since the assessment was last discovered during data ingestion.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "description": {"caption": "Description", "description": "Detailed explanation of the entity. For example it includes details about purpose of the assessment.", "navigator_attribute_description": "Comprehensive explanation of the assessment's objectives and benefits.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "location_country": {"caption": "Location Country", "description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard. For example 'South Africa'.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information"}, "location_city": {"caption": "Location City", "description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard. For example 'Sydney'.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information"}, "department": {"caption": "Department", "description": "Tag used to identify or categorize the resource based on its association with a specific business department within an organization.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity. It is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "range_selection": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considered for updating the last updated date of an entity", "group": "common", "type": "string", "ui_visibility": false, "data_structure": "struct", "internally_generated": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive. For example 1 day.", "group": "common", "type": "integer", "ui_visibility": false}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "category": "General Information", "type": "string", "navigator_is_full_value_required": true}, "assessment_id": {"caption": "Assessment ID", "description": "Unique Identifier of the assessment. For example, In azure: 18bf29b3-a844-e170-2826-4e95d0ba4dc9, In AWS: securityhub-access-keys-rotated-fbbbbcb9. ", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "title": {"caption": "Title", "description": "Title refers to the name or description of a specific assessment. It summarizes the security issue or best practice that the assessment has identified, helping users quickly understand what aspect of their environment needs attention. For example, a title might be \"Enable Multi-Factor Authentication\" or \"Unencrypted Data Storage Detected,\" indicating the security concern or action to be taken.", "navigator_attribute_description": "Name of the security assessment that identified this finding.", "group": "enrichment", "type": "string", "ui_visibility": true, "category": "General Information", "navigator_is_full_value_required": true}, "tenant_id": {"caption": "Organization ID", "description": "The unique identifier of the Azure Active Directory instance or the unique identifier (ID) of an organization in AWS. For example in azure: 'd9b72e31-456c-4f8f-a367-e7f01234abcd' and in aws: 'o-abc123xyz'. ", "group": "entity_specific", "type": "string", "ui_visibility": true}, "associated_standards": {"caption": "Associated Standard", "description": "Complance Standards to which the assessment is associated with or belong to. For example 'MICROSOFT CLOUD SECURITY BENCHMARK'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Compliance and Security"}, "associated_controls": {"caption": "Associated Control", "description": "Security Controls to which the assessment is associated with or belong to. For example 'DP.5'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Compliance and Security"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud service provider to which the assessment belongs. Eg. AWS, Azure", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information", "navigator_is_full_value_required": true}, "assessment_severity": {"caption": "Assessment Severity", "description": "Level of impact or seriousness of an assessment. The possible values are Critical, High, Low, Medium, Informational.", "group": "enrichment", "type": "string", "ui_visibility": true, "category": "General Information", "navigator_is_full_value_required": true}, "status": {"caption": "Status", "description": "Operational status of an assessment. The possible values are 'Enabled' and 'Disabled'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "scope": {"caption": "<PERSON><PERSON>", "description": "Specifies the scope of the Assessment used to evaluate success and failure.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "policy_definition_id": {"caption": "Policy Definition ID", "description": "", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "affected_resource_type": {"caption": "Affected Resource Type", "description": "", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "associated_framework": {"caption": "Associated Framework", "description": "Framework to which this assessment is associated.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources", "data_structure": "list"}, "exposure_category": {"caption": "Exposure Category", "description": "Type of exposure category the assessment belongs to. It can have values like Software Vulnerability, Control Gap and Threat Detection.", "navigator_attribute_description": "Classification of security issue type this assessment evaluates.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information", "navigator_is_full_value_required": true}, "kg_assessment_weightage": {"caption": "Assessment Weightage", "description": "", "group": "entity_specific", "type": "string", "ui_visibility": false}, "associated_security_control_count": {"caption": "Count of Security Control", "description": "Number of security control associated with assessment.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_resources_count": {"caption": "Associated Resources Count", "description": "Number of resources associated with assessment.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_cloud_account_count_with_assessment": {"caption": "Associated Cloud Account Count", "description": "Number of Cloud Account associated with assessment.", "group": "enrichment", "type": "integer", "range_selection": true}}, "dashboard_identifier": "EI"}