{"caption": "Vulnerability", "entity_classification": "Supporting Entity", "description": "The Vulnerability Dictionary defines attributes defined for Vulnerability entity derived from multiple sources.", "navigator_enabled": true, "navigator_constraints": "Use 'display_label' for CVE ID/vulnerability ID filtering (ID in UPPERCASE), e.g., CVE-2021-44228.\nFor filtering based on vulnerability names like 'log4j vulnerability' or 'zero day', use the 'title' or 'description' fields.\nAlways use 'severity_normalized' for filtering based on severity, unless another severity attribute is explicitly mentioned in the query.\nThe following are known exploited vulnerabilities and their corresponding CVE IDs. When the query mentions any of these names, map them to the relevant CVE:\nZeroLogon (CVE-2020-1472), Log4Shell (CVE-2021-44228), ICMAD (CVE-2022-22536), ProxyLogon (CVE-2021-26855), Spring4Shell (CVE-2022-22965), Atlassian Confluence RCE (CVE-2022-26134), VMware vSphere (CVE-2021-21972), Google Chrome Zero-Day (CVE-2022-0609), <PERSON><PERSON><PERSON> (CVE-2022-30190), PetitPotam (CVE-2021-36942), Ingress (CVE-2021-25742).\nYou can also filter vulnerabilities using keywords in the 'title' or 'description' attributes(take both in OR).\nAnalyze each query as a cybersecurity and exposure management expert, and provide responses accordingly.", "navigator_description": "\n# Vulnerability Table Summary\n\n## Overview\nThe vulnerability table provides comprehensive vulnerability intelligence data sourced from multiple security platforms and standards. It tracks security vulnerabilities through their entire lifecycle with detailed metrics for severity, exploitability, and impact across different scoring frameworks.\n\n## Data Categories\n\n### Identification & Classification\n- **CVE IDs**: Standardized vulnerability identifiers (e.g., CVE-2024-12345)\n- **CWE References**: Common Weakness Enumeration mappings for vulnerability classification\n- **Title & Description**: Detailed vulnerability information with affected products\n- **Type Classification**: Categorized as Vulnerability, Weakness, or Informational\n\n### Severity Metrics\n- **Multi-Framework Scoring**:\n  - CVSS v2.0, v3.0, v3.1, and v4.0 base scores\n  - Corresponding severity ratings (Critical, High, Medium, Low).\n  - Impact and exploitability sub-scores for deeper analysis\n  - Vector strings capturing detailed vulnerability characteristics\n  - Normalized severity and CVSS scores for simplified analysis\n\n### Exploitability Assessment\n- **Exploit Information**:\n  - Exploit availability status (true/false)\n  - EPSS scores and percentiles measuring likelihood of exploitation\n  - Exploitability categorization (Exploitable, Likely Exploitable, etc.)\n  - Patch availability status\n\n### Temporal Information\n- **Date Tracking**:\n  - Published date (initial disclosure)\n  - Last modified date (updates to vulnerability information)\n  - First observed date (when first detected on systems)\n  - CISA exploit add date (addition to known exploit database)\n  - CISA action due dates (required remediation timelines)\n\n### Remediation Guidance\n- **Mitigation Paths**:\n  - Vendor recommendations\n  - CISA required actions for critical vulnerabilities\n  - Microsoft recommended updates and update IDs\n  - General remediation recommendations\n\n### Impact Analysis\n- **Consequence Details**:\n  - Potential impacts (e.g., Remote Code Execution, Privilege Escalation)\n  - Vendor-assigned severity ratings\n\n### Entity Relationships\n- **Associated Asset Counts**:\n  - Hosts with findings (total and open)\n  - Applications with findings (total and open)\n  - Cluster instances with findings (total and open)\n  - containers with findings (total and open)\n\n### Source Attribution\n- **Data Provenance**:\n  - Origin systems (NVD, Qualys, EPSS, Tenable.sc, MS Defender, Wiz, CISA)\n  - Origin contribution type (Unique or Corroborated)\n  - Fragment counts and data source subsets\n  - Count of originating sources\n\n### Entity Management\n- **Identity & Activity**:\n  - Unique identifiers and display labels\n  - Activity status (Active/Inactive)\n  - First seen, last found, and last active dates\n  - Lifetime and observed lifetime metrics\n  - Recency (days since last discovery)\n\n## Key Features\n\n1. **Comprehensive Vulnerability Intelligence**: Integrates data from all major vulnerability databases and security tools\n\n2. **Multi-Dimensional Severity Assessment**: Incorporates multiple CVSS frameworks (v2.0 through v4.0) with normalized metrics\n\n3. **Temporal Tracking**: Captures vulnerability lifecycle from publication through detection and remediation\n\n4. **Actionable Remediation Guidance**: Includes vendor-specific update recommendations and CISA-mandated actions\n\n5. **Exploitability Context**: Combines theoretical exploit potential with real-world exploitation data\n\n6. **Impact Assessment**: Provides consequence categorization and impact scoring\n\n7. **Relationship Mapping**: Tracks affected assets across infrastructure types (hosts, applications, cloud resources)\n\n8. **Data Lineage**: Maintains source attribution and provenance information\n\nThis vulnerability table serves as a central repository for security teams to identify, prioritize, and remediate vulnerabilities across their environment, supporting risk-based security operations with context-rich vulnerability intelligence.\nIMPORTANT:If the query asks for anyting related to any software,that filter for vulnerability will be ''.For example:query:Show all vulnerabilities due to openssl softwares,filter for vulnerability will be ''", "navigator_graph_node_description": "The Vulnerability entity represents weaknesses in software, hardware, or systems that could be exploited by attackers. It includes key attributes such as CVE IDs, severity ratings (CVSS scores), lifecycle timestamps (first observed, disclosure date), affected assets (CPE, CWE classifications), exploitability status (PoC availability, KEV listings), and remediation details (patches, mitigations).This entity enables risk assessment, threat intelligence, and compliance tracking by linking vulnerabilities to impacted hosts, identities, and organizations. It helps prioritize remediation based on exploit likelihood and business impact, ensuring proactive cybersecurity measures and regulatory adherence.", "navigator_entity_description": "\\n### **Vulnerability Table Overview**  \\n\\nThe **Vulnerability Table** is a structured repository for tracking cybersecurity weaknesses in software, hardware, or systems. It helps security teams manage vulnerabilities, assess risk, prioritize remediation efforts, and ensure compliance with security standards. Each record in the table represents a specific vulnerability, providing critical details to support cybersecurity decision-making.  \\n\\n### **Key Attributes**  \\n\\n1. **Identification**  \\n   - Each vulnerability has a unique identifier, typically a **CVE ID** (e.g., CVE-2021-44228) or vendor-specific reference.  \\n\\n2. **Severity and Risk Assessment**  \\n   - Vulnerabilities are scored using the **CVSS (Common Vulnerability Scoring System)** across different versions (2.0, 3.0, 3.1, 4.0).  \\n   - Severity levels are categorized as **Critical, High, Medium, or Low** based on risk assessment.  \\n   - Exploitability vector strings define attack characteristics.  \\n\\n3. **Lifecycle and Timeline**  \\n   - Tracking key dates such as **first observed, public disclosure, and last modification** helps in monitoring vulnerability status.  \\n\\n4. **Technical Details**  \\n   - The table includes affected **software, hardware, versions (CPE identifiers), and CWE (Common Weakness Enumeration) classifications**.  \\n   - Attack vectors and exploitation conditions describe how an attacker could exploit the weakness.  \\n\\n5. **Exploitability**  \\n   - The availability of **proof-of-concept (PoC) exploits** and whether a vulnerability is listed in **CISA\\u2019s Known Exploited Vulnerabilities (KEV)** database help assess the real-world threat level.  \\n\\n6. **Mitigation and Remediation**  \\n   - Includes available **patches, security updates, configuration changes, and compensating controls** to reduce risk.  \\n\\n7. **Threat Intelligence & Risk Prioritization**  \\n   - Incorporates **EPSS (Exploit Prediction Scoring System) ratings**, vendor advisories, and indicators of active exploitation.  \\n\\n8. **Compliance & Regulatory Considerations**  \\n   - Flags for **PCI DSS compliance, STIG severity ratings**, and other security frameworks.  \\n\\n### **Purpose and Importance**  \\n\\nThe Vulnerability Table helps organizations **identify, assess, and remediate** security weaknesses efficiently. By maintaining structured data on vulnerabilities, security teams can:  \\n\\n- **Prioritize threats** based on severity and exploitability.  \\n- **Track mitigation efforts** to ensure timely risk reduction.  \\n- **Align with compliance** frameworks to meet regulatory standards.  \\n- **Gain threat intelligence** on actively exploited vulnerabilities.  \\n\\nThis table is an essential component of cybersecurity risk management, providing a **centralized view of vulnerabilities** to enhance an organization\\u2019s security posture.\\n\\nAdditionally,Vulenrability table contains below details for each Vulnerability\\n- **associated_hosts_with_findings_count (without filter,becase there is so many categories like this only give sum of all)**: The count of hosts/machines/devices affected by vulenrability(open/closed),this field will give how many hosts/machines/devices are affected by that vulenrability(open/closed)\\n- **associated_hosts_with_open_findings_count (without filter,becase there is so many categories like this only give sum of all)**: The number of hosts/machines/devices which have that vulnerability currently open/unresolved, this field will give in how many hosts that vulnerability remains open/unresolved\\n- **associated_application_with_findings_count**: The count of applications/softwares which have that vulenrability(open/closed)\\n    - if user asking about max/high/largest min/least/lower or filter like less than greater than of above return Vulenrability only, dont take above additional attribute for get total count of hosts/applications because redundancy and we dont have *sum* support\\n", "navigator_examples": ["User Query: 'Show all critical vulnerabilities' Output: 'vulnerability'", "User Query: 'Find me denial of service vulnerability' Output: 'vulnerability'"], "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.\n\nIt is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.\nAn example is 0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e.", "llm_description": "Unique alphanumeric identifier generated for each Vulnerability within the Knowledge Graph.\n\nIt is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.\nAn example is 0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e.", "group": "common", "type": "string", "category": "General Information", "internally_generated": true, "navigator_attribute_description": "Unique identifier for each Vulnerability, generated from primary key, origin, class, and attribute name, e.g., 0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e."}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.\n\nFor Vulnerability entity the order is (cve_id, vendor_id, primary_key).\n\nFor example CVE-2007-4559.", "llm_description": "The derived and best known identifier or name of the vulnerability, based on the attribute that best uniquely identifies it.\n\nFor Vulnerability entity the order is (cve_id, vendor_id, primary_key).\n\nFor example CVE-2007-4559.", "group": "common", "type": "string", "category": "General Information", "navigator_attribute_description": "The display_label is the unique identifier for a Vulnerability entity, typically formatted as cve_id, such as CVE-2007-4559."}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself.\n\nExamples include Host, Person, Vulnerability etc.", "llm_description": "The category of the entity, which corresponds to the entity type itself.\n\nIt will be \"Vulnerability\".", "group": "common", "type": "string", "internally_generated": true, "category": "General Information"}, "type": {"caption": "Type", "description": "The specific type of entity within its assigned class varies across different categories.\n\nType is categorized into Vulnerability, Informational, and Weakness.\n\nVulnerability refers to a flaw or weakness in a system, application, or network that can be exploited by attackers to gain unauthorized access or cause harm.\nInformational  provide details that, while not directly exploitable, can give insights into potential vulnerabilities or weaknesses.\nWeaknesses are broader than specific vulnerabilities and can refer to systemic issues or practices that make a system susceptible to threats.  ", "llm_description": "Type attribute classifies the whole set of vulnerabilities into three categories: \"Vulnerability\", \"Informational\" and \"Weakness\".\n\nVulnerability refers to a flaw or weakness in a system, application, or network that can be exploited by attackers to gain unauthorized access or cause harm.\nInformational  provide details that, while not directly exploitable, can give insights into potential vulnerabilities or weaknesses.\nWeaknesses are broader than specific vulnerabilities and can refer to systemic issues or practices that make a system susceptible to threats.  ", "group": "common", "type": "string", "category": "General Information", "navigator_attribute_description": "Type includes Vulnerability (exploitable flaws), Informational (insights on potential vulnerabilities), and Weakness (systemic issues making systems susceptible).", "navigator_is_full_value_required": true}, "origin": {"caption": "Origin", "description": "Data source(s) in which the entity was present.\n\nFor Vulnerability entity data sources are EPSS,MS Defender,NVD,Qualys,Tenable.sc,Wiz.", "llm_description": "Data source(s) such as EPSS,MS Defender,NVD,Qualys,Tenable.sc,Wiz from which the vulnerabilities are extracted.", "group": "common", "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "Origin refers to the data sources where the Vulnerability was present, such as EPSS, MS Defender, NVD, Qualys, Tenable.sc, and Wiz."}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.\n\nFor instance, in the case of a vulnerability, the count of origin signifies the total number of data sources from which that specific vulnerability is resolved or identified.", "llm_description": "The count of origin signifies the total number of data sources from which a specific vulnerability is resolved or identified.", "group": "common", "type": "integer", "range_selection": true, "internally_generated": true, "category": "General Information", "navigator_attribute_description": "Count of origin refers to the number of data sources, like vulnerability databases, from which the entity has been extracted."}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.\nIt is the actual api name from which the data is ingested.\nFor example Qualys Knowledgebase.", "llm_description": "The API feed from which the data is being ingested.\nFor example Qualys Knowledgebase.", "group": "common", "ui_visibility": true, "type": "string", "data_structure": "list", "internally_generated": true, "navigator_attribute_description": "The data_source_subset_name refers to the specific API feed, such as Qualys Knowledgebase, from which data is ingested."}, "first_found_date": {"caption": "First Found", "description": "The date at which the entity was first observed in the ingested data.This will be the minimum time at which the entity is observed within the scope of inventory run.", "llm_description": "The date at which the Vulnerability under consideration was first observed in the ingested data.\n\nThis will be the minimum time at which the vulnerability is observed within the scope of Knowledge Graph.", "group": "common", "type": "timestamp", "internally_generated": true, "category": "General Information", "navigator_attribute_description": "The first_found_date is when the Vulnerability was first observed in the data, marking the earliest time recorded during the inventory run."}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.\nThis is the earliest date across all data sources which are contributing to first seen date from each data sources.\n For any entity, the initial observation date is calculated based on the minimum value between the last active date and the first found date from each contributing data source. \nIf additional dates such as created, registered,first observed in case of vulnerability or onboarded dates are available from individual data sources, they take precedence over the default logic.", "llm_description": "Initial observation date of the vulnerability as inferred from available data sources.\nThis is the earliest date across all data sources which are contributing to first seen date from each data sources.\nThe initial observation date is calculated based on the minimum value between the last active date and the first found date from each contributing data source. \nIf additional dates such as created, registered,first observed in case of vulnerability or onboarded dates are available from individual data sources, they take precedence over the default logic.", "group": "common", "type": "timestamp", "category": "General Information", "navigator_attribute_description": "The first_seen_date is the earliest observation date of an Vulnerability, determined by the minimum of last active and first found dates, with precedence given to specific dates like created or onboarded from various data sources."}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\nDefaults to First Found.\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "llm_description": "Most recent date on which any update happened on a vulnerability as inferred from the data.\nDefaults to First Found.\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "category": "General Information", "type": "timestamp", "internally_generated": true, "navigator_attribute_description": "Last updated date reflects the most recent update to an Vulnerability, defaulting to the first found date if no changes occur."}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "llm_description": "The date at which the Vulnerability was last observed in the ingested data.\nThis will be the highest datetime at which the Vulnerability is observed within the scope of inventory run.", "group": "common", "category": "General Information", "type": "timestamp", "internally_generated": true, "navigator_attribute_description": "The last_found_date indicates the most recent observation of the Vulnerability in the data, marking the maximum time it was noted during the inventory run."}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which entity was active as inferred from available data sources.\nThis date is determined by considering the maximum value of dates contributed by each data source for the entity in question.\nThis includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "llm_description": "Latest date on which vulnerability was active as inferred from available data sources.\nThis date is determined by considering the maximum value of dates contributed by each data source for the vulnerability in question.\nThis includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "timestamp", "category": "General Information", "navigator_attribute_description": "The last_active_date is the most recent date of activity inferred from various data sources like activity logs and event timestamps."}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: if the time since the last inventory update is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive.When there is no information available to determine the activity of a vulnerability, its status is recorded as NULL. \nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.\nFor Vulnerability entity it is based on number of open findings associated.Inactive if no open findings.", "llm_description": "Specifies the current status of the vulnerability. \n\nThe logic is as follows: if the time since the last knowledge graph update is less than the specified inactivity period, the vulnerability is considered active; otherwise, it is inactive. \nFor Vulnerability entity it is based on number of open findings associated. Inactive if no open findings.", "group": "common", "type": "string", "category": "General Information", "navigator_attribute_description": "The activity status indicates if a vulnerability is active (unresolved) or inactive (resolved) based on the time since the last update (2 days for Cloud sources like AWS, Azure, and 180 days for others like VM) or the number of open unresolved findings for vulnerabilities.", "navigator_is_full_value_required": true}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "llm_description": "The duration of time the vulnerability has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment it was first detected or engaged with the system until its most recent activity.", "group": "common", "type": "integer", "category": "General Information", "range_selection": true, "internally_generated": true, "navigator_attribute_description": "Lifetime is the total span in days from the First Seen Date to the Last Active Date."}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "llm_description": "Number of days since the vulnerability was last active as inferred from ingested data sources. It is determined based on the difference between last knowledge graph update date and the last active date inferred from the source.", "group": "common", "type": "integer", "category": "General Information", "range_selection": true, "internally_generated": true, "navigator_attribute_description": "Number of days since the Vulnerability's last activity, calculated from the difference between the last inventory update date and the last inferred active date."}, "completeness_quality_score": {"caption": "Completeness Quality Score", "description": "A metric representing the extent to which required data attributes for the entity are populated across ingested data sources. This score helps evaluate the completeness of the entity's data profile.", "group": "enrichment", "examples": "1", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "aggregated_quality_score": {"caption": "Aggregated Quality Score", "description": "A composite score derived from various quality metrics (such as completeness, correctness, and consistency) to assess the overall data quality of the entity.", "examples": "1", "group": "enrichment", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score_category": {"caption": "Completeness Quality Score Category", "description": "A categorical interpretation of the Completeness Quality Score that classifies the entity's data completeness into levels such as High, Medium, or Low (configurable). This helps in quickly identifying the priority or severity of incomplete data across entities, enabling better triaging and quality improvement decisions.", "examples": "High", "group": "enrichment", "type": "string", "range_selection": true, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.\n\nThis field is calculated as the difference between the time the entity was first discovered in an ingested data source and the time it was last discovered.", "llm_description": "Number of days over which the vulnerability was present in one or more ingested data sources.\n\nThis field is calculated as the difference between the time the entity was first discovered in an ingested data source and the time it was last discovered.", "group": "common", "type": "integer", "range_selection": true, "internally_generated": true, "category": "General Information", "navigator_attribute_description": "Observed lifetime is the number of days an Vulnerability was present in data sources, calculated from its first to last discovery date."}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data.\n\nThis field is determined based on the difference between last inventory update date and the date at which the entity was last discovered in the ingested data", "llm_description": "Number of days since the vulnerability was last discovered in the ingested data.\n\nThis field is determined based on the difference between last knowledge graph update date and the date at which the vulnerability was last discovered in the ingested data.", "examples": "10", "group": "common", "type": "integer", "range_selection": true, "internally_generated": true, "category": "General Information", "navigator_attribute_description": "Recency indicates the number of days since an Vulnerability was last discovered in the ingested data, calculated from the last inventory update date."}, "description": {"caption": "Description", "description": "Detailed description of the vulnerability.", "llm_description": "Detailed description of the vulnerability.", "group": "common", "category": "General Information", "ui_visibility": true, "type": "string", "navigator_attribute_description": "A detailed overview of the vulnerability, including examples."}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "llm_description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "ui_visibility": true, "category": "General Information", "type": "string", "navigator_attribute_description": "A business unit is a department or team responsible for specific functions, products, or markets within an organization."}, "location_country": {"caption": "Location Country", "description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard.\nFor example 'South Africa'.", "llm_description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard.\nFor example 'South Africa'.", "group": "common", "category": "General Information", "ui_visibility": true, "type": "string", "navigator_attribute_description": "Country based on cloud region reported by vendors, normalized to ISO standards, e.g., 'South Africa'."}, "location_city": {"caption": "Location City", "description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard.\nFor example 'Sydney'.", "llm_description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard.\nFor example 'Sydney'.", "group": "common", "ui_visibility": true, "category": "General Information", "type": "string", "navigator_attribute_description": "City derived from cloud region per ISO standard, e.g., 'Sydney'."}, "department": {"caption": "Department", "description": "Name of the department within the business unit.\nFor example 'Product Management'.", "llm_description": "Name of the department within the business unit.\nFor example 'Product Management'.", "group": "common", "ui_visibility": true, "type": "string", "category": "General Information", "navigator_attribute_description": "Department refers to the specific area within a business unit, such as 'Product Management'."}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\n\nIt is determined based on number of sources that gets resolved for each entity.", "llm_description": "Count of partial records or pieces of evidence of a vulnerability.\n\nIt is determined based on number of sources that gets resolved for each vulnerability finding.", "group": "common", "type": "integer", "range_selection": true, "internally_generated": true, "navigator_attribute_description": "Fragments refer to the count of partial records or pieces of evidence for an Vulnerability, based on the number of sources resolved."}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.If any of this field gets updated then that entity got a change.\nFor example host_name from host entity,job title from person entity,cve_id from vulnerability entity.", "llm_description": "Key fields that are considered for capturing any changes to a vulnerability finding. \nFor example, cve_id.", "group": "common", "ui_visibility": false, "internally_generated": true, "type": "string", "data_structure": "struct"}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.\nFor example 180 days.", "llm_description": "Indicates the number of days after which the vulnerability finding will be considered inactive within the knowledge graph.\nFor example 180 days.", "group": "common", "ui_visibility": false, "type": "integer"}, "associated_hosts_with_open_findings_count": {"caption": "Count of Hosts with Open Vulnerability Findings", "description": "Number of hosts associated with open vulnerability findings.", "llm_description": "Number of hosts associated with open vulnerability findings.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Count of hosts with open vulnerabilities."}, "associated_hosts_with_findings_count": {"caption": "Count of Hosts with Vulnerability Findings", "description": "Number of hosts associated with vulnerability findings.", "llm_description": "Number of hosts associated with vulnerability findings.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Count of hosts linked to vulnerability findings."}, "associated_cluster_with_open_findings_count": {"caption": "Count of Cluster with Open Vulnerability Findings", "description": "Number of cluster associated with open vulnerability findings.", "llm_description": "Number of cluster associated with open vulnerability findings.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of cluster instances linked to open vulnerability findings."}, "associated_cluster_with_findings_count": {"caption": "Count of Cluster with Vulnerability Findings", "description": "Number of cluster associated with vulnerability findings.", "llm_description": "Number of cluster associated with vulnerability findings.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of cluster instances linked to vulnerability findings."}, "associated_container_with_open_findings_count": {"caption": "Count of Container with Open Vulnerability Findings", "description": "Number of container associated with open vulnerability findings.", "llm_description": "Number of container associated with open vulnerability findings.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Count of containers linked to open vulnerability findings."}, "associated_container_with_findings_count": {"caption": "Count of Container with Vulnerability Findings", "description": "Number of container associated with vulnerability findings.", "llm_description": "Number of container associated with vulnerability findings.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Count of containers linked to vulnerability findings."}, "associated_application_with_findings_count": {"caption": "Count of Application with Vulnerability Findings", "description": "Number of application associated with vulnerability findings.", "llm_description": "Number of application associated with vulnerability findings.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Count of applications linked to vulnerability findings."}, "associated_application_with_open_findings_count": {"caption": "Count of Application with Open Vulnerability Findings", "description": "Number of application associated with open vulnerability findings.", "llm_description": "Number of application associated with open vulnerability findings.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Count of applications linked to open vulnerability findings."}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "llm_description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "category": "General Information", "type": "string", "navigator_attribute_description": "Origin can be categorized as either unique or corroborated.", "navigator_is_full_value_required": true}, "vulnerability_severity": {"caption": "Vulnerability Severity", "description": "The Vulnerability Severity value combines data from CVSS v2.0, v3.0, v3.1, and v4.0 severities into a single, up-to-date measure of vulnerability severity. The priority order is CVSS v4.0, v3.1, v3.0, and v2.0 severity.The severity levels are Critical, High, Medium, and Low", "llm_description": "The Vulnerability Severity value combines data from CVSS v2.0, v3.0, v3.1, and v4.0 severities into a single, up-to-date measure of vulnerability severity. The priority order is CVSS v4.0, v3.1, v3.0, and v2.0 severity.The severity levels are Critical, High, Medium, and Low", "group": "enrichment", "type": "string", "category": "General Information", "finding_evidence_groupby_enabled": true, "navigator_attribute_description": "The Vulnerability Severity value combines data from CVSS v2.0, v3.0, v3.1, and v4.0 severities into a single, up-to-date measure of vulnerability severity. The priority order is CVSS v4.0, v3.1, v3.0, and v2.0 severity.The severity levels are Critical, High, Medium, and Low", "navigator_is_full_value_required": true}, "cvss_normalised": {"caption": "CVSS Normalised", "description": "The CVSS Normalised Score combines scores from CVSS v2.0, v3.0, v3.1, and v4.0 into a single, up-to-date measure of vulnerability severity. The priority order is CVSS v4.0, v3.1, v3.0, and v2.0. The value ranges from 0 to 10", "llm_description": "The CVSS Normalised Score combines scores from CVSS v2.0, v3.0, v3.1, and v4.0 into a single, up-to-date measure of vulnerability severity. The priority order is CVSS v4.0, v3.1, v3.0, and v2.0. The value ranges from 0 to 10", "group": "enrichment", "type": "string", "min": 0, "max": 10, "step_interval": 0.1, "range_selection": true, "navigator_attribute_description": "The CVSS Normalised Score combines scores from CVSS v2.0, v3.0, v3.1, and v4.0 into a single, up-to-date measure of vulnerability severity. The priority order is CVSS v4.0, v3.1, v3.0, and v2.0. The value ranges from 0 to 10"}, "exploitability": {"caption": "Exploitability", "description": "Exploitability categorises vulnerabilities as Exploitable (if exploit details are available), Likely Exploitable (if exploit details are not available and EPSS is above 0.1) or  Exploitability Unknown (if exploit details and EPSS data are not available)", "llm_description": "Exploitability categorises vulnerabilities as Exploitable (if exploit details are available), Likely Exploitable (if exploit details are not available and EPSS is above 0.1) or  Exploitability Unknown (if exploit details and EPSS data are not available)", "group": "enrichment", "type": "string", "category": "General Information", "navigator_attribute_description": "Exploitability categorises vulnerabilities as Exploitable (if exploit details are available), Likely Exploitable (if exploit details are not available and EPSS is above 0.1) or  Exploitability Unknown (if exploit details and EPSS data are not available)", "navigator_is_full_value_required": true}, "title": {"caption": "Title", "description": "Title of the vulnerability.\nIt describes the nature of the vulnerability in a concise manner. It often includes keywords or phrases that convey the type of issue being addressed.\nFor example Oracle Java JDK and JRE Multiple Vulnerabilities,Mozilla Firefox Multiple Vulnerabilities (MFSA 2015-134 and MFSA 2015-149).", "llm_description": "Title of the vulnerability.\nIt describes the nature of the vulnerability in a concise manner. It often includes keywords or phrases that convey the type of issue being addressed.\nFor example Oracle Java JDK and JRE Multiple Vulnerabilities,Mozilla Firefox Multiple Vulnerabilities (MFSA 2015-134 and MFSA 2015-149).", "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "Concise title of the vulnerability, such as \"Oracle Java JDK and JRE Multiple Vulnerabilities\" or \"Mozilla Firefox Multiple Vulnerabilities (MFSA 2015-134 and MFSA 2015-149).\""}, "cve_id": {"caption": "CVE ID", "description": "Common Vulnerabilities and Exposures ID, is a unique identifier assigned to a specific security vulnerability.\nThe purpose of CVE IDs is to provide a standardized way to reference and track vulnerabilities across different information security tools and databases.Each CVE ID consists of the prefix 'CVE-' followed by a year and a unique number, such as 'CVE-2024-12345'. The year indicates when the vulnerability was assigned the CVE ID, and the number is a unique identifier for that specific vulnerability within that year.\nFor example CVE-2012-1137.", "llm_description": "Common Vulnerabilities and Exposures ID, is a unique identifier assigned to a specific security vulnerability.\nThe purpose of CVE IDs is to provide a standardized way to reference and track vulnerabilities across different information security tools and databases.Each CVE ID consists of the prefix 'CVE-' followed by a year and a unique number, such as 'CVE-2024-12345'. The year indicates when the vulnerability was assigned the CVE ID, and the number is a unique identifier for that specific vulnerability within that year.\nFor example CVE-2012-1137.", "group": "entity_specific", "type": "string", "data_structure": "list", "candidate_key": true, "category": "General Information", "navigator_attribute_description": "CVE ID is a unique identifier for security vulnerabilities, formatted as 'cve-year-number' (e.g., 'cve-2024-12345')."}, "v40_score": {"caption": "CVSSv4.0 Score", "description": "CVSSv4.0 (Common Vulnerability Scoring System version 4.0) rating quantifies the severity of security vulnerabilities.\nIt uses a numerical score from 0.0 to 10.0, with higher scores indicating greater severity.\nSecurity professionals rely on this standardized framework to assess and prioritize vulnerabilities, enabling effective resource allocation for mitigating critical threats.\n For instance, a V4 severity score of 9.8 or 6.1 would correspond to significant vulnerabilities", "llm_description": "CVSSv4.0 (Common Vulnerability Scoring System version 4.0) rating quantifies the severity of security vulnerabilities.\nIt uses a numerical score from 0.0 to 10.0, with higher scores indicating greater severity.\nSecurity professionals rely on this standardized framework to assess and prioritize vulnerabilities, enabling effective resource allocation for mitigating critical threats.\n For instance, a V4 severity score of 9.8 or 6.1 would correspond to significant vulnerabilities", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "category": "Severity and Impact Metrics", "navigator_attribute_description": "CVSSv4.0 rating scores vulnerabilities from 0.0 to 10.0, with higher scores like 9.8 indicating greater severity for prioritizing security threats."}, "v40_severity": {"caption": "CVSSv4.0 Severity", "description": "CVSSv4.0 Severity V4.0 severity categorizes vulnerabilities based on their potential impact and exploitability.\n Critical vulnerabilities under V4.0 denote the highest severity level, indicating flaws that are easily exploitable and capable of causing catastrophic damage or compromise to systems or data, necessitating immediate remediation. \nHigh-severity vulnerabilities represent significant risks with potential for complete compromise or widespread disruption if exploited, demanding prompt attention. Medium-severity vulnerabilities pose moderate risks, potentially leading to partial compromise or disruption, requiring timely mitigation efforts. Low-severity vulnerabilities, while posing minimal immediate risk, may still warrant attention to prevent future exploitation or to maintain security standards.\n V4.0 severity ratings help organizations prioritize their response efforts effectively, ensuring resources are allocated based on the criticality and potential impact of each identified vulnerability.", "llm_description": "CVSSv4.0 Severity V4.0 severity categorizes vulnerabilities based on their potential impact and exploitability.\n Critical vulnerabilities under V4.0 denote the highest severity level, indicating flaws that are easily exploitable and capable of causing catastrophic damage or compromise to systems or data, necessitating immediate remediation. \nHigh-severity vulnerabilities represent significant risks with potential for complete compromise or widespread disruption if exploited, demanding prompt attention. Medium-severity vulnerabilities pose moderate risks, potentially leading to partial compromise or disruption, requiring timely mitigation efforts. Low-severity vulnerabilities, while posing minimal immediate risk, may still warrant attention to prevent future exploitation or to maintain security standards.\n V4.0 severity ratings help organizations prioritize their response efforts effectively, ensuring resources are allocated based on the criticality and potential impact of each identified vulnerability.", "group": "entity_specific", "type": "string", "category": "Severity and Impact Metrics", "navigator_attribute_description": "CVSSv4.0 Severity categorizes vulnerabilities as Critical, High, Medium, or Low based on their exploitability and impact, guiding organizations in prioritizing remediation efforts.", "navigator_is_full_value_required": true}, "v31_score": {"caption": "CVSSv3.1 Score", "description": "CVSSv3.1 (Common Vulnerability Scoring System version 3.1) is a standardized framework used to assess and communicate the severity of security vulnerabilities. \nIt provides a numerical score ranging from 0.0 to 10.0, with higher scores indicating greater severity.\nThe CVSSv3.1 score provides a standardized way for security professionals to assess and prioritize vulnerabilities, helping organizations effectively allocate resources to mitigate the most critical threats.\nFor example 9.8,6.1.", "llm_description": "CVSSv3.1 (Common Vulnerability Scoring System version 3.1) is a standardized framework used to assess and communicate the severity of security vulnerabilities. \nIt provides a numerical score ranging from 0.0 to 10.0, with higher scores indicating greater severity.\nThe CVSSv3.1 score provides a standardized way for security professionals to assess and prioritize vulnerabilities, helping organizations effectively allocate resources to mitigate the most critical threats.\nFor example 9.8,6.1.", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "category": "Severity and Impact Metrics", "navigator_attribute_description": "CVSSv3.1 scores range from 0.0 to 10.0 to assess vulnerability severity, with examples like 9.8 and 6.1."}, "v31_vector": {"caption": "CVSSv3.1 Vector", "description": "CVSSv3.1 Vector is a string representation of the various metrics used to calculate the CVSSv3.1 score for a vulnerability.\nIt provides a standardized way to communicate the specifics of a vulnerability's severity and helps security professionals understand and prioritize remediation efforts.\nThe CVSSv3.1 Vector consists of several components, each representing a specific metric:\nAttack Vector (AV): Describes how the vulnerability is accessed by an attacker (e.g., Local, Adjacent Network, Network),\nAttack Complexity (AC): Indicates the level of complexity required to exploit the vulnerability (e.g., Low, High),\nPrivileges Required (PR): Specifies the level of privileges an attacker needs to exploit the vulnerability (e.g., None, Low, High),\nUser Interaction (UI): Reflects whether user interaction is required to exploit the vulnerability (e.g., None, Required).\nScope (S): Determines whether the vulnerability impacts the confidentiality, integrity, and availability of resources beyond the immediate scope (e.g., Unchanged, Changed).\nEach component in the CVSSv3.1 Vector is represented by a single character, and when combined, they form a concise representation of the vulnerability's characteristics.\nFor example CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:C.", "llm_description": "CVSSv3.1 Vector is a string representation of the various metrics used to calculate the CVSSv3.1 score for a vulnerability.\nIt provides a standardized way to communicate the specifics of a vulnerability's severity and helps security professionals understand and prioritize remediation efforts.\nThe CVSSv3.1 Vector consists of several components, each representing a specific metric:\nAttack Vector (AV): Describes how the vulnerability is accessed by an attacker (e.g., Local, Adjacent Network, Network),\nAttack Complexity (AC): Indicates the level of complexity required to exploit the vulnerability (e.g., Low, High),\nPrivileges Required (PR): Specifies the level of privileges an attacker needs to exploit the vulnerability (e.g., None, Low, High),\nUser Interaction (UI): Reflects whether user interaction is required to exploit the vulnerability (e.g., None, Required).\nScope (S): Determines whether the vulnerability impacts the confidentiality, integrity, and availability of resources beyond the immediate scope (e.g., Unchanged, Changed).\nEach component in the CVSSv3.1 Vector is represented by a single character, and when combined, they form a concise representation of the vulnerability's characteristics.\nFor example CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:C.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "CVSSv3.1 Vector is a string that represents various metrics for calculating a vulnerability's severity, including components like Attack Vector (e.g., Network), Attack Complexity (e.g., Low), and Privileges Required (e.g., None), exemplified by CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:C."}, "v31_severity": {"caption": "CVSSv3.1 Severity", "description": "CVSSv3.1 Severity refers to the overall severity rating assigned to a vulnerability based on the Common Vulnerability Scoring System version 3.1 (CVSSv3.1). \nIt is derived from the CVSSv3.1 Base Score, which is calculated using various metrics such as exploitability, impact, and environmental factors.CVSSv3.1 Severity provides a standardized way to categorize vulnerabilities based on their potential impact and helps organizations prioritize remediation efforts accordingly. It assists security teams in understanding the urgency and severity of a vulnerability in the context of their specific environment.\nThe CVSSv3.1 Severity levels are typically categorized as follows:\nNone: Vulnerabilities with no impact on the confidentiality, integrity, or availability of the affected system or data,\nLow: Vulnerabilities with minor impact or limited scope, typically requiring significant effort to exploit or posing minimal risk,\nMedium: Vulnerabilities with moderate impact, potentially leading to partial compromise of the system or data,\nHigh: Vulnerabilities with significant impact, potentially resulting in complete compromise of the system or data, or causing widespread disruption,\nCritical: Vulnerabilities with severe impact, often easily exploitable, and capable of causing catastrophic damage or compromise.\nFor example High.", "llm_description": "CVSSv3.1 Severity refers to the overall severity rating assigned to a vulnerability based on the Common Vulnerability Scoring System version 3.1 (CVSSv3.1). \nIt is derived from the CVSSv3.1 Base Score, which is calculated using various metrics such as exploitability, impact, and environmental factors.CVSSv3.1 Severity provides a standardized way to categorize vulnerabilities based on their potential impact and helps organizations prioritize remediation efforts accordingly. It assists security teams in understanding the urgency and severity of a vulnerability in the context of their specific environment.\nThe CVSSv3.1 Severity levels are typically categorized as follows:\nNone: Vulnerabilities with no impact on the confidentiality, integrity, or availability of the affected system or data,\nLow: Vulnerabilities with minor impact or limited scope, typically requiring significant effort to exploit or posing minimal risk,\nMedium: Vulnerabilities with moderate impact, potentially leading to partial compromise of the system or data,\nHigh: Vulnerabilities with significant impact, potentially resulting in complete compromise of the system or data, or causing widespread disruption,\nCritical: Vulnerabilities with severe impact, often easily exploitable, and capable of causing catastrophic damage or compromise.\nFor example High.", "group": "entity_specific", "type": "string", "category": "Severity and Impact Metrics", "navigator_attribute_description": "CVSSv3.1 Severity rates vulnerabilities as None, Low, Medium, High, or Critical based on their potential impact, with High indicating significant risk of complete system compromise.", "navigator_is_full_value_required": true}, "v31_exploitability": {"caption": "CVSSv3.1 Exploitability", "description": "CVSSv3.1 Exploitability refers to a metric used in the Common Vulnerability Scoring System version 3.1 (CVSSv3.1) to evaluate the likelihood that a vulnerability will be exploited in a real-world scenario. It is based on the exploitablity score.\nIt assesses the ease with which an attacker can exploit the vulnerability to achieve their objectives.\nFor example 1.7.", "llm_description": "CVSSv3.1 Exploitability refers to a metric used in the Common Vulnerability Scoring System version 3.1 (CVSSv3.1) to evaluate the likelihood that a vulnerability will be exploited in a real-world scenario. It is based on the exploitablity score.\nIt assesses the ease with which an attacker can exploit the vulnerability to achieve their objectives.\nFor example 1.7.", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "category": "Severity and Impact Metrics", "navigator_attribute_description": "CVSSv3.1 Exploitability measures the likelihood of a vulnerability being exploited, with an example score of 1.7 indicating ease of exploitation."}, "v31_impact_score": {"caption": "CVSSv3.1 Impact Score", "description": "Common Vulnerability Scoring System version 3.1, is a standard method for assessing the severity of security vulnerabilities. \nIt ranges from 0.0 to 10.0, with 10.0 being the highest impact. The Impact Score is divided into three sub-scores: \nConfidentiality Impact (C): This metric measures the potential impact on the confidentiality of the information that could be compromised by the vulnerability. ,\nIntegrity Impact (I): This metric measures the potential impact on the integrity of the information or system that could be compromised by the vulnerability. ,\nAvailability Impact (A): This metric measures the potential impact on the availability of the affected system. \nEach of these sub-scores is rated as None (0.0), Low (0.1), Low-Medium (0.3), Medium (0.5), High (0.7), or High (1.0), and they are combined using a formula to calculate the overall Impact Score.\nFor example 1.0.", "llm_description": "Common Vulnerability Scoring System version 3.1, is a standard method for assessing the severity of security vulnerabilities. \nIt ranges from 0.0 to 10.0, with 10.0 being the highest impact. The Impact Score is divided into three sub-scores: \nConfidentiality Impact (C): This metric measures the potential impact on the confidentiality of the information that could be compromised by the vulnerability. ,\nIntegrity Impact (I): This metric measures the potential impact on the integrity of the information or system that could be compromised by the vulnerability. ,\nAvailability Impact (A): This metric measures the potential impact on the availability of the affected system. \nEach of these sub-scores is rated as None (0.0), Low (0.1), Low-Medium (0.3), Medium (0.5), High (0.7), or High (1.0), and they are combined using a formula to calculate the overall Impact Score.\nFor example 1.0.", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "category": "Severity and Impact Metrics", "navigator_attribute_description": "The v31_impact_score, ranging from 0.0 to 10.0, assesses security vulnerability severity through Confidentiality, Integrity, and Availability impacts, with ratings from None (0.0) to High (1.0)."}, "v30_score": {"caption": "CVSSv3.0 Score", "description": "Common Vulnerability Scoring System version 3.0, is a standard method for assessing the severity of security vulnerabilities.\nThe CVSSv3.0 score consists of two main components: the Base Score and the Temporal and Environmental Metrics.\nFor example 2.0.", "llm_description": "Common Vulnerability Scoring System version 3.0, is a standard method for assessing the severity of security vulnerabilities.\nThe CVSSv3.0 score consists of two main components: the Base Score and the Temporal and Environmental Metrics.\nFor example 2.0.", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "category": "Severity and Impact Metrics", "navigator_attribute_description": "The v30_score is the CVSS version 3.0 standard for assessing security vulnerability severity, comprising a Base Score and additional metrics, such as 2.0."}, "temporal_cvss_score": {"caption": "CVSSv3.0 Temporal Score", "description": "The CVSSv3.0 Temporal Score is one of the components used in the Common Vulnerability Scoring System version 3.0 (CVSSv3.0) to assess the severity of security vulnerabilities. \nThe Temporal Score includes metrics such as: Exploit Code Maturity (E): This metric assesses the maturity level of any known or potential exploit for the vulnerability. \nPossible values are Unproven (U), Proof-of-Concept (P), Functional (F), High (H), or Not Defined (X). Remediation Level (RL): Indicates the existence and availability of a fix or workaround for the vulnerability. Possible values are Official Fix (O), Temporary Fix (T), Workaround (W), Unavailable (U), or Not Defined (X). Report Confidence (RC): Reflects the confidence level of the vulnerability information sources. \nFor example 3.0", "llm_description": "The CVSSv3.0 Temporal Score is one of the components used in the Common Vulnerability Scoring System version 3.0 (CVSSv3.0) to assess the severity of security vulnerabilities. \nThe Temporal Score includes metrics such as: Exploit Code Maturity (E): This metric assesses the maturity level of any known or potential exploit for the vulnerability. \nPossible values are Unproven (U), Proof-of-Concept (P), Functional (F), High (H), or Not Defined (X). Remediation Level (RL): Indicates the existence and availability of a fix or workaround for the vulnerability. Possible values are Official Fix (O), Temporary Fix (T), Workaround (W), Unavailable (U), or Not Defined (X). Report Confidence (RC): Reflects the confidence level of the vulnerability information sources. \nFor example 3.0", "group": "entity_specific", "type": "double", "range_selection": true, "category": "Severity and Impact Metrics", "navigator_attribute_description": "The CVSSv3.0 Temporal Score assesses vulnerability severity using metrics like Exploit Code Maturity (e.g., Unproven, Proof-of-Concept), Remediation Level (e.g., Official Fix, Workaround), and Report Confidence, with examples including values from 3.0."}, "v30_vector": {"caption": "CVSSv3.0 Vector", "description": "CVSSv3.0 stands for the Common Vulnerability Scoring System version 3.0, which is a standardized system for assessing and scoring the severity of software vulnerabilities.\nThe CVSSv3.0 vector is a string that represents various attributes of a vulnerability and its impact. It includes metrics such as attack complexity, exploit code maturity, impact on confidentiality, integrity, and availability, among others.\n This vector is used to calculate a numerical score, which helps prioritize security vulnerabilities and determine the appropriate response.\neach representing a specific metric:Attack Vector (AV): Describes how the vulnerability is accessed by an attacker (e.g., Local, Adjacent Network, Network),Attack Complexity (AC): Indicates the level of complexity required to exploit the vulnerability (e.g., Low, High),Privileges Required (PR): Specifies the level of privileges an attacker needs to exploit the vulnerability (e.g., None, Low, High),User Interaction (UI): Reflects whether user interaction is required to exploit the vulnerability (e.g., None, Required).Scope (S): Determines whether the vulnerability impacts the confidentiality, integrity, and availability of resources beyond the immediate scope (e.g., Unchanged, Changed).\nFor example CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:H/E:U/RL:O/RC:R", "llm_description": "CVSSv3.0 stands for the Common Vulnerability Scoring System version 3.0, which is a standardized system for assessing and scoring the severity of software vulnerabilities.\nThe CVSSv3.0 vector is a string that represents various attributes of a vulnerability and its impact. It includes metrics such as attack complexity, exploit code maturity, impact on confidentiality, integrity, and availability, among others.\n This vector is used to calculate a numerical score, which helps prioritize security vulnerabilities and determine the appropriate response.\neach representing a specific metric:Attack Vector (AV): Describes how the vulnerability is accessed by an attacker (e.g., Local, Adjacent Network, Network),Attack Complexity (AC): Indicates the level of complexity required to exploit the vulnerability (e.g., Low, High),Privileges Required (PR): Specifies the level of privileges an attacker needs to exploit the vulnerability (e.g., None, Low, High),User Interaction (UI): Reflects whether user interaction is required to exploit the vulnerability (e.g., None, Required).Scope (S): Determines whether the vulnerability impacts the confidentiality, integrity, and availability of resources beyond the immediate scope (e.g., Unchanged, Changed).\nFor example CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:H/E:U/RL:O/RC:R", "group": "entity_specific", "type": "string", "category": "Severity and Impact Metrics", "navigator_attribute_description": "CVSSv3.0 vector is a standardized string representing vulnerability attributes like Attack Vector (e.g., Network), Attack Complexity (e.g., Low), and others, used to calculate a numerical score for prioritizing security vulnerabilities."}, "v30_severity": {"caption": "CVSSv3.0 Severity", "description": "CVSSv3.0 Severity is a numerical score assigned to a vulnerability based on the Common Vulnerability Scoring System version 3.0 (CVSSv3.0).\n This score helps to quantify the severity of a vulnerability, allowing organizations to prioritize their responses to security threats. The CVSSv3.0 Severity score ranges from 0.0 to 10.0, with 10.0 being the most severe.\nThe severity is determined by assessing various metrics such as the exploitability of the vulnerability, the impact it could have on the system, and whether mitigations or workarounds are available.\n Here's how the CVSSv3.0 Severity scores typically translate: 0.0 to 3.9: Low severity 4.0 to 6.9: Medium severity 7.0 to 8.9: High severity 9.0 to 10.0: Critical severity.\nFor example Critical.", "llm_description": "CVSSv3.0 Severity is a numerical score assigned to a vulnerability based on the Common Vulnerability Scoring System version 3.0 (CVSSv3.0).\n This score helps to quantify the severity of a vulnerability, allowing organizations to prioritize their responses to security threats. The CVSSv3.0 Severity score ranges from 0.0 to 10.0, with 10.0 being the most severe.\nThe severity is determined by assessing various metrics such as the exploitability of the vulnerability, the impact it could have on the system, and whether mitigations or workarounds are available.\n Here's how the CVSSv3.0 Severity scores typically translate: 0.0 to 3.9: Low severity 4.0 to 6.9: Medium severity 7.0 to 8.9: High severity 9.0 to 10.0: Critical severity.\nFor example Critical.", "group": "entity_specific", "type": "string", "category": "Severity and Impact Metrics", "navigator_attribute_description": "CVSSv3.0 Severity is a score from 0.0 to 10.0 that quantifies vulnerability severity, with ranges indicating Low (0.0-3.9), Medium (4.0-6.9), High (7.0-8.9), and Critical (9.0-10.0) severity levels.", "navigator_is_full_value_required": true}, "v30_exploitability": {"caption": "CVSSv3.0 Exploitability", "description": "CVSSv3.0 (Common Vulnerability Scoring System version 3.0) is a framework used to assess and communicate the severity of security vulnerabilities. It includes several metrics to evaluate the impact and exploitability of vulnerabilities. One of these metrics is Exploitability.Exploitability in CVSSv3.0 refers to the ease or difficulty with which a vulnerability can be exploited. It considers factors such as the complexity of the attack required, the skill level of the attacker, and the likelihood that an exploit will be developed.\n Exploitability is scored on a scale of 0.0 to 1.0, with higher scores indicating greater exploitability.\nFor example 3.0.", "llm_description": "CVSSv3.0 (Common Vulnerability Scoring System version 3.0) is a framework used to assess and communicate the severity of security vulnerabilities. It includes several metrics to evaluate the impact and exploitability of vulnerabilities. One of these metrics is Exploitability.Exploitability in CVSSv3.0 refers to the ease or difficulty with which a vulnerability can be exploited. It considers factors such as the complexity of the attack required, the skill level of the attacker, and the likelihood that an exploit will be developed.\n Exploitability is scored on a scale of 0.0 to 1.0, with higher scores indicating greater exploitability.\nFor example 3.0.", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "category": "Severity and Impact Metrics", "navigator_attribute_description": "Exploitability in CVSSv3.0 measures how easily a vulnerability can be exploited, scored from 0.0 to 1.0, with higher scores indicating greater ease, exemplified by a score of 3.0."}, "v30_impact_score": {"caption": "CVSSv3.0 Impact Score", "description": "CVSSv3.0 (Common Vulnerability Scoring System version 3.0) Impact Score is a metric used to quantify the potential impact of a security vulnerability on an affected system.\nThe Impact Score is calculated based on three submetrics: Confidentiality Impact (C): This submetric measures the potential impact on the confidentiality of information if the vulnerability is exploited. It assesses whether the vulnerability could lead to the unauthorized disclosure of sensitive information. Integrity Impact (I): This submetric evaluates the potential impact on the integrity of the system or data if the vulnerability is exploited. It assesses whether the vulnerability could result in unauthorized modification or manipulation of data. Availability Impact (A): This submetric assesses the potential impact on the availability of the system or resource if the vulnerability is exploited. It evaluates whether the vulnerability could lead to denial of service or other disruptions that affect the availability of the system. Each submetric is scored on a scale of 0 to 3, with 0 indicating no impact, 1 indicating low impact, 2 indicating moderate impact, and 3 indicating high impact. These submetric scores are then combined to calculate the overall Impact Score, which ranges from 0.0 to 10.0, with higher scores indicating a more severe impact.\nFor example 1.2.", "llm_description": "CVSSv3.0 (Common Vulnerability Scoring System version 3.0) Impact Score is a metric used to quantify the potential impact of a security vulnerability on an affected system.\nThe Impact Score is calculated based on three submetrics: Confidentiality Impact (C): This submetric measures the potential impact on the confidentiality of information if the vulnerability is exploited. It assesses whether the vulnerability could lead to the unauthorized disclosure of sensitive information. Integrity Impact (I): This submetric evaluates the potential impact on the integrity of the system or data if the vulnerability is exploited. It assesses whether the vulnerability could result in unauthorized modification or manipulation of data. Availability Impact (A): This submetric assesses the potential impact on the availability of the system or resource if the vulnerability is exploited. It evaluates whether the vulnerability could lead to denial of service or other disruptions that affect the availability of the system. Each submetric is scored on a scale of 0 to 3, with 0 indicating no impact, 1 indicating low impact, 2 indicating moderate impact, and 3 indicating high impact. These submetric scores are then combined to calculate the overall Impact Score, which ranges from 0.0 to 10.0, with higher scores indicating a more severe impact.\nFor example 1.2.", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "category": "Severity and Impact Metrics", "navigator_attribute_description": "The CVSSv3.0 Impact Score quantifies the potential impact of a security vulnerability on a system, based on Confidentiality, Integrity, and Availability submetrics, each scored from 0 to 3, resulting in a total score from 0.0 to 10.0, with higher scores indicating greater severity, such as a score of 1.2."}, "v2_score": {"caption": "CVSSv2 Score", "description": "CVSSv2 (Common Vulnerability Scoring System version 2) is a framework used to assess and communicate the severity of security vulnerabilities.\n It provides a numerical score ranging from 0.0 to 10.0 to represent the severity of a vulnerability. The CVSSv2 score consists of three metric groups: Base Score: This group includes metrics that measure the intrinsic qualities of a vulnerability. It evaluates factors such as the impact on confidentiality, integrity, and availability, as well as the complexity of exploitation and authentication required.\n The Base Score ranges from 0.0 to 10.0, with higher scores indicating greater severity.\n Temporal Score: This group includes metrics that measure the characteristics of a vulnerability that may change over time.\n It considers factors such as the availability of exploit code or patches, as well as the maturity of the vulnerability. The Temporal Score can modify the Base Score but does not directly impact the severity rating. Environmental Score: This group includes metrics that allow organizations to customize the CVSS score based on their specific environment. \nFor example 4.5.", "llm_description": "CVSSv2 (Common Vulnerability Scoring System version 2) is a framework used to assess and communicate the severity of security vulnerabilities.\n It provides a numerical score ranging from 0.0 to 10.0 to represent the severity of a vulnerability. The CVSSv2 score consists of three metric groups: Base Score: This group includes metrics that measure the intrinsic qualities of a vulnerability. It evaluates factors such as the impact on confidentiality, integrity, and availability, as well as the complexity of exploitation and authentication required.\n The Base Score ranges from 0.0 to 10.0, with higher scores indicating greater severity.\n Temporal Score: This group includes metrics that measure the characteristics of a vulnerability that may change over time.\n It considers factors such as the availability of exploit code or patches, as well as the maturity of the vulnerability. The Temporal Score can modify the Base Score but does not directly impact the severity rating. Environmental Score: This group includes metrics that allow organizations to customize the CVSS score based on their specific environment. \nFor example 4.5.", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "navigator_attribute_description": "CVSSv2 scores vulnerabilities from 0.0 to 10.0 based on their severity, using Base, Temporal, and Environmental metrics, such as a score of 4.5 for customization."}, "v2_vector": {"caption": "CVSSv2 Vector", "description": "CVSSv2 (Common Vulnerability Scoring System version 2) Vector is a concise representation of the characteristics and attributes of a vulnerability. \nIt's a string that encapsulates various parameters used to calculate the CVSSv2 Base Score, which is a numerical representation of the severity of the vulnerability.The CVSSv2 Vector consists of several components, each representing different aspects of the vulnerability: Base Metrics: These are the core metrics used to calculate the Base Score. They include: Access Vector (AV): Describes how an attacker can exploit the vulnerability (e.g., Local, Adjacent Network, Network). Access Complexity (AC): Describes how complex the exploitation of the vulnerability is (e.g., Low, Medium, High). Authentication (Au): Describes the level of authentication required to exploit the vulnerability (e.g., None, Single, Multiple). Confidentiality Impact (C): Describes the impact on confidentiality if the vulnerability is exploited (e.g., None, Partial, Complete). Integrity Impact (I): Describes the impact on integrity if the vulnerability is exploited (e.g., None, Partial, Complete). Availability Impact (A): Describes the impact on availability if the vulnerability is exploited (e.g., None, Partial, Complete). Temporal Metrics: These are additional metrics that may change over time. They include: Exploitability (E): Describes the likelihood that an exploit will be developed (e.g., Unproven, Proof-of-Concept, Functional, High). Remediation Level (RL): Describes the availability of a solution to mitigate the vulnerability (e.g., Official Fix, Temporary Fix, Workaround, Unavailable). Report Confidence (RC): Describes the confidence in the existence of the vulnerability and the reliability of the report (e.g., Unknown, Reasonable, Confirmed). Environmental Metrics: These metrics are specific to the environment in which the vulnerability exists. \nThey include: Collateral Damage Potential (CDP): Describes the potential impact on assets beyond the one directly affected by the vulnerability (e.g., None, Low, Medium, High). \nTarget Distribution (TD): Describes the distribution and diversity of potential targets (e.g., None, Low, Medium, High). \nConfidentiality Requirement (CR): Describes the level of confidentiality of the affected assets (e.g., None, Low, Medium, High). \nIntegrity Requirement (IR): Describes the level of integrity required for the affected assets (e.g., None, Low, Medium, High). \nAvailability Requirement (AR): Describes the level of availability required for the affected assets (e.g., None, Low, Medium, High).\nFor example AV:N/AC:L/Au:N/C:C/I:C/A:C.", "llm_description": "CVSSv2 (Common Vulnerability Scoring System version 2) Vector is a concise representation of the characteristics and attributes of a vulnerability. \nIt's a string that encapsulates various parameters used to calculate the CVSSv2 Base Score, which is a numerical representation of the severity of the vulnerability.The CVSSv2 Vector consists of several components, each representing different aspects of the vulnerability: Base Metrics: These are the core metrics used to calculate the Base Score. They include: Access Vector (AV): Describes how an attacker can exploit the vulnerability (e.g., Local, Adjacent Network, Network). Access Complexity (AC): Describes how complex the exploitation of the vulnerability is (e.g., Low, Medium, High). Authentication (Au): Describes the level of authentication required to exploit the vulnerability (e.g., None, Single, Multiple). Confidentiality Impact (C): Describes the impact on confidentiality if the vulnerability is exploited (e.g., None, Partial, Complete). Integrity Impact (I): Describes the impact on integrity if the vulnerability is exploited (e.g., None, Partial, Complete). Availability Impact (A): Describes the impact on availability if the vulnerability is exploited (e.g., None, Partial, Complete). Temporal Metrics: These are additional metrics that may change over time. They include: Exploitability (E): Describes the likelihood that an exploit will be developed (e.g., Unproven, Proof-of-Concept, Functional, High). Remediation Level (RL): Describes the availability of a solution to mitigate the vulnerability (e.g., Official Fix, Temporary Fix, Workaround, Unavailable). Report Confidence (RC): Describes the confidence in the existence of the vulnerability and the reliability of the report (e.g., Unknown, Reasonable, Confirmed). Environmental Metrics: These metrics are specific to the environment in which the vulnerability exists. \nThey include: Collateral Damage Potential (CDP): Describes the potential impact on assets beyond the one directly affected by the vulnerability (e.g., None, Low, Medium, High). \nTarget Distribution (TD): Describes the distribution and diversity of potential targets (e.g., None, Low, Medium, High). \nConfidentiality Requirement (CR): Describes the level of confidentiality of the affected assets (e.g., None, Low, Medium, High). \nIntegrity Requirement (IR): Describes the level of integrity required for the affected assets (e.g., None, Low, Medium, High). \nAvailability Requirement (AR): Describes the level of availability required for the affected assets (e.g., None, Low, Medium, High).\nFor example AV:N/AC:L/Au:N/C:C/I:C/A:C.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "v2_severity": {"caption": "CVSSv2 Severity", "description": "CVSSv2 (Common Vulnerability Scoring System version 2) Severity refers to the level of threat posed by a vulnerability as determined by its CVSSv2 Base Score. The Base Score is calculated based on various metrics, including the impact and exploitability of the vulnerability. Once calculated, the Base Score is mapped to a severity rating to help organizations prioritize their response to vulnerabilities.\nThe CVSSv2 Severity levels are: None: Vulnerabilities with a Base Score of 0.0. These vulnerabilities have no significant impact and are typically considered low risk. Low: Vulnerabilities with Base Scores ranging from 0.1 to 3.9. These vulnerabilities may have limited impact and are often considered lower priority for remediation. Medium: Vulnerabilities with Base Scores ranging from 4.0 to 6.9.\nThese vulnerabilities have a moderate impact and may require attention, especially if they affect critical systems or data. High: Vulnerabilities with Base Scores ranging from 7.0 to 8.9. These vulnerabilities have a significant impact and should be addressed promptly to mitigate potential risks. Critical: Vulnerabilities with Base Scores ranging from 9.0 to 10.0.\nFor example Critical", "llm_description": "CVSSv2 (Common Vulnerability Scoring System version 2) Severity refers to the level of threat posed by a vulnerability as determined by its CVSSv2 Base Score. The Base Score is calculated based on various metrics, including the impact and exploitability of the vulnerability. Once calculated, the Base Score is mapped to a severity rating to help organizations prioritize their response to vulnerabilities.\nThe CVSSv2 Severity levels are: None: Vulnerabilities with a Base Score of 0.0. These vulnerabilities have no significant impact and are typically considered low risk. Low: Vulnerabilities with Base Scores ranging from 0.1 to 3.9. These vulnerabilities may have limited impact and are often considered lower priority for remediation. Medium: Vulnerabilities with Base Scores ranging from 4.0 to 6.9.\nThese vulnerabilities have a moderate impact and may require attention, especially if they affect critical systems or data. High: Vulnerabilities with Base Scores ranging from 7.0 to 8.9. These vulnerabilities have a significant impact and should be addressed promptly to mitigate potential risks. Critical: Vulnerabilities with Base Scores ranging from 9.0 to 10.0.\nFor example Critical", "group": "entity_specific", "type": "string", "navigator_attribute_description": "CVSSv2 Severity indicates the threat level of a vulnerability based on its Base Score, categorized as None (0.0), Low (0.1-3.9), Medium (4.0-6.9), High (7.0-8.9), and Critical (9.0-10.0), with Critical representing the highest risk.", "navigator_is_full_value_required": true}, "v2_exploitability": {"caption": "CVSSv2 Exploitability", "description": "CVSSv2 (Common Vulnerability Scoring System version 2) Exploitability refers to the ease with which a vulnerability can be exploited to carry out an attack. It is one of the metrics used to calculate the CVSSv2 Base Score, which represents the severity of the vulnerability.\nFor example 6.8.", "llm_description": "CVSSv2 (Common Vulnerability Scoring System version 2) Exploitability refers to the ease with which a vulnerability can be exploited to carry out an attack. It is one of the metrics used to calculate the CVSSv2 Base Score, which represents the severity of the vulnerability.\nFor example 6.8.", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "navigator_attribute_description": "CVSSv2 Exploitability measures how easily a vulnerability can be exploited, contributing to the Base Score, such as a score of 6.8."}, "v2_impact_score": {"caption": "CVSSv2 Impact Score", "description": "CVSSv2 (Common Vulnerability Scoring System version 2) Impact Score is a metric used to quantify the potential impact of a security vulnerability on an affected system.It is scored on a scale of 0 to 10, with 0 indicating no impact and 10 indicating the highest impact.\nThe CVSSv2 Impact Score is the weighted sum of the Confidentiality, Integrity, and Availability impacts, with weights applied to reflect their relative importance. The resulting score ranges from 0.0 to 10.0, with higher scores indicating a more severe impact on the affected system or data.\nFor example 5.1.", "llm_description": "CVSSv2 (Common Vulnerability Scoring System version 2) Impact Score is a metric used to quantify the potential impact of a security vulnerability on an affected system.It is scored on a scale of 0 to 10, with 0 indicating no impact and 10 indicating the highest impact.\nThe CVSSv2 Impact Score is the weighted sum of the Confidentiality, Integrity, and Availability impacts, with weights applied to reflect their relative importance. The resulting score ranges from 0.0 to 10.0, with higher scores indicating a more severe impact on the affected system or data.\nFor example 5.1.", "group": "entity_specific", "type": "double", "range_selection": true, "min": 0.0, "max": 10.0, "navigator_attribute_description": "CVSSv2 Impact Score quantifies the potential impact of a security vulnerability on a system, ranging from 0 (no impact) to 10 (highest impact), calculated as a weighted sum of Confidentiality, Integrity, and Availability impacts, such as a score of 5.1."}, "software_list": {"caption": "Software List", "description": "Softwares affected by Vulnerability.\nThis list is crucial for identifying and prioritizing remediation efforts to address security vulnerabilities effectively.\nFor example product:dns_server,vendor:cisco.", "llm_description": "Softwares affected by Vulnerability.\nThis list is crucial for identifying and prioritizing remediation efforts to address security vulnerabilities effectively.\nFor example product:dns_server,vendor:cisco.", "group": "entity_specific", "type": "string", "candidate_key": false, "ui_visibility": false}, "vulnerability_first_observed_date": {"caption": "Vulnerability First Observed", "description": "Date at which the vulnerability was first observed on a host.\nThis date marks the initial recognition or awareness of the vulnerability's existence.", "llm_description": "Date at which the vulnerability was first observed on a host.\nThis date marks the initial recognition or awareness of the vulnerability's existence.", "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "Date when the vulnerability was first recognized on a host."}, "last_modified_date": {"caption": "Last Modified", "description": "Date when the information or details related to that vulnerability was last updated or changed.\nThis date indicates the most recent changes made to the vulnerability entry, such as updates to its description, associated metadata, remediation guidance, or status.", "llm_description": "Date when the information or details related to that vulnerability was last updated or changed.\nThis date indicates the most recent changes made to the vulnerability entry, such as updates to its description, associated metadata, remediation guidance, or status.", "group": "entity_specific", "type": "timestamp", "category": "Risk and Mitigation", "navigator_attribute_description": "Last modified date indicates when the vulnerability entry was last updated, including changes to its description, metadata, or remediation guidance."}, "published_date": {"caption": "Published On", "description": "Date when the vulnerability information is made publicly available.\n It's the date when the security community, software vendors, and the public become aware of the vulnerability's existence.", "llm_description": "Date when the vulnerability information is made publicly available.\n It's the date when the security community, software vendors, and the public become aware of the vulnerability's existence.", "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "Date when the vulnerability information is publicly disclosed, marking its awareness by the security community and software vendors."}, "vendor_severity": {"caption": "<PERSON><PERSON><PERSON>", "description": "Severity level assigned assigned by a vendor to a security vulnerability or issue discovered in their product or service.\nThis severity level indicates the potential impact or risk posed by the vulnerability.\nFor example 7,High.", "llm_description": "Severity level assigned assigned by a vendor to a security vulnerability or issue discovered in their product or service.\nThis severity level indicates the potential impact or risk posed by the vulnerability.\nFor example 7,High.", "group": "entity_specific", "type": "string", "category": "Severity and Impact Metrics", "navigator_attribute_description": "Vendor severity is the risk level, such as 7 (High), assigned to a security vulnerability in their product.", "navigator_is_full_value_required": true}, "patch_available": {"caption": "Patch Available", "description": "Indicates whether a patch or fix is available from the vendor to address the vulnerability.\nWhen a vulnerability is discovered in software or systems, vendors often work to develop patches or updates that can remediate the issue and improve the security of the affected product.\nIt is boolean value derived based on the fields from each data source.", "group": "entity_specific", "type": "string", "category": "Risk and Mitigation", "navigator_attribute_description": "Indicates if a vendor-provided patch or fix is available for a discovered vulnerability in software or systems.", "navigator_is_full_value_required": true}, "exploit_available": {"caption": "Exploit Available", "description": "Indicating whether an exploit is available or not.In vulnerability management and security contexts, this term is often used to indicate the level of risk associated with a particular vulnerability.\n\nIf marked as 'true', it means that there is a known exploit publicly available that attackers could potentially use to take advantage of the vulnerability.If marked as 'false', it means that there is no known exploit publicly available at the time. However, this doesn't necessarily mean the vulnerability is not dangerous or can't be exploited in other ways.", "llm_description": "Indicating whether an exploit is available or not.In vulnerability management and security contexts, this term is often used to indicate the level of risk associated with a particular vulnerability.\n\nIf marked as 'true', it means that there is a known exploit publicly available that attackers could potentially use to take advantage of the vulnerability.If marked as 'false', it means that there is no known exploit publicly available at the time. However, this doesn't necessarily mean the vulnerability is not dangerous or can't be exploited in other ways.", "group": "entity_specific", "type": "string", "category": "Risk and Mitigation", "navigator_attribute_description": "Indicates if a known exploit is publicly available for a vulnerability ('true' if available, 'false' if not, but still potentially dangerous).", "navigator_is_full_value_required": true}, "recommendation": {"caption": "Recommendation", "description": "Recommendations provide guidance on actions to take to reduce or eliminate the risk associated with the vulnerability.", "llm_description": "Recommendations provide guidance on actions to take to reduce or eliminate the risk associated with the vulnerability.", "group": "entity_specific", "type": "string", "category": "Risk and Mitigation", "navigator_attribute_description": "Recommendations guide actions to mitigate risks from vulnerabilities."}, "ms_recommended_update": {"caption": "Microsoft Recommended Update", "description": "Recommended update of the vulnerability.\n\nMicrosoft Defender continuously monitors system activity and checks for potential security vulnerabilities, malware infections, and other threats. When it identifies a security issue that can be addressed by installing a specific update provided by Microsoft, it may label that update as a 'RecommendedSecurityUpdate'.\nFor example 'Uninstall JDK V3'.", "llm_description": "Recommended update of the vulnerability.\n\nMicrosoft Defender continuously monitors system activity and checks for potential security vulnerabilities, malware infections, and other threats. When it identifies a security issue that can be addressed by installing a specific update provided by Microsoft, it may label that update as a 'RecommendedSecurityUpdate'.\nFor example 'Uninstall JDK V3'.", "examples": "Uninstall JDK V3", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Recommended updates for vulnerabilities, such as 'Uninstall JDK V3', are provided by Microsoft Defender based on its continuous security monitoring."}, "ms_recommended_update_id": {"caption": "Microsoft Recommended Update ID", "description": "Recommended update ID of the vulnerability.Identifier used by Microsoft Defender to reference specific updates or patches recommended for their products, such as Windows operating systems, Office suite, or other Microsoft software.\n\nMicrosoft releases updates regularly to address various issues, including security vulnerabilities, performance enhancements, and bug fixes. These updates are often categorized and assigned unique identifiers for tracking and reference purposes. This allows users, administrators, and automated systems to easily identify and manage the recommended updates provided by Microsoft.", "llm_description": "Recommended update ID of the vulnerability.Identifier used by Microsoft Defender to reference specific updates or patches recommended for their products, such as Windows operating systems, Office suite, or other Microsoft software.\n\nMicrosoft releases updates regularly to address various issues, including security vulnerabilities, performance enhancements, and bug fixes. These updates are often categorized and assigned unique identifiers for tracking and reference purposes. This allows users, administrators, and automated systems to easily identify and manage the recommended updates provided by Microsoft.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Recommended update ID used by Microsoft Defender to reference specific updates for products like Windows and Office, facilitating tracking and management of updates addressing vulnerabilities and enhancements."}, "cisa_exploit_add_date": {"caption": "CISA Exploit Addition", "description": "Indicate the date when an exploit associated with a vulnerability was added to the CISA Key Exploited Vulnerabilities (KEV) list sourced from the National Vulnerability Database (NVD).", "llm_description": "Indicate the date when an exploit associated with a vulnerability was added to the CISA Key Exploited Vulnerabilities (KEV) list sourced from the National Vulnerability Database (NVD).", "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "Date when an exploit linked to a vulnerability was added to the CISA KEV list from the NVD."}, "cisa_action_due_date": {"caption": "CISA Action Due", "description": "Date that CISA say an action should be completed by.\nBy tracking the action due date, organizations can prioritize remediation efforts for vulnerabilities listed in the CISA KEV list, ensuring timely mitigation to reduce the risk of exploitation and potential impact on critical systems and infrastructure.", "llm_description": "Date that CISA say an action should be completed by.\nBy tracking the action due date, organizations can prioritize remediation efforts for vulnerabilities listed in the CISA KEV list, ensuring timely mitigation to reduce the risk of exploitation and potential impact on critical systems and infrastructure.", "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "CISA action due date indicates when organizations must complete remediation for vulnerabilities in the CISA KEV list to mitigate risks to critical systems."}, "cisa_required_action": {"caption": "CISA Required Action", "description": "Action reported in CISA KEV. Helps prioritize vulnerabilities. If CISA mandates specific actions, it likely indicates a more critical or imminent threat.", "llm_description": "Action reported in CISA KEV. Helps prioritize vulnerabilities. If CISA mandates specific actions, it likely indicates a more critical or imminent threat.", "examples": "Apply updates per vendor instructions", "group": "entity_specific", "type": "string", "navigator_attribute_description": "CISA required actions prioritize vulnerabilities and indicate critical threats when mandated."}, "epss": {"caption": "EPSS", "description": "EPSS stands for Exploit Prediction Scoring System. \nIt's a data-driven framework designed to estimate the likelihood (probability) of a software vulnerability being exploited in the real world.", "llm_description": "EPSS stands for Exploit Prediction Scoring System. \nIt's a data-driven framework designed to estimate the likelihood (probability) of a software vulnerability being exploited in the real world.", "group": "entity_specific", "type": "double", "range_selection": true, "ui_visibility": false}, "epss_percentile": {"caption": "EPSS Percentile", "description": "EPSS percentage score assigned to a specific vulnerability that indicates how likely it is to be exploited compared to other vulnerabilities.\nFor example 2.0.", "llm_description": "EPSS percentage score assigned to a specific vulnerability that indicates how likely it is to be exploited compared to other vulnerabilities.\nFor example 2.0.", "group": "entity_specific", "type": "double", "range_selection": true, "step_interval": 0.01, "category": "Risk and Mitigation", "navigator_attribute_description": "EPSS percentile denotes the exploitability likelihood of a vulnerability, such as a score of 2.0."}, "cwe": {"caption": "CWE", "description": "CWE stands for Common Weakness Enumeration.It is a community-developed list of common software and hardware weaknesses that can lead to security vulnerabilities.\nCWE provides a standardized way to identify, categorize, and describe software and hardware weaknesses, making it easier to understand and address security vulnerabilities across different systems and applications.\nFor Example CWE-427.", "llm_description": "CWE stands for Common Weakness Enumeration.It is a community-developed list of common software and hardware weaknesses that can lead to security vulnerabilities.\nCWE provides a standardized way to identify, categorize, and describe software and hardware weaknesses, making it easier to understand and address security vulnerabilities across different systems and applications.\nFor Example CWE-427.", "group": "entity_specific", "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "CWE (Common Weakness Enumeration) is a standardized list of software and hardware weaknesses, like CWE-427, that can lead to security vulnerabilities."}, "cpe": {"caption": "CPE", "description": "The Common Platform Enumeration (CPE) identifier for the software affected by the vulnerability.\nIt is a standardized method for identifying and describing software applications, operating systems, and hardware devices. CPE is maintained by the National Institute of Standards and Technology (NIST) as part of the Common Vulnerabilities and Exposures (CVE) system.\nFor example 'cpe:/a:microsoft:internet_explorer:11.0'.", "llm_description": "The Common Platform Enumeration (CPE) identifier for the software affected by the vulnerability.\nIt is a standardized method for identifying and describing software applications, operating systems, and hardware devices. CPE is maintained by the National Institute of Standards and Technology (NIST) as part of the Common Vulnerabilities and Exposures (CVE) system.\nFor example 'cpe:/a:microsoft:internet_explorer:11.0'.", "group": "entity_specific", "type": "string", "data_structure": "list", "ui_visibility": false}, "normalized_severity": {"caption": "Severity", "description": "This field is a coalesce of CVSS V31 Severity, CVSS V30 Severity and CVSS V2 severity respectively.\nExamples are 2,High.", "llm_description": "Normalized Severity is the severity score assigned to a vulnerability based on the Common Vulnerability Scoring System versions 3.1, 3.0 and 2.0\nThis score helps to quantify the severity of a vulnerability, allowing organizations to prioritize their responses to security threats. It ranges from 0.0 to 10.0, with 10.0 being the most severe.\nThe severity is determined by assessing various metrics such as the exploitability of the vulnerability, the impact it could have on the system, and whether mitigations or workarounds are available.\nHere's how the Normalized Severity scores typically translate: For vulnerabilities with V3.1 severity available:\nNone: Vulnerabilities with no impact on the confidentiality, integrity, or availability of the affected system or data,\nLow: Vulnerabilities with minor impact or limited scope, typically requiring significant effort to exploit or posing minimal risk,\nMedium: Vulnerabilities with moderate impact, potentially leading to partial compromise of the system or data,\nHigh: Vulnerabilities with significant impact, potentially resulting in complete compromise of the system or data, or causing widespread disruption,\nCritical: Vulnerabilities with severe impact, often easily exploitable, and capable of causing catastrophic damage or compromise.\nAnd for the remaining: 0.0 to 3.9: Low severity 4.0 to 6.9: Medium severity 7.0 to 8.9: High severity 9.0 to 10.0: Critical severity.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "vendor_id": {"caption": "Vendor ID", "description": "ID given by each vendor to the vulnerabilities found in the host.\n It serves as a unique reference point for tracking and managing vulnerabilities across different systems and platforms.\nFrom Qualys,it is based on qid\nDefender based on cveid,Tenable.sc based on pluginID.\nExample is 23456.", "llm_description": "ID given by each vendor to the vulnerabilities found in the host.\n It serves as a unique reference point for tracking and managing vulnerabilities across different systems and platforms.\nFrom Qualys,it is based on qid\nDefender based on cveid,Tenable.sc based on pluginID.\nExample is 23456.", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Vendor_id is a unique identifier assigned by vendors like Qualys (qid), Defender (cveid), and Tenable.sc (pluginID) for tracking vulnerabilities, such as 23456."}, "found_in_organisation": {"caption": "Found In Organisation", "description": "To filter the vulnerabilities found in the organisation.", "examples": "False", "group": "source_specific", "type": "string", "derived_field": true, "ui_visibility": false, "to_be_deprecated": true}, "qualys_pci_flag": {"caption": "Qualys PCI Flag", "description": "Qualys PCI Flag is a binary indicator used by the Qualys platform to assess and track an organization's compliance with the Payment Card Industry Data Security Standard (PCI DSS).\n It's a crucial tool for organizations handling cardholder data to ensure they meet the rigorous security requirements set by the PCI Council.\n\n1: This indicates that a specific asset or resource within the organization has a configuration or vulnerability that directly violates a PCI DSS requirement.\n0: This means that the asset or resource does not have any known configurations or vulnerabilities that violate PCI DSS requirements.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "qualys_consequence": {"caption": "Qualys Consequence", "description": "Potential impact or outcome that could occur if a vulnerability is successfully exploited by an attacker.\nThis field provides details on the ramifications of a security flaw being leveraged maliciously.\nQualys example is 'Successfully exploiting this vulnerability can cause a denial of service'.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "qualys_category": {"caption": "Qualys Category", "description": "Category from Qualys the vulnerability is assigned to.\nThe classification or grouping of vulnerabilities based on their characteristics, impact, or affected systems. This field helps users to organize and prioritize vulnerabilities by providing a high-level overview of the types of threats they may face.\n For example Firewall,Database.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "qualys_threat_intel": {"caption": "<PERSON><PERSON>ys Threat <PERSON>", "description": "Threat Intels reported by Qualys.\nThis is obtained by using threat_intel_lookup by keeping id and value and as a comma separated list.\nFor example ['Easy_Exploit','No_Patch'].", "group": "source_specific", "type": "string", "data_structure": "list", "ui_visibility": false, "to_be_deprecated": true}, "bugtraq_id": {"caption": "Bugtraq ID", "description": "A unique identifier assigned to a security vulnerability that is documented and listed in the Bugtraq mailing list, which is a public forum for discussing computer security vulnerabilities and related topics.\nFrom Qualys bugtraq id is extracted from BUGTRAQ_LIST.\nFrom Tenable.sc bugtraq id is bid\nFor example 10979.", "group": "source_specific", "type": "string", "data_structure": "list", "ui_visibility": false, "to_be_deprecated": true}, "nvd_status": {"caption": "NVD Status", "description": "Status of vulnerability in the NVD Catalogue.\nNVD is a comprehensive repository of information about known vulnerabilities in software products and systems.\nThis field provides information about the current state of a vulnerability, including whether it is actively being monitored, whether it has been resolved, or whether it is still open and poses a risk to affected systems.For example Analyzed.", "llm_description": "Status of vulnerability in the NVD Catalogue.\nNVD is a comprehensive repository of information about known vulnerabilities in software products and systems.\nThis field provides information about the current state of a vulnerability, including whether it is actively being monitored, whether it has been resolved, or whether it is still open and poses a risk to affected systems.For example Analyzed.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Status of vulnerability in the NVD Catalogue, indicating if it is monitored, resolved, or still poses a risk, such as \"Analyzed.\"", "navigator_is_full_value_required": true}, "tenable_vulnerability_mitigation_status": {"caption": "Tenable Vulnerability Mitigation Status", "description": "Indicates whether a vulnerability has been mitigated or not. \nIt serves as a marker to track the status of mitigation efforts for identified vulnerabilities using tenable.sc.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "tenablesc_accept_risk": {"caption": "Tenable Risk Accepted", "description": "The acceptability of a risk identified by Tenable.\nIndicates whether the risk associated with a vulnerability has been accepted by the organization.\nIt is represented as a boolean value, meaning it can have two states: true or false.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "tenablesc_recast_risk": {"caption": "Tenable Risk Recast", "description": "This indicates whether the risk associated with a vulnerability has been recalculated or adjusted.\nIf tenable recast risk is set to true for a particular vulnerability, it indicates that the risk associated with that vulnerability has been re-evaluated or adjusted, possibly due to new information or changes in the threat landscape.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "tenablesc_stig_severity": {"caption": "Tenable STIG Severity", "description": "This is a rating or score assigned to a vulnerability based on its alignment with the guidelines and requirements outlined in the Security Technical Implementation Guide (STIG).\n In Tenable.sc, refers to the severity rating assigned to a vulnerability based on the Security Technical Implementation Guide (STIG).For example I.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "tenable_exploit_ease": {"caption": "Tenable Exploit Ease", "description": "The metric that assesses the ease with which a particular vulnerability can be exploited by malicious actors to compromise a system or network. \nThis metric helps organizations gauge the urgency of remediation efforts by considering the likelihood of an exploit being developed and deployed in the wild.For example Exploits are available, No known exploits are available.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "tenablesc_risk_factor": {"caption": "Tenable Risk Factor", "description": "The severity or risk level associated with a particular vulnerability or security issue identified during vulnerability assessment or analysis using tenable.\nIt helps organizations prioritize remediation efforts based on the potential impact of the vulnerability.\nFor example critical.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "has_cisa_kev_exploit": {"caption": "CISA Known Exploited Vulnerabilities", "description": "This is a boolean flag indicating whether a vulnerability identified by Wiz's Vulnerability Findings API is associated with a CISA Known Exploited Vulnerability (KEV).\n Vulnerabilities flagged as CISA Known Exploited Vulnerabilities (KEV) require immediate attention and remediation since they are actively being leveraged by threat actors in cyber attacks.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "tenableio_exploit_available": {"caption": "Tenable.io Exploit Available", "description": "Indicates whether the vulnerability is associated with a software, package or application that has reached its end-of-life (EOL) status and no longer receives security updates.", "llm_description": "Indicates whether the vulnerability is associated with a software, package or application that has reached its end-of-life (EOL) status and no longer receives security updates.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates if a vulnerability relates to software that is no longer supported, such as an end-of-life application."}, "tenableio_unsupported_by_vendor": {"caption": "Tenable.io Unsupported By <PERSON><PERSON><PERSON>", "description": "Indicates whether the Tenable.io is unsupported by vendor.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "rapid7_vulnerability_risk_score": {"caption": "Rapid 7 Vulnerability Risk Score", "description": "The risk score of the vulnerability in Rapid 7.", "group": "source_specific", "examples": "720.0", "type": "integer"}, "is_malwarekit_available": {"caption": "Malware Kit Available", "description": "A boolean flag indicating if threat intelligence has associated the vulnerability with any known malware kit infrastructure.\n A value of true signifies active malware kit activity is present, prompting high-priority remediation, while false indicates no current evidence of such a threat.", "group": "entity_specific", "examples": "", "type": "string"}}, "dashboard_identifier": "EI"}