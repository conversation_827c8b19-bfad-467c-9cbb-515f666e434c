{"caption": "Finding", "entity_classification": "Supporting Entity", "navigator_enabled": true, "description": "A finding is a specific piece of information that indicates a potential security issue or anomaly within an organization's IT infrastructure, systems, applications, or processes.", "navigator_constraints": "Tracked finding refers to an open finding.\nIf the user inquires about CVE-related findings, do not apply the CVE filter at the Finding entity. Instead, apply the filter at the Vulnerability entity if available; otherwise, apply it at the Finding entity.\nDo not apply filters for a query or property on other entities or tables if it is already filtered in the Finding entity via finding_title or display_label (avoid duplicate filtering)", "navigator_description": "# Finding Table Information Description\n\n## Overview\nThe Finding table is a comprehensive repository tracking security issues identified across an organization's IT infrastructure. Each record captures detailed information about a specific security finding, enabling risk assessment, prioritization, and remediation tracking.\n\n## Each Finding Record Contains:\n\n**Identity Information**\n- Unique identifier (p_id) serving as the primary key for each finding\n- Human-readable display label that describes the finding in user-friendly terms\n- Type classification categorizing the finding (Software Vulnerability, Control Gap, Threat Detection)\n- Origin data identifying the source systems that discovered the issue (AWS, CrowdStrike, MS Azure, etc.)\n- Count of origin showing how many different data sources contributed to the finding\n\n**Risk Assessment Data**\n- Exposure severity rating (Critical, High, Medium, Low) indicating risk-based classification\n- Numerical exposure score quantifying overall risk level based on impact and likelihood\n- Likelihood/criticality score (0-1000) assessing probability of exploitation\n- Assessment severity rating showing importance of the associated security assessment\n- Exposure weightage determining how much this finding contributes to overall scoring\n\n**Temporal Information**\n- First found date marking initial discovery during data ingestion\n- First seen date showing earliest observation across all data sources\n- Last updated date indicating most recent modification to any attribute\n- Last found date showing most recent observation during data ingestion\n- Last active date indicating most recent active detection in the environment\n- Reopened date (when applicable) showing when a previously closed finding recurred\n- Activity status (Active/Inactive) reflecting current detection state\n- Lifetime metrics measuring duration between first seen and last active dates\n- Recent activity tracking days since the finding was last observed\n- Observed lifetime calculating duration between first and last found dates\n- Recency measuring days since last discovery during data ingestion\n\n**Status and Lifecycle Data**\n- Remediation status (Open/Closed) showing current state of issue resolution\n- Contributing module indicating which system components use this finding (Exposure, Reporting, or both)\n\n**Descriptive Content**\n- Detailed finding evidence explaining the security issue, context, and potential impact\n- Failure reasons specifying which rules or conditions were violated\n- Assessment title naming the security evaluation that generated the finding\n\n**Scope and Context**\n- Scope definition outlining evaluation boundaries for the finding\n- Exposure category classifying the type of security issue\n- Data source subset names identifying specific API endpoints providing finding data\n\nThis rich dataset enables security teams to identify, prioritize, and remediate vulnerabilities based on severity, status, recency, and affected assets. The table supports sophisticated queries for security posture assessment, compliance verification, and risk management across the organization's technology environment.", "navigator_graph_node_description": "The Finding entity represents identified security issues within an organization's environment, including vulnerabilities, misconfigurations, compliance gaps, and control weaknesses. It captures key attributes such as finding title, description, status (open/closed), severity level, and associated assessment information. Security attributes include exposure severity, likelihood scores, vulnerability metrics derived from CVSS ratings, and contribution to overall security posture. Findings are linked to affected assets across the infrastructure and maintain lifecycle information from first discovery through remediation or closure. This entity enables prioritization of security efforts through risk-based scoring that considers both the severity of issues and the exposure level of affected assets.", "navigator_entity_description": "\nA Finding is a specific piece of information that indicates a potential security issue or anomaly within an organization's IT infrastructure, systems, applications, processes, or operations. Findings represent the actionable output of security assessments and evaluations. \n\nAs the assessment fails, a finding is generated for an asset. \nThe following are the list of assessments and the corresponding findings:\n\nTitle of the security assessment ==> Associated finding title                        \n1.'Devices do not have vulnerabilities related to expired SSL certificates'==> 'Expired SSL certificates'\n2.'Devices do not have password related vulnerabilities'==> 'Device has password-related vulnerabilities'\n3.'Devices employ full-disk encryption'==> 'Full disk encryption not enforced'\n4.'Devices have endpoint protection signatures frequently updated'==> 'Outdated EDR signatures'\n5.'Devices are regularly reconciled in the appropriate CMDB'==> 'CMDB reconciliation failed'\n6.'Devices have firewall protection enabled'==> 'Host firewall disabled'\n7.'Devices are recorded in the appropriate CMDB'==> 'Asset not in CMDB'\n8.'Devices have endpoint protection agents onboarded'==>'Missing EDR agent'\n9.'Devices have a single assigned owner'==> 'Ownership conflict'\n10.'Devices are scanned for malicious code at the defined frequency'==>'Malware scan overdue'\n11.'Devices enforce blocking of known malware'==>'Malware blocking disabled'\n12.'Devices follow approved naming conventions'==>'Inconsistent asset naming'\n13.'Devices have an accountable owner'==> 'Unaccountable devices'\n14.'Windows devices are configured with right User Account Control (UAC)'==> 'UAC Misconfigured' \n15.'Software vulnerabilities on internal devices are remediated within SLA'==>'CVE-XXXX-XXXX'\n16.'Software vulnerabilities on network devices are remediated within SLA'==>'CVE-XXXX-XXXX'\n17.'Software vulnerabilities on external devices are remediated within SLA'==>'CVE-XXXX-XXXX'\n\nfor software vulnerabilities, the corresponding finding title is the CVE ID.\n\nThe Finding entity contains essential details about identified security concerns, their nature, severity, and remediation status. It includes attributes such as display label, detailed description, current status (open/closed), first found date, and last updated date. Each finding is classified by type (such as Software Vulnerability, Control Gap, or Misconfiguration) and categorized based on exposure category.  \n\nSecurity-related attributes provide context for risk prioritization. These include exposure severity ratings (Critical to Low), likelihood assessments, vulnerability severity derived from standards like CVSS, and numerical scoring metrics that quantify risk levels. Findings maintain temporal information tracking their lifecycle from discovery through remediation, including status changes and reopening events if vulnerabilities reoccur.  \n\nFindings are directly connected to affected assets within the organization's environment, creating relationships between security issues and the infrastructure components they impact. This connection enables effective remediation planning and risk management.  \n\nThe Finding entity aggregates information from various security tools, vulnerability scanners, compliance frameworks, and cloud security platforms, offering a comprehensive view of security posture across the organization.  \n\nIn addition, the Finding entity includes specific quantitative attributes:  \n\n- Count of Origin: The total count of data sources that contributed to the identification of the finding.  \n- Exposure Score: Numerical value quantifying the risk level of the finding based on asset impact and exploitation likelihood.  \n- Likelihood Score: Numerical value (0-1000) assessing the probability of exploitation based on vulnerability severity and asset exposure factors.  \n- Recent Activity: Number of days since the finding was last updated or observed in the environment.  \n    - When a user queries for maximum, minimum, highest, lowest, or filtered values (e.g., greater than or less than) related to the above quantitative attributes, results should focus only on the Finding entity without including other entities for that specific part of the query. However, for other aspects of the query, additional entities may be included as needed. \n    - The above quantitative attributes should not be used when calculating the total count of findings to avoid redundancy and we dont have *sum* operation support.\n\t\n\n", "navigator_examples": ["User Query: 'Show all Active Findings' Output: 'finding'", "User Query: 'Show all Control Gap findings in the organization' Output: 'finding'"], "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.", "navigator_attribute_description": "Unique identifier that serves as the primary key for each Finding record", "group": "common", "type": "string", "category": "General Information", "internally_generated": true}, "display_label": {"caption": "Display Label", "description": "The derived and \"best known\" identifier or name, based on the attribute that best uniquely identifies it. Finding Title field is used and if in case its not available then the ID of the Finding is used Eg. AWS Config should be enabled", "navigator_attribute_description": "A human-readable display label(to show in UI) of the finding which is a CVE ID when applicable to vulnerability findings.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Finding, Host, Person, Vulnerability etc.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information", "internally_generated": true, "navigator_disabled": true}, "type": {"caption": "Type", "description": "The specific type of the Entity. Eg. Cloud", "navigator_attribute_description": "The specific category of finding (e.g., 'Software Vulnerability', 'Control Gap', 'Threat Detection').", "group": "common", "type": "string", "ui_visibility": true, "navigator_is_full_value_required": true, "category": "General Information"}, "origin": {"caption": "Origin", "description": "Data source in which the entity was extracted (found or derived). Eg. AWS, MS Azure", "navigator_attribute_description": "The data source based on which this finding was generated (e.g. Knowledge Graph).", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information", "data_structure": "list", "navigator_is_full_value_required": true}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.", "navigator_attribute_description": "Total number of data sources that contributed to identifying this finding.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested. Eg. AWS SH Findings", "navigator_attribute_description": "The data source used to provide data for this finding.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information", "data_structure": "list", "internally_generated": true, "navigator_is_full_value_required": true}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.\nThis will be the minimum time at which the entity is observed within the scope of inventory run.", "navigator_attribute_description": "The initial date when this finding was discovered during data ingestion.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "The initial observation date of the entity as inferred from available data sources.", "navigator_attribute_description": "The earliest date when this finding was observed across all data sources.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\\nDefaults to First Found.\\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "navigator_attribute_description": "Most recent date when any attribute of this finding was modified.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "navigator_attribute_description": "Most recent date when this finding was observed during data ingestion.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which entity was active as inferred from available data sources.\nThis date is determined by considering the maximum value of dates contributed by each data source for the entity in question.\nThis includes data such as Finding Last seen date, Status changes, or any other indicators of the entity's recent engagement or interaction.", "navigator_attribute_description": "Most recent date when this finding was actively detected in the environment.", "group": "common", "type": "timestamp", "category": "General Information", "ui_visibility": true}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \\n\\nIf the time since the last inventory update is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \\nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "navigator_attribute_description": "Current status of the finding - 'Active' if recently observed, 'Inactive' if not.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information", "navigator_is_full_value_required": true}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "navigator_attribute_description": "Total duration in days between the first_seen_date and last_active_date.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "navigator_attribute_description": "Number of days since the finding was last observed as active.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score": {"caption": "Completeness Quality Score", "description": "A metric representing the extent to which required data attributes for the entity are populated across ingested data sources. This score helps evaluate the completeness of the entity's data profile.", "group": "enrichment", "examples": "1", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "aggregated_quality_score": {"caption": "Aggregated Quality Score", "description": "A composite score derived from various quality metrics (such as completeness, correctness, and consistency) to assess the overall data quality of the entity.", "examples": "1", "group": "enrichment", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score_category": {"caption": "Completeness Quality Score Category", "description": "A categorical interpretation of the Completeness Quality Score that classifies the entity's data completeness into levels such as High, Medium, or Low (configurable). This helps in quickly identifying the priority or severity of incomplete data across entities, enabling better triaging and quality improvement decisions.", "examples": "High", "group": "enrichment", "type": "string", "range_selection": true, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "navigator_attribute_description": "Duration in days between first_found_date and last_found_date.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "navigator_attribute_description": "Number of days since the finding was last discovered during data ingestion.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "description": {"caption": "Description", "description": "A detailed description of the entity. This includes details such as root cause for the finding, how to remediate etc.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "location_country": {"caption": "Location Country", "description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information"}, "location_city": {"caption": "Location City", "description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information"}, "department": {"caption": "Department", "description": "The name of the department within the business unit.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considered for updating the last updated date of an entity", "group": "common", "type": "string", "ui_visibility": false, "data_structure": "struct", "internally_generated": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "type": "integer", "ui_visibility": false, "range_selection": true}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive", "group": "common", "type": "integer", "ui_visibility": false}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "type": "string", "category": "General Information", "navigator_is_full_value_required": true}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud provider to which the finding belongs. Eg. AWS, Azure", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Information"}, "account_id": {"caption": "Cloud Account ID", "description": "Unique identifier associated with the account in a cloud provider.In AWS account ID is a 12-digit number unique to each AWS account and in Azure account ID is known as the subscription ID represented as 32-character hexadecimal string. Each cloud resource is associated with an account ID which is either available directly in the source or extracted from the resource ID.Examples are **********, 011c41b3-ba33-448e-86d2-34252xxx.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Information"}, "finding_id": {"caption": "ID", "description": "Unique Identifier of the finding. This includes the account and resource hierarchy under which a finding is generated and is kept as the primary key for the Entity.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "finding_title": {"caption": "Finding Title", "description": "Name of the finding. Derived from fields like Title, Display name etc. from data feeds. For eg. AWS Config should be enabled", "navigator_attribute_description": "Title of the finding, typically a CVE ID (in case of vulnerability finding ) or descriptive label.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "assessment_title": {"caption": "Assessment", "description": "An assessment title is a clear identifier used to evaluate an organization's security measures, focusing on exposure, effectiveness, implementation, and compliance.", "navigator_attribute_description": "Name of the security assessment that identified this finding.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification", "navigator_is_full_value_required": true}, "finding_sub_type": {"caption": "Finding Sub Type", "description": "Sub Type of the finding. For AWS this can be in the format of namespace/category/classifier that classify a finding. For Azure its the unique identifier for the detection logic For eg. Software and Configuration Checks, VM_SuspiciousScreenSaver", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "status": {"caption": "Status", "description": "The normalized status of the investigation into the finding across all vendors is categorized as either Open or Closed. Statuses such as Healthy, Dismissed, Resolved, Suppressed, and Not Applicable are grouped under Closed. Also, if the affected resource is no longer active or has been terminated, the status is set to Closed (with the Finding Activity status becoming Inactive). For active findings, any other status, such as New or Unhealthy, is classified as Open.", "navigator_attribute_description": "Current remediation state of the finding ('Open' or 'Closed').", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification", "navigator_is_full_value_required": true}, "workflow_status": {"caption": "Workflow Status", "description": "Workflow status tracks the progress of investigation into a finding. The possible values are 'Resolved, 'Unresolved' and 'Suppressed'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "vendor_severity_normalised": {"caption": "Severity", "description": "The normalized severity corresponding to the finding across all vendors. Logical grouping is Informational, Low, Medium, High, Critical and Other.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "remediation": {"caption": "Remediation", "description": "Efficiently address identified issues through targeted remediation strategies, ensuring swift resolution and optimization of processes.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Remediation and Compliance"}, "techniques": {"caption": "Techniques", "description": "Refers to specific techniques or methods utilized by attackers or adversaries to exploit vulnerabilities, compromise security, and carry out malicious activities within the assessed environment. These techniques are typically classified based on well-known attack frameworks such as the MITRE ATT&CK framework.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Risk and Threat Intelligence"}, "threats": {"caption": "Threats", "description": "Refers to the specific threats or risks that are associated with the security findings or issues identified during the assessment. These threats describe the potential consequences or impacts of the security vulnerabilities or weaknesses detected in the assessed environment. Each finding may be linked to one or more threats, which helps security teams understand the potential risk posed by the identified issues and prioritize their remediation efforts accordingly.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Risk and Threat Intelligence"}, "tactics": {"caption": "Tactics", "description": "Refers to the tactics employed by attackers or adversaries to exploit vulnerabilities and compromise security within the assessed environment. These tactics are often categorized based on common attack patterns and strategies outlined by frameworks such as the MITRE ATT&CK framework.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Risk and Threat Intelligence"}, "categories": {"caption": "Categories", "description": "refers to the different categories or classifications of security issues or findings identified during the assessment. These categories help to organize and prioritize the remediation efforts based on the nature and severity of the security issues detected. Each finding or security issue may be assigned to one or more categories, such as \"Vulnerability Management,\" \"Identity and Access Management,\" \"Data Protection,\" \"Network Security,\" etc.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Risk and Threat Intelligence"}, "affected_resource_id": {"caption": "Affected Resource ID", "description": "The resource or asset that is affected by the finding. The resource affected could also be the cloud Account in which the finding is generated.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources"}, "affected_asset_display_label": {"caption": "Associated Entities Display Label", "description": "The resource or asset that is affected by the finding. The resource affected could also be the cloud Account in which the finding is generated.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources"}, "affected_asset": {"caption": "Affected Asset", "description": "The resource or asset that is affected by the finding. The resource affected could also be the cloud Account in which the finding is generated.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources", "data_structure": "list"}, "affected_resource_type": {"caption": "Affected Vendor Resource Type", "description": "The vendor specific resource type that is affected by the Finding. For eg.AwsEc2Volume, virtualMachines", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources"}, "affected_resource_type_normalized": {"caption": "Affected Resource Type", "description": "Normalized names for Affected resource Type. Resource type name coming from each source is compared with existing types in EI and categorized. For eg. AWS EC2 Volume is classified as Volume, AWS Account is classified as Cloud Account", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources"}, "finding_resolved_date": {"caption": "Resolved Date", "description": "Date on which the finding was resolved. Only applicable to Findings whose status is marked as closed.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true, "category": "Status Tracking and Resolution"}, "associated_standard": {"caption": "Associated Standard", "description": "The pre-configured security and compliance standards associated with a finding. This indicates the Non-transformed (raw) values. For eg. aws-foundational-security-best-practices/v/1.0.0, nist-800-53/v/5.0.0 ", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Remediation and Compliance"}, "policy_definition_id": {"caption": "Policy Definition ID", "description": "", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "finding_latest_open_date": {"caption": "Latest Open Date", "description": "The most recent date when the finding's status was set to Open, regardless of whether it is the initial opening or a re-opening after being closed.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true, "category": "Status Tracking and Resolution"}, "assessment_id": {"caption": "Assessment ID", "description": "Unique code of each assessment. Derived from Name field in the data feed.", "navigator_attribute_description": "Unique identifier code for the assessment that generated this finding", "group": "entity_specific", "type": "string", "ui_visibility": true}, "scope": {"caption": "<PERSON><PERSON>", "description": "Specifies the scope of the Assessment used to evaluate success and failure.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "exposure_category": {"caption": "Exposure Category", "description": "Type of exposure category the assessment belongs to. It can have values like Software Vulnerability, Control Gap and Threat Detection.", "navigator_attribute_description": "Classification of the finding type (e.g., 'Software Vulnerability').", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information", "navigator_is_full_value_required": true}, "azure_assessment_type": {"caption": "Azure Assessment Type", "description": "A defined category or classification used to specify the type of assessment being conducted within the Azure environment, providing clarity on the focus and purpose of the evaluation.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_user_impact": {"caption": "Azure User Impact", "description": "Refers to the potential effect or consequence that a security finding or issue may have on users or stakeholders within the assessed environment. This attribute provides information on how the identified security vulnerabilities or weaknesses could impact the usability, functionality, accessibility, or overall experience for users interacting with the affected systems or resources.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_implementation_effort": {"caption": "Azure Implementation Effort", "description": "Refers to the potential effect or consequence that a security finding or issue may have on users or stakeholders within the assessed environment. This attribute provides information on how the identified security vulnerabilities or weaknesses could impact the usability, functionality, accessibility, or overall experience for users interacting with the affected systems or resources.", "group": "source_specific", "type": "string", "ui_visibility": true}, "associated_assessment_count": {"caption": "Count of Assessment", "description": "Number assessment associated with findings.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_storage_resource_count": {"caption": "Count of Storage Resource with Open Findings", "description": "Number storage resource associated with open findings.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_cloud_account_with_open_finding_count": {"caption": "Count of Cloud Account with Open Findings", "description": "Number cloud account associated with open findings.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_cloud_account_count": {"caption": "Count of Cloud Account", "description": "Number cloud account associated with findings.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_cluster_resource_count": {"caption": "Count of Cluster Resource with Open Findings", "description": "Number of cluster resource associated with open findings.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_container_resource_count": {"caption": "Count of Container Resource with Open Findings", "description": "Number of container resources associated with open findings.", "group": "enrichment", "type": "integer", "range_selection": true}}, "dashboard_identifier": "EI"}