{"caption": "Storage", "entity_classification": "Entity", "description": "Storage is a service where data is stored and managed remotely on internet servers, rather than on local hardware.", "navigator_enabled": true, "navigator_graph_node_description": "Storage represents a service where data is stored and managed remotely on internet servers rather than on local hardware within an organization's cloud infrastructure. It captures essential attributes including unique identifiers (resource_id, resource_name), cloud provider details (AWS, Azure), encryption status, and access configurations. The schema tracks temporal aspects through timestamps (first_seen_date, last_found_date), activity status, and operational state. Security attributes include internet exposure flags, encryption implementation, and vulnerability metrics. Storage is distinguished from other cloud resources like Cluster and Container by its primary function of data persistence rather than processing or application hosting. This entity enables comprehensive storage inventory management, risk evaluation, and security monitoring across the organization's cloud data ecosystem.", "navigator_entity_description": "# Storage\n\nStorage represents digital storage resources provisioned and managed within cloud computing environments, enabling organizations to store data remotely on internet servers rather than on local hardware. This entity encompasses various storage types including object storage (buckets), block storage (volumes), file storage services, and specialized storage accounts across multiple cloud providers.\n\n## Core Characteristics\n\nStorage is distinguished by its remote accessibility, scalability, and service-oriented nature. Unlike physical storage hardware or on-premises solutions, storage resources are provisioned on-demand, can be rapidly scaled, and are accessed via network protocols. The entity tracks both infrastructure-level attributes (encryption, access controls, geographic distribution) and operational characteristics (performance metrics, attachment status, capacity).\n\n## Storage Types and Classification\n\nThe entity captures diverse storage implementations through type classification:\n- **Volumes**: Block storage resources (AWS EBS, Azure Disks) that provide persistent storage for cloud compute instances\n- **Buckets**: Object storage containers (AWS S3, Azure Blob Storage) for unstructured data\n- **File System Services**: Managed file storage solutions (AWS EFS, Azure Files) providing shared file access\n- **Storage Accounts**: Container structures (primarily in Azure) that organize multiple storage services\n\nEach type maintains specific attributes reflecting its unique characteristics, such as volume size and IOPS for block storage, or public access settings for object storage.\n\n## Security and Compliance Attributes\n\nStorage extensively tracks security configurations including:\n- **Encryption settings**: Both at-rest and in-transit encryption status\n- **Access controls**: Public network access settings, ACLs, bucket policies\n- **Authentication mechanisms**: SFTP enablement, shared key access\n- **Compliance features**: Immutability policies, legal holds, versioning status\n\nThese security attributes enable comprehensive assessment of storage resource protection and regulatory compliance across cloud environments.\n\n## Geographic Distribution and Availability\n\nThe entity captures location-related attributes at multiple levels:\n- **Regional placement**: Which geographic regions host the storage resources\n- **Zone availability**: Whether resources are deployed across single or multiple availability zones\n- **Replication status**: Cross-region replication configurations for disaster recovery\n\nThis geographic tracking enables assessment of data sovereignty compliance and disaster recovery capabilities.\n\n## Relationships and Associations\n\nStorage maintains relationships with other entities through association counts and direct references:\n- **Cloud Account**: Tracks ownership through account_id attributes\n- **Virtual Machines**: Captures attachments via cloud_instance_id and specific VM identifiers\n\nThese relationships provide context for understanding storage resource usage patterns and dependencies.\n\n## Distinguishing Features\n\nWhat sets Storage apart from other entities:\n- Unlike Cluster/Cloud Compute, focuses on persistent data storage rather than processing capacity\n- Unlike Network entities, emphasizes data at rest rather than data in transit\n- Unlike Application entities, represents infrastructure components rather than software systems\n- Unlike Cloud Container entities, provides persistent rather than ephemeral storage\n\n## Operational Monitoring\n\nThe entity tracks operational states and performance metrics including:\n- **Activity status**: Whether resources are active or not\n- **Operational state**: Current state (active, inactive.)\n- **Performance metrics**: IOPS, throughput, capacity utilization\n- **Lifecycle events**: Creation dates, deletion dates, modification timestamps\n\nThese attributes enable monitoring of storage resource utilization and operational health.", "navigator_examples": ["User Query: 'List all cloud storage with public network access enabled' Output: 'storage'", "User Query: 'Find cloud storage with versioning disabled' Output: 'storage'"], "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data", "group": "common", "type": "string", "category": "General Information", "internally_generated": true}, "display_label": {"caption": "Display Label", "description": "The derived and \"best known\" identifier or name, based on the attribute that best uniquely identifies it.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host,Cluster, Storage, Vulnerability etc.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "type": {"caption": "Type", "description": "The specific role of the entity based on its functionality within an organisation. E.g Volume, Bucket, File System Service etc.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information", "navigator_is_full_value_required": true}, "origin": {"caption": "Origin", "description": "Data source(s) from which the entity has been extracted. For example AWS, Qualys etc.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information", "data_structure": "list"}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity is extracted.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested. It is the actual api name from which the data is ingested. Some of the examples for data feed in Host entity are Qualys Host List, MS Azure AD Devices etc.", "group": "common", "examples": "", "ui_visibility": true, "category": "General Information", "type": "string", "data_structure": "list", "internally_generated": true}, "first_found_date": {"caption": "First Found", "description": "The date at which the entity was first observed in the ingested data.This will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources. By default first seen date date is calculated based on the minimum value between the last active date and the first found date of the entity.\\nIf the data sources provides information regarding first seen activity of the entity for example AD created date in Host, they take precedence over the default logic.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data. Defaults to First Found. If any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data. This will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "\"Latest date on which entity was active as inferred from available data sources. This date is determined by considering the maximum value of dates contributed by each data source for the entity in question. This includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "timestamp", "category": "General Information", "ui_visibility": true}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. The logic is as follows: If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. ", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score": {"caption": "Completeness Quality Score", "description": "A metric representing the extent to which required data attributes for the entity are populated across ingested data sources. This score helps evaluate the completeness of the entity's data profile.", "group": "enrichment", "examples": "1", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "aggregated_quality_score": {"caption": "Aggregated Quality Score", "description": "A composite score derived from various quality metrics (such as completeness, correctness, and consistency) to assess the overall data quality of the entity.", "examples": "1", "group": "enrichment", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "completeness_quality_score_category": {"caption": "Completeness Quality Score Category", "description": "A categorical interpretation of the Completeness Quality Score that classifies the entity's data completeness into levels such as High, Medium, or Low (configurable). This helps in quickly identifying the priority or severity of incomplete data across entities, enabling better triaging and quality improvement decisions.", "examples": "High", "group": "enrichment", "type": "string", "range_selection": true, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "description": {"caption": "Description", "description": "Detailed explanation of the storage. For example it includes details about specification,location details etc.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "location_country": {"caption": "Location Country", "description": "Location Country of the entity. The location country property is obtained from the cloud region. For example, if region is 'centralindia', location_country will be India.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information", "finding_evidence_groupby_enabled": true, "navigator_is_full_value_required": true}, "location_city": {"caption": "Location City", "description": "Location City of the entity. The location city property is retrieved from the cloud region. For example, if the region is 'centralindia', location_city will be 'Pune' according to cloud provider mapping.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information", "navigator_is_full_value_required": true}, "department": {"caption": "Department", "description": "Tag used to identify or categorize the resource based on its association with a specific business department within an organization.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity. It is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "The key fields that are considering for updating the last updated date of an entity.", "group": "common", "type": "string", "ui_visibility": false, "data_structure": "struct", "internally_generated": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive. For example 2 days.", "group": "common", "type": "integer", "ui_visibility": false, "range_selection": true}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive. For example 2 days.", "group": "common", "type": "integer", "ui_visibility": false}, "infrastructure_type": {"caption": "Deployment Type", "description": "Indicates the deployment environment of a Storage, specifying whether it is hosted on a cloud platform (e.g., AWS, Azure, Google Cloud) or located on-premise within an organization's data center. Examples include 'Cloud' and 'On-Premise'.\n", "type": "string", "group": "entity_specific", "category": "Asset Identification"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "type": "string", "category": "General Information"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Company that provides scalable computing resources that businesses can access on demand over a network, including cloud-based compute, storage, platform, and application services.\nSome of the cloud provider values are aws, azure and gcp.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "account_id": {"caption": "Cloud Account ID", "description": "Unique identifier associated with the account in a cloud provider. In AWS account ID is a 12-digit number unique to each AWS account and in Azure account ID is known as the subscription ID represented as 32-character hexadecimal string. Each cloud resource is associated with an account ID which is either available directly in the source or extracted from the resource ID.Examples are **********, 011c41b3-ba33-448e-86d2-34252xxx.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "region": {"caption": "Cloud Region", "description": "Geographical location where the data centers of Azure and AWS are located. This field is mapped directly from the awsregion in AWS and location in Azure.\nExamples include ap-south-1,centralindia.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Resource Location and Availability", "finding_evidence_groupby_enabled": true}, "kubernetes_cluster_name": {"caption": "Kubernetes Cluster Name", "description": "Unique identifier associated with Kubernetes Cluster for the type volume and is extracted from the tags in both AWS and Azure.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Compute and Kubernetes"}, "cloud_instance_id": {"caption": "Cloud Instance ID", "description": "The unique identifier provided by the cloud provider for a virtual machine. Each storage resource is attached to one or more instances. This field directly extracts the instanceID from those storage resources having single instance attached to it. Examples are 'i-1234567890aaaaa'", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "instance_name": {"caption": "Cloud Instance Name", "description": "Refers to the user-defined name or label assigned to an instance. In the context of storage resources this field is derived from the 'managed by' attribute retrieved from Azure. Examples of such labels include vm-pfsense-in-ce", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "resource_id": {"caption": "Cloud Resource ID", "description": "Unique identifier assigned to each resource in the cloud environment.\nEach cloud provider assigns a unique identifier to its resources, with formatting that varies between providers. For AWS, this identifier is known as the ARN (Amazon Resource Name), following the general format 'arn:partition:service:region:account-id:resource-type/resource-id'.\nIn Azure, resources typically follow this format: '/subscriptions/{subscription-id}/resourceGroups/{resource-group-name}/providers/{resource-provider-namespace}/{resource-type}/{resource-name}'.\n For example, '/subscriptions/0f2e3e80-6e2c-47c6-977a-c83zzzzzzz/resourceGroups/mgmnt/providers/Microsoft.Compute/virtualMachines/vm-psense-xx-zz'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "General Information"}, "resource_name": {"caption": "Cloud Resource Name", "description": "Human-readable unique name assigned to each cloud resource.\n Resource names typically adheres to specific naming constraints based on the resource type, cloud provider etc and might include alphanumeric characters, hyphens, and underscores. Examples are aws-athena-query-results-soln-aaa-xxx,vm-pfsense-xx-yy", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "native_type": {"caption": "Cloud Native Type", "description": "It denotes the specific cloud service designated by the Cloud Provider, typically identified by a prefix indicating the provider (such as AWS or Azure), followed by the service name as documented officially. Native types are either hardcoded or standardized using the type field sourced from available data. Examples include AWS S3 Bucket,Azure Storage Accounts ", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "operational_state": {"caption": "Cloud Last Known Operational State", "description": "Current operational status of the resource.\nResource is determined operational by assessing its state related attributes such as diskstate and deletion status in the case for blob storage. Field values are normalized to either 'Active' or 'Inactive' based on their state of action. For instance, fields such as 'in-use' or 'available' are categorised as active, while fields like 'deleting' or 'deleted' are categorized as inactive.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Operational and Lifecycle State", "navigator_is_full_value_required": true}, "environment": {"caption": "Environment", "description": "Metadata tag to categorize resources based on the environment they are deployed to. It helps organizations manage and organize their resources more efficiently by providing a way to identify resources based on their deployment environment, such as development, testing, staging, or production. These fields are extracted from inside the tags. Examples are devops,qaregress etc", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information", "navigator_is_full_value_required": true}, "provisioning_state": {"caption": "Provisioning State", "description": "Refers to the current status of a resource during its creation or deployment process. It indicates whether the resource creation or modification operation is ongoing, completed successfully, encountered errors, or has been canceled.The possible distinct values are 'Creating', 'Updating', 'Deleting', 'Succeeded', 'Failed' and 'canceled'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Operational and Lifecycle State"}, "encryption_at_rest": {"caption": "Encryption at Rest", "description": "Storage encryption at rest refers to the process of encrypting data while it is stored in storage systems, such as disk drives, databases, or storage services. This encryption ensures that the data remains protected and unreadable to unauthorized users or entities, even if the physical storage media is compromised or accessed without proper authorization.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Encryption"}, "encryption_in_transit": {"caption": "Encryption in Transit", "description": "Storage encryption in transit refers to the process of encrypting data while it is being transmitted or transferred between different locations, systems, or devices over a network. This encryption ensures that the data remains protected and unreadable to unauthorized users or entities while it is in transit.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Encryption"}, "zone_availability": {"caption": "Cloud Zone Availability", "description": "Checks if the resources are deployed in single or multiple availability zones within the region. For AWS this field is powered directly from the availability zones and in Azure it's based upon the count of zones from azure zones field,if count of zones exceeds one, it is classified as 'Multiple', otherwise, it is categorized as 'Single'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Resource Location and Availability"}, "active_operational_date": {"caption": "Active Operational Date", "description": "Date at which the operational state of the resource was found active.", "group": "entity_specific", "type": "timestamp", "category": "Operational and Lifecycle State"}, "billing_tag": {"caption": "Billing Tag", "description": "Field used to classify the resources based on the billing criteria. It helps in categorizing costs by runtime environment, such as the billing usage for VMs running in the production environment etc. These fields are extracted from the tags array in both Azure and AWS and are custom given for each resource by the organisation.\nExamples are sds-tpdev,sds3-pg ", "group": "entity_specific", "type": "string", "ui_visibility": true}, "volume_name": {"caption": "Volume Name", "description": "Unique identifier or label assigned to a storage volume within a cloud provider. For AWS it is derived from the name field inside the tags and for Azure it is mapped directly from the name. Examples include aaa-sol-dev-1344.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Storage"}, "bucket_name": {"caption": "Bucket Name", "description": "Unique identifier or label assigned to a storage bucket within a cloud provider. For AWS it is derived from the name field inside the tags and for Azure it is mapped directly from the name. Examples include azure-webjobs-hosts.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Storage"}, "file_system_service_name": {"caption": "File System Service Name", "description": "Unique identifier or label assigned to a filesystem within a cloud provider. For AWS it is derived from the name field inside the tags and for Azure it is mapped directly from the name. Examples include aaa-sol-dev-1344.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Storage"}, "storage_account_name": {"caption": "Storage Account Name", "description": "Name of the storage account within which the different storage services are hosted. Storage account comes only from Azure and the name is extracted from the azure tags. Examples are aksfunctioappsoluti8d35.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Storage"}, "storage_account_access_tier": {"caption": "Storage Account Access Tier", "description": "In Azure storage accounts, the \"access tier\" refers to the level of accessibility and availability of stored data. The possible values are Hot, Cool and Premium.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "is_sftp_enabled": {"caption": "Is SFTP Enabled", "description": "Indicates whether Secure File Transfer Protocol is enabled. SFTP (Secure File Transfer Protocol) is a network protocol used to securely transfer files between computers over an encrypted connection. It combines the functionality of traditional FTP (File Transfer Protocol) with a secure layer provided by SSH (Secure Shell), ensuring that data, including login credentials, is transmitted safely. The possible vales are 'true' and 'false'.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "is_nfs_v3_enabled": {"caption": "Is NFS V3 Enabled", "description": "Indicates whether NFS 3.0 protocol support is enabled. NFS (Network File System) is a distributed file system protocol that allows clients to access and manage files stored on remote servers over a network. The possible vales are 'true' and 'false'.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "infrastructure_encryption_applied": {"caption": "Infrastructure Encryption Applied", "description": "A boolean indicating whether or not the service applies a secondary layer of encryption with platform managed keys for data at rest. The possible values are 'true' and 'false'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Encryption"}, "public_network_access": {"caption": "Public Network Access", "description": "Indicates whether a disk resource in Azure has public network access enabled or disabled. Derived from the configuration settings of the disk resource in Azure, specifically from the 'PublicNetworkAccess' property associated with the disk.\nThe possible values are Disabled, and Enabled.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Encryption"}, "volume_pvc_namespace": {"caption": "Volume PVC Namespace", "description": "In a Kubernetes environment, a PersistentVolumeClaim (PVC) is a request for storage by a pod. When a PVC is created and bound to an EBS volume in AWS, it establishes a dynamic relationship between the PVC and the EBS volume.\nThe namespace in Kubernetes organizes objects in a Kubernetes cluster, providing a scope for their names.The field is extracted from the tags in both AWS and Azure.\nExamples are qa,dev etc", "group": "entity_specific", "type": "string", "ui_visibility": true}, "volume_pvc_name": {"caption": "Volume PVC Name", "description": "Refers to the name of a PersistentVolumeClaim (PVC) associated with an Amazon Elastic Block Store (EBS) volume in a Kubernetes environment.\nThe field is extracted from the tags in both AWS and Azure.\nExamples include data-volume-druid-sds-cluster-historicals-0.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "volume_pv_name": {"caption": "Volume PV Name", "description": "Refers to the name of a PersistentVolume (PV) associated with a volume in a Kubernetes environment.\nThe field is extracted from the tags in both AWS and Azure.\nExamples include pvc-16f116f3-8322-473d-8f00-8dc376ed8399.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "volume_size": {"caption": "Volume Size", "description": "Refers to the storage capacity allocated to cloud volume resource, measured in gigabytes (GB).\nVolume size has been derived using the fields such as size in AWS and disksizeGB in Azure.", "group": "entity_specific", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "Storage"}, "volume_iops": {"caption": "Volume IOPS", "description": "Refers to the input/output operations per second (IOPS) allocated to a cloud volume resource.\nVolume IOPS has been derived using fields such as iops in AWS and diskIOPSReadWrite in Azure.", "group": "entity_specific", "type": "integer", "ui_visibility": true}, "volume_type": {"caption": "Volume Type", "description": "Refers to the specific characteristics and capabilities of a storage volume that is allocated within a cloud environment.\nIn AWS, the volume type attribute is contributed by fields such as 'VolumeType' in the EC2 Service.In Azure, the volume type attribute is contributed by sku name in the Azure Disk Service.\nExamples of volume types include 'Standard_LRS','gp2'", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Storage"}, "volume_throughput": {"caption": "Volume Throughput", "description": "Specifies the maximum amount of data that can be transferred per unit of time for a storage volume, indicating its data processing capacity. in MiB/s.\nIn AWS, the volume throughput attribute is contributed by field 'throughput' from Amazon EC2 service.\nIn Azure, the volume throughput attribute is contributed by fields such as 'diskMBpsReadWrite' in the Azure Disk service.", "group": "entity_specific", "type": "integer", "ui_visibility": true, "category": "Storage"}, "allow_blob_public_access": {"caption": "Allow Blob Public Access", "description": "Allow or disallow public access to all blobs or containers in the storage account. The possible values are 'true' and 'false'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Security and Encryption"}, "capacity": {"caption": "Capacity", "description": "Denotes the storage capacity provisioned for file-based storage services within a cloud environment in giga bytes.\nIt is derived from the field file share quota in Azure.", "group": "entity_specific", "type": "integer", "ui_visibility": true}, "lease_status": {"caption": "Lease Status", "description": "Lease Status details for a storage service refer to the current status of any leases that have been acquired on the resource.\nThe possible values are Locked: The resource has an active lease.\nUnlocked: The resource does not have an active lease and is available for leasing", "group": "source_specific", "type": "string", "ui_visibility": true}, "properties": {"caption": "Properties", "description": "", "group": "entity_specific", "type": "string", "ui_visibility": false}, "project": {"caption": "Project", "description": "The project attribute is typically derived from metadata or tagging mechanisms within AWS or Azure.\nUsers assign a project tag to resources during provisioning or management, allowing them to categorize and track resources belonging to specific projects.", "group": "entity_specific", "type": "string", "ui_visibility": true, "navigator_is_full_value_required": true}, "aws_resource_created_date": {"caption": "AWS Resource Created Date", "description": "Refers to the timestamp when the AWS resource was initially created.Derived from the date fields such as resourcecreationTime,createddate etc.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "aws_tags": {"caption": "AWS Tags", "description": "Metadata elements associated with AWS resources,represented as key-value pairs.\nTags are typically used to categorize resources based on attributes such as project names, department codes, environment types, cost centers, etc.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "aws_ebs_attach_date": {"caption": "AWS EBS Attach Date", "description": " Indicates the date when an Amazon Elastic Block Store (EBS) volume was attached to an EC2 instance within the AWS cloud environment.\nField is extracted from attachments property, which contains details about the attached instances including the attachment time.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "aws_ebs_size": {"caption": "AWS EBS Size", "description": "Denotes the storage capacity, measured in gigabytes (GB) of an Amazon Elastic Block Store (EBS) volume within the AWS cloud environment.\n Attribute is retrieved from the Size property associated with the specific Amazon EBS volume.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "aws_ebs_attachment_state": {"caption": "AWS EBS Attachment State", "description": "The attachment state of an EBS volume reflects whether the volume is currently attached to an EC2 instance or detached.\n Field is extracted from attachments property of the volume.\n The possible values are 'attaching','attached','detaching','detached','busy'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ebs_associated_resource": {"caption": "AWS EBS Associated Resource", "description": "The ARN of the Amazon ECS or Fargate task to which the volume is attached.\nField is extracted from attachments property of the volume.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ebs_state": {"caption": "AWS EBS State", "description": "Indicates the current operational state of an Amazon Elastic Block Store (EBS) volume within the AWS cloud environment.\n Attribute is retrieved from the State field associated with the specific Amazon EBS volume. The possible values are 'creating','available','in-use','deleting','deleted','error'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_kms_key_id": {"caption": "AWS KMS Key ID", "description": "The Amazon Resource Name (ARN) of the Key Management Service (KMS) KMS key that was used to protect the data stored in the associated AWS storage resource.Field has been derived using the 'kmsKeyID' attribute.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ebs_snapshot_id": {"caption": "AWS EBS Snapshot ID", "description": "Represents the unique identifier assigned to an Amazon EBS (Elastic Block Store) snapshot, which captures the state of an EBS volume at a specific point in time.\nAttribute is retrieved from the SnapshotId property associated with the specific EBS snapshot.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_instance_id": {"caption": "AWS Instance ID", "description": "Unique identifier assigned to an Amazon Elastic Compute Cloud (EC2) instance that is associated with a particular storage resource within the AWS cloud environment.\nField return value only on cases where multiattachenabled is false i.e when volume is attached to a single instance only.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_multi_attached_instances": {"caption": "AWS Multi Attached Instances", "description": "List of unique IDs assigned to AWS EC2 Instances attached to the EBS volume.\nField returns values only on cases where multiattachenabled is false i.e when volume is attached to more than one instance.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "aws_ebs_multi_attach_enabled": {"caption": "AWS EBS Multi Attach Enabled", "description": "Indicates whether the Amazon Elastic Block Store (EBS) volume is configured to support attachment to multiple EC2 instances simultaneously within the AWS cloud environment.\nAttribute is retrieved from the MultiAttachEnabled property associated with the specific Amazon EBS volume.\nThe possible values are True or False.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ebs_fast_restored": {"caption": "AWS EBS Fast Restored", "description": "Indicates whether the volume was created from a snapshot that is enabled for fast snapshot restore.\nFast snapshot restore (FSR) enables you to create a volume from a snapshot that is fully initialized at creation.\nThe possible values are True or False.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ebs_delete_on_termination": {"caption": "AWS EBS Delete On Termination", "description": "Indicates whether the EBS volume is automatically deleted on instance termination.\n Attribute is derived from the DeleteOnTermination property extracted from inside the attachments associated with the specific Amazon EBS volume.\nThe possible values are True or False.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_availability_zone": {"caption": "AWS Availability Zone", "description": "Indicates the specific data center location or zone within an AWS region where a storage resource is provisioned.\n Attribute is taken from the AvailabilityZone property associated with the specific storage resource.\n If more than one zone is available value will be given as 'Multiple'.\nExamples are ap-southeast-2a,us-east-1a etc.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_region": {"caption": "AWS Region", "description": "Attribute identifies the geographic location where an AWS storage resource is provisioned within the AWS cloud infrastructure.\nExample are us-east-1,ap-south-1 etc.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ebs_csi_name": {"caption": "AWS EBS Container Storage Interface", "description": "Container Storage Interface (CSI) driver provided by AWS for Kubernetes clusters enables Kubernetes workloads to use EBS volumes as persistent storage for containers running in Kubernetes pods.\nExtracted from the CSIVolumeName property inside aws tags.\nExample include 'pvc-5191fa26-78ea-41b5-9bea-821d3860fdec'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_efs_throughput_mode": {"caption": "AWS EFS Throughput Mode", "description": "Specifies the mode or interface used to determine the throughput performance characteristics of an Amazon Elastic File System (EFS) within the AWS.\nAttribute is retrieved from the ThroughputMode property associated with the specific Amazon EFS.\nThe possible values are bursting,provisioned,elastic.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_efs_performance_mode": {"caption": "AWS EFS Performance Mode", "description": "The Performance mode of the file system. The possible values are :\n generalPurpose - This mode is suitable for a wide range of workloads and offers balanced performance.\n maxIO - This mode is optimized for high-performance applications requiring maximum IOPS and throughput.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_efs_transition_to_ia": {"caption": "AWS EFS Transition To IA", "description": "AWS EFS Transition to IA refers to the process of transitioning files from the Standard storage class to the Infrequent Access (IA) storage class within Amazon Elastic File System (EFS).\n Attribute is derived from the file system's lifecycle management policy.\n Valid values are AFTER_7_DAYS , AFTER_14_DAYS , AFTER_30_DAYS , AFTER_60_DAYS , AFTER_90_DAYS , AFTER_1_DAY , AFTER_180_DAYS ,AFTER_270_DAYS , AFTER_365_DAYS.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_efs_backup_policy_status": {"caption": "AWS EFS Backup Policy Status", "description": "The AWS EFS Backup Policy Status refers to the current status of the backup policy configured for an Amazon Elastic File System (EFS) file system.\n Attribute is mapped from the status field of backup policy configuration associated with the specific Amazon EFS file system.\nThe possible values are DISABLED, ENABLED.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_owner_id": {"caption": "AWS S3 Owner ID", "description": "Refers to a unique identifier assigned to the owner of an Amazon S3 bucket or object.\nRetrieved from the Owner property associated with the specific Amazon S3 bucket.\nID is typically a long alphanumeric string that uniquely identifies the bucket owner.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_access_control_list": {"caption": "AWS S3 Access Control List", "description": "An AWS S3 Access Control List (ACL) is a set of permissions attached to an Amazon S3 bucket or object that defines who can access the resource and what actions they can perform.", "group": "source_specific", "type": "string", "ui_visibility": false, "data_structure": "struct"}, "aws_s3_block_public_acls": {"caption": "AWS S3 Block Public ACLs", "description": "S3 will block public access permissions applied to newly added buckets or objects, and prevent the creation of new public access ACLs for existing buckets and objects.\nThis setting doesnt change any existing permissions that allow public access to S3 resources using ACLs.\nDerived from the PublicAccessBlockConfiguration of supplementary configurations of the specific S3 bucket.\nPossible values are true or false.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_ignore_public_acls": {"caption": "AWS S3 Ignore Public ACLs", "description": "S3 will ignore all ACLs that grant public access to buckets and objects.\nDerived from the PublicAccessBlockConfiguration of supplementary configurations of the specific S3 bucket.\n Possible values are:\nTrue: Indicates that Amazon S3 should ignore public ACLs set on objects within the bucket.\nFalse: Indicates that Amazon S3 should honor public ACLs set on objects within the bucket.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_block_public_policy": {"caption": "AWS S3 Block Public Policy", "description": "S3 will ignore public and cross-account access for buckets or access points with policies that grant public access to buckets and objects.\nDerived from the PublicAccessBlockConfiguration of supplementary configurations of the specific S3 bucket.\nPossible values are True or False.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_restrict_public_buckets": {"caption": "AWS S3 Restrict Public Buckets", "description": "S3 will block new bucket and access point policies that grant public access to buckets and objects.\nThis setting doesn't change any existing policies that allow public access to S3 resources.\nDerived from the PublicAccessBlockConfiguration of supplementary configurations of the specific S3 bucket.\nPossible values are True or False.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_log_bucket_name": {"caption": "AWS S3 Log Bucket Name", "description": "The AWS S3 Log Bucket Name refers to the name of the S3 bucket where access logs for another S3 bucket are stored.\nDerived from the BucketLoggingConfiguration of supplementary configurations of the specific S3 bucket.\nExample is s3-ektt.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_bucket_policy_text": {"caption": "AWS S3 Bucket Policy Text", "description": "The AWS S3 Bucket Policy Text refers to the JSON-formatted policy document that defines access control rules for an S3 bucket. This policy text specifies who can access the bucket and what actions they can perform on the objects within the bucket.", "group": "source_specific", "type": "string", "ui_visibility": false, "data_structure": "struct"}, "aws_s3_is_mfa_delete_enabled": {"caption": "AWS S3 Is MFA Delete Enabled", "description": "An additional layer of security that requires multi-factor authentication for changing Bucket Versioning settings and permanently deleting object versions.\n Derived from the BucketVersioningConfiguration property inside supplementary configurations of the specific S3 bucket.The possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_bucket_versioning_status": {"caption": "AWS S3 Bucket Versioning Status", "description": "Versioning is a means of keeping multiple variants of an object in the same bucket.Can use versioning to preserve, retrieve, and restore every version of every object stored in the Amazon S3 bucket.\n Derived from the BucketVersioningConfiguration property inside supplementary configurations of the specific S3 bucket.\n The possible values are Enabled, Disabled.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_server_side_encryption": {"caption": "AWS S3 Encryption Type", "description": "Refers to the method or mechanism used to encrypt data stored in S3 buckets. \nS3 supports different encryption options to ensure the security and protection of data at rest.\nAttribute is retrieved from the server-side encryption configuration associated with the specific Amazon S3 bucket.\nThe possible values are Server-side encryption with Amazon S3 managed keys (SSE-S3) (the default), Server-side encryption with AWS Key Management Service (AWS KMS) keys (SSE-KMS), Dual-layer server-side encryption with AWS KMS keys (DSSE-KMS).", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_server_side_encryption_kms_master_key": {"caption": "AWS S3 SSE KMS Master Key", "description": "Amazon S3 SSE with AWS KMS (Key Management Service) utilizes a customer-managed AWS KMS master key to encrypt and decrypt objects stored in an S3 bucket.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_bucket_encryption_key_enabled": {"caption": "AWS S3 SSE Key Enabled", "description": "When KMS encryption is used to encrypt new objects in this bucket, the bucket key reduces encryption costs by lowering calls to AWS KMS. The possible values are Enabled and Disabled.\nAttribute is taken from the server-side encryption settings where the user specifies the KMS CMK to be used for encryption.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_transfer_acceleration": {"caption": "AWS S3 Transfer Acceleration", "description": "Amazon S3 Transfer Acceleration, which enables fast, easy, and secure transfers of files over long distances between your client and your S3 bucket.\nAttribute is taken from the BucketAccelerateConfiguration property of the S3 bucket.\nThe possible values are Enabled, Suspended.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_requester_charged": {"caption": "AWS S3 Requester Pays", "description": "AWS S3 Requester Pays is a feature that allows the owner of an S3 bucket to configure the bucket so that the requester of data is responsible for paying the data transfer and request costs associated with accessing the data.\nAttribute is taken from the BucketAccelerateConfiguration property of the S3 bucket.\nThe possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_s3_storage_class": {"caption": "AWS S3 Storage Class", "description": "Storage Class refers to the categorization or classification of data stored in an S3 bucket based on its access patterns, durability, availability, and cost characteristics. AWS offers several storage classes, each optimized for different use cases and requirements.\nAttribute is taken from the BucketAccelerateConfiguration property of the S3 bucket.\n The possible values are Standard, Standard-IA (Infrequent Access), One Zone-IA, Intelligent-Tiering, Glacier, Glacier Deep Archive.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_resource_configuration_change_date": {"caption": "AWS Resource Configuration Change Date", "description": "The AWS Resource Configuration Change Date refers to the date when a configuration change was made to an AWS resource.\nDerived using the field 'configurationItemCaptureTime' associated with each resource.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_availability_zone": {"caption": "Azure Availability Zone", "description": "Azure Availability Zones are physically separate datacenters within an Azure region that are interconnected through high-speed, low-latency networking.\nAttribute is retrieved from the zones property associated with the specific storage resource.Examples of Azure Availability Zone values include:\n'1': Indicates the storage resource is located in Availability Zone 1 within the Azure region.\n'2': Indicates the storage resource is located in Availability Zone 2 within the Azure region.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_resource_created_date": {"caption": "Azure Resource Created Date", "description": "Refers to the timestamp when the Azure resource was initially created.Derived based on creationTime ,timeCreated fields available from properties of the corresponding storage resource.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_resource_last_modified_date": {"caption": "Azure Resource Modified", "description": "Indicates the date when a storage resource was last modified within the Azure cloud environment.\nAttribute is derived based on the greatest of last modified fields available from the specific storage resources, such as a blob container or file share.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_region": {"caption": "Azure Region", "description": "Specifies the geographical location where a storage resource is deployed within the Azure cloud environment.\nAttribute is retrieved from the location property associated with the specific Azure storage resource.\nExamples include East US,Southeast Asia etc.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_resource_group": {"caption": "Azure Resource Group", "description": "An Azure resource group is a logical container that holds related Azure resources such as virtual machines, storage accounts, databases, and networking resources.\nIt acts as a management layer for organizing and managing Azure resources based on their lifecycle, ownership, or application context.\n Field is extracted from the id of the corresponding storage resource.\nExamples are devTestRG,prodRG etc.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_ou": {"caption": "Azure OU", "description": "Organizational unit details mentioned in the Azure resources.\n Extracted from the Azure tags.\nExample is Platform Engineering.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_tags": {"caption": "Azure Tags", "description": "Metadata elements associated with Azure resources, represented as key-value pairs.\nTags are typically used to categorize resources based on attributes such as project names, environment types, cost centers, owners, etc.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "azure_network_policy_access": {"caption": "Azure Network Policy Access", "description": "Refers to the network access policy configuration that governs the inbound and outbound network traffic to and from the disk in the Azure cloud environment.\n The possible values are AllowAll, AllowPrivate, DenyAll.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_encryption_type": {"caption": "Azure Disk Encryption Type", "description": "The type of key used to encrypt the data of the disk.\nThe possible values are EncryptionAtRestWithCustomerKey, EncryptionAtRestWithPlatformAndCustomerKeys, EncryptionAtRestWithPlatformKey.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_image_id": {"caption": "Azure Disk Image ID", "description": "A relative uri containing either a Platform Image Repository, user image, or Azure Compute Gallery image reference.\nWhen provisioning an Azure Disk, users have the option to create the disk from a disk image, which serves as a template for the disk's initial state.\nThe Azure Disk Image ID is derived from the unique identifier of the selected disk image", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_create_option": {"caption": "Azure Disk Create Option", "description": "Determines how the disk should be created in Azure, specifying whether to create an empty disk, from a snapshot, from a VHD, or from a copy of an existing disk.\n Derived from the creationData properties of the specific Azure Disk.\nThe possible values are Attach, Copy, CopyFromSanSnapshot, CopyStart, Empty, FromImage, Import, ImportSecure, Restore, Upload, UploadPreparedSecure.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_state": {"caption": "Azure Disk State", "description": "Indicates the current operational state of a disk resource.\nAttribute is mapped from diskstate property of the corresponding Azure disk.\nThe possible values are \"ActiveSAS, ActiveSASFrozen, ActiveUpload, Attached, Frozen, ReadyToUpload, Reserved, Unattached\"", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_last_ownership_update": {"caption": "Azure Disk Last Ownership Change", "description": "The timestamp when the ownership state of the disk was last changed i.e., the timestamp the disk was last attached or detached from a VM or the timestamp when the VM to which the disk was attached was deallocated or started.\n Attribute has been mapped using the field LastOwnershipUpdateTime specific to the Azure Disk.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_disk_os_type": {"caption": "Azure Disk OS Type", "description": "Specifies the operating system installed on the disk. It helps in identifying the appropriate disk image to use for virtual machine provisioning.\n The possible values are Linux and Windows.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_unique_id": {"caption": "Azure Disk Unique ID", "description": "Unique Guid identifying the resource.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_tier": {"caption": "Azure Disk Tier", "description": "Represents the performance and pricing tier of managed disks in Azure storage.\nEach label corresponds to a distinct combination of performance metrics.\nExamples include P4: Denotes a Premium SSD with a specific level of performance.\nS10: Represents a Standard SSD disk with a particular performance profile.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_security_type": {"caption": "Azure Disk Security Type", "description": "Specifies the SecurityType of the VM. Applicable for OS disks only.\nDerived from securityprofiles property associated with disk.\nThe possible values are TrustedLaunch, ConfidentialVM_VMGuestStateOnlyEncryptedWithPlatformKey, ConfidentialVM_NonPersistedTPM, ConfidentialVM_DiskEncryptedWithPlatformKey, ConfidentialVM_DiskEncryptedWithCustomerKey", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_supports_hibernation": {"caption": "Azure Disk Hibernation Support", "description": "Indicates the OS on a disk supports hibernation or not.\nThe possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_disk_controller_types": {"caption": "Azure Disk Controller Types", "description": "The disk controllers that an OS disk supports.\nPossible values include SCSI (Small Computer System Interface) and NVMe (Non-Volatile Memory Express) which are both interfaces used for connecting storage devices, including disks, to a computer system.\nIf set it can be SCSI or SCSI, NVME or NVME, SCSI.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_vm_id": {"caption": "Azure Virtual Machine ID", "description": "Unique identifier for the Azure virtual machine to which the disk has been attached.\nAttribute is mapped from the managedBy field which indicates the virtual machine asscoiated with the Azure Disk.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_vmss_name": {"caption": "Azure Virtual Machine Scaleset Name", "description": "Name of the Virtual Machine Scaleset from which the virtual machines has been created.\n VM ID typically includes information about the VMSS to which the VM belongs\nScaleleset name is derived from the managedBy attribute which gives the VM ID.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_vm_name": {"caption": "Azure Virtual Machine Name", "description": "Represents the name of the virtual machine associated with the storage disk.\nExtracted from the managedBy attribute which indicates VM ID of the corresponding disk.Examples include 'sds-tpdev-ddd-in-ce-da56dxxx-cast_0'", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_remaining_retention_days": {"caption": "Azure Remaining Retention Days", "description": "Azure Blob Storage containers refers to the amount of time left before the retention period expires for data stored within the container.\nIf a blob has a retention policy set to retain for 30 days and it was created 10 days ago, field would show 20 days remaining", "group": "source_specific", "type": "integer", "ui_visibility": true}, "azure_version_level_immutability_support": {"caption": "Azure Version Level Immutability Support", "description": "Azure Blob Storage containers support version-level immutability, allowing you to enforce immutability at the level of individual blob versions within the container.\nThis feature ensures that once a blob version is created and committed, it cannot be modified, overwritten, or deleted, providing a tamper-proof record of data changes over time.\nThe possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_bsc_default_encryption_scope": {"caption": "Azure Blob Default Encryption Scope", "description": "Default the container to use specified encryption scope for all writes.\nWhen you create a container, you can specify a default encryption scope for the blobs that are subsequently uploaded to that container.\nThis field is powered by the 'encryptionScope' attribute in Azure Blob Storage.\n Example When '$account-encryption-key' is specified as the default encryption scope, it means that the encryption keys managed at the storage account level are used to encrypt the data stored within the blob containers.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_bsc_deny_encryption_scope_override": {"caption": "Azure Blob Deny Encryption Scope Override", "description": "Restricts users from overriding encryption scopes for blobs within the container.\nThe possible values are 'true' and 'false'.\nIf set to True, users are prevented from overriding encryption scopes for blobs within the container, ensuring consistent encryption policies.\nIf set to False, users retain the ability to override encryption scopes, allowing for flexibility in encryption configurations.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_bsc_public_access": {"caption": "Azure Blob Public Access", "description": "Specifies whether data in the container may be accessed publicly and the level of access.\nThe possible values are :\nNone: No public access. The data in the container can be accessed only by the account owner.\nBlob: Public read access for blobs. Blob data within the container can be read via anonymous requests, but container metadata and list operations are not accessible.\nContainer: Full public read access. All data within the container, including the blobs and container metadata, can be read via anonymous requests.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_lease_state": {"caption": "Azure Lease State", "description": "Lease State attribute provides more detailed information about the state of the lease on a blob.\nA lease is a lock on the container that allows exclusive write access to the data within the container for a specified period of time.\nThe possible values are Available, Leased, Expired, Breaking, Broken.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_bsc_has_immutability_policy": {"caption": "Azure Blob Has Immutability Policy", "description": "Immutability Policy details in Azure Blob Storage containers refer to the feature that allows you to enforce data immutability for specified blobs within a container.\nWhen an immutability policy is applied to a blob, it prevents the blob from being modified or deleted for a specified retention period, ensuring its integrity and protection against tampering or unauthorized changes.\nThe possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_bsc_has_legal_hold": {"caption": "Azure Blob Has Legal Hold", "description": "Legal hold details in Azure Blob Storage containers refer to the feature that allows you to place a legal hold on specific blobs within a container.\nWhen a legal hold is applied to a blob, it prevents the blob from being modified or deleted for a specified period of time, ensuring its preservation for compliance or legal reasons.\nThe possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_deleted": {"caption": "Azure Is Deleted", "description": "Indicates whether a file share or blob in Azure Storage has been marked as deleted.\nThe possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_deleted_date": {"caption": "Azure Deleted Date", "description": "Indicates the date when a file share was deleted in Azure Storage.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_storage_account_id": {"caption": "Azure Storage Account ID", "description": "Identifier of the Azure Storage Account.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_file_share_access_tier": {"caption": "Azure File Share Access Tier", "description": "Access tier for specific share. GpV2 account can choose between TransactionOptimized (default), Hot, and Cool. FileStorage account can choose Premium.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_file_share_access_tier_change_date": {"caption": "Azure File Share Access Tier Change Date", "description": "Indicates the last modification date for share access tier.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_file_share_quota": {"caption": "Azure File Share Quota", "description": "The maximum size of the share, in gigabytes.", "group": "source_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "azure_file_share_enabled_protocols": {"caption": "Azure File Share Enabled Protocols", "description": "The authentication protocol that is used for the file share. The possible values are 'NFS' and 'SMB'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_file_share_version": {"caption": "Azure File Share Version", "description": "The version of the share.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_encryption_key_source": {"caption": "Azure Encryption Key Source", "description": "The encryption key source (provider). Possible values: Microsoft.Storage, Microsoft.Keyvault", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_blob_encryption_enabled": {"caption": "Azure Blob Encryption Enabled", "description": "A boolean indicating whether or not the service encrypts the data as it is stored. Encryption at rest is enabled by default today and cannot be disabled.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_blob_encryption_enabled_date": {"caption": "Azure Blob Encryption Enabled Date", "description": "Gets a rough estimate of the date when the encryption was last enabled by the user. Data is encrypted at rest by default today and cannot be disabled.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_blob_encryption_key_type": {"caption": "Azure Blob Encryption Key Type", "description": "Encryption key type to be used for the encryption service. 'Account' key type implies that an account-scoped encryption key will be used. 'Service' key type implies that a default service key is used.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_file_encryption_enabled": {"caption": "Azure File Encryption Enabled", "description": "A boolean indicating whether or not the service encrypts the data as it is stored. Encryption at rest is enabled by default today and cannot be disabled.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_file_encryption_enabled_date": {"caption": "Azure File Encryption Enabled Date", "description": "Gets a rough estimate of the date when the encryption was last enabled by the user. Data is encrypted at rest by default today and cannot be disabled.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_file_encryption_key_type": {"caption": "Azure File Encryption Key Type", "description": "Encryption key type to be used for the encryption service. 'Account' key type implies that an account-scoped encryption key will be used. 'Service' key type implies that a default service key is used.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_supports_http_traffic_only": {"caption": "Azure Supports HTTP Traffic Only", "description": "Allows https traffic only to storage service if sets to true. The possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_minimum_tls_version": {"caption": "Azure Minimum TLS Version", "description": "Minimum TLS version to be permitted on requests to storage. The default interpretation is TLS 1.0 for this property. The possible values are TLS1_0, TLS1_1 and TLS1_2.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_primary_endpoints": {"caption": "Azure Primary Endpoints", "description": "Gets the URLs that are used to perform a retrieval of a public blob, queue, or table object.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_storage_account_primary_location": {"caption": "Azure Storage Account Primary Location", "description": "Location of the primary data center for the storage account.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_storage_account_secondary_location": {"caption": "Azure Storage Account Secondary Location", "description": "Location of the secondary data center for the storage account.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_storage_account_key1_creation_date": {"caption": "Azure Storage Account Key1 Creation", "description": "Storage account keys creation date.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_storage_account_key2_creation_date": {"caption": "Azure Storage Account Key2 Creation", "description": "Storage account keys creation date.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_allow_cross_tenant_replication": {"caption": "Azure Allow Cross Tenant Replication", "description": "Allow or disallow cross AAD tenant object replication. The default interpretation is false for new accounts to follow best security practices by default.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_default_o_auth": {"caption": "Azure De<PERSON>ult <PERSON>", "description": "A boolean flag which indicates whether the default authentication is OAuth or not. The possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_storage_account_allow_shared_key": {"caption": "Azure Storage Account Allow Shared Key", "description": "Indicates whether the storage account permits requests to be authorized with the account access key via Shared Key. If false, then all requests, including shared access signatures, must be authorized with Azure Active Directory (Azure AD). The default value is null, which is equivalent to true.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_is_hns_enabled": {"caption": "Azure Is HNS Enabled", "description": "Indicates whether Account HierarchicalNamespace is enabled. The possible vales are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_large_file_shares_state": {"caption": "Azure Large File Shares State", "description": "Indicates whether large file shares is enabled. The possible vales are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_identity_type": {"caption": "Azure Identity Type", "description": "The Azure identity type. The possible values are SystemAssigned, 'SystemAssigned,UserAssigned', UserAssigned.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_storage_account_type": {"caption": "Azure Storage Account Type", "description": "Type of the Azure storage account. The possible values are Premium_LRS, Premium_ZRS, Standard_GRS, Standard_GZRS, Standard_LRS, Standard_RAGRS, Standard_RAGZRS, Standard_ZRS.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_storage_account_kind": {"caption": "Azure Storage Account Kind", "description": "Indicates the type of storage account. The possible values are BlobStorage, BlockBlobStorage, FileStorage, Storage, StorageV2.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_geo_replication_status": {"caption": "Azure Geo Replication Status", "description": "The status of the secondary location. Possible values are: - Live: Indicates that the secondary location is active and operational. - Bootstrap: Indicates initial synchronization from the primary location to the secondary location is in progress. This typically occurs when replication is first enabled. - Unavailable: Indicates that the secondary location is temporarily unavailable.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_geo_replication_fail_over": {"caption": "Azure Geo Replication Fail Over", "description": "A boolean flag which indicates whether or not account failover is supported for the account. The possible values are 'true' and 'false'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_geo_replication_last_sync": {"caption": "Azure Geo Replication Last Sync", "description": "All primary writes preceding this UTC date value are guaranteed to be available for read operations.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_network_acls_bypass": {"caption": "Azure Network ACLs Bypass", "description": "Specifies whether traffic is bypassed for Logging/Metrics/AzureServices. Possible values are any combination of Logging,Metrics,AzureServices (For example, Logging, Metrics), or None to bypass none of those traffics.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_dns_endpoint_type": {"caption": "Azure DNS Endpoint Type", "description": "Specifies the type of endpoint. AzureDNSZone to create a large number of accounts in a single subscription, which creates accounts in an Azure DNS Zone and the endpoint URL will have an alphanumeric DNS Zone identifier. The possible values are 'AzureDNSZone ' and 'Standard'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "associated_cloud_account_count": {"caption": "Count of Cloud Account", "description": "Number of cloud account associated with storage.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_virtual_machine_count": {"caption": "Count of Virtual Machine", "description": "Number of virtual machine associated with volume.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_storage_account_has_bucket_count": {"caption": "Count of Storage Account has Bucket", "description": "Number of storage account associated with bucket.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_bucket_count": {"caption": "Count of Bucket", "description": "Number of bucket associated with storage account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_storage_account_has_file_system_service_count": {"caption": "Count of Storage Account has File System Service", "description": "Number of storage account associated with file system service.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_file_system_service_count": {"caption": "Count of File System Service", "description": "Number of file system service associated with storage account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_storage_account_has_queue_service_count": {"caption": "Count of Storage Account has Queue Service", "description": "Number of storage account associated with queue service.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_queue_service_count": {"caption": "Count of Queue Service", "description": "Number of queue service associated with storage account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_storage_account_has_table_service_count": {"caption": "Count of Storage Account has Table Service", "description": "Number of storage account associated with table service.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_table_service_count": {"caption": "Count of Table Service", "description": "Number of queue service associated with storage account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_open_finding_count": {"caption": "Count of Open Findings", "description": "Number of open findings related to storage service.", "group": "enrichment", "type": "integer", "range_selection": true}, "active_storage_finding_count": {"caption": "Active Storage Finding Count", "description": "Number of active findings related to storage service.", "group": "enrichment", "type": "integer", "range_selection": true}}, "dashboard_identifier": "EI"}