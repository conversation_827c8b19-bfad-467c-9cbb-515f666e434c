{"name": "Vulnerability Finding On Container", "caption": "Vulnerability Finding On Container", "navigator_enabled": true, "detail_view_caption": "Open Vulnerability Finding On Container", "sorting_columns": [{"field": "current_status", "type": "string", "desc": true}, {"field": "recency_relationship", "type": "integer", "desc": false}, {"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "isInverse": false, "inverse_relationship_name": "Container Has Vulnerability Finding", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "type": {"caption": "Type", "description": "The specific type of the entity.", "type": "string", "data_structure": "list", "visualization_enabled": true}, "native_type": {"caption": "Cloud Native Type", "description": "It specifies the exact cloud service named by the Cloud Provider.", "type": "string"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud provider to which the resource belongs.", "type": "string", "visualization_enabled": true}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the entity is active within the configured inactivity period.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the vulnerability was first seen on Container as inferred from data source.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Time at which the vulnerability was last seen on Container as inferred from data source.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "How long the vulnerability was present on the Container.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the vulnerability last observed on the Container.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "current_status": {"caption": "Vulnerability Finding Status", "description": "Current Status of the vulnerability on Container.", "type": "string"}, "initial_status": {"caption": "Initial Status", "description": "Initial Status of the vulnerability on Container.", "type": "string", "detailed_view_hide": true}, "software_vendor": {"caption": "Software Vendor", "description": "Software vendor is a company or individual that develops, sells, or licenses software products or services. These vendors can range from large multinational corporations to smaller boutique firms. They provide a variety of software solutions.", "type": "string", "detailed_view_hide": true}, "software_name": {"caption": "Software Name", "description": "A software name is a unique identifier or label given to a particular software application. It serves as a way to identify, reference, and distinguish the software from others.", "type": "string", "detailed_view_hide": true}, "software_version": {"caption": "Software Version", "description": "A software version is a unique identifier that indicates a specific release or iteration of a software application. It helps track changes, updates, and bug fixes made to the software over time.", "type": "string", "detailed_view_hide": true}, "software_full_name": {"caption": "Software", "description": "Concatenated form of Software Vendor, Software Name and Software Version", "type": "string"}, "software_product": {"caption": "Software Product", "description": "Concatenated form of Software Vendor and Software Name", "type": "string"}, "path_details": {"caption": "Path Details", "description": "Specify the exact path or location within the software/system where the vulnerability exists. This might include file paths, URL endpoints, or specific components/modules.", "type": "string", "data_structure": "list"}}, "target_entity": "Container", "dashboard_identifier": "EI", "source_entity": "Vulnerability"}