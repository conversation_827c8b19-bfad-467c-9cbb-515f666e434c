{"name": "Network Has Finding", "caption": "Network Has Finding", "detail_view_caption": "Network Has Open Finding", "customFilter": [{"filterType": "rel_finding_status", "filterData": ["Open"], "isExtra": false, "isDate": false}], "sorting_columns": [{"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "isInverse": true, "inverse_relationship_name": "Finding Associated With Network", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud provider to which the resource belongs.", "type": "string", "visualization_enabled": true}, "vendor_severity_normalised": {"caption": "Severity Normalized", "description": "The normalized severity corresponding to the finding across all vendors", "type": "string", "visualization_enabled": true}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the entity is active within the configured inactivity period.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the Network to finding relation is observed.", "type": "string", "data_structure": "list"}, "rel_finding_status": {"caption": "Finding Status", "description": "Status of the finding. For eg. Open", "type": "string"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the Network connected to finding.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the Network to finding relation is inferred from data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the Network has finding.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "network_finding_sla_duration": {"caption": "Network Finding SLA Duration", "description": "Calculates the number of days between the finding reopening date and its resolution date for network findings.", "type": "integer", "range_selection": true, "is_enrichment": true}, "network_finding_sla_resolve_status": {"caption": "Network Finding SLA Resolve Status", "description": "Indicates whether a network finding was resolved within the defined SLA period.", "type": "string", "is_enrichment": true}}, "target_entity": "Finding", "dashboard_identifier": "EI", "source_entity": "Network"}