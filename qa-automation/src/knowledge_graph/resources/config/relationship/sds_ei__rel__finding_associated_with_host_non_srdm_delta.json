{"name": "Finding associated with Host", "inverseRelationshipName": "Host Has Finding", "inputSourceInfo": [{"sdmPath": "kg1_test.sds_em__finding_evidence_test", "origin": "Assessment Evidence", "dataIntervalTimestampKey": "updated_at_ts", "dataEventTimestampKey": "updated_at_ts", "uniqueRecordIdentifierKey": "unique_id", "sourceMappingInfo": ["s3a://product-df-dte-apps/kg1/test_configs/loader_configs_rel/sds_em__finding_config.json"], "targetMappingInfo": {"tableName": "kg1_test.sds_ei__host__enrich_test", "joinCondition": "s.host_p_id=e.p_id"}, "temporaryProperties": [{"colName": "host_p_id_struct", "colExpr": "explode(filter(affected_asset, x -> x.class = 'Host'))"}, {"colName": "host_p_id", "colExpr": "host_p_id_struct['p_id']"}, {"colName": "unique_id", "colExpr": "concat(uuid,updated_at_ts)"}, {"colName": "upper_exposure_category", "colExpr": "UPPER(exposure_category)"}]}], "optionalAttributes": [{"name": "operational_status_test", "exp": "Case when exposure_category ='Misconfiguration' Then UPPER(exposure_category) when exposure_category='Control Gap' Then LOWER(exposure_category) ELSE lower('TEST') END", "occurrence": "LAST"}, {"name": "unique_id_test", "exp": "unique_id", "occurrence": "FIRST"}, {"name": "affected_asset_origin_test", "exp": "affected_asset_origin", "occurrence": "COLLECT"}, {"name": "upper_exposure_category_test", "exp": "upper_exposure_category", "occurrence": "FIRST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "kg1_test_new.sds_ei__rel__finding_associated_with_host_non_srdm", "prevMiniSDM": "kg1_test_new.sds_ei__rel_mini_sdm__finding_associated_with_host_non_srdm"}}