{"name": "Person Has Identity", "inverseRelationshipName": "Identity Associated With Person", "intraSourcePath": "kg3_test.sds_ei_intra_source_resolver_rerun", "interSourcePath": "kg3_test.sds_ei__resolver__person_rerun", "interTargetPath": "kg3_test.sds_ei__resolver__identity_rerun", "inputSourceInfo": [{"sdmPath": "kg1_test.microsoft_azure__ad_user_test", "origin": "MS Azure AD", "filter": "country like 'IN%' ", "sourceMappingInfo": ["s3a://product-df-dte-apps/kg1/test_configs/loader_configs_rel/sds_ei__person__ms_azure_ad_users__aad_id__job_config.json"], "targetMappingInfo": ["s3a://product-df-dte-apps/kg1/test_configs/loader_configs_rel/sds_ei__identity__ms_azure_ad_users__user_principal_name__job_config.json", "s3a://product-df-dte-apps/kg1/test_configs/loader_configs_rel/sds_ei__identity__ms_azure_ad_users__other_mails__job_config.json"], "enrichments": [{"lookupInfo": {"tableName": "lookup_v2_ds.test_lookup", "enrichmentColumns": ["test_1", "test_2", "test_3"]}, "joinCondition": "s.cveId = e.test_1"}]}], "optionalAttributes": [{"name": "display_name", "exp": "displayName", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "kg1_test_new.sds_ei__rel__ms_azure_ad_users__person_has_identity", "prevMiniSDM": "kg1_test_new.sds_ei__rel_mini_sdm__ms_azure_ad_users__person_has_identity"}, "relationship": {"rel_name": "Person Has Identity", "name": "MS Azure AD", "feedName": "Users"}}