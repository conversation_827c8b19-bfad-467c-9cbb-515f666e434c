{"name": "Vulnerability Finding On Host", "origin": "MS Defender", "inverseRelationshipName": "Host Has Vulnerability Finding", "intraSourcePath": "kg3.sds_ei_intra_source_resolver_rerun", "interSourcePath": "kg3_fragment.sds_ei__resolver__vulnerability_rerun", "interTargetPath": "kg3_fragment.sds_ei__resolver__host_rerun", "inputSourceInfo": [{"sdmPath": "kg1_test.microsoft_azure__defender_device_software_vuln_delta_test_retest", "origin": "MS Defender", "sourceMappingInfo": ["s3a://product-df-dte-apps/kg1/test_configs/loader_configs_rel/sds_ei__vulnerability__ms_defender_device_tvm_software_vulnerabilities_delta__cve_id__job_config.json"], "targetMappingInfo": ["s3a://product-df-dte-apps/kg1/test_configs/loader_configs_rel/sds_ei__host__ms_defender_device_tvm_software_vulnerabilities_delta__device_id__job_config.json"], "enrichments": [{"lookupInfo": {"tableName": "lookup_v2_ds.microsoft_azure__defender_device_software_vuln_delta_software_retest", "enrichmentColumns": ["software_version", "software_vendor", "software_full_name", "software_product"]}, "joinCondition": "s.cveId = e.cveId AND e.deviceId = s.deviceId"}], "temporaryProperties": [{"colName": "temp_extracted_disk_path_test", "colExpr": "explode_outer(diskPaths)"}, {"colName": "temp_device_software_concat_test", "colExpr": "CONCAT_WS('_', deviceName, softwareName)"}, {"colName": "temp_unique_registry_paths_test", "colExpr": "collect_set(registryPaths) OVER (PARTITION BY deviceId ORDER BY event_timestamp_epoch ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_extracted_year_test", "colExpr": "REGEXP_EXTRACT(eventTimestamp, '(\\\\d{4})', 1)"}, {"colName": "temp_array_of_device_and_software_test", "colExpr": "array(deviceName, softwareName)"}, {"colName": "temp_event_timestamp_millis_test", "colExpr": "UNIX_MILLIS(event_timestamp_ts)"}, {"colName": "temp_unique_software_vendor_combinations_test", "colExpr": "collect_set(array(softwareVendor, softwareVersion))OVER (PARTITION BY deviceId ORDER BY event_timestamp_epoch ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_software_struct_test", "colExpr": "struct(softwareName, softwareVendor, softwareVersion)"}, {"colName": "temp_software_metadata_array_test", "colExpr": "array(softwareName, softwareVersion, softwareVendor)"}, {"colName": "temp_software_info_string_test", "colExpr": "CONCAT_WS(' | ', temp_software_metadata_array_test[0], temp_software_metadata_array_test[1], temp_software_metadata_array_test[2])"}, {"colName": "temp_software_info_summary_test", "colExpr": "UPPER(CONCAT('Detected software: ', temp_software_info_string_test))"}, {"colName": "temp_trusted_domains_list_test", "colExpr": "array('microsoft.com', 'qualys.com', 'redhat.com', 'ubuntu.com')"}, {"colName": "temp_severity_class_test", "colExpr": "CASE WHEN vulnerabilitySeverityLevel in ('High','Critical')THEN 'Severe' else 'Not-Severe' END"}, {"colName": "temp_first_seen_timestamp_epoch_test", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstSeenTimestamp)))"}]}], "optionalAttributes": [{"name": "extracted_disk_path_test", "exp": "temp_extracted_disk_path_test", "occurrence": "LAST"}, {"name": "device_software_concat_test", "exp": "temp_device_software_concat_test", "occurrence": "LAST"}, {"name": "unique_registry_paths_test", "exp": "temp_unique_registry_paths_test", "occurrence": "LAST"}, {"name": "extracted_year_test", "exp": "temp_extracted_year_test", "occurrence": "LAST"}, {"name": "array_of_device_and_software_test", "exp": "temp_array_of_device_and_software_test", "occurrence": "COLLECT"}, {"name": "event_timestamp_millis_test", "exp": "temp_event_timestamp_millis_test", "occurrence": "LAST"}, {"name": "unique_software_vendor_combinations_test", "exp": "temp_unique_software_vendor_combinations_test", "occurrence": "LAST"}, {"name": "software_struct_test", "exp": "temp_software_struct_test", "occurrence": "LAST"}, {"name": "software_metadata_array_test", "exp": "temp_software_metadata_array_test", "occurrence": "LAST"}, {"name": "software_info_string_test", "exp": "temp_software_info_string_test", "occurrence": "LAST"}, {"name": "software_info_summary_test", "exp": "temp_software_info_summary_test", "occurrence": "LAST"}, {"name": "trusted_domains_list_test", "exp": "temp_trusted_domains_list_test", "occurrence": "LAST"}, {"name": "severity_class_test", "exp": "temp_severity_class_test", "occurrence": "FIRST"}, {"name": "ms_recommended_update", "exp": "recommendedSecurityUpdate", "occurrence": "LAST"}, {"name": "ms_recommended_update_id", "exp": "recommendedSecurityUpdateId", "occurrence": "LAST"}, {"name": "vulnerability_fixed_date", "exp": "case when (max(event_timestamp_epoch) OVER (PARTITION BY target_p_id,source_p_id,software_full_name,temp_first_seen_timestamp_epoch_test ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING))=event_timestamp_epoch then (CASE WHEN ((CASE WHEN lower(status)='fixed' OR (datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))/1000))) > 180  )  THEN 'Closed' ELSE 'Open' END) = 'Closed') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp))) ELSE NULL END)END", "occurrence": "LAST"}, {"name": "current_status", "exp": "CASE WHEN lower(status)='fixed' OR (datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))/1000))) > 180  )  THEN 'Closed' ELSE 'Open' END", "occurrence": "LAST"}, {"name": "initial_status", "exp": "'Open'", "occurrence": "LAST"}, {"name": "vendor_status", "exp": "status", "occurrence": "LAST"}, {"name": "inactivity_period", "exp": "180", "occurrence": "LAST"}, {"name": "software_vendor", "exp": "software_vendor", "occurrence": "LAST"}, {"name": "software_version", "exp": "software_version", "occurrence": "LAST"}, {"name": "software_product", "exp": "software_product", "occurrence": "LAST"}, {"name": "software_full_name_test", "exp": "software_full_name", "occurrence": "LAST"}, {"name": "relationship_first_seen_date", "exp": "temp_first_seen_timestamp_epoch_test", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))", "occurrence": "LAST"}, {"name": "path_details", "exp": "diskPaths", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id", "software_full_name", "temp_first_seen_timestamp_epoch_test"]}, "output": {"outputTable": "kg3_test_new.sds_ei__rel__ms_defender__vulnerability_finding_on_host", "prevMiniSDM": "kg3_test_new.sds_ei__rel_mini_sdm__ms_defender__vulnerability_finding_on_host"}, "relationship": {"rel_name": "Vulnerability Finding On Host", "name": "Microsoft Defender For Endpoint", "feedName": "Device Software Vulnerability"}}