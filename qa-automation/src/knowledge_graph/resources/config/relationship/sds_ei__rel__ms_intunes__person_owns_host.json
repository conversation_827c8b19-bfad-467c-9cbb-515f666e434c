{"name": "Person Owns Host", "origin": "MS Intune", "inverseRelationshipName": "Host Owned By Person", "intraSourcePath": "kg_cloud_run_2.sds_ei_intra_source_resolver", "interSourcePath": "kg_fragment_cloud_run_2.sds_ei__resolver__person", "interTargetPath": "kg_fragment_cloud_run_2.sds_ei__resolver__host", "inputSourceInfo": [{"sdmPath": "kg1_test.microsoft__intune_test", "origin": "MS Intune", "sourceMappingInfo": {"tableName": "kg1_test.sds_ei__person__ms_intunes__user_id_test", "joinCondition": "s.temp_person_pk=e.primary_key"}, "targetMappingInfo": {"tableName": "kg1_test.sds_ei__host__ms_intunes__device_id", "joinCondition": "s.temp_host_pk=e.primary_key"}, "enrichments": [{"lookupInfo": {"preTransform": [{"colName": "software_vendor", "colExpr": "UPPER(software_vendor)"}], "tableName": "kg1_test.microsoft_azure__defender_device_software_vuln_delta_software_test", "enrichmentColumns": ["software_version", "software_name", "software_vendor", "software_full_name", "software_product"]}, "joinCondition": "e.deviceId_upper = s.temp_deviceId", "sourcePreTransform": [{"colName": "deviceId", "colExpr": "UPPER(deviceId)"}]}], "temporaryProperties": [{"colName": "temp_person_pk", "colExpr": "userId"}, {"colName": "temp_host_pk", "colExpr": "Id"}, {"colName": "temp_deviceId", "colExpr": "UPPER(deviceId)"}]}], "optionalAttributes": [{"name": "software_name", "exp": "software_name", "occurrence": "LAST"}], "entityBasedRelationBuilderStrategySpec": {"baseEntity": "targetEntity"}, "output": {"outputTable": "kg1_test.sds_ei__rel__ms_intunes__person_owns_host", "prevMiniSDM": "kg1_test.sds_ei__rel_mini_sdm__ms_intunes__person_owns_host"}, "relationship": {"rel_name": "Person Owns Host", "name": "MS Intune", "feedName": "MDM"}}