from pyspark.sql import functions as F
from pyspark.sql.types import ArrayType, StructType, StringType, LongType, IntegerType
from functools import reduce
from core.common.logging_utils import setup_logger
from core.api.api_client import APIClient
from src.utilities.common.api_utils import APIUtils
from src.utilities.common.readProperties import ReadConfig
from src.utilities.data_test.common import Common
import json
logger = setup_logger(__name__)

class DQValidation:
    def __init__(self, spark, context):
        self.spark = spark
        self.context = context
        self._api_client = APIClient()
        self._api_utils = APIUtils()
        self._read_config = ReadConfig()

    def _derive_entity_from_table(self, table_name):
        try:
            # expects formats like: sds_ei__{entity} or sds_ei__{entity}__publish
            if table_name.startswith("sds_ei__"):
                remainder = table_name[len("sds_ei__"):]
                return remainder.split("__")[0]
        except Exception:
            pass
        return table_name

    def _get_drop_fields(self, entity):
        """Return dynamic drop fields only from DQ config API. No static defaults."""
        try:
            headers = self._api_utils.set_headers()
            dq_url_template = self._read_config.get_dq_spark_jobs_url()
            dq_url = dq_url_template.format(entity=entity)
            response = self._api_client.get(dq_url, headers=headers)
            logger.info(f"DQ config API called: {dq_url}")
            if response and response.get("status") != "ERROR":
                completeness = response.get("completeness") or {}
                skip_columns = completeness.get("skipColumns")
                if isinstance(skip_columns, list):
                    return [str(c).strip() for c in skip_columns if str(c).strip()]
                if isinstance(skip_columns, str):
                    return [c.strip() for c in skip_columns.split(',') if c.strip()]
                logger.warning("DQ config missing 'skipColumns'; proceeding without drops")
                return []
            else:
                logger.warning(f"DQ config API error for '{entity}': {response.get('detail') if response else 'no response'}; proceeding without drops")
        except Exception as e:
            logger.warning(f"Failed to fetch dynamic drop fields for '{entity}': {str(e)}; proceeding without drops")
        return []

    def _get_completeness_expr(self, col_name):
        return F.expr(f"""
            CASE 
                WHEN {col_name} IS NULL THEN 0
                ELSE 1
            END
        """)

    def load_main_table(self, entity, table, updated_at):
        """Load dq completeness table for the entity."""
        # Remember current table/entity for downstream use
        try:
            self.context.current_table = table
            self.context.current_entity = self._derive_entity_from_table(table)
        except Exception:
            pass
        main_table_schema_key = 'EI_SCHEMA_NAME'
        schema_name= self.get_schema_from_context(main_table_schema_key)
        main_table_name=f"iceberg_catalog.{schema_name}.{table}"
        df= self.spark.sql(f"""
            SELECT * 
            FROM {main_table_name}
            WHERE updated_at_ts = '{updated_at}'
        """)
        return df
        

    def load_dq_table(self, entity,table, updated_at):
        """Load dq completeness table for the entity."""
        dq_table_schema_key = 'DQ_SCHEMA_NAME'
        schema_name= self.get_schema_from_context(dq_table_schema_key)
        logger.info("Using DQ schema: %s", schema_name)
        dq_table_name=f"iceberg_catalog.{schema_name}.{table}"
        query= f"SELECT * FROM {dq_table_name} WHERE updated_at_ts = '{updated_at}'"
        logger.info(f"query,{query}")    
        return self.spark.sql(f"""
            SELECT * 
            FROM {dq_table_name}
            WHERE updated_at_ts = '{updated_at}'
            AND kg_job_type = 'entity_rel_enrich'
        """)
        
    def compute_cq_columns(self, df, drop_fields=None):
        """Generate _cq columns and compute dq_completeness."""
        if drop_fields is None:
            # Try to derive from API config using current_entity if available
            entity = None
            try:
                entity = getattr(self.context, 'current_entity', None)
            except Exception:
                entity = None
            if not entity:
                # Last resort: attempt to find entity from a known column or fallback
                entity = 'unknown'
            drop_fields = self._get_drop_fields(entity)
        try:
            logger.info("Drop fields used for completeness: %s", ", ".join(drop_fields))
        except Exception:
            logger.info("Drop fields used for completeness: %s", str(drop_fields))
        # Log only the fields that actually exist in the DataFrame schema
        try:
            schema_cols = {f.name for f in df.schema.fields}
            to_drop_present = [c for c in drop_fields if c in schema_cols]
            logger.info("List of columns present in DataFrame to be dropped: %s", ", ".join(to_drop_present) if to_drop_present else "<none>")
        except Exception:
            pass

        fields = [f for f in df.schema.fields if f.name not in drop_fields]

        for field in fields:
            col_name = field.name
            dtype = field.dataType

            if isinstance(dtype, ArrayType):
                elem_type = dtype.elementType

                if isinstance(elem_type, StructType):
                    df = df.withColumn(
                        f"{col_name}_cq",
                        F.expr(f"CASE WHEN CAST({col_name}[0] AS STRING) IS NULL THEN 0 ELSE 1 END")
                    )

                elif isinstance(elem_type, StringType):
                    df = df.withColumn(
                        f"{col_name}_cq",
                        F.when(F.col(col_name).isNull(), 0)
                        .when(F.size(F.col(col_name)) == 0, 0)
                        .when(F.col(col_name)[0] == '  !', None)
                        .when((F.size(F.col(col_name)) == 1) & (F.col(col_name)[0].isNull()), 0)
                        .when(F.size(F.expr(f"filter({col_name}, x -> x IS NOT NULL)")) > 0, 1)
                        .otherwise(0)
                    )

                elif isinstance(elem_type, LongType):
                    df = df.withColumn(
                        f"{col_name}_cq",
                        F.when(F.col(col_name).isNull(), 0)
                        .when(F.col(col_name)[0] == -9223372036854775808, None)
                        .otherwise(1)
                    )

                elif isinstance(elem_type, IntegerType):
                    df = df.withColumn(
                        f"{col_name}_cq",
                        F.when(F.col(col_name).isNull(), 0)
                        .when(F.col(col_name)[0] == -2147483648, None)
                        .otherwise(1)
                    )

            elif isinstance(dtype, StructType):
                df = df.withColumn(
                    f"{col_name}_cq",
                    F.expr(f"CASE WHEN CAST({col_name} AS STRING) IS NULL THEN 0 ELSE 1 END")
                )

            elif isinstance(dtype, StringType):
                df = df.withColumn(
                    f"{col_name}_cq",
                    F.when(F.col(col_name).isNull(), 0)
                    .when(F.col(col_name) == '  !', None)
                    .otherwise(1)
                )

            elif isinstance(dtype, LongType):
                df = df.withColumn(
                    f"{col_name}_cq",
                    F.when(F.col(col_name).isNull(), 0)
                    .when(F.col(col_name) == -9223372036854775808, None)
                    .otherwise(1)
                )

            elif isinstance(dtype, IntegerType):
                df = df.withColumn(
                    f"{col_name}_cq",
                    F.when(F.col(col_name).isNull(), 0)
                    .when(F.col(col_name) == -2147483648, None)
                    .otherwise(1)
                )

            else:
                df = df.withColumn(f"{col_name}_cq", self._get_completeness_expr(col_name))

        # Compute dq_completeness
        cq_columns = [c for c in df.columns if c.endswith("_cq")]

        if cq_columns:
            sum_cq = reduce(lambda a, b: a + b, [F.when(F.col(c).isNull(), 0).otherwise(F.col(c)) for c in cq_columns])
            count_not_null_cq = reduce(lambda a, b: a + b, [F.when(F.col(c).isNotNull(), 1).otherwise(0) for c in cq_columns])

            df = df.withColumn(
                "dq_completeness",
                F.when(count_not_null_cq == 0, None).otherwise(sum_cq / count_not_null_cq)
            )
        return df

    def join_main_and_dq(self, df, dq_df):
        """Join main and dq DataFrames on p_id."""
        joined_df = df.alias("df").join(dq_df.alias("dq"), on="p_id", how="inner")
        joined_df = joined_df.withColumn(
            "dq_completeness_rounded",
            F.round(F.col("df.dq_completeness") * 100, 4)
        ).withColumn(
            "dq_quality_score_rounded",
            F.round(F.col("dq.completeness_quality_score"), 4)
        )
        return joined_df

    def validate_pid_counts(self, df, dq_df):
        """Validate distinct p_id counts match."""
        df_pid_count = df.select("p_id").distinct().count()
        dq_pid_count = dq_df.select("p_id").distinct().count()
        logger.info(f"Distinct p_id counts — main={df_pid_count}, dq={dq_pid_count}")
        assert df_pid_count == dq_pid_count, f"p_id count mismatch: main={df_pid_count}, dq={dq_pid_count}"

    def validate_completeness_scores(self, joined_df):
        """Validate dq_completeness matches dq_quality_score."""
        mismatches = joined_df.filter(F.col("dq_completeness_rounded") != F.col("dq_quality_score_rounded"))
        mismatch_count = mismatches.count()
        assert mismatch_count == 0, f"Found {mismatch_count} mismatches in completeness scores."

    def show_mismatches(self, joined_df):
        """Display mismatches if any."""
        mismatches = joined_df.filter(F.col("dq_completeness_rounded") != F.col("dq_quality_score_rounded"))
        if mismatches.count() > 0:
            mismatches.select("p_id", "dq_completeness_rounded", "dq_quality_score_rounded").show(100, truncate=False)

    def get_mismatched_pids(self, joined_df):
        """Return list of p_id values where completeness does not match."""
        mismatches = joined_df.filter(
            F.col("dq_completeness_rounded") != F.col("dq_quality_score_rounded")
        ).select("p_id").distinct()
        return [r[0] for r in mismatches.collect()]

    def get_cq_json_for_pid(self, df, pid):
        """For a given p_id, serialize all *_cq columns into a JSON map."""
        cq_columns = [c for c in df.columns if c.endswith("_cq")]
        if not cq_columns:
            return json.dumps({})
        row_df = (
            df.filter(F.col("p_id") == pid)
              .select(
                  F.to_json(
                      F.map_from_arrays(
                          F.array([F.lit(c) for c in cq_columns]),
                          F.array([F.col(c) for c in cq_columns])
                      )
                  ).alias("cq_json")
              )
        )
        rows = row_df.collect()
        return rows[0]["cq_json"] if rows else json.dumps({})

    def get_dq_json_for_pid(self, dq_df, pid):
        """For a given p_id, serialize dq completeness_attr_scores struct to JSON."""
        row_df = (
            dq_df.filter(F.col("p_id") == pid)
                 .select(F.to_json(F.col("completeness_attr_scores")).alias("completeness_attr_scores_json"))
        )
        rows = row_df.collect()
        return rows[0]["completeness_attr_scores_json"] if rows else json.dumps({})

    def compare_cq_with_dq(self, cq_json_str, dq_json_str):
        """Compare cq JSON (keys with _cq) against dq JSON (plain keys) and return mismatches dict."""
        try:
            df_dict = json.loads(cq_json_str) if cq_json_str else {}
        except Exception:
            df_dict = {}
        try:
            dq_dict = json.loads(dq_json_str) if dq_json_str else {}
        except Exception:
            dq_dict = {}

        # Normalize df_dict keys by removing suffix _cq
        df_dict_clean = {str(k).replace("_cq", ""): v for k, v in df_dict.items()}
        all_keys = set(df_dict_clean.keys()) | set(dq_dict.keys())

        mismatches = {}
        for key in sorted(all_keys):
            v1 = df_dict_clean.get(key, None)
            v2 = dq_dict.get(key, None)
            if v1 != v2:
                mismatches[key] = {"df_value": v1, "dq_value": v2}
        return mismatches

    def get_schema_from_context(self, schema_key):
        """Fetch schema name from context API."""
        try:
            headers = self._api_utils.set_headers()
            url = self._read_config.get_context_dq()
            response = self._api_client.get(url, headers=headers)

            if response and response.get("status") != "ERROR":
                schema = response.get("config_value", {}).get(schema_key)
                if schema:
                    logger.info(f"Schema obtained for {schema_key}: {schema}")
                    return schema
                else:
                    logger.warning(f"Schema key '{schema_key}' not found in context response")
            else:
                logger.warning(f"Context API error for '{schema_key}': {response.get('detail') if response else 'no response'}")

        except Exception as e:
            logger.error(f"Failed to fetch schema for '{schema_key}': {str(e)}")

        return None  # safe fallback
