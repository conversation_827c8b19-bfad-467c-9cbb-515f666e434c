
from pyspark.sql.functions import  col
import pandas as pd
import numpy as np
from src.utilities.data_test.common import Common
from core.common.logging_utils import setup_logger
import logging
import os , sys
import shutil
logger = setup_logger(__name__)
import pandas as pd
import numpy as np
import datacompy
import tempfile
from pyspark.sql.types import TimestampType

class OuputComparison:
    def __init__(self, spark, data,run,context):
        updated_at = Common.UPDATED_AT
        logger.info(" ✅ updated_at ✅",updated_at)
        if context.folder_name =='loader' :
            self.output_table = data['outputTableInfo']['outputTableName']
            self.output_table_without_schema = self.output_table.split('.')[-1]
            val = "CREATE TABLE"
            if run == 'normal':
                table_type = spark.sql(f"SHOW CREATE TABLE ei_test_kg.{self.output_table_without_schema}")                
                stmt = table_type.collect()[0]["createtab_stmt"]
                table_type = " ".join(stmt.split()[:2])
                if val in table_type:
                    self.df1 = spark.sql(f"SELECT * FROM iceberg_catalog.ei_test_kg.{self.output_table_without_schema} where updated_at ={updated_at}")
                    logger.info("Reading data from old iceberg table ✅",self.output_table_without_schema)
                else:
                    self.df1 = spark.sql(f"SELECT * FROM spark_catalog.ei_test_kg.{self.output_table_without_schema} where updated_at ={updated_at}")
                    logger.info("Reading data from old iceberg view ✅",self.output_table_without_schema)
            else:
                table_type = spark.sql(f"SHOW CREATE TABLE ei_test_delta_kg.{self.output_table_without_schema}")                
                stmt = table_type.collect()[0]["createtab_stmt"]
                table_type = " ".join(stmt.split()[:2])
                if val in table_type:
                    self.df1 = spark.sql(f"SELECT * FROM iceberg_catalog.ei_test_delta_kg.{self.output_table_without_schema} where updated_at ={updated_at}")
                    logger.info("Reading data from old iceberg delta table ✅",self.output_table_without_schema)
                else:
                    self.df1 = spark.sql(f"SELECT * FROM spark_catalog.ei_test_delta_kg.{self.output_table_without_schema} where updated_at ={updated_at}")
                    logger.info("Reading data from old iceberg delta view ✅",self.output_table_without_schema)
                
            table_type = spark.sql(f"SHOW CREATE TABLE {self.output_table}")             
            stmt = table_type.collect()[0]["createtab_stmt"]
            table_type = " ".join(stmt.split()[:2])
            if val in table_type:
                    self.df2 = spark.sql(f"SELECT * FROM iceberg_catalog.{self.output_table} where updated_at ={updated_at}")
                    logger.info("Reading data from new iceberg table ✅",self.output_table)
            else:
                    self.df2 = spark.sql(f"SELECT * FROM spark_catalog.{self.output_table} where updated_at ={updated_at}")   
                    logger.info("Reading data from new iceberg view ✅",self.output_table)
            
            self.join_col ='primary_key' 




        elif context.folder_name == 'relationship':
            self.output_table = data['output']['outputTable']
            logger.info("✅✅✅✅",self.output_table)
            self.output_table_without_schema = self.output_table.split('.')[-1]
            val = "CREATE TABLE"
            if run == 'normal':
                table_type = spark.sql(f"SHOW CREATE TABLE ei_test_kg.{self.output_table_without_schema}")                
                stmt = table_type.collect()[0]["createtab_stmt"]
                table_type = " ".join(stmt.split()[:2])
                if val in table_type:
                    self.df1 = spark.sql(f"SELECT * FROM iceberg_catalog.ei_test_kg.{self.output_table_without_schema} where updated_at ={updated_at}")
                    logger.info("Reading data from old iceberg table ✅",self.output_table_without_schema)
                else:
                    self.df1 = spark.sql(f"SELECT * FROM spark_catalog.ei_test_kg.{self.output_table_without_schema} where updated_at ={updated_at}")
                    logger.info("Reading data from old iceberg view ✅",self.output_table_without_schema)
            else:
                table_type = spark.sql(f"SHOW CREATE TABLE ei_test_delta_kg.{self.output_table_without_schema}")                
                stmt = table_type.collect()[0]["createtab_stmt"]
                table_type = " ".join(stmt.split()[:2])
                if val in table_type:
                    self.df1 = spark.sql(f"SELECT * FROM iceberg_catalog.ei_test_delta_kg.{self.output_table_without_schema} where updated_at ={updated_at}")
                    logger.info("Reading data from old iceberg delta table ✅",self.output_table_without_schema)
                else:
                    self.df1 = spark.sql(f"SELECT * FROM spark_catalog.ei_test_delta_kg.{self.output_table_without_schema} where updated_at ={updated_at}")
                    logger.info("Reading data from old iceberg delta view ✅",self.output_table_without_schema)
                
            table_type = spark.sql(f"SHOW CREATE TABLE iceberg_catalog.{self.output_table}")             
            stmt = table_type.collect()[0]["createtab_stmt"]
            table_type = " ".join(stmt.split()[:2])
            if val in table_type:
                    self.df2 = spark.sql(f"SELECT * FROM iceberg_catalog.{self.output_table} where updated_at ={updated_at}")
                    logger.info("Reading data from new iceberg table ✅",self.output_table)
            else:
                    self.df2 = spark.sql(f"SELECT * FROM spark_catalog.{self.output_table} where updated_at ={updated_at}")   
                    logger.info("Reading data from new iceberg view ✅",self.output_table)

            self.join_col ='relationship_id'




        else:
            print("Handle for other modules")
           
      


    def output_comparison(self):
        # Step 1: Find common and mismatched columns
        df1_cols = set(self.df1.columns)
        df2_cols = set(self.df2.columns)

        common_cols = sorted(df1_cols & df2_cols)
        df1_only = df1_cols - df2_cols
        df2_only = df2_cols - df1_cols

        # Step 2: Set schema mismatch flag
        schema_mismatch = bool(df1_only or df2_only)

        # Step 3: Compare only common columns
        df1_common = self.df1.select(common_cols)
        df2_common = self.df2.select(common_cols)

        # Step 4: Check row-level differences
        try:
            is_diff_more_than_one = (
                df1_common.exceptAll(df2_common).count() > 0 or
                df2_common.exceptAll(df1_common).count() > 0
            )
        except Exception as e:
            # Log the error if needed
            print(f"EXCEPT ALL failed due to schema incompatibility: {e}")
            is_diff_more_than_one = True  # Force mismatch flag

        # Step 5: Final mismatch flag
        mismatch_flag = schema_mismatch or is_diff_more_than_one

        logger.info("✅ Mismatch flag ✅",mismatch_flag)

        if mismatch_flag:
            def safe_timestamp_to_pandas(spark_df):
                schema = spark_df.schema
                timestamp_columns = [field.name for field in schema.fields if isinstance(field.dataType, TimestampType)]

                if not timestamp_columns:
                    return spark_df.toPandas()

                print(f"Found timestamp columns: {timestamp_columns}")
                converted_df = spark_df
                for col_name in timestamp_columns:
                    converted_df = converted_df.withColumn(col_name, col(col_name).cast('string'))

                setting = "spark.sql.execution.arrow.pyspark.enabled"
                spark = converted_df.sql_ctx.sparkSession
                original_arrow_setting = spark.conf.get(setting, "true")
                spark.conf.set(setting, "false")
                logger.info("✅✅✅✅enterd to mismatch function ✅✅✅✅")

                try:
                    pandas_df = converted_df.toPandas()
                    print(f"Timestamp columns kept as strings for accurate comparison: {timestamp_columns}")
                finally:
                    spark.conf.set(setting, original_arrow_setting)

                return pandas_df

            df1 = safe_timestamp_to_pandas(self.df1)
            df2 = safe_timestamp_to_pandas(self.df2)
           

            pd.set_option('display.max_rows', None)
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            pd.set_option('display.max_colwidth', None)
            logger.info("✅✅✅✅reached_1 ✅✅✅✅")
            # Ensure all columns except join_col are string type
            
            join_col = self.join_col
            df1[join_col] = df1[join_col].astype(str)
            df2[join_col] = df2[join_col].astype(str)

            for col_name in df1.columns:
                if col_name != join_col:
                    df1[col_name] = df1[col_name].astype(str)

            for col_name in df2.columns:
                if col_name != join_col:
                    df2[col_name] = df2[col_name].astype(str)

            

            compare = datacompy.Compare(
                df1,
                df2,
                join_columns=[self.join_col],
                df1_name='Old Data',
                df2_name='New Data'
            )

            # Redirect all print output to a temporary file
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
                temp_file = f.name
                original_stdout = sys.stdout
                sys.stdout = f  # Redirect print to file

                try:
                    logger.info("✅✅✅✅reached_2 ✅✅✅✅")
                    logger.info("Are DataFrames exactly equal ", compare.matches(ignore_extra_columns=False))
                    print(compare.report(sample_count=500))
                    logger.info('✅Columns unique to New Data:')
                    print(compare.df2_unq_columns())
                    print('Columns unique to Old Data:')
                    print(compare.df1_unq_columns())
                finally:
                    sys.stdout = original_stdout  # Restore original stdout

                # Log to logger (not redirected)
                logger.info(compare.report(sample_count=500))
                logger.info('✅ Columns unique to New Data: ✅')
                logger.info(compare.df2_unq_columns())
                logger.info('✅ Columns unique to Old Data: ✅')
                logger.info(compare.df1_unq_columns())

        else:
            temp_file = None

        return temp_file, mismatch_flag




    def generate_report(self, report_file, context):
        logger.info("✅✅✅✅reached_reports ✅✅✅✅")
        logger.info("Entered report generation function")
        current_directory = os.getcwd()
        out_folder_name = 'output'
        folder_path = os.path.join(current_directory, out_folder_name)
        os.makedirs(folder_path, exist_ok=True)

        report_file_path = os.path.join(folder_path, f'{context.folder_name}_{self.output_table_without_schema}.txt')

        if report_file:
            shutil.copy(report_file, report_file_path)
        else:
        
            if os.path.exists(report_file_path):
                with open(report_file_path, 'w') as f:
                    f.truncate(0)  

        logger.info("✅ Report generation completed ✅")

                
 