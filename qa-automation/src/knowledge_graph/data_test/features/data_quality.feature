Feature: Validate Data Quality Completeness for Enrich Layer

@sanity-test @vulnerability @DQ-001 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity vulnerability for entity enrich layer
  Given I load main table sds_ei__vulnerability__enrich for the vulnerability and compute completeness
  Then I load dq table sds_ei__vulnerability__dq for the vulnerability and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @network_interface @DQ-002 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity network_interface for entity enrich layer
  Given I load main table sds_ei__network_interface__enrich for the network_interface and compute completeness
  Then I load dq table sds_ei__network_interface__dq for the network_interface and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @identity @DQ-003 @DF-T4216@runner.continue_after_failed_step
Sc<PERSON><PERSON>: Validate dq_completeness for the entity identity for entity enrich layer
  Given I load main table sds_ei__identity__enrich for the identity and compute completeness
  Then I load dq table sds_ei__identity__dq for the identity and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @cloud_account @DQ-004 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity cloud_account for entity enrich layer
  Given I load main table sds_ei__cloud_account__enrich for the cloud_account and compute completeness
  Then I load dq table sds_ei__cloud_account__dq for the cloud_account and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @network_services @DQ-005 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity network_services for entity enrich layer
  Given I load main table sds_ei__network_services__enrich for the network_services and compute completeness
  Then I load dq table sds_ei__network_services__dq for the network_services and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @person @DQ-006 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity person for entity enrich layer
  Given I load main table sds_ei__person__enrich for the person and compute completeness
  Then I load dq table sds_ei__person__dq for the person and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @storage @DQ-007 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity storage for entity enrich layer
  Given I load main table sds_ei__storage__enrich for the storage and compute completeness
  Then I load dq table sds_ei__storage__dq for the storage and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @host @DQ-008 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity host for entity enrich layer
  Given I load main table sds_ei__host__enrich for the host and compute completeness
  Then I load dq table sds_ei__host__dq for the host and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @container @DQ-009 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity container for entity enrich layer
  Given I load main table sds_ei__container__enrich for the container and compute completeness
  Then I load dq table sds_ei__container__dq for the container and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @network @DQ-010 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity network for entity enrich layer
  Given I load main table sds_ei__network__enrich for the network and compute completeness
  Then I load dq table sds_ei__network__dq for the network and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @application @DQ-011 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity application for entity enrich layer
  Given I load main table sds_ei__application__enrich for the application and compute completeness
  Then I load dq table sds_ei__application__dq for the application and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @account @DQ-012 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity account for entity enrich layer
  Given I load main table sds_ei__account__enrich for the account and compute completeness
  Then I load dq table sds_ei__account__dq for the account and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

@sanity-test @cluster @DQ-013 @DF-T4216 @runner.continue_after_failed_step
Scenario: Validate dq_completeness for the entity cluster for entity enrich layer
  Given I load main table sds_ei__cluster__enrich for the cluster and compute completeness
  Then I load dq table sds_ei__cluster__dq for the cluster and join both tables
  Then I verify no mismatches in p_id count and completeness score
  Then I report mismatched p_ids and field-level differences

      