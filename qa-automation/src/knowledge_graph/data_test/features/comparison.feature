Feature: Compare two output

@compare_loader_srdm @runner.continue_after_failed_step @DF-T2746 @DF-T2747 @DF-T2748 @DF-T2749 @DF-T2750 @DF-T2751 @DF-T2752 @DF-T2753 @DF-T2754 @DF-T2755 @DF-T2756 @DF-T2757 @DF-T2758 @DF-T2759 @DF-T2760 @DF-T2761 @DF-T2762 @DF-T2763 @DF-T2764 @DF-T2765 @DF-T2766 @DF-T2767 @DF-T2768 @DF-T2769 @DF-T2770 @DF-T2771 @DF-T2772 @DF-T2773 @DF-T2774 @DF-T2775 @DF-T2776 @DF-T2777 @DF-T2778 @DF-T2779 @DF-T2780 @DF-T2781 @DF-T2782 @DF-T2783 @DF-T2784 @DF-T2785 @DF-T2786 @DF-T2792 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883

Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - normal - loader - sds_ei__vulnerability__qualys_knowledgebase__qid_cve_id
   Then   validate if run output and expected output is the same
   And    generate the comparsion report


@compare_loader_srdm_delta @runner.continue_after_failed_step @DF-T2744 @DF-T2793 @DF-T2794 @DF-T2795 @DF-T2796 @DF-T2797 @DF-T2798 @DF-T2799 @DF-T2800 @DF-T2801 @DF-T2802 @DF-T2803 @DF-T2804 @DF-T2805 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883

Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - delta  - loader - sds_ei__vulnerability__qualys_knowledgebase__qid_cve_id_delta
   Then   validate if run output and expected output is the same
   And    generate the comparsion report


@compare_loader_struct @runner.continue_after_failed_step @DF-T2787 @DF-T2788 @DF-T2791 @DF-T2961 @DF-T2962 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883
Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - normal - loader - sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key
   Then   validate if run output and expected output is the same
   And    generate the comparsion report


@compare_loader_struct_delta @runner.continue_after_failed_step @DF-T2960 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883 
Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - delta - loader - sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key_delta
   Then   validate if run output and expected output is the same
   And    generate the comparsion report


@compare_loader_non_srdm @runner.continue_after_failed_step @DF-T2789 @DF-T2790 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883 
Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - normal - loader - sds_em__finding_evidence
   Then   validate if run output and expected output is the same
   And    generate the comparsion report

@compare_relationship_srdm @runner.continue_after_failed_step @DF-T3741 @DF-T3742 @DF-T3743 @DF-T3744 @DF-T3745 @DF-T3746 @DF-T3747 @DF-T3748 @DF-T3749 @DF-T3750 @DF-T3751 @DF-T3752 @DF-T3753 @DF-T3754 @DF-T3755 @DF-T3756 @DF-T3757 @DF-T3758 @DF-T3759 @DF-T3760 @DF-T3761 @DF-T3762 @DF-T3763 @DF-T3764 @DF-T3765 @DF-T3766 @DF-T3767 @DF-T3768 @DF-T3769 @DF-T851 @DF-T847 @DF-T845 @DF-T841 
Scenario: To compare the relationship output with the expected output 
   Given  The run output and the expected output is read - normal - relationship - sds_ei__rel__ms_defender__vulnerability_finding_on_host
   Then   validate if run output and expected output is the same
   And    generate the comparsion report

@compare_relationship_srdm_delta @runner.continue_after_failed_step @DF-T3770 @DF-T3771 @DF-T3772 @DF-T3773 @DF-T3774 @DF-T3775 @DF-T3776 @DF-T3777 @DF-T3778 @DF-T3779 @DF-T3780 @DF-T3781 @DF-T3782 @DF-T3783 @DF-T3784 @DF-T3785 @DF-T3786 @DF-T3787 @DF-T3788 @DF-T3789 @DF-T3790 @DF-T3791 @DF-T851 @DF-T847 @DF-T845 @DF-T841   
Scenario: To compare the relationship output with the expected output 
   Given  The run output and the expected output is read - delta - relationship - sds_ei__rel__ms_defender__vulnerability_finding_on_host_delta
   Then   validate if run output and expected output is the same
   And    generate the comparsion report

@compare_relationship_non_srdm @runner.continue_after_failed_step @DF-T3792 @DF-T3793 @DF-T3794 @DF-T3795 @DF-T3796 @DF-T3797 @DF-T3798 @DF-T3799 @DF-T3800 @DF-T3801 @DF-T851 @DF-T847 @DF-T845 @DF-T841
Scenario: To compare the relationship output with the expected output 
   Given  The run output and the expected output is read - normal - relationship - sds_ei__rel__finding_associated_with_host_non_srdm
   Then   validate if run output and expected output is the same
   And    generate the comparsion report

@compare_relationship_non_srdm_delta @runner.continue_after_failed_step @DF-T3802 @DF-T3803 @DF-T3804 @DF-T851 @DF-T847 @DF-T845 @DF-T841
Scenario: To compare the relationship output with the expected output 
   Given  The run output and the expected output is read - delta - relationship - sds_ei__rel__finding_associated_with_host_non_srdm_delta
   Then   validate if run output and expected output is the same
   And    generate the comparsion report

@compare_relationship_multi_loader @runner.continue_after_failed_step @DF-T3805 @DF-T3806 @DF-T3807 @DF-T3808 @DF-T851 @DF-T847 @DF-T845 @DF-T841
Scenario: To compare the relationship output with the expected output 
   Given  The run output and the expected output is read - normal - relationship - sds_ei__rel__ms_azure_ad_users__person_has_identity
   Then   validate if run output and expected output is the same
   And    generate the comparsion report

@compare_relationship_multi_loader_delta @runner.continue_after_failed_step @DF-T851 @DF-T847 @DF-T845 @DF-T841 @DF-T3805 @DF-T3806 @DF-T3807 @DF-T3808
Scenario: To compare the relationship output with the expected output 
   Given  The run output and the expected output is read - delta - relationship - sds_ei__rel__ms_azure_ad_users__person_has_identity_delta
   Then   validate if run output and expected output is the same
   And    generate the comparsion report

@compare_relationship_multi_tables @runner.continue_after_failed_step @DF-T3809 @DF-T3810 @DF-T3811 @DF-T3812 @DF-T851 @DF-T847 @DF-T845 @DF-T841
Scenario: To compare the relationship output with the expected output 
   Given  The run output and the expected output is read - normal - relationship - sds_ei__rel__ms_intunes__person_owns_host
   Then   validate if run output and expected output is the same
   And    generate the comparsion report

@compare_relationship_multi_tables_delta @runner.continue_after_failed_step @DF-T851 @DF-T847 @DF-T845 @DF-T841 @DF-T3813 @DF-T3814
Scenario: To compare the relationship output with the expected output 
   Given  The run output and the expected output is read - delta - relationship - sds_ei__rel__ms_intunes__person_owns_host_delta
   Then   validate if run output and expected output is the same
   And    generate the comparsion report
