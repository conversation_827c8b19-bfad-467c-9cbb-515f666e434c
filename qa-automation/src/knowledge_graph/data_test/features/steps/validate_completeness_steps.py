from behave import given, when, then
from pyspark.sql import functions as F
from core.common.logging_utils import setup_logger
from src.knowledge_graph.data_test.tests.dq_validation import DQValidation
from src.utilities.data_test.common import Common
import json
import random
import tempfile
import sys
import os
import shutil
from datetime import datetime


# Initialize logger
logger = setup_logger(__name__)


@given('I load main table {table} for the {entity} and compute completeness')
def step_load_main_and_compute_cq(context, entity, table):
    try:
        context.dq_validator = DQValidation(context.spark, context)
        #updated_at = "2024-08-07 23:59:59.999"
        updated_at = Common.UPDATED_AT
        logger.info(f"updated_at,{updated_at}")
        context.df = context.dq_validator.load_main_table(entity, table, updated_at)
        context.df = context.dq_validator.compute_cq_columns(context.df)
        logger.info(f"[SUCCESS] Loaded main table: {table} at {updated_at} and computed cq columns")
    except Exception as e:
        logger.error(f"[ERROR] Failed to load main table: {str(e)}")
        raise

@then('I load dq table {table} for the {entity} and join both tables')
def step_load_dq_and_join(context, table, entity):
    try:
        if not hasattr(context, 'dq_validator'):
            context.dq_validator = DQValidation(context.spark, context)
        #updated_at = "2024-08-07 23:59:59.999"
        updated_at = Common.UPDATED_AT
        context.dq_df = context.dq_validator.load_dq_table(entity,table, updated_at)
        context.joined_df = context.dq_validator.join_main_and_dq(context.df, context.dq_df)
        logger.info(f"[SUCCESS] Loaded dq completeness table for entity: {entity} and joined on p_id")
    except Exception as e:
        logger.error(f"[ERROR] Failed to load dq completeness table: {str(e)}")
        raise


@then('I verify no mismatches in p_id count and completeness score')
def step_validate_all(context):
    try:
        # Assert p_id counts match; if this fails, stop scenario
        context.dq_validator.validate_pid_counts(context.df, context.dq_df)
        logger.info("[STEP_PASS] Distinct p_id counts matched")

        # Evaluate completeness mismatches but do NOT assert here
        mismatches = context.joined_df.filter(
            F.col("dq_completeness_rounded") != F.col("dq_quality_score_rounded")
        )
        context.mismatch_count = mismatches.count()
        context.has_mismatches = context.mismatch_count > 0
        if context.has_mismatches:
            logger.info("[INFO] Found %s completeness mismatches; will generate report next.", context.mismatch_count)
        else:
            logger.info("[STEP_PASS] Completeness scores matched")
    except AssertionError as e:
        logger.error(f"[STEP_FAIL] {str(e)}")
        context.status = "Fail"
        raise
    except Exception as e:
        logger.error(f"[ERROR] Validation error: {str(e)}")
        context.status = "Fail"
        raise


@then('I report mismatched p_ids and field-level differences')
def step_report_mismatches(context):
    try:
        # Only execute if mismatches were detected in the previous step
        if not getattr(context, 'has_mismatches', False):
            logger.info("[REPORT] Skipping report; no mismatches detected.")
            # Mirror comparison step semantics: mark as Pass when no mismatches
            context.status = "Pass"
            return
        if not hasattr(context, 'dq_validator'):
            context.dq_validator = DQValidation(context.spark, context)

        mismatched_pids = context.dq_validator.get_mismatched_pids(context.joined_df)
        if not mismatched_pids:
            logger.info("[REPORT] No mismatched p_ids found.")
            return

        # Use a random mismatched p_id for detailed field-level diff
        selected_pid = random.choice(mismatched_pids)
        context.selected_mismatched_pid = selected_pid
        logger.info("[REPORT] Using single mismatched p_id for analysis: %s", selected_pid)

        cq_json_str = context.dq_validator.get_cq_json_for_pid(context.df, selected_pid)
        dq_json_str = context.dq_validator.get_dq_json_for_pid(context.dq_df, selected_pid)
        diffs = context.dq_validator.compare_cq_with_dq(cq_json_str, dq_json_str)

        logger.info("[REPORT] p_id=%s completeness diffs:", selected_pid)
        if not diffs:
            logger.info("  All fields match for this p_id (unexpected since overall score mismatched)")
        else:
            # Compact pretty print for logs
            logger.info("  %s", json.dumps(diffs, sort_keys=True))

        # Write a detailed comparison report (fields and scores) to a temp file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='_dq_report.txt') as f:
            temp_file = f.name
            original_stdout = sys.stdout
            sys.stdout = f
            try:
                print(f"DQ Completeness Mismatch Report for p_id: {selected_pid}")
                print("")
                print("field\tdf_score\tdq_score")
                for field, vals in sorted(diffs.items()):
                    print(f"{field}\t{vals.get('df_value')}\t{vals.get('dq_value')}")
            finally:
                sys.stdout = original_stdout
        context.dq_report_file = temp_file
        logger.info("[REPORT] DQ mismatch report written to: %s", context.dq_report_file)
        # Mirror comparison step semantics: mark as Fail when mismatches exist
        context.status = "Fail"
    except Exception as e:
        logger.error(f"[ERROR] Failed to generate mismatch report: {str(e)}")
        raise

    # Fail the scenario after reporting, since mismatches exist
    raise AssertionError(f"Found {context.mismatch_count} mismatches in completeness scores.")


def copytooutputfolder(context):
    """Delegate to existing output comparison report generator for consistency."""
    try:
        context.output_output_comarison.generate_report(context.report_file, context)
    except Exception as e:
        logger.error("[REPORT] Failed to generate comparison report via generator: %s", str(e))
