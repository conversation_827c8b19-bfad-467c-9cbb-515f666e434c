import os
from pyspark.sql import SparkSession
from datetime import datetime, timezone
from core.common.common_utils import CommonUtils
from core.common.logging_utils import setup_logger
from core.common.constants_utils import MyConstantsUtils
import configparser

config_path = MyConstantsUtils.CONFIG_PATH
logger = setup_logger(__name__)
common = CommonUtils()

class Common:
    UPDATED_AT = (int(os.environ["UPDATED_AT"]) * 1000) + 999
    def __init__(self):
        self.config = configparser.ConfigParser()
        self.config.read(config_path)
        local_mode = False if self.config['credentials']['qa_local_mode'] in ('False', False) else True
        if not local_mode:
            self.environment = common.get_config_value('data_setup', 'environment', True)
            logger.info(f'Environment is {self.environment}')
            self.NAMESPACE = os.environ["NAMESPACE"]
            
            #Common configurations
            self.configs = {
                "NAMESPACE": os.environ["NAMESPACE"],
                "EXECUTOR_MEMORY": os.environ["EXECUTOR_MEMORY"],
                "DRIVER_MEMORY": os.environ["DRIVER_MEMORY"],
                "EXECUTOR_INSTANCES": str(os.environ["EXECUTOR_INSTANCES"]).strip("n"),
                "IMAGE": os.environ["IMAGE"],
                "HOSTNAME": os.environ["HOSTNAME"],
                "POD_IP": os.environ["POD_IP"],
                "SERVICE_ACCOUNT": os.environ["SERVICE_ACCOUNT"],
                "APP_NAME": os.environ["APP_NAME"],
                "ICEBERG_CATALOG_TYPE": os.environ["ICEBERG_CATALOG_TYPE"],
                "ICEBERG_CATALOG_URI": os.environ["ICEBERG_CATALOG_URI"],
                "ICEBERG_WAREHOUSE": os.environ["ICEBERG_WAREHOUSE"]
            }
            #AWS configs
            if self.environment == 'AWS':
                self.configs.update({
                    "HADOOP_FS_IMPL": os.environ["HADOOP_FS_IMPL"],
                    "HADOOP_FS_END_POINT": os.environ["HADOOP_FS_END_POINT"]
                })
                #Azure Configs
            elif self.environment == 'Azure':
                self.configs.update({
                    "TENANT_ID": os.environ["TENANT_ID"],
                    "AZURE_MANAGED_IDENTITY_ID": os.environ["AZURE_MANAGED_IDENTITY_ID"]
                })

    def create_spark_session(self, executor_number=None):
        executor_number = executor_number if executor_number else self.configs["EXECUTOR_INSTANCES"]
        logger.info('Creating Spark session')

        spark_builder = (
            SparkSession.builder.appName(self.configs["APP_NAME"])
            .enableHiveSupport()
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
            .config("spark.kubernetes.namespace", self.configs["NAMESPACE"])
            .config("spark.master", "k8s://https://kubernetes.default.svc.cluster.local:443")
            .config("spark.kubernetes.authenticate.serviceAccountName", self.configs["SERVICE_ACCOUNT"])
            .config("spark.kubernetes.container.image", self.configs["IMAGE"])
            .config("spark.kubernetes.container.image.pullSecrets", "docker-secret")

            # Enable Iceberg extensions
            .config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")

            # External catalog for Iceberg tables
            .config("spark.sql.catalog.iceberg_catalog", "org.apache.iceberg.spark.SparkCatalog")
            .config("spark.sql.catalog.iceberg_catalog.type", "hive")
            .config("spark.sql.catalog.iceberg_catalog.uri", f"thrift://{self.configs['ICEBERG_CATALOG_URI']}")
            .config("spark.sql.catalog.iceberg_catalog.warehouse", self.configs["ICEBERG_WAREHOUSE"])

            # Support for Iceberg views via spark_catalog
            .config("spark.sql.catalog.spark_catalog", "org.apache.iceberg.spark.SparkSessionCatalog")
            .config("spark.sql.catalog.spark_catalog.type", "hive")
            .config("spark.sql.catalog.spark_catalog.uri", f"thrift://{self.configs['ICEBERG_CATALOG_URI']}")
            .config("spark.sql.catalog.spark_catalog.warehouse", self.configs["ICEBERG_WAREHOUSE"])

            # Common Hive metastore config
            .config("spark.hadoop.hive.metastore.uris", f"thrift://{self.configs['ICEBERG_CATALOG_URI']}")

            # Driver and executor config
            .config("spark.driver.port", "22321")
            .config("spark.submit.deployMode", "client")
            .config("spark.blockManager.port", "22322")
            .config("spark.driver.host", self.configs["POD_IP"])
            .config("spark.kubernetes.executor.limit.cores", "4")
            .config("spark.executor.cores", "1")
            .config("spark.executor.memory", self.configs["EXECUTOR_MEMORY"])
            .config("spark.kubernetes.executor.limit.memory", "32g")
            .config("spark.driver.memory", self.configs["DRIVER_MEMORY"])
            .config("spark.executor.instances", executor_number)
        )


        # Apply AWS-specific configurations
        if self.environment == 'AWS':
            spark_builder = (
                spark_builder
                .config("spark.hadoop.fs.s3a.impl", self.configs["HADOOP_FS_IMPL"])
                .config("spark.hadoop.fs.s3a.endpoint", self.configs["HADOOP_FS_END_POINT"])
            )
        
        # Apply Azure-specific configurations
        elif self.environment == 'Azure':
            spark_builder = (
                spark_builder
                .config("spark.hadoop.fs.azure.account.auth.type", "OAuth")
                .config("spark.hadoop.fs.azure.account.oauth.provider.type", "org.apache.hadoop.fs.azurebfs.oauth2.WorkloadIdentityTokenProvider")
            )
        
        spark = spark_builder.getOrCreate()

        for key, value in spark.sparkContext.getConf().getAll():
            logger.info(f"{key} = {value}")

        logger.info('************************************************')
        logger.info('azure.account.auth.type')
        logger.info(spark.conf.get("spark.hadoop.fs.azure.account.auth.type", "Not Set"))   
        logger.info('account.auth.type')
        logger.info(spark.conf.get("fs.azure.account.auth.type", "Not Set"))
        logger.info('oauth.provider.type')
        logger.info(spark.conf.get("fs.azure.account.oauth.provider.type", "Not Set"))
        logger.info('************************************************')


        logger.info("Spark Session Creation Successful")
        return spark
    
    def stop_spark_session(self, spark):
        logger.info('Stopped Spark session')
        spark.stop()