FROM prevalentai/spark:4-1-0-R-3.5.5-2.13-python-v3-12-bookworm-12.10-20250428-slim
ARG QA_DIR=/opt/qa

USER root
ARG ARTIFACTORY_USER=$ARTIFACTORY_USER
ARG ARTIFACTORY_PASSWORD=$ARTIFACTORY_PASSWORD
RUN chmod +x /opt/entrypoint.sh
RUN wget -O /tmp/awscliv2.zip "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" && \
    python -c 'import zipfile; zipfile.ZipFile("/tmp/awscliv2.zip", "r").extractall("/tmp/")'  && \
    chown -R root:root /tmp/aws && chmod +x /tmp/aws/install && \
    mkdir -p /tmp/aws-cli/ && \
    cd /tmp && ./aws/install --bin-dir /tmp/aws-cli/bin --install-dir /tmp/aws-cli/aws-cli --update && \
    chmod +x /tmp/aws-cli/bin/aws  && \
    wget https://aka.ms/downloadazcopy-v10-linux && \
    tar -xvf downloadazcopy-v10-linux && \
    cd azcopy_linux_amd64_*/ && \
    mv azcopy /usr/local/bin/ && \
    chmod +x /usr/local/bin/azcopy && \
    rm -rf azcopy_linux_amd64_*/ downloadazcopy-v10-linux && \
    wget -qO- https://aka.ms/InstallAzureCLIDeb | bash 
WORKDIR ${QA_DIR}

COPY requirements_spark.txt ${QA_DIR}/requirements_spark.txt 
COPY src ${QA_DIR}/src
ENV VIRTUAL_ENV=${QA_DIR}/.venv
ENV PATH="$VIRTUAL_ENV/bin:/tmp/aws-cli/bin:$PATH"
# RUN cd ${QA_DIR}/ && pip install --no-compile -r requirements_spark.txt
ENV UV_INDEX_STRATEGY=unsafe-best-match
RUN pip install uv && uv venv --seed && \
    # Explicitly upgrade setuptools for Python 3.12+ compatibility
    uv pip install --upgrade setuptools && \
    mkdir /opt/qa/.venv/lib/python3.12/site-packages/pyspark && \
    cp -r /opt/spark/python/pyspark /opt/qa/.venv/lib/python3.12/site-packages && \
    uv pip compile --extra-index-url "https://$ARTIFACTORY_USER:$<EMAIL>/artifactory/api/pypi/pai-pypi-local-dev/simple" requirements_spark.txt -o /tmp/requirements_uv.txt && \
    uv pip install --no-cache-dir --extra-index-url "https://$ARTIFACTORY_USER:$<EMAIL>/artifactory/api/pypi/pai-pypi-local-dev/simple" -r /tmp/requirements_uv.txt && \
    chown -R spark:root ${QA_DIR}

USER spark
