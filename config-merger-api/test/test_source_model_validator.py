import pytest
from config_merger_api.utils.source_model_validator import (
    extract_table_name_from_path,
    get_available_inventory_models,
    get_available_intrasource_models,
    validate_inventory_model_input,
    clean_disambiguation_references
)
from config_merger_api.utils.disambiguation_utils import apply_source_model_validation

def test_extract_table_name_from_path():
    # Test normal case
    path = "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid"
    result = extract_table_name_from_path(path)
    assert result == "sds_ei__host__active_directory__object_guid"
    
    # Test without schema prefix
    path = "sds_ei__host__ms_defender"
    result = extract_table_name_from_path(path)
    assert result == "sds_ei__host__ms_defender"
    
    # Test empty/None
    assert extract_table_name_from_path("") is None
    assert extract_table_name_from_path(None) is None

def test_get_available_inventory_models():
    source_models = {
        "inventory_models": [
            "sds_ei__host__active_directory__object_guid",
            "sds_ei__host__ms_defender",
            "sds_ei__host__qualys"
        ]
    }
    
    result = get_available_inventory_models(source_models)
    expected = {
        "sds_ei__host__active_directory__object_guid",
        "sds_ei__host__ms_defender",
        "sds_ei__host__qualys"
    }
    assert result == expected

def test_get_available_intrasource_models():
    source_models = {
        "intrasource_disambiguated_models": [
            "sds_ei__host__ms_azure_ad",
            "sds_ei__host__ms_defender",
            "sds_ei__host__qualys"
        ]
    }
    
    result = get_available_intrasource_models(source_models)
    expected = {
        "sds_ei__host__ms_azure_ad",
        "sds_ei__host__ms_defender", 
        "sds_ei__host__qualys"
    }
    assert result == expected

def test_validate_inventory_model_input():
    inventory_model_input = [
        {
            "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid",
            "name": "sds_ei__host__active_directory__object_guid"
        },
        {
            "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__nonexistent",
            "name": "sds_ei__host__nonexistent"
        },
        {
            "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender",
            "name": "sds_ei__host__ms_defender"
        }
    ]
    
    source_models = {
        "inventory_models": [
            "sds_ei__host__active_directory__object_guid",
            "sds_ei__host__ms_defender"
        ]
    }
    
    result, valid_model_names = validate_inventory_model_input(inventory_model_input, source_models, include_intrasource=False)
    
    # Should keep only the valid inventory objects
    assert len(result) == 2
    valid_names = [item["name"] for item in result]
    assert "sds_ei__host__active_directory__object_guid" in valid_names
    assert "sds_ei__host__ms_defender" in valid_names
    assert "sds_ei__host__nonexistent" not in valid_names
    
    # Should return valid name field values (for confidence matrix validation)
    assert "sds_ei__host__active_directory__object_guid" in valid_model_names
    assert "sds_ei__host__ms_defender" in valid_model_names
    assert len(valid_model_names) == 2

def test_validate_inventory_model_input():
    inventory_model_input = [
        {
            "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid",
            "name": "sds_ei__host__active_directory__object_guid"
        },
        {
            "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad",
            "name": "sds_ei__host__ms_azure_ad"
        },
        {
            "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__nonexistent",
            "name": "sds_ei__host__nonexistent"
        }
    ]
    
    source_models = {
        "inventory_models": [
            "sds_ei__host__active_directory__object_guid"
        ],
        "intrasource_disambiguated_models": [
            "sds_ei__host__ms_azure_ad"
        ]
    }
    
    result, valid_model_names = validate_inventory_model_input(inventory_model_input, source_models, include_intrasource=True)
    
    # Should keep only the valid inventory objects from both inventory and intrasource
    assert len(result) == 2
    valid_names = [item["name"] for item in result]
    assert "sds_ei__host__active_directory__object_guid" in valid_names
    assert "sds_ei__host__ms_azure_ad" in valid_names
    assert "sds_ei__host__nonexistent" not in valid_names
    
    # Should return valid name field values (for confidence matrix validation)
    assert "sds_ei__host__active_directory__object_guid" in valid_model_names
    assert "sds_ei__host__ms_azure_ad" in valid_model_names
    assert len(valid_model_names) == 2

def test_validate_empty_inputs():
    # Test with empty inputs
    assert validate_inventory_model_input([], {}, include_intrasource=False) == ([], set())
    assert validate_inventory_model_input([], {}, include_intrasource=True) == ([], set())
    assert validate_inventory_model_input(None, None, include_intrasource=False) == ([], set())
    assert validate_inventory_model_input(None, None, include_intrasource=True) == ([], set())

def test_validate_exact_match_only():
    inventory_model_input = [
        {
            "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid",
            "name": "sds_ei__host__active_directory__object_guid"
        },
        {
            "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__partial_name",
            "name": "partial_name"  # This should NOT match
        }
    ]
    
    source_models = {
        "inventory_models": [
            "sds_ei__host__active_directory__object_guid"
        ]
    }
    
    result, valid_model_names = validate_inventory_model_input(inventory_model_input, source_models, include_intrasource=False)
    
    # Should only match exact table names from paths
    assert len(result) == 1
    assert result[0]["name"] == "sds_ei__host__active_directory__object_guid"
    
    # Should return only the valid name field value
    assert valid_model_names == {"sds_ei__host__active_directory__object_guid"}

def test_clean_disambiguation_references():
    """Test that clean_disambiguation_references only keeps models that are in valid_name_fields"""
    config = {
        "inventoryModelInput": [
            {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__valid_model", "name": "sds_ei__host__valid_model"},
            {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__another_valid", "name": "sds_ei__host__another_valid"}
        ],
        "disambiguation": {
            "confidenceMatrix": [
                "sds_ei__host__valid_model",        # Should be kept
                "sds_ei__host__another_valid",      # Should be kept  
                "sds_ei__host__not_in_inventory",   # Should be removed
                "sds_ei__host__also_not_valid"      # Should be removed
            ],
            "strategy": {
                "fieldLevelConfidenceMatrix": [
                    {
                        "field": "os",
                        "confidenceMatrix": [
                            "sds_ei__host__valid_model",        # Should be kept
                            "sds_ei__host__not_in_inventory"    # Should be removed
                        ]
                    },
                    {
                        "field": "type",
                        "confidenceMatrix": [
                            "sds_ei__host__another_valid",      # Should be kept
                            "sds_ei__host__also_not_valid"      # Should be removed
                        ]
                    }
                ]
            }
        }
    }
    
    # Only these name field values should be kept (based on validated inventoryModelInput)
    valid_name_fields = {"sds_ei__host__valid_model", "sds_ei__host__another_valid"}
    
    result = clean_disambiguation_references(config, valid_name_fields)
    
    # Check that only valid models remain in confidenceMatrix
    expected_confidence_matrix = ["sds_ei__host__valid_model", "sds_ei__host__another_valid"]
    assert set(result["disambiguation"]["confidenceMatrix"]) == set(expected_confidence_matrix)
    assert "sds_ei__host__not_in_inventory" not in result["disambiguation"]["confidenceMatrix"]
    assert "sds_ei__host__also_not_valid" not in result["disambiguation"]["confidenceMatrix"]
    
    # Check that only valid models remain in fieldLevelConfidenceMatrix
    os_field = next(field for field in result["disambiguation"]["strategy"]["fieldLevelConfidenceMatrix"] 
                   if field["field"] == "os")
    assert os_field["confidenceMatrix"] == ["sds_ei__host__valid_model"]
    assert "sds_ei__host__not_in_inventory" not in os_field["confidenceMatrix"]
    
    type_field = next(field for field in result["disambiguation"]["strategy"]["fieldLevelConfidenceMatrix"] 
                     if field["field"] == "type")
    assert type_field["confidenceMatrix"] == ["sds_ei__host__another_valid"]
    assert "sds_ei__host__also_not_valid" not in type_field["confidenceMatrix"]

def test_clean_disambiguation_references_real_scenario():
    """Test with the exact scenario from the bug report"""
    config = {
        "inventoryModelInput": [
            {
                "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_devices__device_id",
                "name": "ms_azure_ad_devices__device_id"
            },
            {
                "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_abcd",
                "name": "sds_ei__host__aws"
            },
            {
                "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__wiz_abcd",
                "name": "sds_ei__host__wiz"
            }
        ],
        "disambiguation": {
            "confidenceMatrix": [
                "ms_azure_ad_devices__device_id",      # Should match name field
                "sds_ei__host__aws",                    # Should match name field  
                "bbbb"                                  # Should be removed
            ],
            "strategy": {
                "fieldLevelConfidenceMatrix": [
                    {
                        "field": "type",
                        "confidenceMatrix": [
                            "sds_ei__host__aws",                # Should match name field
                            "sds_ei__host__ms_intunes",         # Should be removed
                            "ms_azure_ad_devices__device_id",   # Should match name field
                            "sds_ei__host__ms_defender",        # Should be removed
                            "sds_ei__host__crowdstrike"         # Should be removed
                        ]
                    }
                ]
            }
        }
    }
    
    # Valid name field values (not table names)
    valid_name_fields = {
        "ms_azure_ad_devices__device_id",  # name field from first item
        "sds_ei__host__aws",               # name field from second item
        "sds_ei__host__wiz"                # name field from third item
    }
    
    result = clean_disambiguation_references(config, valid_name_fields)
    
    # The confidenceMatrix should only contain matching name fields (order preserved)
    expected_confidence = ["ms_azure_ad_devices__device_id", "sds_ei__host__aws"]
    assert result["disambiguation"]["confidenceMatrix"] == expected_confidence
    
    # The fieldLevelConfidenceMatrix for "type" should only contain matching name fields (order preserved)
    type_field = next(field for field in result["disambiguation"]["strategy"]["fieldLevelConfidenceMatrix"] 
                     if field["field"] == "type")
    expected_type = ["sds_ei__host__aws", "ms_azure_ad_devices__device_id"]
    assert type_field["confidenceMatrix"] == expected_type

def test_clean_disambiguation_references_order_preservation():
    """Test that the order is preserved when removing invalid models"""
    config = {
        "disambiguation": {
            "confidenceMatrix": [
                "model_a",      # Valid - should be kept
                "model_b",      # Invalid - should be removed  
                "model_c",      # Valid - should be kept
                "model_d",      # Invalid - should be removed
                "model_e"       # Valid - should be kept
            ],
            "strategy": {
                "fieldLevelConfidenceMatrix": [
                    {
                        "field": "test_field",
                        "confidenceMatrix": [
                            "model_x",      # Invalid - should be removed
                            "model_a",      # Valid - should be kept
                            "model_y",      # Invalid - should be removed
                            "model_c"       # Valid - should be kept
                        ]
                    }
                ]
            }
        }
    }
    
    # Only these models are valid
    valid_name_fields = {"model_a", "model_c", "model_e"}
    
    result = clean_disambiguation_references(config, valid_name_fields)
    
    # Check that order is preserved in confidenceMatrix
    # Original: [a, b, c, d, e] -> Expected: [a, c, e] (b and d removed, order preserved)
    assert result["disambiguation"]["confidenceMatrix"] == ["model_a", "model_c", "model_e"]
    
    # Check that order is preserved in fieldLevelConfidenceMatrix
    # Original: [x, a, y, c] -> Expected: [a, c] (x and y removed, order preserved)
    test_field = result["disambiguation"]["strategy"]["fieldLevelConfidenceMatrix"][0]
    assert test_field["confidenceMatrix"] == ["model_a", "model_c"]

def test_apply_source_model_validation_intra():
    """Test the shared validation function for intra-source disambiguation"""
    config = {
        "inventoryModelInput": [
            {
                "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__valid_model",
                "name": "valid_model"
            },
            {
                "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__invalid_model",
                "name": "invalid_model"
            }
        ],
        "disambiguation": {
            "confidenceMatrix": [
                "valid_model",
                "invalid_model",
                "another_invalid"
            ]
        }
    }
    
    source_models = {
        "inventory_models": ["sds_ei__host__valid_model"]
    }
    
    result = apply_source_model_validation(config, source_models, 'intra')
    
    # Should keep only valid inventory input
    assert len(result["inventoryModelInput"]) == 1
    assert result["inventoryModelInput"][0]["name"] == "valid_model"
    
    # Should clean confidence matrix
    assert result["disambiguation"]["confidenceMatrix"] == ["valid_model"]

def test_apply_source_model_validation_inter():
    """Test the shared validation function for inter-source disambiguation"""
    config = {
        "inventoryModelInput": [
            {
                "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__inventory_model",
                "name": "inventory_model"
            },
            {
                "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__intrasource_model",
                "name": "intrasource_model"
            },
            {
                "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__invalid_model",
                "name": "invalid_model"
            }
        ],
        "disambiguation": {
            "confidenceMatrix": [
                "inventory_model",
                "intrasource_model",
                "invalid_model"
            ]
        }
    }
    
    source_models = {
        "inventory_models": ["sds_ei__host__inventory_model"],
        "intrasource_disambiguated_models": ["sds_ei__host__intrasource_model"]
    }
    
    result = apply_source_model_validation(config, source_models, 'inter')
    
    # Should keep both valid inventory inputs
    assert len(result["inventoryModelInput"]) == 2
    names = [item["name"] for item in result["inventoryModelInput"]]
    assert "inventory_model" in names
    assert "intrasource_model" in names
    
    # Should clean confidence matrix
    expected = ["inventory_model", "intrasource_model"]
    assert result["disambiguation"]["confidenceMatrix"] == expected

def test_apply_source_model_validation_no_inventory_input():
    """Test when there's no inventoryModelInput"""
    config = {
        "disambiguation": {
            "confidenceMatrix": ["some_model"]
        }
    }
    
    source_models = {"inventory_models": ["some_model"]}
    
    result = apply_source_model_validation(config, source_models, 'intra')
    
    # Should return unchanged config
    assert result == config

def test_apply_source_model_validation_no_source_models():
    """Test when source_models is None"""
    config = {
        "inventoryModelInput": [
            {"path": "<%EI_SCHEMA_NAME%>.some_model", "name": "some_model"}
        ]
    }
    
    result = apply_source_model_validation(config, None, 'intra')
    
    # Should return unchanged config
    assert result == config

def test_apply_source_model_validation_invalid_type():
    """Test with invalid disambiguation type"""
    config = {
        "inventoryModelInput": [
            {"path": "<%EI_SCHEMA_NAME%>.some_model", "name": "some_model"}
        ]
    }
    
    source_models = {"inventory_models": ["some_model"]}
    
    result = apply_source_model_validation(config, source_models, 'invalid_type')
    
    # Should return unchanged config
    assert result == config

def test_apply_source_model_validation_with_field_level_confidence():
    """Test the shared function also cleans fieldLevelConfidenceMatrix"""
    config = {
        "inventoryModelInput": [
            {
                "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__valid_model",
                "name": "valid_model"
            }
        ],
        "disambiguation": {
            "confidenceMatrix": ["valid_model", "invalid_model"],
            "strategy": {
                "fieldLevelConfidenceMatrix": [
                    {
                        "field": "type",
                        "confidenceMatrix": [
                            "valid_model",
                            "invalid_model",
                            "another_invalid"
                        ]
                    }
                ]
            }
        }
    }
    
    source_models = {
        "inventory_models": ["sds_ei__host__valid_model"]
    }
    
    result = apply_source_model_validation(config, source_models, 'intra')
    
    # Should clean both confidence matrices
    assert result["disambiguation"]["confidenceMatrix"] == ["valid_model"]
    
    type_field = result["disambiguation"]["strategy"]["fieldLevelConfidenceMatrix"][0]
    assert type_field["confidenceMatrix"] == ["valid_model"]
