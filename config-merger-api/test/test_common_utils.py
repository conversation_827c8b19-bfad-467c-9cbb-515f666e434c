import pytest
from config_merger_api.utils import common_utils as cu

def test_merge_colExpr_override():
    child = {"colExpr": "b", "fieldsSpec": {"merge_strategy": "OVERRIDE"}}
    parent = {"colExpr": "a"}
    assert cu.merge_colExpr(child, parent) == "b"

def test_merge_colExpr_client_first():
    child = {"colExpr": "b", "fieldsSpec": {"merge_strategy": "CLIENT_FIRST"}}
    parent = {"colExpr": "a"}
    assert "COALESCE(b, a)" in cu.merge_colExpr(child, parent)

def test_merge_colExpr_product_first():
    child = {"colExpr": "b", "fieldsSpec": {"merge_strategy": "PRODUCT_FIRST"}}
    parent = {"colExpr": "a"}
    assert "COALESCE(a, b)" in cu.merge_colExpr(child, parent)

def test_merge_lists_prioritize_client():
    parent = [{"colName": "a"}]
    child = [{"colName": "b"}]
    result = cu.merge_lists(parent, child, "colName", prioritize_client_order=True)
    assert any(i["colName"] == "b" for i in result)

def test_merge_dictionaries_basic():
    parent = {"a": 1, "b": {"c": 2}}
    child = {"b": {"d": 3}, "e": 4}
    result = cu.merge_dictionaries(parent, child)
    assert result["b"]["d"] == 3 and result["a"] == 1 and result["e"] == 4

def test_merge_config_merges():
    sol = [{"config_item_type": "foo", "name": "bar", "x": 1}]
    client = [{"config_item_type": "foo", "name": "bar", "y": 2}]
    result = cu.merge_config(sol, client)
    assert any("y" in i for i in result) 