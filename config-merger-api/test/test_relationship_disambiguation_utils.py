import pytest
from config_merger_api.utils.relationship_disambiguation_utils import merge_relationship_disambiguation

def test_merge_relationship_disambiguation_both():
    sol = [{"config_item_type": "relationship_disambiguation", "config_value": {"disambiguation": {"disambiguationGrouping": {"type": "NotUnion"}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__foo"}}}]
    client = [{"config_item_type": "relationship_disambiguation", "config_value": {"disambiguation": {"disambiguationGrouping": {"type": "NotUnion"}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__foo"}}}]
    result = merge_relationship_disambiguation(sol, client)
    assert "output" in result
    assert "resolverLocation" in result["output"]
    # fragmentLocation is only added when isFragmentOLAPTable is True, but it's set to False

def test_merge_relationship_disambiguation_solution_only():
    sol = [{"config_item_type": "relationship_disambiguation", "config_value": {"disambiguation": {"disambiguationGrouping": {"type": "NotUnion"}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__foo"}}}]
    client = []
    result = merge_relationship_disambiguation(sol, client)
    assert "output" in result

def test_merge_relationship_disambiguation_client_only():
    sol = []
    client = [{"config_item_type": "relationship_disambiguation", "config_value": {"disambiguation": {"disambiguationGrouping": {"type": "NotUnion"}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__foo"}}}]
    result = merge_relationship_disambiguation(sol, client)
    assert "output" in result 