{"solution_configs": [{"id": 51022, "name": "sds_ei__person", "config_item_type": "intersource_disambiguated_models", "config_item_level": "solution", "config_deploy_type": "spark_job_configs", "version": "2-1-0-********-**********-KG", "updated_at": "2025-08-21T06:11:16.214726Z", "updated_by": "service-account-sds-confidential-client", "created_at": "2025-08-21T05:18:33.319923Z", "created_by": "service-account-sds-confidential-client", "is_available": true, "is_deleted": false, "dashboard_identifier": null, "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__successfactors_hr__employee_id", "name": "sf"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__saviynt_iga", "name": "iga"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__active_directory__object_guid", "name": "ad"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__snow_itsm__sf_id_employee_id", "name": "snow"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_intunes__user_id", "name": "mdm"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad", "name": "msazure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__aws_identitystore_listusers__user_id", "name": "aws_identitystore"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__bamboohr__employee_number", "name": "bamboohr"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__aws__iam_list_users__user_name", "name": "aws__iam_list_users"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name", "name": "sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name"}], "disambiguation": {"candidateKeys": ["employee_id", "aad_user_id", "email_id", "aws_iam_user_name"], "confidenceMatrix": ["sf", "bamboohr", "iga", "ad", "snow", "mdm", "msazure", "aws_identitystore", "aws__iam_list_users", "sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "manager", "confidenceMatrix": ["sf", "bamboohr", "iga", "mdm", "snow", "ad"]}], "rollingUpFields": ["origin", "external_email_id"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "aggregation": [{"field": "first_seen_date", "function": "min"}, {"field": "last_active_date", "function": "max"}, {"field": "recruit_date", "function": "min"}, {"field": "last_known_termination_date", "function": "max"}, {"field": "contract_end_date", "function": "max"}, {"field": "login_last_date", "function": "max"}, {"field": "ad_last_password_change_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "ad_created_date", "function": "min"}, {"field": "termination_date", "function": "max"}]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__person", "filter": "display_label NOT RLIKE '[0-9@]'"}, "entity": {"name": "Person"}}, "solution_name": "ei"}, {"id": 50781, "name": "sds_ei__person__ms_azure_ad", "config_item_type": "intrasource_disambiguated_models", "config_item_level": "solution", "config_deploy_type": "spark_job_configs", "version": "2-1-0-********-**********-KG", "updated_at": "2025-08-21T06:10:59.288525Z", "updated_by": "service-account-sds-confidential-client", "created_at": "2025-08-21T05:18:24.340894Z", "created_by": "service-account-sds-confidential-client", "is_available": true, "is_deleted": false, "dashboard_identifier": null, "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad_registered_users__aad_user_id", "name": "sds_ei__person__ms_azure_ad_registered_users__aad_user_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad_users__aad_id", "name": "sds_ei__person__ms_azure_ad_users__aad_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad_sign_in__user_id", "name": "sds_ei__person__ms_azure_ad_sign_in__user_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__person__ms_azure_ad_users__aad_id", "sds_ei__person__ms_azure_ad_registered_users__aad_user_id", "sds_ei__person__ms_azure_ad_sign_in__user_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "aggregation": [{"field": "first_seen_date", "function": "min"}, {"field": "last_active_date", "function": "max"}], "rollingUpFields": ["external_email_id"]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Azure AD'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "MS Azure AD"}, "entity": {"name": "Person"}}, "solution_name": "ei"}, {"id": 51013, "name": "person_entity_config", "config_item_type": "global_entity_config", "config_item_level": "solution", "config_deploy_type": "spark_job_configs", "version": "2-1-0-********-**********-KG", "updated_at": "2025-08-21T06:10:57.284460Z", "updated_by": "service-account-sds-confidential-client", "created_at": "2025-08-21T05:18:32.946893Z", "created_by": "service-account-sds-confidential-client", "is_available": true, "is_deleted": false, "dashboard_identifier": null, "config_value": {"entityClass": "Person", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(full_name, primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "inactivity_period", "colExpr": "180", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period  THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "middle_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "email_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "manager", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "manager_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "employee_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "company", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "external_email_id", "colExpr": "from_json(null, 'ARRAY<STRING>')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "recruit_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_known_termination_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "contract_end_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "job_title", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "job_position_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "legal_entity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "organisation_unit_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cost_center", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "job_function", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "phone_number", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "address", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "employee_status", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "termination_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "lastUpdateFields": ["type", "business_unit", "department", "description", "location_country", "login_last_date", "ad_last_sync_date", "manager", "ad_last_password_change_date", "full_name", "email_id", "manager_id", "employee_id", "company", "external_email_id", "recruit_date", "contract_end_date", "job_title", "job_position_id", "legal_entity", "organisation_unit_id", "cost_center", "job_function", "phone_number", "address", "employee_status", "termination_date", "aad_user_id", "aad_operational_status", "ad_operational_status", "account_enabled_status"], "entity": {"name": "Person"}}, "solution_name": "ei"}], "client_configs": [{"id": 51363, "name": "sds_ei__person", "config_item_type": "intersource_disambiguated_models", "config_item_level": "client", "config_deploy_type": "spark_job_configs", "version": "2-1-0-********-**********-KG", "updated_at": "2025-08-21T06:11:16.233707Z", "updated_by": "service-account-sds-confidential-client", "created_at": "2025-08-21T06:10:44.396221Z", "created_by": "service-account-sds-confidential-client", "is_available": true, "is_deleted": false, "dashboard_identifier": null, "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__lookup_project__aad_user_id", "name": "person_project_lookup"}], "disambiguation": {"candidateKeys": ["employee_id", "aad_user_id", {"name": "email_id", "exceptionFilter": "email_id IN ('<EMAIL>' , '<EMAIL>', 'no_hr')"}], "confidenceMatrix": ["sf", "bamboohr", "iga", "ad", "snow", "mdm", "msazure", "aws_identitystore", "aws__iam_list_users", "sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name", "person_project_lookup"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "manager", "confidenceMatrix": ["sf", "bamboohr", "iga", "ad", "snow", "mdm", "msazure", "aws_identitystore", "aws__iam_list_users", "sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name", "person_project_lookup"]}, {"field": "termination_date", "confidenceMatrix": ["sf"], "persistNonNullValue": false, "restrictToConfidenceMatrix": true}], "rollingUpFields": ["origin", "external_email_id", "data_source_dev"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__person", "filter": "display_label NOT RLIKE '[0-9@]' AND UPPER(display_label) not in ('ADMINISTRATOR','CONTACT','INFO','PAI-SERV PLATFORM','PAI-SERV PAYSAFE','TEST','TEST DEVOPS','TEST USER','TESTUSERQA','ACCOUNTING SUPPORT','DAST','JIRA INTEGRATION','L&D1','OPTUS SUPPORT','PAYSAFE MONITORING','SAJITH TEST','SCALEIOS ADMIN','SYSTEMS MONITORING','TEST INFOSEC')"}}, "solution_name": "ei"}, {"id": 51356, "name": "person_entity_config", "config_item_type": "global_entity_config", "config_item_level": "client", "config_deploy_type": "spark_job_configs", "version": "2-1-0-********-**********-KG", "updated_at": "2025-08-21T06:10:57.309987Z", "updated_by": "service-account-sds-confidential-client", "created_at": "2025-08-21T06:10:44.215445Z", "created_by": "service-account-sds-confidential-client", "is_available": true, "is_deleted": false, "dashboard_identifier": null, "config_value": {"commonProperties": [{"colName": "inactivity_period", "colExpr": "30", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "data_source_dev", "colExpr": "pai_source"}]}, "solution_name": "ei"}], "deployment_config": [{"id": 51377, "name": "deployment_config", "config_item_type": "deployment_config", "config_item_level": "client", "config_deploy_type": "general_configs", "version": "2-1-0-********-**********-KG", "updated_at": "2025-09-22T06:17:23.775657Z", "updated_by": "", "created_at": "2025-08-21T06:10:45.393660Z", "created_by": "service-account-sds-confidential-client", "is_available": true, "is_deleted": false, "dashboard_identifier": null, "config_value": {"spark_job_configs": {"source_models": {"relationship_data_dictionary_config": ["application_running_on_host", "bucket_belongs_to_storage_account", "cloud_compute_resource_belongs_to_cloud_account", "cloud_container_resource_belongs_to_cloud_account", "cloud_storage_resource_belongs_to_cloud_account", "compute_instance_group_belongs_to_kubernetes_cluster", "compute_instance_group_belongs_to_mapreduce_cluster", "container_belongs_to_container_group", "container_belongs_to_container_service", "file_system_service_belongs_to_storage_account", "host_has_identity", "host_belongs_to_cloud_account", "identity_has_account", "person_has_identity", "person_owns_host", "queue_service_belongs_to_storage_account", "table_service_belongs_to_storage_account", "virtual_machine_belongs_to_compute_instance_group", "volume_associates_to_virtual_machine", "vulnerability_finding_on_application", "vulnerability_finding_on_host", "vulnerability_finding_on_cloud_container"], "olap_configs": ["sds_ei__olap__global"], "global_entity_config": ["account_entity_config", "application_entity_config", "cloud_account_entity_config", "cloud_compute_entity_config", "cloud_container_entity_config", "cloud_storage_entity_config", "finding_entity_config", "host_entity_config", "identity_entity_config", "person_entity_config", "vulnerability_entity_config", "assessment_entity_config"], "intrasource_disambiguated_models": ["sds_ei__account__aws_cloudtrail", "sds_ei__account__aws__iam_list_users", "sds_ei__account__aws__iam_security_center_permission_set_assignment", "sds_ei__account__ms_azure_ad", "sds_ei__account__active_directory", "sds_ei__application__ms_defender", "sds_ei__cloud_account__aws", "sds_ei__cloud_account__ms_azure", "sds_ei__cloud_compute__aws", "sds_ei__cloud_container__aws", "sds_ei__cloud_container__ms_azure", "sds_ei__cloud_storage__aws", "sds_ei__cloud_storage__ms_azure", "sds_ei__host__aws", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure", "sds_ei__host__ms_defender", "sds_ei__identity__active_directory", "sds_ei__identity__aws_cloudtrail", "sds_ei__identity__aws__iam_list_users", "sds_ei__identity__aws__iam_security_center_permission_set_assignment", "sds_ei__identity__ms_azure_ad", "sds_ei__person__ms_azure_ad", "sds_ei__host__infoblox"], "inventory_models": ["sds_ei__account__aws_cloudtrail_console_login__user_name", "sds_ei__cloud_account__aws_sh_finding__resource_account_id", "sds_ei__account__aws__iam_list_users__user_name", "sds_ei__account__aws__iam_security_center_permission_set_assignment__user_name", "sds_ei__account__ms_azure_ad_directory_members__user_principal_name", "sds_ei__account__ms_azure_ad_sign_in__user_principal_name_application", "sds_ei__account__ms_azure_ad_sign_in__user_principal_name", "sds_ei__account__ms_azure_ad_users__user_principal_name", "sds_ei__account__active_directory__sam_account_name_with_domain", "sds_ei__application__mega__software", "sds_ei__application__ms_defender_device_software_inventory__software_name", "sds_ei__application__ms_defender_device_tvm_software_vulnerabilities_delta__software_name", "sds_ei__cloud_account__aws_describe_standards_controls__standards_control_arn", "sds_ei__cloud_account__aws_ec2_instance__owner_id", "sds_ei__cloud_account__ms_azure_security_assessment__resource_account_id", "sds_ei__cloud_account__aws_ecs_task_container__account_id", "sds_ei__cloud_account__aws_eks_container__account_id", "sds_ei__cloud_account__aws_emr_cluster__account_id", "sds_ei__cloud_account__aws_emr_ec2_fleet__account_id", "sds_ei__cloud_account__aws_emr_ec2_instance__account_id", "sds_ei__cloud_account__aws_get_enabled_standards__standards_subscription_arn", "sds_ei__cloud_account__aws_list_standards_control_associations__security_control_arn", "sds_ei__cloud_account__aws_organizations__id", "sds_ei__cloud_account__aws_resource_details__account_id", "sds_ei__cloud_account__aws_sh_finding__account_id", "sds_ei__cloud_account__azure_aci_container__account_id", "sds_ei__cloud_account__azure_blob_storage_container__account_id", "sds_ei__cloud_account__azure_file_share__account_id", "sds_ei__cloud_account__azure_queue_storage__account_id", "sds_ei__cloud_account__azure_regulatory_compliance_control__account_id", "sds_ei__cloud_account__azure_regulatory_compliance_standards_account_id", "sds_ei__cloud_account__azure_resource_details__account_id", "sds_ei__cloud_account__azure_table_storage__account_id", "sds_ei__cloud_account__azure_virtual_machine__account_id", "sds_ei__cloud_account__ms_azure_regulatory_compliance_assessments__account_id", "sds_ei__cloud_account__ms_azure_security_assessment__account_id", "sds_ei__cloud_account__ms_azure_subscriptions__subscriptionid", "sds_ei__cloud_compute__aws_ecs_task_container__cluster_arn", "sds_ei__cloud_compute__aws_emr_cluster__resource_id", "sds_ei__cloud_compute__aws_emr_ec2_fleet__cluster_id", "sds_ei__cloud_compute__aws_emr_ec2_fleet__resource_id", "sds_ei__cloud_compute__aws_emr_ec2_instance__fleet_id", "sds_ei__cloud_compute__aws_resource_details__arn", "sds_ei__cloud_compute__aws_resource_details__aws_autoscaling_group_key", "sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key", "sds_ei__cloud_compute__aws_sh_findings__resource_id", "sds_ei__cloud_compute__ms_azure_aci_container__cluster_id", "sds_ei__cloud_compute__ms_azure_assessments__resource_id", "sds_ei__cloud_compute__ms_azure_resource_details__id", "sds_ei__cloud_compute__ms_azure_virtual_machine__azure_vmss_key", "sds_ei__cloud_container__aws_ecs_task_container__containerarn", "sds_ei__cloud_container__aws_eks_container__containerid", "sds_ei__cloud_container__aws_resource_details__arn", "sds_ei__cloud_container__aws_sh_findings__resource_id", "sds_ei__cloud_container__ms_azure_aci_container__resource_id", "sds_ei__cloud_container__ms_azure_aks_container__containerid", "sds_ei__cloud_container__ms_azure_assessment__resource_id", "sds_ei__cloud_container__ms_azure_resource_details__id", "sds_ei__cloud_storage__aws_resource_details__arn", "sds_ei__cloud_storage__aws_sh_findings__id", "sds_ei__cloud_storage__ms_azure_assessment__resource_id", "sds_ei__cloud_storage__ms_azure_blob_storage_container__id", "sds_ei__cloud_storage__ms_azure_blob_storage_container__sa_id", "sds_ei__cloud_storage__ms_azure_file_share__id", "sds_ei__cloud_storage__ms_azure_file_share__sa_id", "sds_ei__cloud_storage__ms_azure_queue_storage__id", "sds_ei__cloud_storage__ms_azure_queue_storage__sa_id", "sds_ei__cloud_storage__ms_azure_resource_details__id", "sds_ei__cloud_storage__ms_azure_table_storage__id", "sds_ei__cloud_storage__ms_azure_table_storage__sa_id", "sds_ei__host__aws_ec2_instance__instanceid", "sds_ei__host__aws_resource_details__arn", "sds_ei__host__aws_resource_details__instance_id", "sds_ei__host__aws_sh_findings__cloud_instance_id", "sds_ei__host__ms_azure_ad_devices__device_id", "sds_ei__host__ms_azure_ad_registered_users__id", "sds_ei__host__ms_azure_assessment__cloud_resource_id", "sds_ei__host__ms_azure_resource_details__id", "sds_ei__host__ms_azure_resource_details__managed_by", "sds_ei__host__ms_azure_resource_list__resource_id", "sds_ei__host__ms_defender_device_events__device_id", "sds_ei__host__ms_defender_device_list__id", "sds_ei__host__ms_defender_device_software_inventory__device_id", "sds_ei__host__ms_defender_device_tvm_software_vulnerabilities_delta__device_id", "sds_ei__host__active_directory__object_guid", "sds_ei__identity__active_directory__sam_account_name_with_domain", "sds_ei__identity__aws_cloudtrail_console_login__user_name", "sds_ei__identity__aws__iam_list_users__user_name", "sds_ei__identity__aws__iam_security_center_permission_set_assignment__user_name", "sds_ei__identity__ms_azure_ad_devices__device_id", "sds_ei__identity__ms_azure_ad_sign_in__user_principal_name", "sds_ei__identity__ms_azure_ad_users__other_mails", "sds_ei__identity__ms_azure_ad_users__user_principal_name", "sds_ei__person__aws__iam_list_users__user_name", "sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name", "sds_ei__person__bamboohr__employee_number", "sds_ei__person__ms_azure_ad_sign_in__user_id", "sds_ei__person__ms_azure_ad_registered_users__aad_user_id", "sds_ei__person__ms_azure_ad_users__aad_id", "sds_ei__person__active_directory__object_guid", "sds_ei__vulnerability__epss_loader__cve_id", "sds_ei__vulnerability__ms_defender_device_tvm_software_vulnerabilities_delta__cve_id", "sds_ei__vulnerability__nvd_loader__cve_id", "sds_ei__host__ms_defender_tvm__device_id", "sds_ei__vulnerability__cisa_vulnrichment__cve_id", "sds_ei__host__infoblox_ddi_enrichment__ref", "sds_ei__host__infoblox_ddi_host__ipv4addrs_ref", "sds_ei__account__ms_azure_ad_user_registration__user_principal_name", "sds_ei__identity__ms_azure_ad_user_registration__user_principal_name", "sds_ei__host__aws_emr_ec2_instance__instanceid", "sds_ei__host__ms_azure_virtual_machine__resource_id", "sds_ei__host__ms_azure_ad__user_sign_in__device_id"], "relationship_models": ["sds_ei__rel__aws_ec2_instance__host_belongs_to_cloud_account", "sds_ei__rel__aws_ecs_container__cloud_container_resource_belongs_to_cloud_account", "sds_ei__rel__aws_eks_container__cloud_container_resource_belongs_to_cloud_account", "sds_ei__rel__aws_emr_cluster__cloud_compute_resource_belongs_to_cloud_account", "sds_ei__rel__aws_emr_ec2_fleet__cloud_compute_resource_belongs_to_cloud_account", "sds_ei__rel__aws_emr_ec2_instance__host_belongs_to_cloud_account", "sds_ei__rel__aws_emr__compute_instance_group_belongs_to_mapreduce_cluster", "sds_ei__rel__aws_emr__virtual_machine_belongs_to_compute_instance_group", "sds_ei__rel__aws_resource_details__cloud_compute_resource_belongs_to_cloud_account", "sds_ei__rel__aws_resource_details__cloud_container_resource_belongs_to_cloud_account", "sds_ei__rel__aws_resource_details__cloud_storage_resource_belongs_to_cloud_account", "sds_ei__rel__aws_resource_details__compute_instance_group_belongs_to_kubernetes_cluster", "sds_ei__rel__aws_resource_details__host_belongs_to_cloud_account", "sds_ei__rel__aws_resource_details__virtual_machine_belongs_to_compute_instance_group", "sds_ei__rel__aws_resource_details__volume_associates_to_virtual_machine", "sds_ei__rel__aws_task__container_belongs_to_container_service", "sds_ei__rel__aws__cloudtrail__identity_has_account", "sds_ei__rel__aws__iam_list_users__identity_has_account", "sds_ei__rel__aws__iam_list_users__person_has_identity", "sds_ei__rel__aws__iam_security_center_permission_set_assignment_identity_has_account", "sds_ei__rel__aws__iam_security_center_permission_set_assignment__person_has_identity", "sds_ei__rel__azure_aci_container__cloud_container_resource_belongs_to_cloud_account", "sds_ei__rel__azure_aci_container__container_belongs_to_container_group", "sds_ei__rel__azure_blob_storage__bucket_belongs_to_storage_account", "sds_ei__rel__azure_blob_storage__cloud_storage_resource_belongs_to_cloud_account", "sds_ei__rel__azure_file_share__file_system_service_belongs_to_storage_account", "sds_ei__rel__azure_file_storage__cloud_storage_resource_belongs_to_cloud_account", "sds_ei__rel__azure_queue_storage__cloud_storage_resource_belongs_to_cloud_account", "sds_ei__rel__azure_queue_storage__queue_service_belongs_to_storage_account", "sds_ei__rel__azure_resource_details__cloud_compute_resource_belongs_to_cloud_account", "sds_ei__rel__azure_resource_details__cloud_container_resource_belongs_to_cloud_account", "sds_ei__rel__azure_resource_details__cloud_storage_resource_belongs_to_cloud_account", "sds_ei__rel__azure_resource_details__host_belongs_to_cloud_account", "sds_ei__rel__azure_resource_details__volume_associates_to_virtual_machine", "sds_ei__rel__azure_table_storage__cloud_storage_resource_belongs_to_cloud_account", "sds_ei__rel__azure_table_storage__table_service_belongs_to_storage_account", "sds_ei__rel__azure_virtual_machine__host_belongs_to_cloud_account", "sds_ei__rel__ms_azure_ad_devices__host_has_identity", "sds_ei__rel__ms_azure_ad_sign_in__identity_has_account", "sds_ei__rel__ms_azure_ad_users__identity_has_account", "sds_ei__rel__ms_azure_ad_users__person_has_identity", "sds_ei__rel__ms_azure_ad_sign_in__person_has_identity", "sds_ei__rel__ms_azure_security_assessment__cloud_compute_resource_belongs_to_cloud_account", "sds_ei__rel__ms_azure_virtual_machine__virtual_machine_belongs_to_compute_instance_group", "sds_ei__rel__ms_defender_device_software_inventory__application_running_on_host", "sds_ei__rel__ms_defender_device_tvm_software_vulnerabilities_delta__application_running_on_host", "sds_ei__rel__ms_defender__vulnerability_finding_on_application", "sds_ei__rel__ms_defender__vulnerability_finding_on_host", "sds_ei__rel__active_directory__host_has_identity", "sds_ei__rel__active_directory__person_has_identity", "sds_ei__rel__active_directory__identity_has_account", "sds_ei__rel__ms_azure_ad__person_owns_host", "sds_ei__rel__ms_azure_ad_sign_in__host_has_identity"], "relationship_disambiguation": ["sds_ei__rel__application_running_on_host", "sds_ei__rel__bucket_belongs_to_storage_account", "sds_ei__rel__cloud_compute_resource_belongs_to_cloud_account", "sds_ei__rel__cloud_container_resource_belongs_to_cloud_account", "sds_ei__rel__cloud_storage_resource_belongs_to_cloud_account", "sds_ei__rel__compute_instance_group_belongs_to_kubernetes_cluster", "sds_ei__rel__file_system_service_belongs_to_storage_account", "sds_ei__rel__host_belongs_to_cloud_account", "sds_ei__rel__host_has_identity", "sds_ei__rel__identity_has_account", "sds_ei__rel__person_has_identity", "sds_ei__rel__person_owns_host", "sds_ei__rel__queue_service_belongs_to_storage_account", "sds_ei__rel__table_service_belongs_to_storage_account", "sds_ei__rel__virtual_machine_belongs_to_compute_instance_group", "sds_ei__rel__volume_associates_to_virtual_machine", "sds_ei__rel__vulnerability_finding_on_application", "sds_ei__rel__vulnerability_finding_on_host"], "intersource_disambiguated_models": ["sds_ei__account", "sds_ei__application", "sds_ei__cloud_account", "sds_ei__cloud_compute", "sds_ei__cloud_container", "sds_ei__cloud_storage", "sds_ei__host", "sds_ei__identity", "sds_ei__person", "sds_ei__vulnerability"], "publisher": ["sds_ei__account__publish", "sds_ei__application__publish", "sds_ei__cloud_account__publish", "sds_ei__cloud_compute__publish", "sds_ei__cloud_container__publish", "sds_ei__cloud_storage__publish", "sds_ei__host__publish", "sds_ei__identity__publish", "sds_ei__person__publish", "sds_ei__vulnerability__publish", "sds_ei__rel__application_running_on_host__publish", "sds_ei__rel__bucket_belongs_to_storage_account__publish", "sds_ei__rel__cloud_compute_resource_belongs_to_cloud_account__publish", "sds_ei__rel__cloud_container_resource_belongs_to_cloud_account__publish", "sds_ei__rel__cloud_storage_resource_belongs_to_cloud_account__publish", "sds_ei__rel__compute_instance_group_belongs_to_kubernetes_cluster__publish", "sds_ei__rel__file_system_service_belongs_to_storage_account__publish", "sds_ei__rel__host_belongs_to_cloud_account__publish", "sds_ei__rel__host_has_identity__publish", "sds_ei__rel__identity_has_account__publish", "sds_ei__rel__person_has_identity__publish", "sds_ei__rel__person_owns_host__publish", "sds_ei__rel__queue_service_belongs_to_storage_account__publish", "sds_ei__rel__table_service_belongs_to_storage_account__publish", "sds_ei__rel__virtual_machine_belongs_to_compute_instance_group__publish", "sds_ei__rel__volume_associates_to_virtual_machine__publish", "sds_ei__rel__vulnerability_finding_on_application__publish", "sds_ei__rel__vulnerability_finding_on_host__publish"], "entity_rel_enrich": ["sds_ei__account__enrich", "sds_ei__application__enrich", "sds_ei__cloud_account__enrich", "sds_ei__cloud_compute__enrich", "sds_ei__cloud_container__enrich", "sds_ei__cloud_storage__enrich", "sds_ei__host__enrich", "sds_ei__identity__enrich", "sds_ei__person__enrich", "sds_ei__vulnerability__enrich", "sds_ei__rel__vulnerability_finding_on_host__enrich"], "data_quality": ["sds_ei__host_data_quality", "sds_ei__identity_data_quality", "sds_ei__account_data_quality", "sds_ei__person_data_quality", "sds_ei__vulnerability_data_quality", "sds_ei__application_data_quality", "sds_ei__cloud_account_data_quality", "sds_ei__cloud_compute_data_quality", "sds_ei__cloud_container_data_quality", "sds_ei__cloud_storage_data_quality"]}}}, "solution_name": "ei"}]}