import pytest
from config_merger_api.utils.ei_data_dictionary_update_utils import EntityDatadictionaryUpdate

# Helper classes and functions to eliminate duplication
class DummyConfigManager:
    def __init__(self): 
        self.get_headers = {}; 
        self.ssl_verify = False; 
        self.url = "https://dummy/"

def create_dummy_init():
    """Helper function to create a dummy_init function for EntityDatadictionaryUpdate"""
    def dummy_init(self):
        self.config_manager = DummyConfigManager()
        self.headers = self.config_manager.get_headers
        self.ssl_verify = self.config_manager.ssl_verify
        self.base_url = self.config_manager.url
        self.url = self.base_url + '/'
        self.dummy_table = "no.table"
        self.list_meta = f"{self.url}list-configs-meta/?deployed=true"
        self.list_meta_publisher = f"{self.url}list-configs-meta/?config_item_type=publisher&deployed=true"
        self.list_meta_inter = f"{self.url}list-configs-meta/?config_item_type=intersource_disambiguated_models&deployed=true"
        self.list_meta_rel_inter = f"{self.url}list-configs-meta/?config_item_type=relationship_disambiguation&deployed=true"
    return dummy_init

def create_dummy_response(json_data):
    """Helper function to create a DummyResponse object with specified JSON data"""
    class DummyResponse:
        def raise_for_status(self): 
            # Mock method - no need to raise exceptions in successful test scenario
            pass
        def json(self): 
            return json_data
    return DummyResponse()

def test_entity_datadictionary_update_instantiation(monkeypatch):
    # Mock ConfigManager to avoid real API calls
    monkeypatch.setattr("config_merger_api.config_upgrade_utils.common_utils.ConfigManager", DummyConfigManager)
    # Patch EntityDatadictionaryUpdate.__init__ to skip load_catalog
    monkeypatch.setattr(EntityDatadictionaryUpdate, "__init__", create_dummy_init())
    instance = EntityDatadictionaryUpdate()
    assert hasattr(instance, "process_api_request")

def test_filter_names_by_config_item():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    data = [
        {"config_item_type": "data_dictionary_config", "name": "foo"},
        {"config_item_type": "relationship_data_dictionary_config", "name": "bar"},
        {"config_item_type": "other", "name": "baz"},
    ]
    result = instance.filter_names_by_config_item(data, "any")
    assert "foo" in result and "bar" in result
    result2 = instance.filter_names_by_config_item(data, "other")
    assert result2 == ["baz"] 

def test_process_api_request_success(monkeypatch):
    monkeypatch.setattr("config_merger_api.config_upgrade_utils.common_utils.ConfigManager", DummyConfigManager)
    monkeypatch.setattr(EntityDatadictionaryUpdate, "__init__", create_dummy_init())
    instance = EntityDatadictionaryUpdate()
    monkeypatch.setattr(instance, "fetch_all_data_from_api", lambda: [
        {"config_item_type": "data_dictionary_config", "name": "foo"},
        {"config_item_type": "relationship_data_dictionary_config", "name": "bar"}
    ])
    monkeypatch.setattr(instance, "process_names", lambda names: [{n: "Success" for n in names}])
    result = instance.process_api_request("dummy", "any")
    assert {"foo": "Success", "bar": "Success"} in result

def test_process_api_request_error(monkeypatch):
    monkeypatch.setattr("config_merger_api.config_upgrade_utils.common_utils.ConfigManager", DummyConfigManager)
    monkeypatch.setattr(EntityDatadictionaryUpdate, "__init__", create_dummy_init())
    instance = EntityDatadictionaryUpdate()
    monkeypatch.setattr(instance, "fetch_all_data_from_api", lambda: (_ for _ in ()).throw(Exception("fail")))
    result = instance.process_api_request("dummy", "any")
    assert result["status"] == "ERROR"

def test_fetch_all_data_from_api_success(monkeypatch):
    monkeypatch.setattr("requests.get", lambda *a, **kw: create_dummy_response([1, 2, 3]))
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.list_meta = "dummy"
    instance.headers = {}
    instance.ssl_verify = False
    result = instance.fetch_all_data_from_api()
    assert result == [1, 2, 3]

def test_fetch_all_data_from_api_error(monkeypatch):
    monkeypatch.setattr("requests.get", lambda *a, **kw: (_ for _ in ()).throw(Exception("fail")))
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.list_meta = "dummy"
    instance.headers = {}
    instance.ssl_verify = False
    with pytest.raises(Exception):
        instance.fetch_all_data_from_api()

def test_process_names_success():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    monkeypatch = lambda *a, **kw: None
    instance.get_api_response = lambda name: True
    result = instance.process_names(["foo", "bar"])
    assert {"foo": "Success"} in result and {"bar": "Success"} in result

def test_process_names_failure():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.get_api_response = lambda name: False
    result = instance.process_names(["foo"])
    assert {"foo": "Failed"} in result

def test_process_single_api_request_success():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.get_api_response = lambda name: True
    result = instance.process_single_api_request("foo")
    assert {"foo": "Success"} in result

def test_process_single_api_request_error():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.get_api_response = lambda name: (_ for _ in ()).throw(Exception("fail"))
    result = instance.process_single_api_request("foo")
    assert result["status"] == "ERROR"

def test_determine_dict_config_name_entity():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    response = {"config_value": {"isInverse": False}}
    assert instance.determine_dict_config_name("foo__data_dictionary", "entity", response) == "foo"

def test_determine_dict_config_name_relationship_inverse():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    response = {"config_value": {"isInverse": True}, "inverse_relationship_name": "bar"}
    assert instance.determine_dict_config_name("foo", "relationship", response) == "bar"

def test_determine_dict_config_name_invalid_type():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    response = {"config_value": {"isInverse": False}}
    assert instance.determine_dict_config_name("foo", "invalid", response) is False

def test_fetch_config_meta_success(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.headers = {}
    instance.ssl_verify = False
    instance.list_meta_publisher = "dummy"
    instance.list_meta_inter = "dummy"
    instance.list_meta_rel_inter = "dummy"
    instance.get_config_name = lambda config_name, names, config_type: "ok"
    monkeypatch.setattr(instance, "determine_dict_config_name", lambda config_name, data_dict_type, response: "foo")
    monkeypatch.setattr("requests.get", lambda *a, **kw: create_dummy_response([{"config_item_type": "x", "name": "foo"}]))
    response = {"config_value": {}}
    assert instance.fetch_config_meta("foo", "entity", response, "publish") == "ok"

def test_fetch_config_meta_no_names(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.headers = {}
    instance.ssl_verify = False
    instance.list_meta_publisher = "dummy"
    instance.list_meta_inter = "dummy"
    instance.list_meta_rel_inter = "dummy"
    instance.get_config_name = lambda config_name, names, config_type: "ok"
    monkeypatch.setattr(instance, "determine_dict_config_name", lambda config_name, data_dict_type, response: "foo")
    monkeypatch.setattr("requests.get", lambda *a, **kw: create_dummy_response([]))
    response = {"config_value": {}}
    assert instance.fetch_config_meta("foo", "entity", response, "publish") is False

def test_filter_configs_by_name():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    names = ["a__b__c", "x__y__z", "foo__bar__baz"]
    # Should match 'b' for config_name 'b'
    assert instance.filter_configs_by_name("b", names) == ["a__b__c"]

def test_process_multiple_configs(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.isolap_check = lambda config: True
    instance.fetch_config = lambda name: {"ok": name}
    filtered = ["foo", "bar"]
    result = instance.process_multiple_configs(filtered, "publish")
    assert result["ok"] in filtered

def test_get_config_name(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.filter_configs_by_name = lambda config_name, names: ["foo"]
    instance.fetch_config = lambda name: {"ok": name}
    result = instance.get_config_name("foo", ["foo"], "publish")
    assert result["ok"] == "foo"

def test_fetch_config_success(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.url = "https://dummy/"
    instance.headers = {}
    instance.ssl_verify = False
    monkeypatch.setattr("requests.get", lambda *a, **kw: create_dummy_response({"a": 1}))
    result = instance.fetch_config("foo")
    assert result == {"a": 1}

def test_fetch_config_error(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.url = "https://dummy/"
    instance.headers = {}
    instance.ssl_verify = False
    monkeypatch.setattr("requests.get", lambda *a, **kw: (_ for _ in ()).throw(Exception("fail")))
    with pytest.raises(Exception, match="fail"):
        instance.fetch_config("foo") 

def test_fetch_inter_config(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.fetch_config_meta = lambda config_name, data_dict_type, response, config_type: (config_name, data_dict_type, config_type)
    assert instance.fetch_inter_config("foo", "entity", {}) == ("foo", "entity", "inter")

def test_fetch_publish_config(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.fetch_config_meta = lambda config_name, data_dict_type, response, config_type: (config_name, data_dict_type, config_type)
    assert instance.fetch_publish_config("foo", "entity", {}) == ("foo", "entity", "publish")

def test_isolap_check_true():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    publish_config = {"outputTableInfo": {"isOLAPTable": True}}
    assert instance.isolap_check(publish_config) is True

def test_isolap_check_false():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    publish_config = {"outputTableInfo": {"isOLAPTable": False}}
    assert instance.isolap_check(publish_config) is False

def test_isfragment_olap_check_true():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    inter_config = {"output": {"isFragmentOLAPTable": True}}
    assert instance.isfragment_olap_check(inter_config) is True

def test_isfragment_olap_check_false():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    inter_config = {"output": {"isFragmentOLAPTable": False}}
    assert instance.isfragment_olap_check(inter_config) is False

def test_fetch_schema_success(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.fetch_inter_config = lambda config_name, data_dict_type, response: {"output": {"fragmentLocation": "frag"}}
    instance.fetch_publish_config = lambda config_name, data_dict_type, response: {"outputTableInfo": {"outputTableName": "pub"}}
    instance.get_publish_table_info = lambda publish_config: ("pub", True)
    instance.get_fragment_table_info = lambda inter_config: ("frag", True)
    instance.enrich_data_check = lambda pub, frag, is_olap, is_frag, resp, typ: {"ok": True}
    response = {"name": "foo"}
    assert instance.fetch_schema(response, "entity") == {"ok": True}

def test_fetch_schema_error(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.fetch_inter_config = lambda *a, **kw: (_ for _ in ()).throw(Exception("fail"))
    response = {"name": "foo"}
    assert instance.fetch_schema(response, "entity") is False

def test_get_publish_table_info_true():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.isolap_check = lambda config: True
    instance.is_exist = lambda name: True
    publish_config = {"outputTableInfo": {"outputTableName": "pub"}}
    instance.dummy_table = "dummy"
    name, is_olap = instance.get_publish_table_info(publish_config)
    assert name == "pub" and is_olap is True

def test_get_publish_table_info_false():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.isolap_check = lambda config: False
    instance.is_exist = lambda name: False
    publish_config = {"outputTableInfo": {"outputTableName": "pub"}}
    instance.dummy_table = "dummy"
    name, is_olap = instance.get_publish_table_info(publish_config)
    assert name == "pub" and is_olap is False

def test_get_fragment_table_info_true():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.isfragment_olap_check = lambda config: True
    instance.is_exist = lambda name: True
    inter_config = {"output": {"fragmentLocation": "frag"}}
    instance.dummy_table = "dummy"
    name, is_frag = instance.get_fragment_table_info(inter_config)
    assert name == "frag" and is_frag is True

def test_get_fragment_table_info_false():
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.isfragment_olap_check = lambda config: False
    instance.is_exist = lambda name: False
    inter_config = {"output": {"fragmentLocation": "frag"}}
    instance.dummy_table = "dummy"
    name, is_frag = instance.get_fragment_table_info(inter_config)
    assert name == "frag" and is_frag is False

def test_enrich_data_check_entity(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.enrich_check = lambda *a, **kw: {"entity": True}
    instance.enrich_rel_check = lambda *a, **kw: {"rel": True}
    assert instance.enrich_data_check("pub", "frag", True, True, {}, "entity") == {"entity": True}
    assert instance.enrich_data_check("pub", "frag", True, True, {}, "relationship") == {"rel": True}
    assert instance.enrich_data_check("pub", "frag", True, True, {}, "other") is None

def test_enrich_check(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.check_enrichment = lambda *a, **kw: {"ok": True}
    instance.post_response = lambda *a, **kw: {"posted": True}
    assert instance.enrich_check("pub", "frag", True, True, {}) == {"posted": True}

def test_enrich_rel_check(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    instance.check_enrichment = lambda *a, **kw: {"ok": True}
    instance.rel_entity_check = lambda *a, **kw: {"rel": True}
    assert instance.enrich_rel_check("pub", "frag", True, True, {}) == {"rel": True}

def test_rel_entity_attribute_check(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    response = {"config_value": {"entity_attributes": {"foo": {"type": "timestamp"}}}}
    rel_entity_table_check = {"fragment_table_present": True, "ei_fragment_name": "frag"}
    instance.check_attributes = lambda *a, **kw: True
    instance.check_data_type = lambda *a, **kw: "timestamp"
    instance.dummy_table = "dummy"
    entity_attributes_updates = {}
    target_table_schema = None
    instance.rel_entity_attribute_check(rel_entity_table_check, response, entity_attributes_updates, target_table_schema)
    assert "foo" in entity_attributes_updates

def test_rel_entity_check(monkeypatch):
    instance = EntityDatadictionaryUpdate.__new__(EntityDatadictionaryUpdate)
    response = {"config_value": {"source_entity": "src", "target_entity": "tgt"}, "name": "rel"}
    instance.fetch_publish_config = lambda name, typ, resp: {"outputTableInfo": {"outputTableName": "pub"}}
    instance.isolap_check = lambda config: True
    instance.is_exist = lambda name: True
    instance.dummy_table = "dummy"
    entity_attributes_updates = {}
    table_check = {"ei_table_present": True, "fragment_table_present": True, "ei_table_name": "pub", "ei_fragment_name": "frag"}
    # rel_entity_check will return False due to missing catalog attribute
    result = instance.rel_entity_check(response, entity_attributes_updates, table_check)
    assert result is False 