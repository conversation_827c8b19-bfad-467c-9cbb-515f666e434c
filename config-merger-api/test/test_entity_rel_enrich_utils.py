import pytest
from config_merger_api.utils.entity_rel_enrich_utils import merge_entity_rel_enrich

def test_merge_entity_rel_enrich_solution_only():
    sol = [{"config_item_type": "entity_rel_enrich", "config_value": {"a": 1}}]
    client = []
    result = merge_entity_rel_enrich(sol, client)
    assert result == {"a": 1}

def test_merge_entity_rel_enrich_client_only():
    sol = []
    client = [{"config_item_type": "entity_rel_enrich", "config_value": {"b": 2}}]
    result = merge_entity_rel_enrich(sol, client)
    assert result == {"b": 2}

def test_merge_entity_rel_enrich_both():
    sol = [{"config_item_type": "entity_rel_enrich", "config_value": {"a": 1}}]
    client = [{"config_item_type": "entity_rel_enrich", "config_value": {"b": 2}}]
    result = merge_entity_rel_enrich(sol, client)
    assert isinstance(result, dict)

def test_merge_entity_rel_enrich_neither():
    assert merge_entity_rel_enrich([], []) == {} 

def test_merge_entity_rel_enrich_both_with_overlap():
    sol = [{"config_item_type": "entity_rel_enrich", "config_value": {"a": 1, "overlap": 1}}]
    client = [{"config_item_type": "entity_rel_enrich", "config_value": {"overlap": 2, "b": 2}}]
    result = merge_entity_rel_enrich(sol, client)
    assert isinstance(result, dict)
    assert "overlap" in result

def test_merge_entity_rel_enrich_empty_configs():
    assert merge_entity_rel_enrich([], []) == {} 