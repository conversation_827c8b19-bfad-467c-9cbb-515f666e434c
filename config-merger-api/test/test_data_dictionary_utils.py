import pytest
from config_merger_api.utils import data_dictionary_utils as ddu

def test_update_dictionary_with_ui_config():
    sol = [{"config_item_type": "data_dictionary_config", "config_value": {"a": 1}}]
    client = [{"config_item_type": "data_dictionary_config", "config_value": {"a": 2}}]
    result = ddu.update_dictionary_with_ui_config(sol, client)
    assert result["a"] == 2

def test_update_dictionary_with_ui_config_data_dictionary():
    sol_config = []
    client_conf = [
        {"config_item_type": "data_dictionary_config", "config_value": {"attributes": {"a": 1}}}
    ]
    result = ddu.update_dictionary_with_ui_config(sol_config, client_conf)
    assert result == {"attributes": {"a": 1}}

def test_update_dictionary_with_ui_config_relationship():
    sol_config = []
    client_conf = [
        {"config_item_type": "relationship_data_dictionary_config", "config_value": {"rel_attrs": {"b": 2}}}
    ]
    result = ddu.update_dictionary_with_ui_config(sol_config, client_conf)
    assert result == {"rel_attrs": {"b": 2}}

def test_update_dictionary_with_ui_config_multiple():
    sol_config = []
    client_conf = [
        {"config_item_type": "data_dictionary_config", "config_value": {"attributes": {"a": 1}}},
        {"config_item_type": "relationship_data_dictionary_config", "config_value": {"rel_attrs": {"b": 2}}},
        {"config_item_type": "other_config", "config_value": {"c": 3}}
    ]
    result = ddu.update_dictionary_with_ui_config(sol_config, client_conf)
    assert result == {"rel_attrs": {"b": 2}}

def test_get_non_required_fields():
    conf = {"attributes": {"a": {"is_active": False}, "b": {}}}
    result = ddu.get_non_required_fields(conf, "attributes")
    assert "a" in result

def test_get_non_required_fields_with_inactive():
    config = {
        "attributes": {
            "active_field": {"is_active": True},
            "inactive_field": {"is_active": False},
            "no_is_active_field": {}
        }
    }
    result = ddu.get_non_required_fields(config, "attributes")
    assert "inactive_field" in result
    assert "active_field" not in result
    assert "no_is_active_field" not in result

def test_update_ei_existing_attributes():
    conf = {"attributes": {"a": {}}}
    result = ddu.update_ei_existing_attributes("base", conf, "ent", "attributes")
    assert "dashboard_identifier" in result["attributes"]["a"]

def test_update_ei_existing_attributes_new_dashboard_identifier():
    data_dictionary_conf = {
        "attributes": {
            "attr1": {},
            "attr2": {"dashboard_identifier": {"existing": {}}}
        }
    }
    result = ddu.update_ei_existing_attributes("EI", data_dictionary_conf, "entity1", "attributes")
    assert "dashboard_identifier" in result["attributes"]["attr1"]
    assert "EI" in result["attributes"]["attr1"]["dashboard_identifier"]
    assert "entity1" in result["attributes"]["attr1"]["dashboard_identifier"]
    assert "entity1" in result["attributes"]["attr2"]["dashboard_identifier"]

def test_update_dashboard_identifier():
    conf = {}
    result = ddu.update_dashboard_identifier("base", conf, {"is_active": True}, "ent", "sol")
    assert "dashboard_identifier" in result

def test_update_dashboard_identifier_new():
    data_dictionary_conf = {}
    config = {"is_active": True}
    result = ddu.update_dashboard_identifier("EI", data_dictionary_conf, config, "entity1", "solution1")
    assert "dashboard_identifier" in result
    assert "EI" in result["dashboard_identifier"]
    assert "entity1" in result["dashboard_identifier"]
    assert "solution1" in result["dashboard_identifier"]

def test_update_dashboard_identifier_existing():
    data_dictionary_conf = {"dashboard_identifier": {"existing": {}}}
    config = {"is_active": True}
    result = ddu.update_dashboard_identifier("EI", data_dictionary_conf, config, "entity1", "solution1")
    assert "existing" in result["dashboard_identifier"]
    assert "solution1" in result["dashboard_identifier"]

def test_update_dashboard_identifier_inactive():
    data_dictionary_conf = {}
    config = {"is_active": False}
    # Should raise KeyError because dashboard_identifier is not created
    with pytest.raises(KeyError):
        ddu.update_dashboard_identifier("EI", data_dictionary_conf, config, "entity1", "solution1")

def test_update_dashboard_identifier_ei_only():
    data_dictionary_conf = {}
    result = ddu.update_dashboard_identifier_ei_only("EI", data_dictionary_conf, "entity1")
    assert "dashboard_identifier" in result
    assert "EI" in result["dashboard_identifier"]
    assert "entity1" in result["dashboard_identifier"]

def test_update_attribute_dashboard_identifier():
    conf = {"attributes": {"a": {"dashboard_identifier": {}}}}
    config = {"attributes": {"a": {}}}
    result = ddu.update_attribute_dashboard_identifier(conf, config, "sol", "attributes", "alias")
    assert "sol" in result["attributes"]["a"]["dashboard_identifier"]

def test_merge_dictionaries():
    sol = {"a": 1, "b": {"c": 2}}
    client = {"b": {"d": 3}, "e": 4}
    result = ddu.merge_dictionaries(sol, client)
    assert result["b"]["d"] == 3 and result["a"] == 1 and result["e"] == 4 

def test_process_and_remove_dashboard_configs_merges():
    configs = [
        {"name": "dashboard_config", "config_value": {"a": 1}},
        {"name": "dashboard_config", "config_value": {"b": 2}},
        {"name": "other_config", "config_value": {"c": 3}}
    ]
    result = ddu.process_and_remove_dashboard_configs(configs)
    dashboard = [c for c in result if c["name"] == "dashboard_config"]
    assert len(dashboard) == 1
    assert "a" in dashboard[0]["config_value"] and "b" in dashboard[0]["config_value"]

def test_process_and_remove_dashboard_configs_no_dashboard():
    configs = [
        {"name": "other_config", "config_value": {"c": 3}}
    ]
    result = ddu.process_and_remove_dashboard_configs(configs)
    assert result == configs

def test_check_keys_valid():
    sol_configs = [
        {"dashboard_identifier": "EI", "config_item_type": "test", "solution_name": "test"}
    ]
    ddu.check_keys(sol_configs)  # Should not raise

def test_check_keys_invalid():
    sol_configs = [
        {"dashboard_identifier": "OTHER", "config_item_type": "test", "solution_name": "test"}
    ]
    with pytest.raises(Exception, match="EI or EM config is missing"):
        ddu.check_keys(sol_configs)

def test_get_base_config_ei():
    configs = [
        {"config_item_type": "test", "dashboard_identifier": "EI", "config_value": {"base_config": True}}
    ]
    base_key, base_config = ddu.get_base_config(configs, "test")
    assert base_key == "EI"
    assert base_config["dashboard_identifier"] == "EI"

def test_get_base_config_em():
    configs = [
        {"config_item_type": "test", "dashboard_identifier": "EM", "config_value": {"base_config": True}}
    ]
    base_key, base_config = ddu.get_base_config(configs, "test")
    assert base_key == "EM"
    assert base_config["dashboard_identifier"] == "EM"

def test_get_base_config_base_config_true():
    configs = [
        {"config_item_type": "test", "dashboard_identifier": "OTHER", "config_value": {"base_config": True}}
    ]
    base_key, base_config = ddu.get_base_config(configs, "test")
    assert base_key == "OTHER"
    assert base_config["config_value"]["base_config"] is True

def test_get_base_config_not_found():
    configs = [
        {"config_item_type": "other", "dashboard_identifier": "EI"}
    ]
    with pytest.raises(Exception, match="No base configuration found with EI or EM identifier"):
        ddu.get_base_config(configs, "test")

def test_merge_data_dictionary_error(monkeypatch):
    def bad_get_base_config(*a, **kw):
        raise Exception("fail")
    monkeypatch.setattr(ddu, "get_base_config", bad_get_base_config)
    # Provide 'solution_name' and 'name' to avoid KeyError
    with pytest.raises(Exception, match="fail"):
        ddu.merge_data_dictionary([
            {"config_item_type": "data_dictionary_config", "dashboard_identifier": "EI", "solution_name": "EI", "name": "not_dashboard_config"}
        ], [])

def test_merge_relationship_data_dictionary_error(monkeypatch):
    def bad_get_base_config(*a, **kw):
        raise Exception("fail")
    monkeypatch.setattr(ddu, "get_base_config", bad_get_base_config)
    with pytest.raises(Exception, match="fail"):
        ddu.merge_relationship_data_dictionary([
            {"config_item_type": "relationship_data_dictionary_config", "dashboard_identifier": "EI", "solution_name": "EI", "name": "not_dashboard_config"}
        ], [])

def test_check_keys_logs_and_raises(monkeypatch):
    # Should log error and raise
    with pytest.raises(Exception, match="EI or EM config is missing"):
        ddu.check_keys([{"dashboard_identifier": "OTHER", "config_item_type": "test", "solution_name": "test"}])

def test_merge_dictionaries_deep():
    sol = {"a": 1, "b": {"c": 2}}
    client = {"b": {"d": 3}, "e": 4}
    result = ddu.merge_dictionaries(sol, client)
    assert result["b"]["c"] == 2 and result["b"]["d"] == 3 and result["a"] == 1 and result["e"] == 4

def test_update_data_dictionary_empty():
    result = ddu.update_data_dictionary("EI", None, {"attributes": {}}, "solution1", {"attributes": {}}, False, "entity1")
    assert "attributes" in result

def test_update_relationship_data_dictionary_empty():
    result = ddu.update_relationship_data_dictionary("EI", None, {"relationship_attributes": {}}, "solution1", {"relationship_attributes": {}}, False, "entity1")
    assert "relationship_attributes" in result 

# Add more edge case tests for coverage
def test_merge_dictionaries_empty():
    assert ddu.merge_dictionaries({}, {}) == {}
    assert ddu.merge_dictionaries(None, None) == {}

def test_process_and_remove_dashboard_configs_empty():
    assert ddu.process_and_remove_dashboard_configs([]) == []

def test_check_keys_missing_flag():
    with pytest.raises(Exception, match="EI or EM config is missing"):
        ddu.check_keys([{"dashboard_identifier": "OTHER", "config_item_type": "test", "solution_name": "test"}]) 

# More edge case and branch tests for coverage
def test_update_data_dictionary_type_error():
    # Should handle TypeError gracefully
    result = ddu.update_data_dictionary("EI", {}, {"attributes": {}}, "solution1", {"attributes": {}}, False, "entity1")
    assert "attributes" in result

def test_update_relationship_data_dictionary_type_error():
    # Should handle TypeError gracefully
    result = ddu.update_relationship_data_dictionary("EI", {}, {"relationship_attributes": {}}, "solution1", {"relationship_attributes": {}}, False, "entity1")
    assert "relationship_attributes" in result

def test_merge_dictionaries_nested():
    sol = {"a": {"b": {"c": 1}}}
    client = {"a": {"b": {"d": 2}}}
    result = ddu.merge_dictionaries(sol, client)
    assert result["a"]["b"]["c"] == 1 and result["a"]["b"]["d"] == 2

def test_process_and_remove_dashboard_configs_merges_multiple():
    configs = [
        {"name": "dashboard_config", "config_value": {"a": 1}},
        {"name": "dashboard_config", "config_value": {"b": 2}},
        {"name": "dashboard_config", "config_value": {"c": 3}},
        {"name": "other_config", "config_value": {"d": 4}}
    ]
    result = ddu.process_and_remove_dashboard_configs(configs)
    dashboard = [c for c in result if c["name"] == "dashboard_config"]
    assert len(dashboard) == 1
    assert all(k in dashboard[0]["config_value"] for k in ["a", "b", "c"]) 

def test_merge_data_dictionary_no_dashboard_configs():
    # Should return input if no dashboard_config present
    configs = [
        {"name": "other_config", "config_value": {"a": 1}, "config_item_type": "data_dictionary_config", "dashboard_identifier": "EI", "solution_name": "EI"}
    ]
    result = ddu.process_and_remove_dashboard_configs(configs)
    assert result == configs

def test_update_dashboard_identifier_inactive_keyerror():
    conf = {}
    config = {"is_active": False}
    # Should raise KeyError because dashboard_identifier is not created
    with pytest.raises(KeyError):
        ddu.update_dashboard_identifier("EI", conf, config, "entity1", "solution1")

def test_update_attribute_dashboard_identifier_selective_merge():
    conf = {"attributes": {"a": {"dashboard_identifier": {}}}}
    config = {"attributes": {"a": {}}}
    # Should not update dashboard_identifier if selective_merge is True
    result = ddu.update_attribute_dashboard_identifier(conf, config, "sol", "attributes", "alias", selective_merge=True)
    assert "sol" not in result["attributes"]["a"]["dashboard_identifier"]

def test_check_keys_no_ei_em():
    # Should raise Exception if no EI/EM config
    with pytest.raises(Exception, match="EI or EM config is missing"):
        ddu.check_keys([
            {"dashboard_identifier": "OTHER", "config_item_type": "test", "solution_name": "test"}
        ])

def test_merge_data_dictionary_missing_caption():
    # Should raise KeyError if caption is missing
    configs = [
        {"name": "other_config", "config_value": {"a": 1}, "config_item_type": "data_dictionary_config", "dashboard_identifier": "EI", "solution_name": "EI"}
    ]
    client = []
    with pytest.raises(KeyError, match="caption"):
        ddu.merge_data_dictionary(configs, client)

def test_merge_relationship_data_dictionary_missing_caption():
    configs = [
        {"name": "other_config", "config_value": {"a": 1}, "config_item_type": "relationship_data_dictionary_config", "dashboard_identifier": "EI", "solution_name": "EI"}
    ]
    client = []
    with pytest.raises(KeyError, match="caption"):
        ddu.merge_relationship_data_dictionary(configs, client)

def test_update_ei_existing_attributes_missing_dashboard_identifier():
    conf = {"attributes": {"a": {}}}
    result = ddu.update_ei_existing_attributes("base", conf, "ent", "attributes")
    assert "dashboard_identifier" in result["attributes"]["a"]

def test_update_dashboard_identifier_existing_key():
    conf = {"dashboard_identifier": {"existing": {}}}
    config = {"is_active": True}
    result = ddu.update_dashboard_identifier("EI", conf, config, "entity1", "solution1")
    assert "existing" in result["dashboard_identifier"]
    assert "solution1" in result["dashboard_identifier"]

def test_update_dashboard_identifier_ei_only_new():
    conf = {}
    result = ddu.update_dashboard_identifier_ei_only("EI", conf, "entity1")
    assert "dashboard_identifier" in result
    assert "EI" in result["dashboard_identifier"]
    assert "entity1" in result["dashboard_identifier"] 

def test_merge_data_dictionary_vra_logic():
    # Test VRA logic branch
    configs = [
        {"name": "VRA", "config_value": {"caption": "entity1", "attributes": {"a": {}}}, "config_item_type": "data_dictionary_config", "dashboard_identifier": "VRA", "solution_name": "VRA"},
        {"name": "other", "config_value": {"caption": "entity1", "attributes": {"b": {}}}, "config_item_type": "data_dictionary_config", "dashboard_identifier": "EI", "solution_name": "EI"}
    ]
    client = []
    # Should not raise
    try:
        ddu.merge_data_dictionary(configs, client)
    except Exception:
        assert False, "merge_data_dictionary should not raise on VRA logic"

def test_merge_relationship_data_dictionary_vra_logic():
    configs = [
        {"name": "VRA", "config_value": {"caption": "entity1", "relationship_attributes": {"a": {}}}, "config_item_type": "relationship_data_dictionary_config", "dashboard_identifier": "VRA", "solution_name": "VRA"},
        {"name": "other", "config_value": {"caption": "entity1", "relationship_attributes": {"b": {}}}, "config_item_type": "relationship_data_dictionary_config", "dashboard_identifier": "EI", "solution_name": "EI"}
    ]
    client = []
    # Should not raise
    try:
        ddu.merge_relationship_data_dictionary(configs, client)
    except Exception:
        assert False, "merge_relationship_data_dictionary should not raise on VRA logic"

def test_merge_data_dictionary_selective_merge():
    configs = [
        {"name": "other", "config_value": {"caption": "entity1", "attributes": {"a": {}}}, "config_item_type": "data_dictionary_config", "dashboard_identifier": "EI", "solution_name": "EI"}
    ]
    client = []
    # Add dashboard_conf to trigger selective_merge logic
    ddu.dashboard_conf = {"EI": {"selective_merge": True}}
    try:
        ddu.merge_data_dictionary(configs, client)
    except Exception:
        assert False, "merge_data_dictionary should not raise on selective_merge logic"
    finally:
        if hasattr(ddu, 'dashboard_conf'):
            del ddu.dashboard_conf

def test_merge_relationship_data_dictionary_selective_merge():
    configs = [
        {"name": "other", "config_value": {"caption": "entity1", "relationship_attributes": {"a": {}}}, "config_item_type": "relationship_data_dictionary_config", "dashboard_identifier": "EI", "solution_name": "EI"}
    ]
    client = []
    ddu.dashboard_conf = {"EI": {"selective_merge": True}}
    try:
        ddu.merge_relationship_data_dictionary(configs, client)
    except Exception:
        assert False, "merge_relationship_data_dictionary should not raise on selective_merge logic"
    finally:
        if hasattr(ddu, 'dashboard_conf'):
            del ddu.dashboard_conf 