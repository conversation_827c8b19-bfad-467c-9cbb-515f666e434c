import pytest
from unittest.mock import patch, MagicMock
from config_merger_api.utils.http_utils import HttpUtils
import os

@patch.dict(os.environ, {"CLIENT_ID": "id", "CLIENT_SECRET": "secret", "KEYCLOAK_URL": "https://fake-keycloak"})
@patch("requests.post")
def test_token_fetch(mock_post):
    mock_post.return_value.json.return_value = {"access_token": "abc"}
    http = HttpUtils(url="https://fake", headers={})
    assert http.token == "abc"

@patch("requests.get")
@patch.object(HttpUtils, "token", new_callable=MagicMock, return_value="abc")
def test_execute_api_call_get(mock_token, mock_get):
    mock_get.return_value.text = "{\"result\": 1}"
    mock_get.return_value.json.return_value = {"result": 1}
    mock_get.return_value.raise_for_status = lambda: None
    http = HttpUtils(url="https://fake")
    result = http.execute_api_call("GET", endpoint="foo")
    assert result["result"] == 1

@patch("requests.post")
@patch.object(HttpUtils, "token", new_callable=MagicMock, return_value="abc")
def test_execute_api_call_post(mock_token, mock_post):
    mock_post.return_value.text = "{\"result\": 2}"
    mock_post.return_value.json.return_value = {"result": 2}
    mock_post.return_value.raise_for_status = lambda: None
    http = HttpUtils(url="https://fake")
    result = http.execute_api_call("POST", endpoint="foo", body={"a": 1})
    assert result["result"] == 2

@patch("requests.patch")
@patch.object(HttpUtils, "token", new_callable=MagicMock, return_value="abc")
def test_execute_api_call_patch(mock_token, mock_patch):
    mock_patch.return_value.text = "{\"result\": 3}"
    mock_patch.return_value.json.return_value = {"result": 3}
    mock_patch.return_value.raise_for_status = lambda: None
    http = HttpUtils(url="https://fake")
    result = http.execute_api_call("PATCH", endpoint="foo", body={"a": 1})
    assert result["result"] == 3

@patch("requests.put")
@patch.object(HttpUtils, "token", new_callable=MagicMock, return_value="abc")
def test_execute_api_call_put(mock_token, mock_put):
    mock_put.return_value.text = "{\"result\": 4}"
    mock_put.return_value.json.return_value = {"result": 4}
    mock_put.return_value.raise_for_status = lambda: None
    http = HttpUtils(url="https://fake")
    result = http.execute_api_call("PUT", endpoint="foo", body={"a": 1})
    assert result["result"] == 4

@patch("requests.delete")
@patch.object(HttpUtils, "token", new_callable=MagicMock, return_value="abc")
def test_execute_api_call_delete(mock_token, mock_delete):
    mock_delete.return_value.text = "{\"result\": 5}"
    mock_delete.return_value.json.return_value = {"result": 5}
    mock_delete.return_value.raise_for_status = lambda: None
    http = HttpUtils(url="https://fake")
    result = http.execute_api_call("DELETE", endpoint="foo", body={"a": 1})
    assert result["result"] == 5

def test_execute_api_call_invalid_method():
    http = HttpUtils(url="https://fake")
    with pytest.raises(Exception):
        http.execute_api_call("INVALID")

@patch.object(HttpUtils, "token", new_callable=MagicMock, return_value="abc")
def test_execute_api_call_get_with_params(mock_token, monkeypatch):
    class DummyResponse:
        def __init__(self):
            self.text = '{"result": 10}'
        def json(self):
            return {"result": 10}
        def raise_for_status(self):
            # Mock method - no need to raise exceptions in successful test scenario
            pass
    def dummy_get(url, headers, params):
        return DummyResponse()
    monkeypatch.setattr("requests.get", dummy_get)
    http = HttpUtils(url="https://fake")
    result = http.execute_api_call("GET", endpoint="foo", params={"x": 1})
    assert result["result"] == 10

@patch.object(HttpUtils, "token", new_callable=MagicMock, return_value="abc")
def test_execute_api_call_raises(mock_token, monkeypatch):
    class DummyResponse:
        def raise_for_status(self):
            raise Exception("fail")
        @property
        def text(self):
            return ""
        def json(self):
            return {}
    def dummy_get(url, headers, params):
        return DummyResponse()
    monkeypatch.setattr("requests.get", dummy_get)
    http = HttpUtils(url="https://fake")
    with pytest.raises(Exception, match="fail"):
        http.execute_api_call("GET", endpoint="foo", params={}) 