import pytest
from config_merger_api.utils import disambiguation_utils as du

def test_replace_confidence_matrix_recursively():
    obj = {"confidenceMatrix": ["a", "b"], "nested": {"confidenceMatrix": ["a"]}}
    result = du.replace_confidence_matrix_recursively(obj, "a")
    assert "a" not in result["confidenceMatrix"]
    assert "a" not in result["nested"]["confidenceMatrix"]

def test_extract_and_combine_strategy_values():
    data = {"disambiguation": {"strategy": {"fieldLevelConfidenceMatrix": [{"field": "a"}], "valueConfidence": [{"field": "b"}], "aggregation": [{"field": "c"}], "rollingUpFields": ["d"]}}}
    result = du.extract_and_combine_strategy_values(data)
    assert set(result) == {"a", "b", "c", "d"}

def test_inter_candidate_keys_mapper():
    model = {"disambiguation": {"candidateKeys": ["a", {"name": "b"}]}}
    result = du.inter_candidate_keys_mapper(model)
    assert set(result) == {"a", "b"}

def test_intra_rollup_fields_mapper():
    models = [{"disambiguation": {"candidateKeys": ["a"], "strategy": {"rollingUpFields": ["b"]}}}, {"disambiguation": {"candidateKeys": ["c"], "strategy": {"rollingUpFields": ["d"]}}}]
    rollup, keys = du.intra_rollup_fields_mapper(models)
    assert set(rollup) == {"b", "d"} and set(keys) == {"a", "c"} 

def test_filter_disambiguation_folder():
    sol = [{"inventoryModelInput": [{"path": "p1", "name": "n1"}], "confidenceMatrix": ["n1"]}]
    client = [{"inventoryModelInput": [{"path": "p1"}]}]
    result = du.filter_disambiguation_folder(sol, client)
    assert isinstance(result, list)

def test_process_and_write_to_solution_file():
    client = {"disambiguation": {"strategy": {"fieldLevelConfidenceMatrix": [{"field": "a"}], "rollingUpFields": ["b"]}}}
    solution = {"disambiguation": {"strategy": {"fieldLevelConfidenceMatrix": [{"field": "a"}], "rollingUpFields": ["b", "c"]}}}
    result = du.process_and_write_to_solution_file(client, solution)
    assert isinstance(result, list)

def test_merge_candidate_keys():
    sol = [{"config_item_type": "intrasource_disambiguated_models", "config_value": {"disambiguation": {"candidateKeys": ["a"]}}}]
    client = [{"config_item_type": "intrasource_disambiguated_models", "config_value": {"disambiguation": {"candidateKeys": ["b"]}}}]
    s, c = du.merge_candidate_keys(sol, client, "intrasource_disambiguated_models")
    assert isinstance(s, list) and isinstance(c, list)

def test_update_derived_properties():
    data = {"disambiguation": {"candidateKeys": ["primary_key"]}}
    result = du.update_derived_properties(data)
    assert any(p["colName"] == "primary_key" for p in result["derivedProperties"]) 