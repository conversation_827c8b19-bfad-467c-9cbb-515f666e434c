import pytest
from config_merger_api.utils.data_quality_utils import merge_data_quality_models

def test_merge_data_quality_models_global_only():
    sol = [{"name": "test__global", "config_value": {"a": 1}}]
    client = []
    result = merge_data_quality_models(sol, client)
    assert result == {"a": 1}

def test_merge_data_quality_models_data_quality_only():
    sol = [{"name": "test", "config_value": {"b": 2}}]
    client = []
    result = merge_data_quality_models(sol, client)
    assert result == {"b": 2}

def test_merge_data_quality_models_both():
    sol = [
        {"name": "test__global", "config_value": {"a": 1}},
        {"name": "test", "config_value": {"b": 2}}
    ]
    client = []
    result = merge_data_quality_models(sol, client)
    assert result == {"b": 2}  # Only data_quality_config is returned, not merged

def test_merge_data_quality_models_empty():
    assert merge_data_quality_models([], []) == {}

def test_merge_data_quality_models_client_overrides():
    sol = [
        {"name": "test__global", "config_value": {"a": 1}},
        {"name": "test", "config_value": {"b": 2}}
    ]
    client = [
        {"name": "test", "config_value": {"b": 3, "c": 4}}
    ]
    result = merge_data_quality_models(sol, client)
    assert result["b"] == 3 or result["c"] == 4 

def test_merge_data_quality_models_no_configs():
    assert merge_data_quality_models([], []) == {}

def test_merge_data_quality_models_only_client():
    sol = []
    client = [{"name": "test", "config_value": {"x": 42}}]
    result = merge_data_quality_models(sol, client)
    assert result["x"] == 42

def test_merge_data_quality_models_global_and_client():
    sol = [{"name": "test__global", "config_value": {"a": 1}}]
    client = [{"name": "test", "config_value": {"b": 2}}]
    result = merge_data_quality_models(sol, client)
    assert result["b"] == 2 