import pytest
from config_merger_api.utils import inventory_models_utils as imu

def test_file_to_spec_mapper():
    conf = {"entityClass": "foo", "defaultEntitySpec": {"x": 1}, "fieldLevelSpec": [1], "commonProperties": [2], "sourceSpecificProperties": [3], "entitySpecificProperties": [4], "lastUpdateFields": [5]}
    result = imu.file_to_spec_mapper(conf)
    assert result["entity_class"] == "foo"
    assert result["default_spec"]["x"] == 1

def test_file_to_spec_mapper_complete():
    global_entity_config = {
        "entityClass": "TestEntity",
        "defaultEntitySpec": {"persistNonNullValue": True},
        "fieldLevelSpec": [{"colName": "field1"}],
        "commonProperties": [{"colName": "common1"}],
        "sourceSpecificProperties": [{"colName": "source1"}],
        "entitySpecificProperties": [{"colName": "entity1"}],
        "lastUpdateFields": ["field1", "field2"]
    }
    result = imu.file_to_spec_mapper(global_entity_config)
    assert result["entity_class"] == "TestEntity"
    assert result["default_spec"]["persistNonNullValue"] is True
    assert len(result["field_level_spec"]) == 1
    assert len(result["common_properties"]) == 1
    assert len(result["source_specific_properties"]) == 1
    assert len(result["entity_specific_props"]) == 1
    assert len(result["last_update_fields"]) == 2

def test_file_to_spec_mapper_minimal():
    global_entity_config = {}
    result = imu.file_to_spec_mapper(global_entity_config)
    assert result["entity_class"] == ""
    assert result["default_spec"]["persistNonNullValue"] is True
    assert result["field_level_spec"] == []
    assert result["common_properties"] == []
    assert result["source_specific_properties"] == []
    assert result["entity_specific_props"] == []
    assert result["last_update_fields"] == []

def test_update_entity_config():
    data = {"entity": {}}
    imu.update_entity_config(data, "foo", {"x": 1}, [5])
    assert data["entity"]["name"] == "foo"
    assert data["entity"]["fieldSpec"]["x"] == 1
    assert data["entity"]["lastUpdateFields"] == [5]

def test_update_entity_config_extended():
    inventory_data = {"entity": {}}
    entity_class = "TestEntity"
    default_spec = {"persistNonNullValue": True}
    last_update_fields = ["field1", "field2"]
    imu.update_entity_config(inventory_data, entity_class, default_spec, last_update_fields)
    assert inventory_data["entity"]["name"] == "TestEntity"
    assert inventory_data["entity"]["fieldSpec"] == default_spec
    assert inventory_data["entity"]["lastUpdateFields"] == last_update_fields

def test_update_properties():
    data = {"commonProperties": []}
    props = [{"colName": "a"}]
    imu.update_properties(data, props, None, "commonProperties")
    assert data["commonProperties"][0]["colName"] == "a"

def test_update_properties_with_enrichments():
    inventory_data = {"commonProperties": []}
    properties = [
        {"colName": "prop1", "colExpr": "expr1"},
        {"colName": "prop2", "colExpr": "expr2"}
    ]
    enrichments = [
        {"lookupInfo": {"enrichmentColumns": ["enrich1", "enrich2"]}}
    ]
    imu.update_properties(inventory_data, properties, enrichments, "commonProperties")
    assert len(inventory_data["commonProperties"]) == 2
    assert inventory_data["commonProperties"][0]["colName"] == "prop1"
    assert inventory_data["commonProperties"][1]["colName"] == "prop2"

def test_update_properties_without_enrichments():
    inventory_data = {"commonProperties": []}
    properties = [
        {"colName": "prop1", "colExpr": "expr1"}
    ]
    imu.update_properties(inventory_data, properties, None, "commonProperties")
    assert len(inventory_data["commonProperties"]) == 1
    assert inventory_data["commonProperties"][0]["colName"] == "prop1"

def test_update_properties_existing_properties():
    inventory_data = {
        "commonProperties": [
            {"colName": "existing1"},
            {"colName": "existing2"}
        ]
    }
    properties = [
        {"colName": "new1", "colExpr": "expr1"},
        {"colName": "existing1", "colExpr": "expr2"}  # Should not be added
    ]
    imu.update_properties(inventory_data, properties, None, "commonProperties")
    col_names = [p["colName"] for p in inventory_data["commonProperties"]]
    assert "existing1" in col_names
    assert "existing2" in col_names
    assert "new1" in col_names
    assert col_names.count("existing1") == 1

def test_update_common_properties():
    data = {"commonProperties": []}
    props = [{"colName": "a"}]
    imu.update_common_properties(data, props, None)
    assert data["commonProperties"][0]["colName"] == "a"

def test_update_common_properties_extended():
    inventory_data = {"commonProperties": []}
    common_properties = [
        {"colName": "common1", "colExpr": "expr1"}
    ]
    enrichments = [
        {"lookupInfo": {"enrichmentColumns": ["enrich1"]}}
    ]
    imu.update_common_properties(inventory_data, common_properties, enrichments)
    assert len(inventory_data["commonProperties"]) == 1
    assert inventory_data["commonProperties"][0]["colName"] == "common1"

def test_update_entity_specific_properties():
    data = {"entitySpecificProperties": []}
    props = [{"colName": "b"}]
    imu.update_entity_specific_properties(data, props, None)
    assert data["entitySpecificProperties"][0]["colName"] == "b"

def test_update_entity_specific_properties_extended():
    inventory_data = {"entitySpecificProperties": []}
    entity_specific_props = [
        {"colName": "entity1", "colExpr": "expr1"}
    ]
    enrichments = [
        {"lookupInfo": {"enrichmentColumns": ["enrich1"]}}
    ]
    imu.update_entity_specific_properties(inventory_data, entity_specific_props, enrichments)
    assert len(inventory_data["entitySpecificProperties"]) == 1
    assert inventory_data["entitySpecificProperties"][0]["colName"] == "entity1"

def test_update_field_specs():
    data = {"entitySpecificProperties": [{"colName": "a"}]}
    field_spec = [{"colName": "a", "fieldsSpec": {"persistNonNullValue": True}}]
    imu.update_field_specs(data, field_spec)
    assert data["entitySpecificProperties"][0]["fieldsSpec"]["persistNonNullValue"] is True

def test_update_field_specs_with_matching_fields():
    inventory_data = {
        "entitySpecificProperties": [
            {"colName": "field1"},  # No fieldsSpec key at all
            {"colName": "field2", "fieldsSpec": {"existing": True}}
        ]
    }
    field_level_spec = [
        {"colName": "field1", "fieldsSpec": {"persistNonNullValue": True}}
    ]
    imu.update_field_specs(inventory_data, field_level_spec)
    field1 = next(f for f in inventory_data["entitySpecificProperties"] if f["colName"] == "field1")
    field2 = next(f for f in inventory_data["entitySpecificProperties"] if f["colName"] == "field2")
    assert field1["fieldsSpec"]["persistNonNullValue"] is True
    assert field2["fieldsSpec"]["existing"] is True

def test_compare_properties_raises():
    with pytest.raises(Exception):
        imu.compare_properties([{"colName": "a"}], [{"colName": "a"}])

def test_compare_properties_conflict():
    common = [{"colName": "a"}, {"colName": "b"}]
    entity = [{"colName": "b"}, {"colName": "c"}]
    with pytest.raises(Exception, match="Common elements found"):
        imu.compare_properties(common, entity)

def test_compare_properties_no_conflict():
    common = [{"colName": "a"}]
    entity = [{"colName": "b"}]
    imu.compare_properties(common, entity)  # Should not raise

def test_compare_properties_in_global_entity_config():
    sol = [{"commonProperties": [{"colName": "a"}], "entitySpecificProperties": [{"colName": "b"}]}]
    client = [{"commonProperties": [{"colName": "c"}], "entitySpecificProperties": [{"colName": "d"}]}]
    imu.compare_properties_in_global_entity_config(sol, client)  # Should not raise

def test_compare_properties_in_global_entity_config_conflict():
    sol = [{"commonProperties": [{"colName": "a"}], "entitySpecificProperties": [{"colName": "b"}]}]
    client = [{"commonProperties": [{"colName": "c"}], "entitySpecificProperties": [{"colName": "a"}]}]
    with pytest.raises(Exception, match="Common elements found"):
        imu.compare_properties_in_global_entity_config(sol, client)

def test_filter_by_computation_phase():
    props = [
        {"colName": "a", "fieldsSpec": {"computationPhase": "loader"}},
        {"colName": "b", "fieldsSpec": {"computationPhase": "inter"}},
        {"colName": "c"}
    ]
    filtered = imu.filter_by_computation_phase(props, dis_type="loader")
    assert any(p["colName"] == "a" for p in filtered)

def test_filter_by_computation_phase_loader():
    props = [
        {"colName": "a", "fieldsSpec": {"computationPhase": "loader"}},
        {"colName": "b", "fieldsSpec": {"computationPhase": "inter"}},
        {"colName": "c"}  # No computationPhase
    ]
    filtered = imu.filter_by_computation_phase(props, dis_type="loader")
    col_names = [p["colName"] for p in filtered]
    assert "a" in col_names and "c" in col_names

def test_filter_by_computation_phase_inter():
    props = [
        {"colName": "a", "fieldsSpec": {"computationPhase": "loader"}},
        {"colName": "b", "fieldsSpec": {"computationPhase": "inter"}},
        {"colName": "c"}  # No computationPhase
    ]
    filtered = imu.filter_by_computation_phase(props, dis_type="inter")
    col_names = [p["colName"] for p in filtered]
    assert "b" in col_names

def test_merge_inventory_models_basic():
    sol = [
        {"config_item_type": "inventory_models", "config_value": {"entity": {}, "sourceSpecificProperties": []}}
    ]
    client = []
    result = imu.merge_inventory_models(sol, client, config_manipulation=False, is_studio_request=False)
    assert "entity" in result

def test_loader_global_config_merger():
    loader_config = {"entity": {}, "enrichments": []}
    global_entity_config = {
        "entityClass": "TestEntity",
        "defaultEntitySpec": {"persistNonNullValue": True},
        "fieldLevelSpec": [{"colName": "field1", "fieldsSpec": {"persistNonNullValue": True}}],
        "commonProperties": [{"colName": "common1"}],
        "entitySpecificProperties": [{"colName": "entity1"}],
        "lastUpdateFields": ["field1"]
    }
    result = imu.loader_global_config_merger(loader_config, global_entity_config)
    assert result["entity"]["name"] == "TestEntity"
    assert result["entity"]["fieldSpec"]["persistNonNullValue"] is True
    assert result["entity"]["lastUpdateFields"] == ["field1"]
    assert len(result["commonProperties"]) == 1
    assert len(result["entitySpecificProperties"]) == 1 