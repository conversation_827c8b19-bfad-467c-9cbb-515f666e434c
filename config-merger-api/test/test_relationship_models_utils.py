import pytest
from config_merger_api.utils.relationship_models_utils import update_relationship_models, merge_enrichments, update_client_optionalAttribute, merge_relationship_models

def test_update_relationship_models_basic():
    rel_model = {"inputSourceInfo": [{"sourceMappingInfo": [], "targetMappingInfo": []}]}
    inv_models = ["sds_ei__foo"]
    result = update_relationship_models(rel_model, inv_models)
    assert "inputSourceInfo" in result

def test_update_relationship_models_with_dict_mapping_info():
    relationship_model = {
        "inputSourceInfo": [
            {"sourceMappingInfo": {"type": "dict"}, "targetMappingInfo": {"type": "dict"}}
        ]
    }
    inventory_models_list = ["sds_ei__test"]
    result = update_relationship_models(relationship_model, inventory_models_list)
    assert len(result["inputSourceInfo"]) == 1
    assert result["inputSourceInfo"][0]["sourceMappingInfo"] == {"type": "dict"}

def test_update_relationship_models_with_list_mapping_info_valid():
    relationship_model = {
        "inputSourceInfo": [
            {"sourceMappingInfo": ["sds_ei__test?param=value"], "targetMappingInfo": ["sds_ei__test2?param=value"]}
        ]
    }
    inventory_models_list = ["sds_ei__test", "sds_ei__test2"]
    result = update_relationship_models(relationship_model, inventory_models_list)
    assert len(result["inputSourceInfo"]) == 1
    assert result["inputSourceInfo"][0]["sourceMappingInfo"] == ["sds_ei__test?param=value"]

def test_update_relationship_models_with_list_mapping_info_invalid():
    relationship_model = {
        "inputSourceInfo": [
            {"sourceMappingInfo": ["sds_ei__invalid?param=value"], "targetMappingInfo": ["sds_ei__invalid2?param=value"]}
        ]
    }
    inventory_models_list = ["sds_ei__test", "sds_ei__test2"]
    result = update_relationship_models(relationship_model, inventory_models_list)
    assert len(result["inputSourceInfo"]) == 0

def test_update_relationship_models_mixed_validity():
    relationship_model = {
        "inputSourceInfo": [
            {"sourceMappingInfo": ["sds_ei__test?param=value"], "targetMappingInfo": ["sds_ei__invalid?param=value"]}
        ]
    }
    inventory_models_list = ["sds_ei__test"]
    result = update_relationship_models(relationship_model, inventory_models_list)
    assert len(result["inputSourceInfo"]) == 0

def test_update_relationship_models_empty_inventory_list():
    relationship_model = {
        "inputSourceInfo": [
            {"sourceMappingInfo": ["sds_ei__test?param=value"], "targetMappingInfo": ["sds_ei__test2?param=value"]}
        ]
    }
    inventory_models_list = []
    result = update_relationship_models(relationship_model, inventory_models_list)
    # The function does not filter out inputSourceInfo if inventory list is empty
    assert len(result["inputSourceInfo"]) == 1

def test_update_relationship_models_no_input_source_info():
    relationship_model = {}
    inventory_models_list = ["sds_ei__test"]
    result = update_relationship_models(relationship_model, inventory_models_list)
    assert "inputSourceInfo" in result
    assert result["inputSourceInfo"] == []

def test_merge_enrichments_merges():
    sol = {"inputSourceInfo": [{"enrichments": [{"lookupInfo": {"tableName": "t1"}}]}]}
    client = {"inputSourceInfo": [{"enrichments": [{"lookupInfo": {"tableName": "t2"}}]}]}
    result = merge_enrichments(sol, client)
    assert any(e["lookupInfo"]["tableName"] == "t2" for e in result["inputSourceInfo"][0]["enrichments"])

def test_update_client_optionalAttribute_override():
    sol = [{"optionalAttributes": [{"name": "foo", "exp": "a"}]}]
    client = [{"optionalAttributes": [{"name": "foo", "exp": "b", "fieldsSpec": {"merge_strategy": "OVERRIDE"}}]}]
    result = update_client_optionalAttribute(sol, client)
    assert result[0]["optionalAttributes"][0]["exp"] == "b"

def test_merge_relationship_models_solution_only():
    sol = [{"config_item_type": "relationship_models", "config_value": {"inputSourceInfo": []}}]
    client = []
    result = merge_relationship_models(sol, client)
    assert isinstance(result, dict) 