import pytest
from config_merger_api.config_trigger_utils import common_utils, data_dictionary_utils

def test_make_get_request_success(monkeypatch):
    class DummyResponse:
        def raise_for_status(self): 
            # Mock method - no need to raise exceptions in successful test scenario
            pass
        def json(self): return {"ok": True}
    def dummy_get(url, params=None, headers=None):
        return DummyResponse()
    monkeypatch.setattr(common_utils.requests, "get", dummy_get)
    result = common_utils.make_get_request("https://test")
    assert result == {"ok": True}

def test_make_get_request_failure(monkeypatch):
    def dummy_get(url, params=None, headers=None):
        raise Exception("fail")
    monkeypatch.setattr(common_utils.requests, "get", dummy_get)
    with pytest.raises(Exception, match="GET request failed"):
        common_utils.make_get_request("https://fail")

def test_data_dictionary_utils_import():
    assert hasattr(data_dictionary_utils, "__doc__") or True

from config_merger_api.config_trigger_utils.data_dictionary_utils import ConfigTriggerUtils

def test_config_trigger_utils_instantiation():
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    assert hasattr(instance, "process_full_run")

def test_config_trigger_utils_process_full_run():
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    responses = [{"task1": "Success"}, {"task2": "Failure"}]
    result, failed = instance.process_full_run(responses)
    assert result["task1"] == "Success"
    assert result["task2"] == "Failure"
    assert "task2" in failed 

def test_config_trigger_utils_process_task_based_run():
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    responses = [{"task1": "Success"}, {"task2": "Failure"}]
    tasks = ["task1", "task2", "task3"]
    result, failed = instance.process_task_based_run(responses, tasks)
    assert result["task1"] == "Success"
    assert result["task2"] == "Failure"
    assert result["task3"] == "Failure"
    assert "task2" in failed and "task3" in failed

def test_config_trigger_utils_process_responses_full_run(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    monkeypatch.setattr(instance, "process_full_run", lambda responses: ("full", ["fail1"]))
    result, failed = instance.process_responses([{"task": "Failure"}], ["task"], is_full_run=True)
    assert result == "full"
    assert failed == ["fail1"]

def test_config_trigger_utils_process_responses_task_based(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    monkeypatch.setattr(instance, "process_task_based_run", lambda responses, tasks: ("task", ["fail2"]))
    result, failed = instance.process_responses([{"task": "Failure"}], ["task"], is_full_run=False)
    assert result == "task"
    assert failed == ["fail2"]

def test_config_trigger_utils_retry_task_success(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    def dummy_make_get_request(url, params=None):
        return {"task": "Success"}
    monkeypatch.setattr(data_dictionary_utils, "make_get_request", dummy_make_get_request)
    result = instance.retry_task("https://test", "task", "type", 2)
    assert result == "Success"

def test_config_trigger_utils_retry_task_failure(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    def dummy_make_get_request(url, params=None):
        raise Exception("fail")
    monkeypatch.setattr(data_dictionary_utils, "make_get_request", dummy_make_get_request)
    result = instance.retry_task("https://test", "task", "type", 2)
    assert result.startswith("Failure:")

def test_config_trigger_utils_retry_failed_tasks(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    monkeypatch.setattr(instance, "retry_task", lambda url, task, config_item_type, max_retries: f"Retried-{task}")
    result = instance.retry_failed_tasks("https://test", "type", ["task1", "task2"], max_retries=1)
    assert result["task1"] == "Retried-task1"
    assert result["task2"] == "Retried-task2"

def test_config_trigger_utils_trigger_data_dictionary_full_run(monkeypatch):
    class DummyConfigManager:
        def patch_config(self, name, data, params):
            return {"patched": True}
    instance = ConfigTriggerUtils(config_manager=DummyConfigManager(), config_map={}, data={"name": "foo", "details": {"trigger_full_run": True}})
    monkeypatch.setattr(data_dictionary_utils, "make_get_request", lambda url, params=None: [{"foo": "Success"}])
    monkeypatch.setattr(instance, "trigger_status_endpoint", lambda data: {"triggered": data})
    monkeypatch.setenv("EI_CONFIG_MERGE_ENDPOINT", "https://test")
    result = instance.trigger_data_dictionary()
    assert "triggered" in result

def test_config_trigger_utils_trigger_data_dictionary_task_run(monkeypatch):
    class DummyConfigManager:
        def patch_config(self, name, data, params):
            return {"patched": True}
    instance = ConfigTriggerUtils(config_manager=DummyConfigManager(), config_map={}, data={"name": "foo", "details": {"tasks": ["task1"]}})
    monkeypatch.setattr(data_dictionary_utils, "make_get_request", lambda url, params=None: [{"task1": "Success"}])
    monkeypatch.setattr(instance, "trigger_status_endpoint", lambda data: {"triggered": data})
    monkeypatch.setenv("EI_CONFIG_MERGE_ENDPOINT", "https://test")
    result = instance.trigger_data_dictionary()
    assert "triggered" in result

def test_config_trigger_utils_trigger_data_dictionary_no_endpoint(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={"name": "foo", "details": {}})
    monkeypatch.delenv("EI_CONFIG_MERGE_ENDPOINT", raising=False)
    with pytest.raises(ValueError, match="No EI_CONFIG_MERGE_ENDPOINT provided"):
        instance.trigger_data_dictionary()

def test_config_trigger_utils_initiate_trigger_process_success(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    class DummyThread:
        def __init__(self, target, args, daemon): 
            # Mock constructor - just store parameters for testing, no actual thread creation needed
            pass
        def start(self): 
            # Mock method - no actual thread starting needed for this test scenario
            pass
    monkeypatch.setattr(data_dictionary_utils.threading, "Thread", lambda target, args, daemon: DummyThread(target, args, daemon))
    result = instance.initiate_trigger_process({})
    assert result["current_state"] == "trigger_started"

def test_config_trigger_utils_initiate_trigger_process_error(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={})
    def raise_error(*a, **kw): raise Exception("fail")
    monkeypatch.setattr(data_dictionary_utils.threading, "Thread", raise_error)
    result = instance.initiate_trigger_process({})
    assert result["current_state"] == "trigger_operation_failed"

def test_config_trigger_utils_trigger_status_endpoint():
    class DummyConfigManager:
        def patch_config(self, name, data, params):
            return {"patched": True, "name": name, "data": data, "params": params}
    instance = ConfigTriggerUtils(config_manager=DummyConfigManager(), config_map={}, data={"name": "foo", "version": "1.0"})
    result = instance.trigger_status_endpoint({"state": "ok"})
    assert result["patched"] is True
    assert result["name"] == "foo"
    assert result["params"]["version"] == "1.0"

def test_config_trigger_utils_async_trigger_process_success(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={"name": "foo"})
    def dummy_method(data): return {"ok": True}
    config_trigger_method_map = {"foo": dummy_method}
    result = instance.async_trigger_process(config_trigger_method_map)
    assert result == {"ok": True}

def test_config_trigger_utils_async_trigger_process_invalid(monkeypatch):
    class DummyConfigManager:
        def patch_config(self, name, data, params):
            return {"patched": True}
    instance = ConfigTriggerUtils(config_manager=DummyConfigManager(), config_map={}, data={"name": "bar"})
    config_trigger_method_map = {"foo": lambda data: {"ok": True}}
    called = {}
    def fake_trigger_status_endpoint(data): called["called"] = True
    instance.trigger_status_endpoint = fake_trigger_status_endpoint
    result = instance.async_trigger_process(config_trigger_method_map)
    assert called["called"] is True

def test_config_trigger_utils_async_trigger_process_error(monkeypatch):
    instance = ConfigTriggerUtils(config_manager=None, config_map={}, data={"name": "foo"})
    def raise_error(data): raise Exception("fail")
    config_trigger_method_map = {"foo": raise_error}
    called = {}
    def fake_trigger_status_endpoint(data): called["called"] = True
    instance.trigger_status_endpoint = fake_trigger_status_endpoint
    result = instance.async_trigger_process(config_trigger_method_map)
    assert called["called"] is True 