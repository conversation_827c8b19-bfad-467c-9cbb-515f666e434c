import pytest
from config_merger_api.utils.olap_utils import merge_olap_models

def test_merge_olap_models_global_only():
    sol = [{"name": "foo__global", "config_value": {"a": 1}}]
    client = []
    result = merge_olap_models(sol, client)
    assert result == {"a": 1}

def test_merge_olap_models_entity_only():
    sol = [{"name": "foo", "config_value": {"b": 2, "analysisPeriodSpec": 123}}]
    client = []
    result = merge_olap_models(sol, client)
    assert "analysisPeriodSpec" not in result
    assert result["b"] == 2

def test_merge_olap_models_both():
    sol = [
        {"name": "foo__global", "config_value": {"a": 1}},
        {"name": "foo", "config_value": {"b": 2, "analysisPeriodSpec": 123}}
    ]
    client = []
    result = merge_olap_models(sol, client)
    # The function should merge global and entity configs, but entity config takes precedence
    assert result["b"] == 2  # Entity config value
    assert "analysisPeriodSpec" not in result  # Should be removed

def test_merge_olap_models_empty():
    assert merge_olap_models([], []) == {} 

def test_merge_olap_models_only_client():
    sol = []
    client = [{"name": "foo", "config_value": {"z": 9, "analysisPeriodSpec": 123}}]
    result = merge_olap_models(sol, client)
    assert result["z"] == 9
    assert "analysisPeriodSpec" not in result

def test_merge_olap_models_both_with_client():
    sol = [{"name": "foo__global", "config_value": {"a": 1}}]
    client = [{"name": "foo", "config_value": {"b": 2, "analysisPeriodSpec": 123}}]
    result = merge_olap_models(sol, client)
    assert result["b"] == 2
    assert "analysisPeriodSpec" not in result 