ARG PYTHON_BASE_IMAGE="prevalentai/python:4-1-0-3.10.4-bookworm-12.10-20250428-slim"
FROM ${PYTHON_BASE_IMAGE}

WORKDIR "/opt/airflow"
###########
RUN mkdir /opt/airflow/config_merger_api

COPY . /opt/airflow/config_merger_api
COPY ./requirements.txt /opt/airflow/requirements.txt

RUN useradd -ms /bin/bash confmerg && chown -R confmerg:root /opt/airflow \
&& pip install -r /opt/airflow/requirements.txt
USER confmerg

RUN chmod +x /opt/airflow/config_merger_api/entrypoint.sh


EXPOSE 8080

CMD ["/opt/airflow/config_merger_api/entrypoint.sh"]

###
