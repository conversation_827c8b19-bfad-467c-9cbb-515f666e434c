from typing import Dict
import os
from config_merger_api.logger import get_logger

logger = get_logger()

key_dict = {
    "commonProperties": "colName",
    "entitySpecificProperties": "colName",
    "sourceSpecificProperties": "colName",
    "temporaryProperties": "colName",
    "inventoryModelInput": "path",
    "aggregation": "field",
    "fieldLevelConfidenceMatrix": "field",
    "valueConfidence": "field",
    "optionalAttributes": "name",
    "derivedProperties": "colName",
    "inputSourceInfo": "sdmPath",
    "dimensions": "name",
    "relationshipModels":"tableName",
    "countEnriches":"colName"
}


def merge_colExpr(child_item, matching_parent_item):
    """<PERSON>les merging of colExpr field with consideration for `client_first` and `solution_first` preferences."""

    solution_colExpr = matching_parent_item.get("colExpr")
    client_colExpr = child_item.get("colExpr")
    
    merge_strategy="OVERRIDE"

    # Check if merge already happened by checking merged_config in fieldsSpec
    if child_item.get("fieldsSpec", {}).get("merged_config", False):
        # If merged_config is True, return the existing colExpr (no merge needed)
        return client_colExpr  # The merged expression should already be set

    merged_config = False

    merge_strategy_in_spec = child_item.get("fieldsSpec", {}).get("merge_strategy", merge_strategy).upper()

    # If 'PRODUCT_FIRST' is True, prioritize solution_colExpr
    if merge_strategy_in_spec == "PRODUCT_FIRST":
        merged_config = True  # Set merged_config to True
        result = f"COALESCE({solution_colExpr}, {client_colExpr})"
    # If 'CLIENT_FIRST' is True, prioritize client_colExpr
    elif merge_strategy_in_spec == "CLIENT_FIRST":
        merged_config = True  # Set merged_config to True
        result = f"COALESCE({client_colExpr}, {solution_colExpr})"
    elif merge_strategy_in_spec == "OVERRIDE":
        # Default behavior is client_only, so result remains client_colExpr
        merged_config = False  # No merge if default behavior is client_only
        result = client_colExpr
    else:
        raise ValueError(f"Unknown merge_strategy: {merge_strategy_in_spec}")

    # Only set merged_config in fieldsSpec if a merge actually occurred
    if merged_config:
        if "fieldsSpec" not in child_item:
            child_item["fieldsSpec"] = {}
        child_item["fieldsSpec"]["merged_config"] = merged_config

    # Return the result
    return result

def merge_lists(parent_list, child_list, merge_key, prioritize_client_order=False):
    parent_dicts = [item for item in parent_list if isinstance(item, dict)]
    child_dicts = [item for item in child_list if isinstance(item, dict)]
    parent_non_dicts = [item for item in parent_list if not isinstance(item, dict)]
    child_non_dicts = [item for item in child_list if not isinstance(item, dict)]

    # Remove fieldsSpec from solution based on colName in client
    for child_item in child_dicts:
        col_name = child_item.get(merge_key)
        matching_parent_item = next((p_item for p_item in parent_dicts if p_item.get(merge_key) == col_name), None)

        if matching_parent_item:
            # Get colExpr from both parent and child
            solution_colExpr = matching_parent_item.get("colExpr")
            client_colExpr = child_item.get("colExpr")

            # Only merge if both colExprs exist
            if solution_colExpr and client_colExpr:
                merged_colExpr = merge_colExpr(child_item, matching_parent_item)
                if merged_colExpr:
                    child_item["colExpr"] = merged_colExpr
            elif client_colExpr:
                # If only client has colExpr, don't modify it and skip merging
                child_item["colExpr"] = client_colExpr
            # If neither have colExpr, don't add colExpr at all

        if matching_parent_item and 'fieldsSpec' in matching_parent_item:
            del matching_parent_item['fieldsSpec']

        
    merged_dicts = []
    merged_keys = set()

    if prioritize_client_order:
        # Client order first, then extra solution fields
        merged_dicts = child_dicts[:]
        merged_keys = {item.get(merge_key) for item in child_dicts}
        for parent_item in parent_dicts:
            if parent_item.get(merge_key) not in merged_keys:
                merged_dicts.append(parent_item)
    else:
        # Solution order first, then extra client fields
        for parent_item in parent_dicts:
            col_name = parent_item.get(merge_key)
            matching_child_item = next((c_item for c_item in child_dicts if c_item.get(merge_key) == col_name), None)
            if matching_child_item:
                merged_dicts.append(merge_dictionaries(parent_item, matching_child_item))
            else:
                merged_dicts.append(parent_item)
            merged_keys.add(col_name)
        for child_item in child_dicts:
            col_name = child_item.get(merge_key)
            if col_name not in merged_keys:
                merged_dicts.append(child_item)
                merged_keys.add(col_name)

    return merged_dicts + parent_non_dicts + child_non_dicts


def merge_dictionaries(parent_dict: dict, child_dict: dict):
    child_dict = child_dict or {}
    parent_dict = parent_dict or {}

    for key in child_dict:
        if key in parent_dict:
            if isinstance(child_dict[key], list) and isinstance(parent_dict[key], list):
                merge_key = key_dict.get(key)
                if merge_key:
                    prioritize_client = key == "derivedProperties"
                    parent_dict[key] = merge_lists(parent_dict[key], child_dict[key], merge_key, prioritize_client)
                else:
                    parent_dict[key] = child_dict[key]  
            elif not isinstance(child_dict[key], dict) and not isinstance(parent_dict[key], dict):
                    parent_dict[key] = child_dict[key]  

            else:
                parent_dict[key] = merge_dictionaries(parent_dict[key], child_dict[key])
        else:
            parent_dict[key] = child_dict[key]

    return parent_dict


def merge_config(solution_config_list, client_config_list):
    merged_list = []
    solution_config_dict = {}
    client_config_dict = {}
    [solution_config_dict.update({f"{i.get('config_item_type', '')}__{i.get('name')}": i}) if i.get(
        'config_item_type') else solution_config_dict.update({"config": i}) for i in solution_config_list]
    [client_config_dict.update({f"{i.get('config_item_type', '')}__{i.get('name')}": i}) if i.get(
        'config_item_type') else client_config_dict.update({"config": i}) for i in client_config_list]

    for key, solution_config in solution_config_dict.items():
        if key in client_config_dict.keys():
            merged_config = merge_dictionaries(solution_config, client_config_dict.get(key))
            merged_list.append(merged_config)
        else:
            merged_list.append(solution_config)
    for client_config in [value for key, value in client_config_dict.items() if key not in solution_config_dict.keys()]:
        merged_list.append(client_config)
    return merged_list


def strtobool(val):
    """
    Convert a string representation of truth to true (1) or false (0).
    
    True values: y, yes, t, true, on, 1 (case insensitive)
    False values: n, no, f, false, off, 0 (case insensitive)
    
    Args:
        val: String or boolean value to convert
        
    Returns:
        1 for true values, 0 for false values
        
    Raises:
        ValueError: If the value cannot be converted to boolean
    """
    if isinstance(val, bool):
        return int(val)
    
    if isinstance(val, str):
        val = val.lower().strip()
        if val in ('y', 'yes', 't', 'true', 'on', '1'):
            return 1
        elif val in ('n', 'no', 'f', 'false', 'off', '0'):
            return 0
    
    raise ValueError(f"invalid truth value {val!r}")


def extract_source_models_from_data(data):
    """
    Extracts source_models from the new format structure.
    
    Format: data["deployment_config"][0]["config_value"]["spark_job_configs"]["source_models"]
    
    Args:
        data: The input data dictionary
        
    Returns:
        Dictionary containing source models or None if not found
    """
    deployment_configs = data.get("deployment_config", [])
    if deployment_configs and len(deployment_configs) > 0:
        deployment_config = deployment_configs[0]
        config_value = deployment_config.get("config_value", {})
        spark_job_configs = config_value.get("spark_job_configs", {})
        source_models = spark_job_configs.get("source_models", None)
        
        if source_models:
            logger.info("Using source_models from deployment_config")
            return source_models
    
    logger.info("No source_models found in deployment_config")
    return None
