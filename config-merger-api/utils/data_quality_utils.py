import json
from config_merger_api.utils.common_utils import merge_config,merge_dictionaries

def merge_data_quality_models(sol_configs, client_configs):
    data_quality_dict = {}
    merged_configs = merge_config(sol_configs, client_configs)

    print(sol_configs)
    print(client_configs)
    print(merged_configs)

    for i in merged_configs:
        if i["name"].endswith("__global"):
            data_quality_dict["global_data_quality_config"] = i.get("config_value", {})
        else:
            data_quality_dict["data_quality_config"] = i.get("config_value", {})

    return merge_dictionaries(data_quality_dict.get("global_data_quality_config"), data_quality_dict.get("data_quality_config"))