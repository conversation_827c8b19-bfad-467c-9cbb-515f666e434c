# Helper utility for validating inventory model paths against source models
from config_merger_api.logger import LogManager, get_logger

LogManager()
logger = get_logger()

def extract_table_name_from_path(path):
    """
    Extracts the table name from a path like:
    "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid" -> "sds_ei__host__active_directory__object_guid"
    """
    if not path:
        return None

    if "." in path:
        return path.split(".")[-1]
    return path

def get_available_inventory_models(source_models):
    """
    Extracts all available inventory model names from source models
    """
    available_models = set()
    
    if isinstance(source_models, dict):
        inventory_models = source_models.get("inventory_models", [])
        if isinstance(inventory_models, list):
            available_models.update(inventory_models)
    
    return available_models

def get_available_intrasource_models(source_models):
    """
    Extracts all available intrasource disambiguation model names from source models
    """
    available_models = set()

    if isinstance(source_models, dict):
        intrasource_models = source_models.get("intrasource_disambiguated_models", [])
        if isinstance(intrasource_models, list):
            available_models.update(intrasource_models)
    
    return available_models

def validate_inventory_model_input(inventory_model_input, source_models, include_intrasource=False):
    """
    Validates inventoryModelInput for disambiguation.
    
    Args:
        inventory_model_input: List of inventory model input objects with 'path' and 'name'
        source_models: Dictionary containing 'inventory_models' and optionally 'intrasource_disambiguated_models'
        include_intrasource: If True, also checks against intrasource_disambiguated_models (for inter-source)
    
    Returns:
        Tuple of (valid_inventory_inputs, valid_name_fields_set)
        - valid_inventory_inputs: List of valid inventory input objects (with path and name)
        - valid_name_fields_set: Set of 'name' field values to check in confidence matrices
    """
    if not inventory_model_input or not source_models:
        return inventory_model_input or [], set()

    available_inventory = get_available_inventory_models(source_models)
    
    if include_intrasource:
        available_intrasource = get_available_intrasource_models(source_models)
        all_available_models = available_inventory.union(available_intrasource)
        validation_type = "inter-source"
    else:
        all_available_models = available_inventory
        validation_type = "intra-source"
    
    logger.info(f"Available models for {validation_type} validation: {all_available_models}")
    
    validated_inputs = []
    valid_model_names = set()
    removed_count = 0
    
    for input_item in inventory_model_input:
        if not isinstance(input_item, dict):
            continue
            
        path = input_item.get("path", "")
        name = input_item.get("name", "")
        
        table_name = extract_table_name_from_path(path)
        
        is_valid = table_name in all_available_models
        
        if is_valid:
            validated_inputs.append(input_item)
            valid_model_names.add(name)
            logger.debug(f"Keeping inventory model input: {name} (path: {path})")
        else:
            removed_count += 1
            logger.info(f"Removing inventory model input: {name} (path: {path}) - not found in source models")
    
    if removed_count > 0:
        logger.info(f"Removed {removed_count} invalid inventory model inputs for {validation_type} disambiguation")
    
    return validated_inputs, valid_model_names




def clean_confidence_matrix(matrix, valid_name_fields, matrix_type):
    """
    Helper function to clean a confidence matrix by keeping only valid models.
    
    Args:
        matrix: The confidence matrix list to clean
        valid_name_fields: Set of valid name field values
        matrix_type: String describing the matrix type for logging
    
    Returns:
        Cleaned matrix with only valid models (order preserved)
    """
    original_count = len(matrix)
    cleaned_matrix = [model for model in matrix if model in valid_name_fields]
    
    removed_count = original_count - len(cleaned_matrix)
    if removed_count > 0:
        logger.info(f"Removed {removed_count} invalid models from {matrix_type} (order preserved)")
    
    return cleaned_matrix


def clean_field_level_confidence_matrices(field_level_matrices, valid_name_fields):
    """
    Helper function to clean all fieldLevelConfidenceMatrix entries.
    
    Args:
        field_level_matrices: List of field level confidence matrix configurations
        valid_name_fields: Set of valid name field values
    """
    for field_config in field_level_matrices:
        if "confidenceMatrix" in field_config:
            field_name = field_config.get("field", "unknown")
            matrix_type = f"fieldLevelConfidenceMatrix for field '{field_name}'"
            
            field_config["confidenceMatrix"] = clean_confidence_matrix(
                field_config["confidenceMatrix"], 
                valid_name_fields, 
                matrix_type
            )


def clean_disambiguation_references(config, valid_name_fields):
    """
    Cleans disambiguation configuration to only include models that are valid based on inventoryModelInput.
    Preserves the original order while removing invalid models.
    
    Args:
        config: The disambiguation configuration dictionary
        valid_name_fields: Set of valid 'name' field values from validated inventoryModelInput
    
    Returns:
        Updated configuration with only valid model references (order preserved)
    """
    if not config:
        return config
    
    if not valid_name_fields:
        valid_name_fields = set()
    
    logger.info(f"Cleaning disambiguation references to only include valid models: {valid_name_fields}")
    disambiguation = config.get("disambiguation", {})
    if "confidenceMatrix" in disambiguation:
        disambiguation["confidenceMatrix"] = clean_confidence_matrix(
            disambiguation["confidenceMatrix"], 
            valid_name_fields, 
            "disambiguation.confidenceMatrix"
        )
    strategy = disambiguation.get("strategy", {})
    if "fieldLevelConfidenceMatrix" in strategy:
        clean_field_level_confidence_matrices(
            strategy["fieldLevelConfidenceMatrix"], 
            valid_name_fields
        )
    
    return config
