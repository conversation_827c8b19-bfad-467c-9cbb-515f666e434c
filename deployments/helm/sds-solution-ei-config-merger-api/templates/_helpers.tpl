{{/*
Expand the name of the chart.
*/}}
{{- define "eiConfigMergerApi.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "eiConfigMergerApi.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "eiConfigMergerApi.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "eiConfigMergerApi.labels" -}}
helm.sh/chart: {{ include "eiConfigMergerApi.chart" . }}
{{ include "eiConfigMergerApi.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "eiConfigMergerApi.selectorLabels" -}}
app.kubernetes.io/name: {{ include "eiConfigMergerApi.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "eiConfigMergerApi.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "eiConfigMergerApi.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}


{{- define "extra_volumes_mounts" }}
  {{- $Global := . }}
  {{- with .Values.volumeMounts }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "extra_volumes" }}
  {{- $Global := . }}
  {{- with .Values.volumes }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}


{{- define "eiConfigMergerApi.getImageTag" -}}
{{- if index .Values "bundle-version" -}}
{{- $version := index .Values "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- .Values.image.tag -}}
{{- end -}}
{{- end -}}


{{/* User defined ei-config-merger api environment variables */}}
{{- define "custom_ei_config_merger_api_env" }}
  {{- $combinedEnv := dict -}}

  {{- /* Handle common environment variables with conditions */ -}}
  {{- range .Values.env}}
    {{- if .value }}
      {{- $_ := set $combinedEnv .name (dict "value" .value) -}}
    {{- else if .valueFrom }}
      {{- $_ := set $combinedEnv .name (dict "valueFrom" .valueFrom) -}}
    {{- end }}
  {{- end }}


  {{- /* Handle additional environment variables */ -}}
  {{- range .Values.extraEnv }}
    {{- if .value }}
      {{- $_ := set $combinedEnv .name (dict "value" .value) -}}
    {{- else if .valueFrom }}
      {{- $_ := set $combinedEnv .name (dict "valueFrom" .valueFrom) -}}
    {{- end }}
  {{- end }}

  {{- /* Output the environment variables */ -}}
  {{- range $key, $value := $combinedEnv }}
    - name: {{ $key }}
      {{- if $value.valueFrom }}
      valueFrom:
          {{- toYaml $value.valueFrom | nindent 8 }} 
      {{- else if $value.value }}
      value: {{ tpl (quote $value.value) $ }}
      {{- end }}
  {{- end }}

  {{- /* Handle secrets */ -}}
  {{- range $i, $config := .Values.secret }}
    - name: {{ $config.envName }}
      valueFrom:
        secretKeyRef:
          name: {{ tpl $config.secretName $ }}
          key: {{ default "value" $config.secretKey }}
  {{- end }}
{{- end }}
