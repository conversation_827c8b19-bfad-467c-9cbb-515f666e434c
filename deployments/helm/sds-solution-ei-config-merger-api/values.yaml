# Default values for eiConfigMergerApi.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
image:
  repository: prevalentai/sds-solution-ei-configs
  tag: ""
  pullPolicy: IfNotPresent



imagePullSecret: 
  - name: docker-secret
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 5000

env:
- name: CONFIG_MANAGER_ENDPOINT
  value: http://mgmtapis.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:8080/sds_mgmnt/config-manager/api/v1/config-item
- name: STATUS_MANAGER_ENDPOINT
  value: http://mgmtapis.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:8080/sds_mgmnt/config-manager/api/v1/status-manager
- name: EI_CONFIG_MERGE_ENDPOINT
  value: http://ei-config-merger-api.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:5000/ei/config-merge/
- name: KEYCLOAK_URL 
  value: https://{{  .Values.global.KEYCLOAK_DOMAIN_NAME | default .Values.global.SDS3_DOMAIN_NAME }}/realms/{{ .Values.global.DEPLOY_NAMESPACE }}/protocol/openid-connect/token
- name: PYICEBERG_CATALOG__DEFAULT__URI
  value: 'thrift://hivemetastore.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:9083'
- name: AZURE_STORAGE_ANON
  value: "false"
- name: AZURE_STORAGE_ACCOUNT_NAME
  value: "{{  .Values.global.AZURE_STORAGE_ACCOUNT_NAME  }}"
secret:
  - envName: "CLIENT_ID"
    secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
    secretKey: "clientId"
  - envName: "CLIENT_SECRET"
    secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
    secretKey: "clientSecret"


serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "api-sa"

podAnnotations: {}

deploymentAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

resources:
  limits:
    cpu: "2"
    memory: 4Gi
  requests:
    cpu: "1"
    memory: 2Gi

nodeSelector: {}

tolerations: []

affinity: {}

volumes: []

volumeMounts: []

extraEnv: []       
