# Default values for expressionValidatorApi.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
image:  
  repository: prevalentai/spark
  tag: "4-1-0-R-3.5.5-2.13-iceberg-v1-9-bookworm-12.10-********-slim"
  pullPolicy: IfNotPresent

initContainer:
  kubectl:
    image:
      repository: prevalentai/devops-utils
      tag: "4-2-0-T-kubectl1.31.7-awscliv2-azcpv10-azcli-bookworm-12.10-********-slim"
  command:
    aws: ["bash", "-c", "/tmp/aws/bin/aws s3 cp s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE}}/sds/data-analytics/lib/latest/sds-ei-validator.jar /shared/jar/"]
    azure: ["bash", "-c", "az login --output none --only-show-errors --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID; az storage blob download --output none --only-show-errors --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name /{{ .Values.global.DEPLOY_NAMESPACE}}/sds/data-analytics/lib/latest/sds-ei-validator.jar --file /shared/jar/sds-ei-validator.jar --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login"]



imagePullSecret:
  - name: docker-secret
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 9000

env:
  common:
    - name: AUTH_INTROSPECT_URL
      value: "https://{{  .Values.global.KEYCLOAK_DOMAIN_NAME | default .Values.global.SDS3_DOMAIN_NAME }}/realms/{{ .Values.global.DEPLOY_NAMESPACE }}/protocol/openid-connect/token/introspect"
    - name: SDS_DOMAIN_WITH_PORT
      value: "{{  .Values.global.SDS3_DOMAIN_NAME  }}:443"
    - name: LINEAGE_API_URL
      value: "https://{{ .Values.global.SDS3_DOMAIN_NAME }}/sds_mgmnt/config-manager/api/v1/lineage/?deployment_config=true"
    - name: CONFIG_API_URL
      value: "https://{{ .Values.global.SDS3_DOMAIN_NAME }}/sds_mgmnt/config-manager/api/v1/config-item"
    - name: DOMAIN_NAME
      value: "https://{{ .Values.global.SDS3_DOMAIN_NAME }}"
    - name: REDIS_DATABASE
      value: "3"
    - name: VALIDATOR_API_PLAY_MAXMEMORYBUFFER
      value: "5000kilobytes"
    - name: VALIDATOR_API_PLAY_MAXMEMORYBUFFER
      value: "5000kilobytes"
    - name: VALIDATOR_SCHEMA
      value: "ei_validator_{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: VALIDATOR_PUBLISH_SCHEMA
      value: "ei_pub_validator_{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: VALIDATOR_EI_ENRICH_SCHEMA
      value: "ei_enrich_validator_{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: VALIDATOR_EI_FRAGMENT_SCHEMA
      value: "ei_fragment_validator_{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: CONFIG_META_ENDPOINT
      value: "list-configs-meta/?solution_edition=new"
    - name: OUTPUT_TABLE_LIMIT
      value: "1000"
    - name: UPGRADE_STATUS_API_ENDPOINT
      value: "sds_mgmnt/upgrade-manager/check-upgrade-status/ei"
    - name: EXECUTION_MODE
      value: PARALLEL
    - name: SRDM_INTERVAL_DAYS
      value: "10"
    - name: NAMESPACE
      value: "{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: DISABLE_QUERY_VALIDATION
      value: "false"
    - name: QUERY_CACHE_ENABLED
      value: "false"
    
  aws:
    - name: REDIS_HOST
      value: "{{ .Values.global.AWS_CACHE_REDIS_ENDPOINT }}"
    - name: REDIS_PORT
      value: "6379"
    - name: REDIS_AUTH_ENABLED
      value: "false"
  azure:
    - name: REDIS_HOST
      value: "{{ .Values.global.AZURE_CACHE_REDIS_ENDPOINT }}"
    - name: REDIS_PORT
      value: "6380"
    - name: REDIS_AUTH_ENABLED
      value: "true"

secret:
- envName: "AUTH_CLIENT_ID"
  secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
  secretKey: "clientId"
- envName: "AUTH_CLIENT_SECRET"
  secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
  secretKey: "clientSecret"
- envName: "OIDC_CLIENT_ID"
  secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
  secretKey: "clientId"
- envName: "OIDC_CLIENT_SECRET"
  secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
  secretKey: "clientSecret"
- envName: "REDIS_PASSWORD"
  secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
  secretKey: "redisPassword"

configMap:
  enabled: true
  common:
    spark.master: "k8s://https://kubernetes.default.svc"
    spark.kubernetes.namespace: "{{ .Values.global.DEPLOY_NAMESPACE }}"
    spark.sql.codegen.wholeStage: "false"
    spark.dynamicAllocation.enabled: "true"
    spark.dynamicAllocation.maxExecutors: "10"
    spark.kubernetes.container.image.pullPolicy: Always
    spark.kubernetes.container.image.pullSecrets: docker-secret
    spark.kubernetes.authenticate.driver.serviceAccountName: spark
    spark.kubernetes.authenticate.executor.serviceAccountName: spark
    spark.kubernetes.authenticate.serviceAccountName: spark
    spark.network.timeout: 10000001s
    spark.driver.port: "22321"
    spark.blockManager.port: "22322"
    spark.driver.host: "expression-validator-api"
    spark.driver.bindAddress: "0.0.0.0"
    spark.kubernetes.executor.annotation.sidecar.istio.io/inject: "false"
    spark.kubernetes.executor.annotation.sidecar.cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
    spark.app.name: SDS EI validator Session
    spark.driver.memory: "8g"
    spark.kubernetes.container.image: prevalentai/spark:4-1-0-R-3.5.5-2.13-iceberg-v1-9-bookworm-12.10-********-slim
    spark.kubernetes.driver.container.image: prevalentai/spark:4-1-0-R-3.5.5-2.13-iceberg-v1-9-bookworm-12.10-********-slim
    spark.kubernetes.executor.container.image: prevalentai/spark:4-1-0-R-3.5.5-2.13-iceberg-v1-9-bookworm-12.10-********-slim
    spark.sds.publisher.writebackBasePath: "http://mgmtapis.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:8080/sds_mgmnt/config-manager/api/v1/config-item/list-configs-meta/?config_item_type=publisher&deployed=true"
    spark.serializer: org.apache.spark.serializer.KryoSerializer
    spark.sql.extensions: org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions
    spark.sql.catalog.iceberg_catalog: org.apache.iceberg.spark.SparkCatalog
    spark.sql.catalog.iceberg_catalog.type: hive
    spark.sds.restapi.eiSparkConfigsBasePath: "/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/"
    spark.sds.restapi.configArtifactoryUri: "http://mgmtapis.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:8080"
    spark.sql.catalog.iceberg_catalog.uri: "thrift://hivemetastore.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:9083"
    spark.sql.defaultCatalog: iceberg_catalog
    spark.sql.catalogImplementation: hive
    spark.hadoop.hive.metastore.uris: "thrift://hivemetastore.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:9083"
    
    spark.local.dir: /tmp/local-dir-1
    spark.sds.restapi.oidcAuthEnabled: "true"
    spark.sds.restapi.oidcUrl: https://{{  .Values.global.KEYCLOAK_DOMAIN_NAME | default .Values.global.SDS3_DOMAIN_NAME }}/realms/{{ .Values.global.DEPLOY_NAMESPACE }}/protocol/openid-connect/token
    spark.sds.config.artifactory.uri: "http://mgmtapis.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:8080"
    spark.sds.config.delta.base-path: "/sds_mgmnt/config-manager/api/v1/config-delta"
    spark.sds.config.item.base-path: "/sds_mgmnt/config-manager/api/v1/config-item"
    spark.sql.caseSensitive: "true"
    spark.sds.iceberg.table.write.parquet.compression-codec: "snappy"
    spark.sds.iceberg.table.write.spark.accept-any-schema: "true"
    spark.sql.iceberg.merge-schema: "true"
    spark.sql.iceberg.set-all-nullable-field: "true"
    spark.sql.iceberg.check-ordering: "false"
    spark.sql.shuffle.partitions: "100"
    spark.executor.cores: "2"
    spark.executor.instances: "2"
    spark.executor.memory: "10g"
    spark.eventLog.enabled: "true"
  aws:
    spark.sql.catalog.iceberg_catalog.warehouse: s3a://{{ .Values.global.S3_DATALAKE_BUCKET_NAME }}/iceberg/
    spark.checkpoint.dir: s3a://{{ .Values.global.S3_DATALAKE_BUCKET_NAME }}/ei-checkpoint/
    spark.hadoop.fs.s3a.aws.credentials.provider: software.amazon.awssdk.auth.credentials.WebIdentityTokenFileCredentialsProvider
    spark.kubernetes.executor.annotation.eks.amazonaws.com/role-arn: "{{ .Values.global.SERVICE_ACCOUNT_ANNOTATION_ROLE_ARN }}"
    spark.kubernetes.authenticate.submission.caCertFile: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    spark.kubernetes.authenticate.submission.oauthTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    spark.hadoop.fs.s3a.impl: org.apache.hadoop.fs.s3a.S3AFileSystem
    spark.hadoop.fs.s3a.endpoint: s3.{{ .Values.global.AWS_REGION }}.amazonaws.com
    spark.jars: s3a://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar
    spark.eventLog.dir: "s3a://{{ .Values.global.S3_LOGS_BUCKET_NAME }}/spark-history/"
  azure:
    spark.hadoop.fs.azure.account.auth.type: OAuth
    spark.hadoop.fs.azure.account.oauth.provider.type: org.apache.hadoop.fs.azurebfs.oauth2.WorkloadIdentityTokenProvider
    spark.hadoop.fs.azure.account.oauth2.msi.tenant: "{{ .Values.global.MICROSOFT_K8S_TENANT_ID  }}"
    spark.hadoop.fs.azure.account.oauth2.client.id: "{{ .Values.global.AZURE_MANAGED_IDENTITY_ID  }}"
    spark.hadoop.fs.azure.account.oauth2.token.file: /var/run/secrets/azure/tokens/azure-identity-token
    spark.sql.catalog.iceberg_catalog.warehouse: "abfs://{{ .Values.global.BLOB_DATALAKE_CONTAINER_NAME  }}@{{.Values.global.AZURE_STORAGE_ACCOUNT_NAME}}.dfs.core.windows.net/iceberg/"
    spark.checkpoint.dir: "abfs://{{ .Values.global.BLOB_DATALAKE_CONTAINER_NAME  }}@{{.Values.global.AZURE_STORAGE_ACCOUNT_NAME}}.dfs.core.windows.net/ei-checkpoint/"
    spark.jars: abfs://{{ .Values.global.BLOB_APPS_CONTAINER_NAME }}@{{.Values.global.AZURE_STORAGE_ACCOUNT_NAME}}.dfs.core.windows.net/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar
    spark.eventLog.dir: "abfs://{{ .Values.global.BLOB_LOGS_CONTAINER_NAME }}@{{.Values.global.AZURE_STORAGE_ACCOUNT_NAME}}.dfs.core.windows.net/spark-history/"
    spark.kubernetes.executor.label.azure.workload.identity/use: "true"

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: spark

podAnnotations: {}

deploymentAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local



resources:
  limits:
    cpu: 3500m
    memory: 14000Mi
  requests:
    cpu: 3000m
    memory: 12000Mi

autoscaling:
  enabled: false
#   minReplicas: 1
#   maxReplicas: 100
#   targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: 
    jobtype: "medium"


tolerations: 
    - key: "job-resource"
      operator: "Equal"
      value: "medium"
      effect: "NoSchedule"
    - key: "kubernetes.azure.com/scalesetpriority"
      operator: "Equal"
      value: "spot"
      effect: "NoSchedule"

affinity: {}

volumeMounts: []

volumes: []

extraEnv: []

SIGNED_CERT: "{{ .Values.global.SELF_SIGNED_CERT }}"

livenessProbe:
  httpGet:
    path: /healthcheck  # Correct endpoint
    port: 9000
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 3

readinessProbe:
  httpGet:
    path: /healthcheck  # Correct endpoint
    port: 9000
  initialDelaySeconds: 5
  periodSeconds: 10
