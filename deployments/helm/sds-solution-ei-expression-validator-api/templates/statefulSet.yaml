apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: expression-validator-{{ .Release.Name }}
  {{- with .Values.deploymentAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    app: expression-validator-api-sts-{{ .Values.global.ENVIRONMENT }}
    env: {{ .Release.Namespace }}
    ns: {{ .Release.Namespace }}
    application_name: expression-validator-api
    {{- include "expressionValidatorApi.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      app: expression-validator-api
      env: {{ .Release.Namespace }}
      {{- include "expressionValidatorApi.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
        azure.workload.identity/use: "true"
        {{ end }}
        app: expression-validator-api
        env: {{ .Release.Namespace }}
        ns: {{ .Release.Namespace }}
        application_name: expression-validator-api
        sds_app_type: application
        {{- include "expressionValidatorApi.selectorLabels" . | nindent 8 }}
    spec:
      initContainers:
      - name: validator-jar-copy
        image: {{ .Values.initContainer.kubectl.image.repository }}:{{ .Values.initContainer.kubectl.image.tag }}
        securityContext:
          runAsUser: 1000
          runAsGroup: 1000   
          fsGroup: 1000
        volumeMounts:
        - name: shared-jar
          mountPath: /shared/jar/
        {{- with .Values.initContainer.command }}
        {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
        command: {{ tpl (toYaml .aws) $ | nindent 12 }}
        {{ else if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
        command: {{ tpl (toYaml .azure) $ | nindent 12 }}
      {{- end }}
      {{- end }}
      {{- with .Values.imagePullSecret }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "expressionValidatorApi.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: expression-validator-api
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
          - java
          - -cp 
          - "$(JARS_PATH)"
          - -Djava.net.preferIPv6Addresses=false
          - -XX:+IgnoreUnrecognizedVMOptions
          - --add-opens=java.base/java.lang=ALL-UNNAMED
          - --add-opens=java.base/java.lang.invoke=ALL-UNNAMED
          - --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
          - --add-opens=java.base/java.io=ALL-UNNAMED
          - --add-opens=java.base/java.net=ALL-UNNAMED
          - --add-opens=java.base/java.nio=ALL-UNNAMED
          - --add-opens=java.base/java.util=ALL-UNNAMED
          - --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
          - --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED
          - --add-opens=java.base/jdk.internal.ref=ALL-UNNAMED
          - --add-opens=java.base/sun.nio.ch=ALL-UNNAMED
          - --add-opens=java.base/sun.nio.cs=ALL-UNNAMED
          - --add-opens=java.base/sun.security.action=ALL-UNNAMED
          - --add-opens=java.base/sun.util.calendar=ALL-UNNAMED
          - --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED
          - -Djdk.reflect.useDirectMethodHandle=false
          - play.core.server.ProdServerStart
          resources:
            {{- toYaml .Values.resources | nindent 12  }}
          env:
          - name: SPARK_CONFIGS
            valueFrom:
              configMapKeyRef:
                name: sds-ei-validator-spark-configs
                key: sparkConfigs
          - name: JARS_PATH
            valueFrom:
              configMapKeyRef:
                name: ei-validator-jars-path-cm
                key: JARS_PATH
          {{- include "custom_expression_validator_api_environment" . | indent 8 }}
          volumeMounts:
            - name: shared-jar
              mountPath: /shared/jar/
            {{- if eq ( tpl .Values.SIGNED_CERT . ) "true" }}
            - name: java11cacerts
              mountPath: /usr/lib/jvm/java-17-amazon-corretto/lib/security/cacerts
              subPath: cacerts
              readOnly: true
            {{ end }}
            {{- include "extra_volumes_mounts" . | indent 10 }}
          {{- if .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          {{- end }}
            
      volumes:
        - name: shared-jar
          emptyDir: {}
        {{- if eq ( tpl .Values.SIGNED_CERT . ) "true" }}
        - name: java11cacerts
          secret:
            secretName: java11cacerts
            items:
            - key: cacerts
              path: cacerts
        {{ end }}
        {{- include "extra_volumes" . | indent 6 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
