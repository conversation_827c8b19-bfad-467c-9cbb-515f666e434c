apiVersion: v1
kind: Service
metadata:
  labels:
    app: expression-validator-api-sts
    env: {{ .Release.Namespace }}
    {{- include "expressionValidatorApi.labels" . | nindent 4 }}
  name: expression-validator-api
spec:
  clusterIP: None
  type: {{ .Values.service.type }}
  ports:
  - port: {{ .Values.service.port }}
    protocol: TCP
    targetPort: {{ .Values.service.port }}
    name: api-port
  - name: driver-rpc-port
    protocol: TCP 
    port: 2231
    targetPort: 2231
  - name: blockmanager
    protocol: TCP 
    port: 2232
    targetPort: 2232
  selector:
    app: expression-validator-api
    application_name: expression-validator-api
    sds_app_type: application
    env: {{ .Release.Namespace }}
    {{- include "expressionValidatorApi.selectorLabels" . | nindent 4 }}
