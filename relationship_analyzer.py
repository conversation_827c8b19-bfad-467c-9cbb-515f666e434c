#!/usr/bin/env python3
"""
Script to analyze relationship data dictionary JSON files and extract relationship information.
"""

import json
import os
import csv
from pathlib import Path

def extract_relationship_info(file_path):
    """
    Extract relationship information from a JSON file.
    
    Args:
        file_path (str): Path to the JSON file
        
    Returns:
        tuple: (relationship_name, source_entity, target_entity)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract relationship name from filename (remove .json extension)
        relationship_name = Path(file_path).stem
        
        # Extract source and target entities
        source_entity = data.get('source_entity', 'unknown')
        target_entity = data.get('target_entity', 'unknown')
        
        return relationship_name, source_entity, target_entity
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        relationship_name = Path(file_path).stem
        return relationship_name, 'unknown', 'unknown'

def analyze_relationship_directory(directory_path):
    """
    Analyze all JSON files in the relationship data dictionary directory.
    
    Args:
        directory_path (str): Path to the directory containing JSON files
        
    Returns:
        list: List of tuples containing (relationship_name, source_entity, target_entity)
    """
    relationships = []
    
    # Get all JSON files in the directory
    directory = Path(directory_path)
    json_files = list(directory.glob('*.json'))
    
    print(f"Found {len(json_files)} JSON files to process...")
    
    for json_file in json_files:
        relationship_info = extract_relationship_info(json_file)
        relationships.append(relationship_info)
        print(f"Processed: {relationship_info[0]}")
    
    # Sort by relationship name
    relationships.sort(key=lambda x: x[0])
    
    return relationships

def generate_csv_output(relationships, output_file='relationship_analysis.csv'):
    """
    Generate CSV output with relationship information.
    
    Args:
        relationships (list): List of relationship tuples
        output_file (str): Output CSV file name
    """
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['relationship_name', 'source_entity', 'target_entity'])
        
        # Write data
        for relationship in relationships:
            writer.writerow(relationship)
    
    print(f"\nCSV output written to: {output_file}")

def main():
    """Main function to execute the analysis."""
    # Directory path
    directory_path = 'configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_relationship_data_dictionary'
    
    print("Starting relationship analysis...")
    print(f"Analyzing directory: {directory_path}")
    
    # Analyze relationships
    relationships = analyze_relationship_directory(directory_path)
    
    # Generate CSV output
    generate_csv_output(relationships)
    
    # Print summary
    print(f"\nSummary:")
    print(f"Total relationships analyzed: {len(relationships)}")
    print(f"Unique source entities: {len(set(r[1] for r in relationships))}")
    print(f"Unique target entities: {len(set(r[2] for r in relationships))}")
    
    # Print first few entries as preview
    print(f"\nFirst 10 relationships:")
    for i, (name, source, target) in enumerate(relationships[:10]):
        print(f"{i+1:2d}. {name} | {source} -> {target}")

if __name__ == "__main__":
    main()
