@Library('sds-common') _

pipeline {
    agent {
        kubernetes {
            label 'debian'
            defaultContainer 'debian'
        }
    }
    parameters {
        string(name: 'bundle_version', defaultValue: '<branch.version>-<date>-<hash-value>[Eg: 1.0.0-20250123-095459026501734]', description: 'Enter the version number to promote from DEV to PROD') 
    }
    environment {
        monoRepoName = 'sds-solution-ei'
        helmAgentLabel = 'helm'
        helmAgentDefaultContainer = 'helm3-11-7'
        jfrogArtifactoryURL = 'https://prevalentai.jfrog.io'
        acrregistryName = 'paiproductacrregistry'
        jfrogDockerRepoName = 'docker-generic-local'
        skopeoAgentLabel = 'dockerPromote'
        skopeoAgentContainer = 'skopeo'
    }

    stages {
        stage('Helm Promotion to Dockerhub'){
            agent {
                    kubernetes {
                        label "${helmAgentLabel}"
                        defaultContainer "${helmAgentDefaultContainer}"
                    }
            }
            steps {
                script {
                try{
                    sh 'apt-get update && apt install curl -y '
                    withCredentials([usernamePassword(credentialsId: 'jfrog-credentials', usernameVariable: 'ARTIFACTORY_USER', passwordVariable: 'ARTIFACTORY_PASSWORD')]) {
                    sh """
                    curl -s -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD -H -H 'Content-Type: application/json' --fail \\
                    ${jfrogArtifactoryURL}/lifecycle/api/v2/release_bundle/records/${monoRepoName}/${bundle_version} > bundle.json
                    """
                    
                    } 
                }catch (Exception e) {
                        echo "ERROR: Failed to fetch release bundle. Details: ${e}"
                        currentBuild.result = 'FAILURE'
                        // Optionally, send a notification or perform cleanup here
                    }
                withCredentials([usernamePassword(credentialsId: 'acr-credentials', usernameVariable: 'ACR_USER', passwordVariable: 'ACR_PASSWORD'),
                    usernamePassword(credentialsId: 'docker-creds', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
                    // Login to Docker Hub Helm registry
                    sh 'echo "$DOCKER_PASSWORD" | helm registry login -u "$DOCKER_USERNAME" --password-stdin registry-1.docker.io'

                    // Login to Azure ACR Helm registry
                    sh "helm registry login ${acrregistryName}.azurecr.io --username $ACR_USER --password $ACR_PASSWORD"

                    // Now you can helm pull/push from both ACR and Docker Hub
                    def chartNames = ["${monoRepoName}", "${monoRepoName}-jobs", "${monoRepoName}-deployer"]
                    for (name in chartNames) {
                        def chart = "${name}-${bundle_version}.tgz"
                        echo "Processing chart: ${chart}"

                        def acrChart = "oci://${acrregistryName}.azurecr.io/prevalentai/${name}"
                        def dockerChart = "oci://registry-1.docker.io/prevalentai"

                        sh """
                            helm pull ${acrChart} --version ${bundle_version}
                            helm push ${chart} ${dockerChart}
                            rm -f ${chart}
                        """
                    }
                
                }
            }

        }
        }
        stage('Prepare Image List') {
            agent {
                kubernetes {
                    label "${helmAgentLabel}"
                    defaultContainer "${helmAgentDefaultContainer}"
                }
            }
            steps {
                script {
                    jfartifactory.prepareImagesToPromote(
                        jfrogArtifactoryURL,
                        monoRepoName,
                        jfrogDockerRepoName,
                        docker_registery,
                        params.bundle_version
                    )
                }
            }
        }
        stage('Promote Images') {
            agent {
                kubernetes {
                    label "${skopeoAgentLabel}"
                    defaultContainer "${skopeoAgentContainer}"
                }
            }
            steps {
                script {
                    unstash 'images-list'
                    def imageNames = readFile('images-to-promote.txt').split('\n') as List
                    echo "Read ${imageNames.size()} images to promote."

                    def batchSize = 5
                    def chunked = imageNames.collate(batchSize)
                    def parallelTasks = [:]

                    chunked.eachWithIndex { batch, idx ->
                        def batchName = "Batch-${idx + 1}"
                        parallelTasks[batchName] = {
                            batch.each { imageTag ->
                                echo "Promoting image: ${imageTag}"
                                sh """
                                    skopeo copy --all --src-tls-verify=false --dest-tls-verify=false docker://${acrregistryName}.azurecr.io/${imageTag} docker://docker.io/${imageTag}
                                """
                                echo "Successfully promoted image: ${imageTag}"
                            }
                        }
                    }

                    parallel parallelTasks
                }
            }
        }
        stage('Promotion of release bundle'){
            steps{
                script{

                    promoteBuild.promotiontoprod()

                }
            }
        }
    
}
}