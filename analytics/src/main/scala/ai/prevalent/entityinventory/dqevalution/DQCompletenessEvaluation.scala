package ai.prevalent.entityinventory.dqevalution

import ai.prevalent.entityinventory.common.configs.{DQJobArgs, DictionaryAttribute}
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.entityinventory.dqevalution.configs.specs.{Config, ConfigValue, DataDictionaryConfig}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{KG_CONTENT_TYPE, LAST_FOUND, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.utils.{DQEntityEvaluationUtils, DQRelationshipEvaluationUtils}
import ai.prevalent.entityinventory.utils.SparkUtil.unionByName
import org.apache.spark.sql.{<PERSON>umn, DataFrame, SparkSession, functions}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

object DQCompletenessEvaluation extends SDSSparkBaseConfigurable[DQJobArgs, (Config, DataDictionaryConfig)] {

  /**
   * Entry point for the job. Enriches data and evaluates completeness.
   */
  def build(config: (Config, DataDictionaryConfig), jobArgs: DQJobArgs, reader: SDSTableReader, writer: SDSTableWriter): Unit = {
    val tables = jobArgs.getListOfTables
    val objectName = jobArgs.objectName
    val objectType = jobArgs.objectType
    val dqConfig: ConfigValue = config._1.config_value
    val dataDict: Map[String, DictionaryAttribute] =
      if (objectType == "entity") config._2.config_value.attributes
      else config._2.config_value.relationship_attributes

    LOGGER.info(s"DQ tables -> $tables")
    LOGGER.info(s"DQ objectName -> $objectName")
    LOGGER.info(s"DQ objectType -> $objectType")

    val enrichedDFs = (objectType match {
      case "relationship" =>
        DQRelationshipEvaluationUtils.enrichWithResolver(tables, objectName, reader, jobArgs.currentUpdateDate)
      case _ =>
        DQEntityEvaluationUtils.enrichWithResolver(tables, objectName, reader, jobArgs.currentUpdateDate)
    })

    val dqSchema = spark.conf.get("spark.sds.kg.dq.schema", "ei")
    LOGGER.info(s"DQ Schema $dqSchema")

    val (idCol, disambCol, objectTypeName) = (objectType match {
      case "relationship" => (DQRelationshipEvaluationUtils.idCol, DQRelationshipEvaluationUtils.disambCol, DQRelationshipEvaluationUtils.objectTypeName)
      case _ =>  (DQEntityEvaluationUtils.idCol, DQEntityEvaluationUtils.disambCol, DQEntityEvaluationUtils.objectTypeName)
    })

    val resultDF = evaluateCompleteness(enrichedDFs, objectType, dataDict, dqConfig, idCol, disambCol, objectTypeName)
    val finalDF = resultDF.withColumn("kg_job_type", lit(jobArgs.kgJobType))
    val outputTable =
      if (objectType == "relationship")
        s"$dqSchema.sds_ei__rel__${jobArgs.objectName.toLowerCase()}__dq_completeness"
      else
        s"$dqSchema.sds_ei__${jobArgs.objectName.toLowerCase()}__dq_completeness"
    writer.overwritePartition(finalDF, outputTable, Array(days(col(UPDATED_AT_TS)), col(objectTypeName), col("table_name")))
  }

  /**
   * Computes completeness score for a given column based on its type and values.
   *
   * Rules:
   * - Null -> null (not applicable)
   * - String == "No Data" => 0
   * - Array[String] == ["No Data"] => 0
   * - Any other value => 1
   */
  def computeScoreColumn(
                          colName: String, dataType: DataType,
                          dataDict: Map[String, DictionaryAttribute], dqConfig: ConfigValue): Column = {
    val colRef = col(colName)
    val naIdentifier = dqConfig.completeness.noDataIdentifier

    // Helper for array types
    def arrayHandler(naVal: Any) =
      when(colRef.isNull, lit(0))
        .when((size(colRef) === 0) || (size(colRef) === 1 && colRef.getItem(0).isNull), lit(0))
        .when(colRef === array(lit(naVal)), lit(null))
        .otherwise(lit(1))
        .alias(colName)

    // Helper for scalar types
    def scalarHandler(naVal: Any) =
      when(colRef.isNull, lit(0))
        .when(colRef === naVal, lit(null))
        .otherwise(lit(1))
        .alias(colName)

    // Type-specific logic
    dataType match {
      case ArrayType(StringType, _) =>
        arrayHandler(naIdentifier)
      case StringType =>
        scalarHandler(naIdentifier)
      case ArrayType(LongType, _) =>
        val longNa = if (dataDict.get(colName).exists(_.`type`.contains("timestamp"))) 0L else Long.MinValue
        arrayHandler(longNa)
      case LongType =>
        val longNa = if (dataDict.get(colName).exists(_.`type`.contains("timestamp"))) 0L else Long.MinValue
        scalarHandler(longNa)
      case ArrayType(IntegerType, _) =>
        arrayHandler(Int.MinValue)
      case IntegerType =>
        scalarHandler(Int.MinValue)
      // Add more types here as needed
      case _ =>
        when(colRef.isNull, lit(0)).otherwise(lit(1)).alias(colName)
    }
  }

  /**
   * Evaluates completeness for a sequence of dataframes.
   * Computes:
   * - A score column per attribute
   * - A total completeness score (sum of all score columns)
   * - Number of non-null attributes (not "No Data"/null)
   */
  def evaluateCompleteness(
                            dfs: Map[String, DataFrame], objectType: String,
                            dataDict: Map[String, DictionaryAttribute],
                            dqConfig: ConfigValue,
                            idCol: String,
                            disambCol: String,
                            objectTypeName: String): DataFrame = {
    // Determine column names based on objectType
    val excludedCols = dqConfig.completeness.skipColumns

    LOGGER.info("Starting Completeness evaluation....")

    val processedDFs = dfs.map {
      case (tableName, df) =>
        LOGGER.info(s"Completeness $tableName in-progress")

        val schema = df.schema
        val includedFields = schema.filterNot(field => excludedCols.contains(field.name))

        // Create struct of completeness scores
        val completenessScores: Seq[Column] = includedFields.map(f => computeScoreColumn(f.name, f.dataType, dataDict, dqConfig: ConfigValue))

        val dfWithStructScores =
          if (completenessScores.nonEmpty)
            df.withColumn("completeness_attr_scores", struct(completenessScores: _*))
          else
            df.withColumn("completeness_attr_scores", lit(null))

        // Computation of completeness total and non-null attribute count in a single pass
        val (completenessTotalCol, nonNullCol) = {
          val scoreCols = includedFields.map(f => col(s"completeness_attr_scores.${f.name}"))
          val coalescedScores = scoreCols.map(sc => coalesce(sc, lit(0)))
          val nonNullScores = scoreCols.map { sc =>
            when(sc.isNull, lit(0))
              .when(sc === 1 || sc === 0, lit(1))
              .otherwise(lit(0))
          }
          (
            coalescedScores.reduceOption(_ + _).getOrElse(lit(0)).alias("cqs_total"),
            nonNullScores.reduceOption(_ + _).getOrElse(lit(0)).alias("number_cq_attributes")
          )
        }

        val dfWithTotal = dfWithStructScores
          .withColumn("cqs_total", completenessTotalCol)
          .withColumn("number_cq_attributes", nonNullCol)

        // Combine cqs_total and number_cq_attributes to compute average completeness
        val dfWithScore = dfWithTotal.withColumn(
          "completeness_quality_score",
          when(col("number_cq_attributes") === 0, lit(0.0))
            .otherwise(round(col("cqs_total").cast("double") / col("number_cq_attributes") * 100, 4))
        )

        // Build bandExpr using dqConfig
        val bandExpr = dqConfig.completeness.qualityBands.foldLeft(lit(null).cast("string")) { (acc, band) =>
          when(
            col("completeness_quality_score") >= band.min && col("completeness_quality_score") <= band.max,
            lit(band.label)
          ).otherwise(acc)
        }

        // Add the quality band column using the bandExpr
        val finalDF = dfWithScore.withColumn("completeness_quality_score_category", bandExpr)

        // Select relevant columns
        val outputCols: Seq[Column] = Seq(
          if (finalDF.columns.contains(objectTypeName)) col(objectTypeName) else lit(null).as(objectTypeName),
          col("table_name"),
          col(UPDATED_AT),
          col(UPDATED_AT_TS),
          col(idCol),
          col(disambCol),
          col("completeness_attr_scores"),
          col("completeness_quality_score"),
          col("completeness_quality_score_category")
        )
        finalDF.select(outputCols: _*)
    }

    // Union all processed DataFrames
    unionByName(processedDFs.toSeq: _*)
  }

  override def execute(jobArgs: DQJobArgs, config: (Config, DataDictionaryConfig)): Unit = {
    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark, tableProperties = Map.empty, options = Map("partitionOverwriteMode" -> "dynamic"))

    build(config, jobArgs, reader, writer)
  }

  override def getConfigManifest: Manifest[(Config, DataDictionaryConfig)] = manifest[(Config, DataDictionaryConfig)]

  override def getInitParams: DQJobArgs = new DQJobArgs()
}
