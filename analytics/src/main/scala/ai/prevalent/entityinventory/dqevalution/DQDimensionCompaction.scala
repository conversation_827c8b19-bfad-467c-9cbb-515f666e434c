package ai.prevalent.entityinventory.dqevalution

import ai.prevalent.entityinventory.common.configs.DQJobArgs
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.dqevalution.DQCompletenessEvaluation.LOGGER
import ai.prevalent.entityinventory.dqevalution.configs.specs.Config
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{KG_CONTENT_TYPE, LAST_FOUND, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.utils.DQEntityEvaluationUtils
import ai.prevalent.entityinventory.utils.DQRelationshipEvaluationUtils
import ai.prevalent.entityinventory.utils.EIUtil.safeRead
import ai.prevalent.entityinventory.utils.{DQEntityEvaluationUtils, EIUtil}
import ai.prevalent.entityinventory.utils.SparkUtil.unionByName
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{Column, DataFrame, SparkSession}

/**
 * DQDimensionCompaction handles the compaction and merging of DQ dimension tables for a given entity.
 * It reads, merges, and writes the compacted DataFrame with calculated quality scores.
 */
object DQDimensionCompaction extends SDSSparkBaseConfigurable[DQJobArgs, Config] {

  /**
   * Entry point for the compaction job. Reads, merges, and writes DQ dimension tables for an entity.
   *
   * @param jobArgs Job arguments containing object name and type
   * @param reader  Table reader
   * @param writer  Table writer
   */
  def build(jobArgs: DQJobArgs, config: Config, reader: SDSTableReader, writer: SDSTableWriter): Unit = {
    val objectName: String = jobArgs.objectName.toLowerCase()
    val objectType: String = jobArgs.objectType.toLowerCase()
    val (idCol, objectTypeName) = (objectType match {
      case "relationship" => (DQRelationshipEvaluationUtils.idCol, DQRelationshipEvaluationUtils.objectTypeName)
      case _ =>  (DQEntityEvaluationUtils.idCol, DQEntityEvaluationUtils.objectTypeName)
    })
    LOGGER.info(s"Starting Compaction for object: $objectName of type: $objectType ...")

    // Get the list of DQ dimensions to process from Spark config
    val dqDimensions: Seq[String] = config.config_value.dqDimensions
    LOGGER.info(s"DQ Dimensions to process: $dqDimensions")

    // Get the DQ schema from Spark config (default: "ei")
    val dqSchema = spark.conf.get("spark.sds.kg.dq.schema", "ei")
    LOGGER.info(s"Using DQ Schema: $dqSchema")

    // Merge all DQ dimension tables for the entity
    val mergedDF: DataFrame = getMergedDimensionTables(spark, objectType, objectName, dqDimensions, jobArgs, reader, dqSchema)

    // Write the merged DataFrame to the normal output table
    val normalTable =
      if (objectType == "relationship")
        s"$dqSchema.sds_ei__rel__${jobArgs.objectName.toLowerCase()}__dq"
      else
        s"$dqSchema.sds_ei__${jobArgs.objectName.toLowerCase()}__dq"
    LOGGER.info(s"Writing merged DataFrame to normal output table: $normalTable")
    writer.overwritePartition(mergedDF, normalTable, Array(days(col(UPDATED_AT_TS)), col(objectTypeName), col("table_name")))

    // Write the enrich table only if enrichFilter is present and non-empty
    val enrichTable =
      if (objectType == "relationship")
        s"$dqSchema.sds_ei__rel__dq__${objectName}__enrich"
      else
        s"$dqSchema.sds_ei__dq__${objectType}__${objectName}__enrich"
    val enrichFilterOpt = config.config_value.enrichFilter
    enrichFilterOpt match {
      case Some(filterExpr) if filterExpr.trim.nonEmpty =>
        var enrichDF = mergedDF.filter(expr(filterExpr))
        config.config_value.enrichRequiredColumns match {
          case Some(cols) if cols.nonEmpty =>
            // Always include mandatory partition columns
            val mandatoryCols = Seq(idCol, UPDATED_AT_TS, objectTypeName, "table_name")
            val allCols = (cols ++ mandatoryCols).distinct
            enrichDF = enrichDF.select(allCols.map(col): _*)
          case _ => // do nothing, keep all columns
        }
        LOGGER.info(s"Writing enrich DataFrame to output table: $enrichTable with filter: $filterExpr and columns: ${config.config_value.enrichRequiredColumns.getOrElse(Seq("<all>"))}")
        writer.overwritePartition(enrichDF, enrichTable, Array(days(col(UPDATED_AT_TS)), col(objectTypeName), col("table_name")))
      case _ =>
        LOGGER.info("No enrichFilter provided; enrich table will not be written.")
    }
    LOGGER.info("Compaction and write completed successfully.")
  }

   /**
   * Merges all DQ dimension tables for the given entity and calculates the overall quality score.
   *
   * @param spark SparkSession
   * @param objectType Type of the object/entity
   * @param objectName Name of the object/entity
   * @param dqDimensions List of DQ dimensions to merge
   * @param jobArgs Job arguments
   * @param reader Table reader
   * @param dqSchema DQ schema name
   * @return Merged DataFrame with overall quality score
   */
  def getMergedDimensionTables(
    spark: SparkSession,
    objectType: String,
    objectName: String,
    dqDimensions: Seq[String],
    jobArgs: DQJobArgs,
    reader: SDSTableReader,
    dqSchema: String
  ): DataFrame = {
    val (disambCol, objectTypeName) = (objectType match {
      case "relationship" => (DQRelationshipEvaluationUtils.disambCol, DQRelationshipEvaluationUtils.objectTypeName)
      case _ =>  (DQEntityEvaluationUtils.disambCol, DQEntityEvaluationUtils.objectTypeName)
    })

    // Read each dimension table or create a default DataFrame if not present
    val dimTables: Seq[(String, DataFrame)] = dqDimensions.map { dimName =>
      val tableName =
        if (objectType == "relationship")
          s"$dqSchema.sds_ei__rel__${jobArgs.objectName.toLowerCase()}__dq_${dimName}"
        else
          s"$dqSchema.sds_ei__${jobArgs.objectName.toLowerCase()}__dq_${dimName}"

      LOGGER.info(s"Processing dimension: $dimName, Table: $tableName")

      // Create a default DataFrame with required columns if the table is missing
      val default: DataFrame = {
        var df = spark.emptyDataFrame
          .withColumn(UPDATED_AT, lit(null).cast(LongType))
          .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
          .withColumn(s"${dimName}_quality_score", lit(null).cast(DoubleType))
        df
      }

      // Filter for the current update date
      val filterCondition = expr(s"$UPDATED_AT_TS = to_timestamp(${jobArgs.currentUpdateDate} / 1000)")
      val df = EIUtil.safeReadEntity(
        tableName,
        filterCondition,
        reader,
        default
      )
      LOGGER.info(s"Loaded DataFrame for dimension: $dimName, Rows: ${df.count()}")
      (dimName, df)
    }

    // Merge all dimension DataFrames on key columns
    val keyCols = Seq(objectTypeName, "table_name", "p_id")
    LOGGER.info(s"Merging dimension DataFrames on keys: $keyCols")
    val mergedDF = dimTables.tail.foldLeft(dimTables.head._2) { case (accDF, (dimName, df)) =>
      accDF.join(df, keyCols, "outer")
        .drop(df(disambCol))
        .drop(df("updated_at_ts"))
    }

    // Add aggregated_quality_score as the average of all *_quality_score columns (ignoring nulls)
    val qualityScoreCols = mergedDF.columns.filter(_.endsWith("_quality_score")).map(col)
    val aggregatedQualityScoreCol = if (qualityScoreCols.nonEmpty) {
      val validScores = qualityScoreCols.map(c => when(c.isNotNull, c).otherwise(lit(null)))
      val sumExpr = validScores.reduce(_ + _)
      val countExpr = qualityScoreCols.map(c => when(c.isNotNull, 1).otherwise(0)).reduce(_ + _)
      // Calculate average percentage (already in 0-100 range)
      (sumExpr / countExpr).alias("aggregated_quality_score")
    } else {
      lit(null).cast(DoubleType).alias("aggregated_quality_score")
    }

    LOGGER.info("Adding aggregated_quality_score column to merged DataFrame.")
    mergedDF
      .withColumn("aggregated_quality_score", aggregatedQualityScoreCol)
      .drop(KG_CONTENT_TYPE, "kg_config")
  }

  /**
   * Main execution entry point. Initializes reader and writer, then calls build().
   */
  override def execute(jobArgs: DQJobArgs, config: Config): Unit = {
    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark, tableProperties = Map.empty, options = Map("partitionOverwriteMode" -> "dynamic"))
    build(jobArgs, config, reader, writer)
  }

  override def getConfigManifest: Manifest[Config] = manifest[Config]

  override def getInitParams: DQJobArgs = new DQJobArgs()
}
