package ai.prevalent.entityinventory.dqevalution.configs.specs

import ai.prevalent.entityinventory.dqevalution.DQCompletenessEvaluation
import org.json4s.jackson.Serialization.write

case class qualityBand(
                        label: String = null,
                        min: Double = 0.0,
                        max: Double = 0.0
                      )

case class completenessProps(
                              noDataIdentifier: String = null,
                              skipColumns: Seq[String] = Seq.empty[String],
                              qualityBands: Seq[qualityBand] = Seq.empty[qualityBand]
                            )

case class ConfigValue(
                        completeness: completenessProps = null,
                        dqDimensions: Seq[String] = Seq.empty[String],
                        enrichFilter: Option[String] = None,
                        enrichRequiredColumns: Option[Seq[String]] = None
                      )

case class Config(
                   config_value: ConfigValue
                 ) {

  override def toString: String = write(this)(DQCompletenessEvaluation.configFormats)
}





