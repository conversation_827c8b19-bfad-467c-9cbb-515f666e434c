package ai.prevalent.entityinventory.dqevalution.configs.specs

import ai.prevalent.entityinventory.common.configs.{DictionaryAttribute, EntityDictionary, RelationshipDictionary}
import ai.prevalent.entityinventory.dqevalution.DQCompletenessEvaluation
import org.json4s.jackson.Serialization.write

case class DQConfigValue(
                        attributes: Map[String, DictionaryAttribute],
                        relationship_attributes: Map[String, DictionaryAttribute]
                      )

case class DataDictionaryConfig(
                   config_value: DQConfigValue
                 ) {

  override def toString: String = write(this)(DQCompletenessEvaluation.configFormats)
}





