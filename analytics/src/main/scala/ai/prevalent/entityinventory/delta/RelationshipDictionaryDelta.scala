package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.{EntityDictionary, RelationshipDictionary}
import ai.prevalent.entityinventory.delta.EntityDictionaryDelta.checkDictionaryAttributeChanges

case class RelationshipDictionaryDelta(description:Option[Change] = Option.empty,
                                       attrChanges:Seq[Change] = Seq.empty) extends Delta

object RelationshipDictionaryDelta extends Delta {

  def apply(prevConfig:RelationshipDictionary, newConfig:RelationshipDictionary): RelationshipDictionaryDelta = {
    val description = checkDescriptionChange(prevDescription = prevConfig.description, newDescription = newConfig.description)
    RelationshipDictionaryDelta(description = description)
    val attrChanges = checkDictionaryAttributeChanges(prevConfig.entity_attributes++prevConfig.relationship_attributes,
      newAttributes = newConfig.entity_attributes++newConfig.relationship_attributes)
    RelationshipDictionaryDelta(description, attrChanges = attrChanges)
  }

  def checkDescriptionChange(prevDescription:String, newDescription:String):Option[Change] ={
    if (!prevDescription.equals(newDescription)) {
      Some(Change("Relationship Description", "Relationship Description",
        Option.empty, Map("Old Description" -> prevDescription, "New Description" -> newDescription), "Relationship"))
    }
    else Option.empty
  }
}
