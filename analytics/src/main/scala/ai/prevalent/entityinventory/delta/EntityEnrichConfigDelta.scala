package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.relationship.entityenrich.configs.Config

case class EntityEnrichConfigDelta(countEnrichChanges: Seq[Change] = Seq.empty,
                                   properties: Seq[Change] = Seq.empty) extends Delta {
}

object EntityEnrichConfigDelta extends Delta {

  def apply(clientConfig: Config, deltas: Seq[Change]): EntityEnrichConfigDelta = {
    val prop = clientConfig.derivedProperties.map((prop => (prop.colName, prop))).toMap
    val changes: Seq[Change] = checkPropertyInheritedChanges(deltas, prop)
    EntityEnrichConfigDelta(properties = changes)
  }

  /**
   * For finding delta between two solution versions
   *
   * @param prevConfig
   * @param config
   * @return
   */
  def apply(prevConfig: Config, config: Config): EntityEnrichConfigDelta = {
    val propertyChanges = checkPropertyChanges(
      oldProperties = prevConfig.derivedProperties,
      newProperties = config.derivedProperties
    )
    val enrichChanges = checkEnrichChanges(prevConfig, config)
    EntityEnrichConfigDelta(countEnrichChanges = enrichChanges, properties = propertyChanges)
  }

  def checkEnrichChanges(prevConfig: Config, config: Config): Seq[Change] = {
    val prevEnriches = prevConfig.countEnriches.map(prop => (s"${prop.colName}", prop)).toMap
    val newEnriches = config.countEnriches.map(prop => (s"${prop.colName}", prop)).toMap
    val enrichNewAdded = newEnriches
      .filter(p => !prevEnriches.isDefinedAt(p._1))
      .map(p => {
        Some(Change("Enrich", p._2.relationshipTableName, Option.empty, Map("New Enrich" -> s"Field Name: ${p._2.colName}, Filter: ${p._2.filter}"), "Enrich"))
      })
      .filter(_.isDefined)
      .map(_.get).toSeq

    val existingEnrichRemoved = prevEnriches
      .filter(p => !newEnriches.isDefinedAt(p._1))
      .map(p => {
        Some(Change("Enrich", p._2.relationshipTableName, Option.empty, Map("Removed Enrich" -> s"Field Name: ${p._2.colName}"), "Enrich"))
      })
      .filter(_.isDefined)
      .map(_.get).toSeq

    val enrichFilterChange = newEnriches
      .filter(p => prevEnriches.isDefinedAt(p._1))
      .map(p => {
        val prevEnrichFilter = prevEnriches(p._1).filter
        if( prevEnrichFilter != p._2.filter) {
          Some(Change("Enrich", p._2.relationshipTableName, Option.empty, Map("Old Enrich Filter" -> prevEnrichFilter, "New Enrich Filter" -> p._2.filter), "Enrich"))
        } else Option.empty
      }
      )
      .filter(_.isDefined)
      .map(_.get).toSeq

    enrichNewAdded ++ existingEnrichRemoved ++ enrichFilterChange
  }
}
