package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.entityinventory.utils.SparkUtil
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.write

case class LoaderConfigDelta(primaryKey: Option[Change]=Option.empty, origin: Option[Change]=Option.empty,
                             filterBy: Option[Change] = Option.empty,
                             properties: Seq[Change] = Seq.empty, dataSourceChange:Option[Change]=Option.empty,
                            enrichmentChange:Seq[Change] = Seq.empty)
  extends  Delta {
}

object LoaderConfigDelta extends Delta {

  def apply(clientConfig:Config, deltas: Seq[Change]) :LoaderConfigDelta = {
    val prop = clientConfig.allProperties(true).map((prop => (prop.colName,prop))).toMap
    val changes: Seq[Change] = checkPropertyInheritedChanges(deltas, prop)
    LoaderConfigDelta(properties = changes)
  }
  /**
   * For finding delta between two solution versions
   * @param prevConfig
   * @param config
   * @return
   */
  def apply(prevConfig: Config, config: Config): LoaderConfigDelta = {
    val primaryKeyChange = checkPrimaryKeyChange(prevConfig, config)
    val filter = checkFilterChange(prevConfig, config)
    val originChange = checkOriginChange(prevConfig, config)
    val dataSourceChange = checkDataSourceChange(prevConfig, config)

    val propertyChanges = checkPropertyChanges(oldProperties = prevConfig.allProperties(true),
      newProperties = config.allProperties(true), includeFieldSpec = true)
    val enrichChanges= config.enrichments.map( e => (e.lookupInfo.tableName,e.lookupInfo.enrichmentColumns))
      .flatMap(e => e._2.map(c => Change("New Enrichment Field", c,
        Some(s"Enrichment field from ${e._1}"), Map.empty, "Attribute")))
    val enrichs = config.enrichments.map( enrich => {
      val enrichJSon = write(enrich)(DefaultFormats)
      Change("New Enrichment", enrich.lookupInfo.tableName,
        Option.empty, Map("Enrichment" -> enrichJSon), "Enrichment")
    })
    LoaderConfigDelta(primaryKeyChange, filter, originChange, propertyChanges,
      dataSourceChange = dataSourceChange, enrichmentChange = enrichChanges++enrichs)
  }

  def checkPrimaryKeyChange(config1: Config, config2: Config): Option[Change] = {
    if (!config1.primaryKey.equals(config2.primaryKey)) {
      Some(Change("Primary Key", "Primary Key Expression",
        Option.empty, Map("Old Value" -> config1.primaryKey, "New Value" -> config2.primaryKey), "Primary Key"))
    }
    else Option.empty
  }

  def checkFilterChange(config1: Config, config2: Config): Option[Change] = {
    if (!config1.filterBy.equals(config2.filterBy)) {
      Some(Change("Filter", "Filter Expression Changes",
        Option.empty, Map("Old Filter" -> config1.filterBy, "New Filter" -> config2.filterBy), "Filter"))
    }
    else Option.empty
  }

  def checkDataSourceChange(config1: Config, config2: Config): Option[Change] ={
    if (!config1.dataSource.equals(config2.dataSource)) {
      Some(Change("Data Source", "Data Source",
        Option.empty, Map("Old Data Source" -> write(config1.dataSource.get)(DefaultFormats), "New Data Source" -> write(config2.dataSource.get)(DefaultFormats)), "Data Source"))
    }
    else Option.empty
  }

  def checkOriginChange(config1: Config, config2: Config): Option[Change] = {
    if (!config1.origin.equals(config2.origin)) {
      Some(Change("Origin", "Origin Change",
        Option.empty, Map("Old Origin" -> config1.origin, "New Origin" -> config2.origin), "Origin"))
    }
    else Option.empty
  }
}
