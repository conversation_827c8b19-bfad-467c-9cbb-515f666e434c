package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.GlobalEntityConfig

case class GlobalEntityConfigDelta(properties: Seq[Change] = Seq.empty,
                                   fieldSpec:Seq[Change] =Seq.empty, lastUpdated:Seq[Change] = Seq.empty) extends Delta

object GlobalEntityConfigDelta extends Delta {

  def apply(clientConfig:GlobalEntityConfig, deltas: Seq[Change]): GlobalEntityConfigDelta = {
    val properties = clientConfig.commonProperties++clientConfig.entitySpecificProperties
    val propChanges = checkPropertyInheritedChanges(deltas,properties.map((prop => (prop.colName,prop))).toMap)
    GlobalEntityConfigDelta(properties = propChanges)
  }

  def apply(prevConfig:GlobalEntityConfig, newConfig: GlobalEntityConfig): GlobalEntityConfigDelta = {

    val propertiesChange = checkPropertyChanges(oldProperties = prevConfig.commonProperties++prevConfig.entitySpecificProperties,
      newProperties = newConfig.commonProperties++newConfig.entitySpecificProperties, includeFieldSpec = true)
    val fieldSpecChanges = checkPropertyChanges(oldProperties = prevConfig.commonProperties++prevConfig.entitySpecificProperties,
      newProperties = newConfig.commonProperties++newConfig.entitySpecificProperties)
    val lastAttrChanges = checkStringListChanges(prevConfig.lastUpdateFields, newConfig.lastUpdateFields, "Last Updated Attributes")
    GlobalEntityConfigDelta(properties = propertiesChange, fieldSpec = fieldSpecChanges, lastUpdated = lastAttrChanges)
  }
}
