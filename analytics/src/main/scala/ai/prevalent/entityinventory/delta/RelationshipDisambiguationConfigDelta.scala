package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.entityinventory.delta.RelationshipConfigDelta.checkAttrInheritedChanges
import ai.prevalent.entityinventory.relationship.disambiguation.config.grouping.Union
import ai.prevalent.entityinventory.relationship.disambiguation.config.{Config, FieldLevelConfidenceMatrix, ValueConfidence}

case class RelationshipDisambiguationConfigDelta(
                                                  relationshipModelInputUpdate: Seq[Change] = Seq.empty,
                                                  filterByChange: Option[Change] = Option.empty,
                                                  disambiguationGroupingChange: Seq[Change] = Seq.empty,
                                                  confidenceMatrixUpdate: Option[Change] = Option.empty,
                                                  strategyChange: Seq[Change] = Seq.empty,
                                                  exludeValuesUpdate: Option[Change] = Option.empty,
                                                  derivedPropertiesChange: Seq[Change] = Seq.empty) extends Delta {
}

object RelationshipDisambiguationConfigDelta extends Delta {

  private val RELATION_MODEL_INPUT = "Relation Model Input"
  private val REL_TEMPORAL_CONFIDENCE_MATRIX = "Relation Temporal Confidence Matrix"
  def apply(prevConfig: Config, config: Config): RelationshipDisambiguationConfigDelta = {
    val modelInputUpdate = checkModelRelationModelUpdate(prevConfig, config)
    val filter = checkOutputFilterChange(prevConfig, config)
    val confidenceMatrixUpdate = checkConfidenceMatrixUpdate(prevConfig, config)
    val excludeValuesUpdate = checkExludeValuesUpdate(prevConfig, config)
    val fieldLevelConfidenceMatrixStrategyChange = checkfieldLevelConfidenceMatrixStrategyChange(prevConfig, config)
    val valueConfidenceStrategyChange = checkvalueConfidenceStrategyChange(prevConfig, config)
    val rollingUpFieldsStrategyChange = checkRollingUpFieldsStrategyChange(prevConfig, config)
    val aggregationStrategyChange = checkAggregationStrategyChange(prevConfig, config)
    val props1 = prevConfig.derivedProperties.map((prop => (prop.colName, prop))).toMap
    val props2 = config.derivedProperties.map((prop => (prop.colName, prop))).toMap
    val expression = checkDerivedPropertyExpressionChange(props1, props2)
    val newFields = checkNewDerivedProperties(props1, props2)
    val removedProps = checkRemovedProperties(props1, props2)
    val disambgGroupinfDeltas = checkDisambiguationGrpingDeltaChange(prevConfig, config)
     RelationshipDisambiguationConfigDelta(
      modelInputUpdate,
      filter,
      disambgGroupinfDeltas,
      confidenceMatrixUpdate,
      fieldLevelConfidenceMatrixStrategyChange ++ valueConfidenceStrategyChange ++ rollingUpFieldsStrategyChange ++ aggregationStrategyChange,
      excludeValuesUpdate,
      expression ++ newFields ++ removedProps
    )
  }

  def apply(clientConfig: Config, deltas: Seq[Change]): RelationshipDisambiguationConfigDelta = {
    val derivedProps = clientConfig.derivedProperties.map((prop => (prop.colName, prop))).toMap
    val derivedPropsInheritedChanges = checkPropertyInheritedChanges(deltas, derivedProps)
    new RelationshipDisambiguationConfigDelta(derivedPropsInheritedChanges)
  }

  /**
   * Checks for update in inventory model input like change in
   * name, table name etc.
   *
   * @param prevConfig
   * @param newConfig
   * @return
   */
  def checkModelRelationModelUpdate(prevConfig: Config, newConfig: Config): Seq[Change] = {

    val prevRelationModelInput = prevConfig.relationshipModels.map(input => (input.tableName, input)).toMap
    val currentRelationModelInput = newConfig.relationshipModels.map(input => (input.tableName, input)).toMap
    val elementsWithFilterChange: Seq[Change] = prevRelationModelInput
      .filter(prevInp => currentRelationModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        val prevFilter = prevInp._2.filter
        val matchEle = currentRelationModelInput(prevInp._1)
        if (!prevFilter.equals(matchEle.filter)) {
          Some(Change(RELATION_MODEL_INPUT, matchEle.name, Option.empty, Map("Old Filter" -> prevFilter, "New Filter" -> matchEle.filter), RELATION_MODEL_INPUT))
        } else {
          Option.empty
        }
      }).filter(_.isDefined).map(_.get).toSeq
    val elementsWithNameChange: Seq[Change] = prevRelationModelInput
      .filter(prevInp => currentRelationModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        val prevName = prevInp._2.name
        val matchEle = currentRelationModelInput(prevInp._1)
        if (prevName != matchEle.name) {
          Some(Change(RELATION_MODEL_INPUT, matchEle.name, Option.empty, Map("Old Name" -> prevName, "New Name" -> matchEle.name), RELATION_MODEL_INPUT))
        } else {
          Option.empty
        }
      }).filter(_.isDefined).map(_.get).toSeq
    val newElementsAddedChange: Seq[Change] = currentRelationModelInput
      .filter(prevInp => !prevRelationModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        Some(Change(RELATION_MODEL_INPUT, prevInp._1, Option.empty, Map("Newly Added Relation Model Input Info" -> prevInp._2.toString), RELATION_MODEL_INPUT))

      }).filter(_.isDefined).map(_.get).toSeq
    val existingElementsRemovedChange: Seq[Change] = prevRelationModelInput
      .filter(prevInp => !currentRelationModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        Some(Change(RELATION_MODEL_INPUT, prevInp._1, Option.empty, Map("Newly Added Relation Model Input Info" -> prevInp._1), RELATION_MODEL_INPUT))

      }).filter(_.isDefined).map(_.get).toSeq
    elementsWithFilterChange ++ elementsWithNameChange ++ newElementsAddedChange ++ existingElementsRemovedChange
  }

  def checkDisambiguationGrpingDeltaChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
    newConfig.disambiguation.disambiguationGrouping.getConfigDelta(prevConfig.disambiguation.disambiguationGrouping).changes()
  }

  /**
   * Checks if any output filter expression is changed
   *
   * @param prevConfig
   * @param newConfig
   * @return
   */
  def checkOutputFilterChange(prevConfig: Config, newConfig: Config): Option[Change] = {
    if (!prevConfig.output.filter.equals(newConfig.output.filter)) {
      Some(Change("Filter", "Output Filter Expression Changes",
        Option.empty, Map("Old Output Filter" -> prevConfig.output.filter, "New Output Filter" -> newConfig.output.filter), "Filter"))
    }
    else Option.empty
  }

  /**
   * Checks if confidence matrix is updated. This check is done considering their order in the list
   *
   * @param prevConfig
   * @param newConfig
   * @return
   */
  def checkConfidenceMatrixUpdate(prevConfig: Config, newConfig: Config): Option[Change] = {
    val prevConfidenceMatrix = prevConfig.disambiguation.confidenceMatrix
    val newConfidenceMatrix = newConfig.disambiguation.confidenceMatrix
    if (prevConfidenceMatrix.isEmpty && newConfidenceMatrix.isEmpty) {
      Option.empty
    } else if (prevConfidenceMatrix.isEmpty) {
      Some(Change(
        "Updated Confidence Matrix", f"Confidence Matrix Defined", Option.empty,
        Map("Confidence Matrix Defined" -> newConfidenceMatrix.mkString(",")), "Confidence Matrix"
      ))
    }
    else if (newConfidenceMatrix.isEmpty) {
      Some(Change(
        "Updated Confidence Matrix", f"Confidence Matrix Removed", Option.empty,
        Map("Removed Confidence Matrix" -> prevConfidenceMatrix.mkString(",")), "Confidence Matrix"
      ))
    } else if (!(prevConfidenceMatrix.iterator.sameElements(newConfidenceMatrix))) {
      Some(Change(
        "Updated Confidence Matrix", "Matrix Updated", Option.empty,
        Map("Old Confidence Matrix" -> prevConfidenceMatrix.mkString(","), "New Confidence Matrix" -> newConfidenceMatrix.mkString(",")), "Confidence Matrix"
      ))
    }
    else Option.empty
  }


  def checkExludeValuesUpdate(prevConfig: Config, newConfig: Config): Option[Change] = {
    val prevExcludeValues = prevConfig.disambiguation.excludeValues
    val newExcludeValues = newConfig.disambiguation.excludeValues
    if (!prevExcludeValues.sorted.equals(newExcludeValues.sorted)) {
      Some(Change("Relation Exclude Values", "Relation Exclude Values Updated",
        Option.empty, Map("Old Exclude Values" -> prevExcludeValues.mkString("Array(", ", ", ")"), "New Exclude Values" -> newExcludeValues.mkString("Array(", ", ", ")")), "Relation Exclude Values"))
    } else Option.empty
  }

  def checkDerivedPropertyExpressionChange(prevConfigProps: Map[String, Property], newConfigProps: Map[String, Property]): Seq[Change] = {
    prevConfigProps
      .filter(p => newConfigProps.isDefinedAt(p._1))
      .map(p => {
        val expr2 = newConfigProps.get(p._1).get.colExpr
        if (!p._2.colExpr.equals(expr2)) {
          Some(Change("Derived Field Expression Updated", p._1, Option.empty, Map("Old Expression" -> p._2.colExpr, "New Expression" -> expr2), "Attribute"))
        } else Option.empty
      })
      .filter(_.isDefined)
      .map(_.get)
      .toSeq
  }

  def checkNewDerivedProperties(prevConfigProps: Map[String, Property], newConfigProps: Map[String, Property]): Seq[Change] = {
    newConfigProps
      .filter(p => !prevConfigProps.isDefinedAt(p._1))
      .map(p => {
        Some(Change("New Derived Field", p._1, Option.empty, Map("Expression" -> p._2.colExpr), "Attribute"))
      })
      .filter(_.isDefined)
      .map(_.get)
      .toSeq
  }

  def checkRemovedProperties(prevConfigProps: Map[String, Property], newConfigProps: Map[String, Property]): Seq[Change] = {
    prevConfigProps
      .filter(p => !newConfigProps.isDefinedAt(p._1))
      .map(p => {
        Some(Change("Removed Derived Field", p._1, Option.empty, Map("Expression" -> p._2.colExpr), "Attribute"))
      })
      .filter(_.isDefined)
      .map(_.get)
      .toSeq
  }

  def checkfieldLevelConfidenceMatrixStrategyChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
    val prevFieldLevelConfidenceMatrix = if (prevConfig.disambiguation.strategy.isDefined) {
      prevConfig.disambiguation.strategy.get.fieldLevelConfidenceMatrix.getOrElse(Array.empty).map((prop => (prop.field, prop))).toMap
    } else Map.empty[String, FieldLevelConfidenceMatrix]
    val newFieldLevelConfidenceMatrix = if (newConfig.disambiguation.strategy.isDefined) {
      newConfig.disambiguation.strategy.get.fieldLevelConfidenceMatrix.getOrElse(Array.empty).map((prop => (prop.field, prop))).toMap
    } else Map.empty[String, FieldLevelConfidenceMatrix]


    val checkConfidenceMatrixChange: Seq[Change] = {
      prevFieldLevelConfidenceMatrix.filter(p => newFieldLevelConfidenceMatrix.isDefinedAt(p._1))
        .map(p => {
          val newConfidenceMatrix = newFieldLevelConfidenceMatrix.get(p._1).get.confidenceMatrix
          val oldConfidenceMatrix = p._2.confidenceMatrix
          if (oldConfidenceMatrix.isEmpty && newConfidenceMatrix.isEmpty) {
            Option.empty
          } else if (oldConfidenceMatrix.isEmpty) {
            Some(Change(
              "Field Level Confidence Matrix", f"Confidence Matrix of ${p._1}", Option.empty,
              Map("Value Confidence Matrix Defined" -> newConfidenceMatrix.mkString(",")), "Field Level Confidence Matrix"
            ))
          }
          else if (newConfidenceMatrix.isEmpty) {
            Some(Change(
              "Field Level Confidence Matrix", f"Confidence Matrix of ${p._1}", Option.empty,
              Map("Removed Value Confidence Matrix" -> oldConfidenceMatrix.mkString(",")), "Field Level Confidence Matrix"
            ))
          } else if (!(p._2.confidenceMatrix sameElements newConfidenceMatrix)) {
            Some(Change(
              "Field Level Confidence Matrix", "Updated Confidence Matrix", Option.empty,
              Map("Old Confidence Matrix" -> p._2.confidenceMatrix.mkString(","), "New Confidence Matrix" -> newConfidenceMatrix.mkString(",")), "Field Level Confidence Matrix"
            ))
          }
          else Option.empty
        })
        .filter(_.isDefined)
        .map(_.get)
        .toSeq
    }

    val checkFieldSpecChange: Seq[Change] = {
      val persistanceSpecChange = prevFieldLevelConfidenceMatrix.filter(p => newFieldLevelConfidenceMatrix.isDefinedAt(p._1))
        .map(p => {
          val persistNonNullValue = newFieldLevelConfidenceMatrix.get(p._1).get.persistNonNullValue.get
          if (!p._2.persistNonNullValue.get.equals(persistNonNullValue)) {
            Some(Change(
              "FieldSpec Updated", "Persist NonNull Value Updated", Option.empty,
              Map("Old FieldSpec" -> p._2.persistNonNullValue.get.toString, "New FieldSpec" -> persistNonNullValue.toString), "FieldSpec"
            ))
          } else Option.empty
        })

      val confidenceMatrixRestrictionSpecChange = prevFieldLevelConfidenceMatrix.filter(p => newFieldLevelConfidenceMatrix.isDefinedAt(p._1))
        .map(p => {
          val confidenceMatrixRestriction = newFieldLevelConfidenceMatrix.get(p._1).get.restrictToConfidenceMatrix
          if (!p._2.restrictToConfidenceMatrix.equals(confidenceMatrixRestriction)) {
            Some(Change(
              "FieldSpec Updated", "Restrict to Confidence Matrix Updated", Option.empty,
              Map("Old FieldSpec" -> p._2.restrictToConfidenceMatrix.toString, "New FieldSpec" -> confidenceMatrixRestriction.toString), "FieldSpec"
            ))
          } else Option.empty
        })
      (persistanceSpecChange ++ confidenceMatrixRestrictionSpecChange).filter(_.isDefined)
        .map(_.get)
        .toSeq


    }

    if (!(prevFieldLevelConfidenceMatrix.isEmpty && newFieldLevelConfidenceMatrix.isEmpty)) {
      checkConfidenceMatrixChange ++ checkFieldSpecChange
    } else Seq.empty
  }

  def checkvalueConfidenceStrategyChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
    val prevStrategy = if (prevConfig.disambiguation.strategy.get.valueConfidence.isDefined) {
      prevConfig.disambiguation.strategy.get.valueConfidence.get.map((prop => (prop.field, prop))).toMap
    } else Map.empty[String, ValueConfidence]

    val newStrategy = if (newConfig.disambiguation.strategy.get.valueConfidence.isDefined) {
      newConfig.disambiguation.strategy.get.valueConfidence.get.map((prop => (prop.field, prop))).toMap
    } else Map.empty[String, ValueConfidence]

    val checkValueConfidenceUpdate: Seq[Change] = {
      prevStrategy.filter(p => newStrategy.isDefinedAt(p._1))
        .map(p => {
          val newValueConfidence = newStrategy.get(p._1).get.confidenceMatrix
          val oldValueConfidence = p._2.confidenceMatrix
          if (!(newValueConfidence sameElements p._2.confidenceMatrix)) {
            Some(Change(
              "Value Confidence Updated", p._1, Option.empty,
              Map("Old Value Confidence" -> oldValueConfidence.mkString(","), "New Value Confidence" -> newValueConfidence.mkString(",")), "Value Confidence"
            ))
          } else if (newValueConfidence.isEmpty) {
            Some(Change(
              "Value Confidence Updated", "Deleted Value Confidence", Option.empty,
              Map(f"Existing Value Confidence of ${p._2}" -> oldValueConfidence.mkString(",")), "Value Confidence"
            ))
          }
          else if (oldValueConfidence.isEmpty) {
            Some(Change(
              "Value Confidence Defined", p._1, Option.empty,
              Map("New Value Confidence Defined" -> newValueConfidence.mkString(",")), "Value Confidence"
            ))
          } else Option.empty
        }).filter(_.isDefined).map(_.get).toSeq
    }

    val checkNewAddedValueConfidence: Seq[Change] = {
      newStrategy
        .filter(p => !prevStrategy.isDefinedAt(p._1))
        .map(p => {
          Some(Change(
            "Value Confidence Added", p._1, Option.empty,
            Map("New Value" -> p._2.confidenceMatrix.mkString("Array(", ", ", ")")), "Attribute"
          ))
        }
        ).filter(_.isDefined).map(_.get).toSeq
    }

    val checkRemovedValueConfidence: Seq[Change] = {
      prevStrategy
        .filter(p => !newStrategy.isDefinedAt(p._1))
        .map(p => {
          Some(Change(
            "Value Confidence Removed", p._1, Option.empty,
            Map("Old Value Confidence" -> p._2.confidenceMatrix.mkString("Array(", ", ", ")")), "Attribute"
          ))
        })
        .filter(_.isDefined)
        .map(_.get)
        .toSeq
    }

    if (!(prevStrategy.isEmpty && newStrategy.isEmpty)) {
      checkValueConfidenceUpdate ++ checkNewAddedValueConfidence ++ checkRemovedValueConfidence
    } else Seq.empty
  }

  def checkRollingUpFieldsStrategyChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
    val prevStrategy = prevConfig.disambiguation.strategy
    val prevRollUp = if(prevStrategy.isDefined) prevStrategy.get.rollingUpFields else Array.empty[String]
    val newStrategy = newConfig.disambiguation.strategy
    val newRollUp = if(newStrategy.isDefined) newStrategy.get.rollingUpFields else Array.empty[String]

    val additionChange = Some(Change(
      "Relation Rolling Up Fields Added", "", Option.empty,
      Map("New Rolling Up Field" -> newRollUp.diff(prevRollUp).mkString(", ")), "Relation RollUp"
    ))
    val removalChange = Some(Change(
      "Relation Rolling Up Fields Removed", "", Option.empty, Map("Old Rolling Up Field" -> prevRollUp.diff(newRollUp).mkString(", ")), "Relation RollUp"
    ))
    Seq(additionChange, removalChange).filter(_.isDefined).map(_.get)
  }


  def checkAggregationStrategyChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
    val prevConfigProps = prevConfig.disambiguation.strategy.get.aggregation.map((prop => (prop.field, prop))).toMap
    val newConfigProps = newConfig.disambiguation.strategy.get.aggregation.map((prop => (prop.field, prop))).toMap

    val aggFieldsRemoveChange = prevConfigProps.filter(p => !newConfigProps.isDefinedAt(p._1))
      .map(p => {
        Some(Change("Removed Aggregation Field", p._1, Option.empty, Map("Aggregation Field" -> p._1), "Attribute"))
      }).filter(_.isDefined).map(_.get).toSeq
    val aggFieldsAddedChange = newConfigProps.filter(p => !prevConfigProps.isDefinedAt(p._1))
      .map(p => {
        Some(Change("Added Aggregation Field", p._1, Option.empty, Map("Aggregation Field" -> p._1), "Attribute"))
      }).filter(_.isDefined).map(_.get).toSeq
    val aggFieldUpdatedChange = prevConfigProps.filter(p => newConfigProps.isDefinedAt(p._1))
      .map(p => {
        val newPropFunc = newConfigProps.get(p._1).get.function
        if (!p._2.function.equals(newPropFunc)) {
          Some(Change("Updated Aggregation Field", p._1, Option.empty, Map("Old Aggregation Function" -> p._2.function, "New Aggregation Function" -> newPropFunc), "Attribute"))
        } else Option.empty
      }).filter(_.isDefined).map(_.get).toSeq
    aggFieldsRemoveChange ++ aggFieldsAddedChange ++ aggFieldUpdatedChange
  }
}
