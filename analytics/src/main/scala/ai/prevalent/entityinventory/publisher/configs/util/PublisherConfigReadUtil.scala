package ai.prevalent.entityinventory.publisher.configs.util

import ai.prevalent.entityinventory.publisher.Publisher
import ai.prevalent.entityinventory.disambiguator.configs.specs.{ConfigurationItem}
import ai.prevalent.sdspecore.utils.ConfigUtils
import ai.prevalent.sdspecore.jobbase.LoggerBase
import org.apache.hadoop.fs.{FileSystem, LocatedFileStatus, Path, RemoteIterator}
import org.apache.spark.sql.SparkSession
import org.json4s.DefaultFormats

import java.net.URI
import scala.collection.mutable.ListBuffer

object PublisherConfigReadUtil extends LoggerBase{
  type PublisherConfig = ai.prevalent.entityinventory.publisher.configs.Config

  def getAllpublisherConfig(spark:SparkSession): List[PublisherConfig] = {

    val basePath = try {
        spark.conf.get("spark.sds.publisher.writebackBasePath")
    } catch {
      case e: NoSuchElementException =>
        LOGGER.error("Missing required config spark.sds.publisher.writebackBasePath")
        throw new IllegalArgumentException("Missing required config", e)
    }
    val uri = new URI(basePath)
    val publisherConfigs = new ListBuffer[PublisherConfig]
    if (uri.getScheme=="https" || uri.getScheme=="http") {
      val configurationItem:Seq[ConfigurationItem]= ConfigUtils.getConfig[Seq[ConfigurationItem]](spark, uri.toString, manifest[Seq[ConfigurationItem]], DefaultFormats)
      val eiInterConfigPath = spark.conf.get("spark.sds.restapi.configArtifactoryUri","") + spark.conf.get("spark.sds.restapi.eiSparkConfigsBasePath","")
      val configurationItemNames = configurationItem.map(x=>x.name)
      configurationItemNames.foreach(item => {
        val sparkJobConfig = eiInterConfigPath+item
        val disambiguationConfig:PublisherConfig = ConfigUtils.getConfig(spark, sparkJobConfig, manifest[PublisherConfig], Publisher.configFormats)
        publisherConfigs += disambiguationConfig
      })
    }
    else {
      val fs = FileSystem.get(uri, spark.sparkContext.hadoopConfiguration)
      val remoteFiles: RemoteIterator[LocatedFileStatus] = fs.listFiles(new Path(uri), false)
      while(remoteFiles.hasNext){
        val file: LocatedFileStatus = remoteFiles.next()
        val path = file.getPath
        val disambiguationConfig:PublisherConfig = ConfigUtils.getConfig(spark, path.toString, manifest[PublisherConfig], Publisher.configFormats)
        publisherConfigs += disambiguationConfig
      }
    }
    publisherConfigs.toList
  }

}
