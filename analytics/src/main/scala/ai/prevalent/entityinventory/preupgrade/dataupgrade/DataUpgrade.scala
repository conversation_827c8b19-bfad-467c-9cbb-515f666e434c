package ai.prevalent.entityinventory.preupgrade.dataupgrade

import ai.prevalent.sdspecore.sparkbase.{SDSSparkBase, SDSSparkBaseConfigurable}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.preupgrade.dataupgrade.configs.DataUpgradeArgs
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReaderFactory, SDSTableWriterFactory}
import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.{P_ID, RELATION_ID}
import org.apache.spark.sql.functions.{col, days, expr, first, lit, sha2, to_timestamp}
import org.apache.spark.sql.{DataFrame, Row, functions}
import org.slf4j.LoggerFactory
import ai.prevalent.entityinventory.preupgrade.dataupgrade.configs.SDSDataUpgradeProperties._

import scala.collection.parallel.CollectionConverters._
import scala.collection.parallel.ParMap


object DataUpgrade extends SDSSparkBase[DataUpgradeArgs] {
  private val logger = LoggerFactory.getLogger(this.getClass)


  override def execute(jobArgs: DataUpgradeArgs): Unit = {
      logger.info(s"Starting data upgrade process with args: $jobArgs")
      try {
        validateArgs(jobArgs)
        val sourceData = readTableWithFilter(jobArgs.inputTableName, jobArgs.dataUpgradeIntervalStart, jobArgs.dataUpgradeIntervalEnd)
        val classRelationType = if (sourceData.columns.contains(CLASS_COL)) CLASS_COL else RELATIONSHIP_NAME_COL
        val splittedColumns = splitColumns(sourceData, classRelationType)
        val splittedData = splittedColumns.keySet.map(typeValue => (typeValue,sourceData.filter(s"$classRelationType='$typeValue'"))).toMap
        val finalData = selectAndDerive(splittedData, splittedColumns)
        upgradeData(finalData, classRelationType, jobArgs.tableType, Option(jobArgs.outputSchemaName).getOrElse(DEFAULT_SCHEMA))

        logger.info("Data upgrade process completed successfully")
      }
    catch {
        case e: Exception =>
          logger.error("Unexpected error during data upgrade process", e)
          throw new RuntimeException("Data upgrade process failed", e)
      }

  }

  override def getInitParams: DataUpgradeArgs = new DataUpgradeArgs()

  def upgradeData(finalData: ParMap[String, DataFrame], classRelationType: String, tableType: String, schemaName: String) = {
    logger.info(s"Starting data upgrade for table type: $tableType")
    try {
      tableType match {
        case "resolver" => upgrade_resolver(finalData, classRelationType, schemaName)
        case "fragments" => upgrade_fragments(finalData, classRelationType, schemaName)
        case "resolution" => upgrade_resolution(finalData, classRelationType, schemaName)
      }
      logger.info(s"Successfully completed upgrade for table type: $tableType")
    } catch {
      case e: Exception =>
        logger.error(s"Failed to upgrade data for table type: $tableType", e)
    }
  }
  def validateArgs(args: DataUpgradeArgs): Unit = {
    if (!VALID_TABLE_TYPES.contains(args.tableType)) {
      throw new IllegalArgumentException(s"Invalid table type: ${args.tableType}. Must be one of: ${VALID_TABLE_TYPES.mkString(", ")}")
    }
  }
  def readTableWithFilter(inputtablename: String, upgrade_start_date: Long, upgrade_end_date: Long): DataFrame = {
    val reader = SDSTableReaderFactory.getDefault(spark)
    if (upgrade_start_date > 0 && upgrade_end_date > 0) {
      reader.read(inputtablename).filter(col(UPDATED_AT) <= to_timestamp(lit(upgrade_end_date).divide(1000)) && col(UPDATED_AT) >= to_timestamp(lit(upgrade_start_date).divide(1000)))
    }
    else {
      reader.read(inputtablename)
    }
  }

  def upgrade_resolver(sourceData: ParMap[String,DataFrame],tabletype: String,outputschemaname:String="kg"): Unit = {


    if (tabletype == "relationship_name"){
      val relPattern = outputschemaname + "." + "sds_ei__rel__resolver__"
      sourceData.foreach { value =>
        val tableName=relPattern + value._1.toLowerCase.replaceAll("\\s+", "_")
        val derived_data=value._2.withColumn("relationship_name", expr(s"'${value._1} Has Fragment'"))
        .withColumn("inverse_relationship_name", expr(s"'Resolved To ${value._1}'"))
        .withColumn("graph_id", sha2(functions.concat(col(RELATION_ID), col("disambiguated_relationship_id"), col("updated_at_ts")), 256))
        .withColumn("source_graph_id", sha2(functions.concat(col("disambiguated_relationship_id"), col("updated_at_ts")), 256))
        .withColumn("target_graph_id", sha2(functions.concat(col(RELATION_ID), lit("Fragment"), col("updated_at_ts")), 256))
        val resolverTableProps = Map("graph.edge.name" -> s"${value._1} Has Fragment", "graph.edge.source.name" -> value._1, "graph.edge.target.name" -> s"Fragment ${value._1}", "graph.cache.enabled" -> "true")

        writeDataFrame(tableName,derived_data,resolverTableProps)
      }

    }
    else{
      val eiPattern = outputschemaname + "." + "sds_ei__resolver__"
      sourceData.foreach { value =>
        val tableName=eiPattern + value._1.toLowerCase.replaceAll("\\s+", "_")
        val derived_data=value._2.withColumn("relationship_name", expr(s"'${value._1} Has Fragment'"))
          .withColumn("inverse_relationship_name", expr(s"'Resolved To ${value._1}'"))
          .withColumn("graph_id", sha2(functions.concat(col(P_ID), col("disambiguated_p_id"), col("updated_at_ts")), 256))
          .withColumn("source_graph_id", sha2(functions.concat(col("disambiguated_p_id"), col("updated_at_ts")), 256))
          .withColumn("target_graph_id", sha2(functions.concat(col(P_ID), lit("Fragment"), col("updated_at_ts")), 256))
        val resolverTableProps = Map("graph.edge.name" -> s"${value._1} Has Fragment", "graph.edge.source.name" -> value._1, "graph.edge.target.name" -> s"Fragment ${value._1}", "graph.cache.enabled" -> "true")
        writeDataFrame(tableName,derived_data,resolverTableProps)
      }
    }

  }

  def upgrade_fragments(sourceData: ParMap[String,DataFrame],tabletype: String,outputschemaname:String="kg"): Unit = {


    if (tabletype == "relationship_name"){
      val relPattern = outputschemaname + "." + "sds_ei__rel__fragment__"
      sourceData.foreach { value =>
        val tableName=relPattern + value._1.toLowerCase.replaceAll("\\s+", "_")
        val derived_data=value._2.withColumn("graph_id", sha2(functions.concat(col(RELATION_ID), lit("Fragment"), col("updated_at_ts")), 256))
        val fragmentTableProps = Map("graph.vertex.name" -> s"Fragment ${value._1}", "graph.cache.enabled" -> "true")

        writeDataFrame(tableName,derived_data,fragmentTableProps)
      }

    }
    else{
      val eiPattern = outputschemaname + "." + "sds_ei__fragment__"
      sourceData.foreach { value =>
        val tableName=eiPattern + value._1.toLowerCase.replaceAll("\\s+", "_")
        val derived_data=value._2.withColumn("graph_id", sha2(functions.concat(col(P_ID), lit("Fragment"), col("updated_at_ts")), 256))
        val fragmentTableProps = Map("graph.vertex.name" -> s"Fragment ${value._1}", "graph.cache.enabled" -> "true")
        writeDataFrame(tableName,derived_data,fragmentTableProps)
      }
    }

  }

  def upgrade_resolution(sourceData: ParMap[String,DataFrame],tabletype: String,outputschemaname:String="kg"): Unit = {


    if (tabletype == "relationship_name"){
      val relPattern = outputschemaname + "." + "sds_ei__rel__"
      sourceData.foreach { value =>
        val tableName=relPattern + value._1.toLowerCase.replaceAll("\\s+", "_") + "__publish"
        val derive_data=value._2.withColumn("graph_id", sha2(functions.concat(col(RELATION_ID), col("updated_at_ts")), 256))
          .withColumn("source_graph_id", sha2(functions.concat(col("source_p_id"), col("updated_at_ts")), 256))
          .withColumn("target_graph_id", sha2(functions.concat(col("target_p_id"), col("updated_at_ts")), 256))
        val sourceClass = derive_data.select("source_entity_class").first().getString(0)
        val targetClass = derive_data.select("target_entity_class").first().getString(0)
        val relName = derive_data.select("relationship_name").first().getString(0)

        val tablProps = Map("graph.edge.name" -> relName, "graph.edge.source.name" -> sourceClass, "graph.edge.target.name" -> targetClass)

        writeDataFrame(tableName,derive_data,tablProps)
      }

    }
    else{
      val eiPattern = outputschemaname + "." + "sds_ei__"
      sourceData.foreach { value =>
        val tableName=eiPattern + value._1.toLowerCase.replaceAll("\\s+", "_") + "__publish"
          val derive_data=value._2.withColumn("graph_id", sha2(functions.concat(col(P_ID), col("updated_at_ts")), 256))
        val tablProps = Map("graph.vertex.name" -> value._1)
        writeDataFrame(tableName,derive_data,tablProps)
      }
    }

  }

  def splitColumns(df: DataFrame, typeName: String) = {
    val aggFun = df.columns.map(c=> first(c,ignoreNulls = true).as(c))
    df.groupBy(typeName)
      .agg(aggFun.head, aggFun.tail:_*)
      .collect()
      .map( row => {
        val typeValue = row.getAs[String](typeName)
        val nonNullColumns = df.columns.filter(c => row.getAs[String](c) != null)
        (typeValue,nonNullColumns)
      }).toMap
  }

  def selectAndDerive(splitDataInfo:Map[String,DataFrame],splitColumnInfo:Map[String,Array[String]]) = {

    splitColumnInfo.map { case (key, columns) =>
      val df = splitDataInfo(key)
      val selectedDf = df.select(columns.map(col): _*)
      (key, selectedDf)

    }.par
  }
  def writeDataFrame(tableName: String, df: DataFrame,tableProps: Map[String, String] = Map.empty) = {
    try {
      val writer = SDSTableWriterFactory.getDefault(
        spark, tableProperties = tableProps, options = Map("partitionOverwriteMode" -> "dynamic"))
      writer.overwritePartition(df, tableName,Array(days(col(UPDATED_AT_TS))))
      logger.info(s"Successfully wrote table: $tableName")
    } catch {
      case e: Exception =>
        logger.error(s"Error writing table $tableName", e)

    }
  }
}
