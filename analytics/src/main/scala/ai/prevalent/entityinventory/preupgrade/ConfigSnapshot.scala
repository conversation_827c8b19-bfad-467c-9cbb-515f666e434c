package ai.prevalent.entityinventory.preupgrade

import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.entityinventory.loader.configs.specs.{Config => InventoryConfig}
import ai.prevalent.entityinventory.preupgrade.configs.ConfigSnapshotArgs
import ai.prevalent.entityinventory.relationship.extractor.configs.specs.{Config => RelationshipConfig}
import ai.prevalent.entityinventory.utils.EILOGGER
import ai.prevalent.sdspecore.sparkbase.{SDSSparkBase, SDSSparkBaseConfigurable}
import ai.prevalent.sdspecore.sparkbase.table.SDSTableWriterFactory
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.write
import org.slf4j.LoggerFactory

import java.net.URI
import scala.collection.mutable.ListBuffer

object ConfigSnapshot extends SDSSparkBase[ConfigSnapshotArgs] {
  private val logger = LoggerFactory.getLogger(this.getClass)
  implicit val formats: DefaultFormats.type = DefaultFormats

  def execute(args: ConfigSnapshotArgs): Unit = {
    val writer = SDSTableWriterFactory.getDefault(spark)
    val configSnapshots = getPreupgradeConfigList(args, spark)
    val outputDf = spark.createDataFrame(
      configSnapshots.map(snapshot =>
        ConfigRecord(snapshot.version, snapshot.config_item_type, snapshot.name, write(snapshot.config))
      )
    ).withColumn(UPDATED_AT_TS,expr(s"to_timestamp(${args.currentTimestamp}/1000)")).withColumn("write_time", current_timestamp())
    writer.overwritePartition(outputDf, args.tableName, Array("version", "config_item_type", "name").map(col))
  }

  override def getInitParams: ConfigSnapshotArgs = new ConfigSnapshotArgs()


  case class snapshotSpec(version: String, config_item_type: String, name: String, config: Either[InventoryConfig, RelationshipConfig])
  private case class ConfigRecord(version: String, config_item_type: String, name: String, config: String)

  private def getPreupgradeConfigList(args: ConfigSnapshotArgs, spark: SparkSession): Array[snapshotSpec] = {
    val baseUrl = spark.conf.get("spark.sds.restapi.configArtifactoryUri", "") +spark.conf.get("spark.sds.restapi.eiSparkConfigsBasePath", "")
    val configDir = baseUrl +"deployment_config"
    val uri = new URI(configDir)
    EILOGGER.jsonMiniPrint("uri",uri)
    val preupdConfigs = new ListBuffer[snapshotSpec]

    if (uri.getScheme == "https" || uri.getScheme == "http") {
      case class SourceModels(inventory_models: List[String], relationship_models: List[String])
      case class SparkJobConfigs(source_models: SourceModels)
      case class ConfigValue(spark_job_configs: SparkJobConfigs)
      case class DeploymentConfig(config_value: ConfigValue)

      val deploymentConfig = ConfigUtils.getConfig[DeploymentConfig](spark, uri.toString, manifest[DeploymentConfig], DefaultFormats)

      def processConfigs[T](configType: String, models: List[String])(implicit m: Manifest[T]): Unit = {
        logger.info(s"Processing $configType")
        models.foreach { modelName =>
          val sparkJobConfig = baseUrl +"spark-job-configs/"+ modelName
          logger.info(s"Processing $configType config for model: $modelName at path: $sparkJobConfig")

          try {
            val config = ConfigUtils.getConfig[T](spark, sparkJobConfig, m)
            preupdConfigs += snapshotSpec(
              version = args.version,
              config_item_type = configType,
              name = modelName,
              config = if (configType == "inventory_models") Left(config.asInstanceOf[InventoryConfig])
              else Right(config.asInstanceOf[RelationshipConfig])
            )
            logger.info(s"Successfully processed $configType config for model: $modelName")
          } catch {
            case e: Exception =>
              logger.error(s"Error processing $configType config for model: $modelName", e)
              throw e
          }
        }
      }
      processConfigs[InventoryConfig]("inventory_models",
        deploymentConfig.config_value.spark_job_configs.source_models.inventory_models)
      processConfigs[RelationshipConfig]("relationship_models",
        deploymentConfig.config_value.spark_job_configs.source_models.relationship_models)
    }
    preupdConfigs.toArray
  }
}
