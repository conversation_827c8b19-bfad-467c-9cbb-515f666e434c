package ai.prevalent.entityinventory.utils

import ai.prevalent.entityinventory.common.configs.TableConfig
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{KG_CONTENT_TYPE, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.{RELATION_ID}
import ai.prevalent.entityinventory.utils.EIUtil.spark
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions.{expr, lit, coalesce, col}
import org.apache.spark.sql.types.StringType

/**
 * Utility functions for Data Quality (DQ) evaluation for relationships, including enrichment with resolver tables.
 */
object DQRelationshipEvaluationUtils extends LoggerBase {

  /**
   * Column names for relationship tables.
   */
  val idCol: String = RELATION_ID
  val disambCol: String = "disambiguated_relationship_id"
  val objectTypeName: String = "relationship_name"

  /**
   * Reads and enriches each relationship table with disambiguation data if a resolver table is provided.
   *
   * @param modelInput Sequence of TableConfig entries containing table and optional resolver table.
   * @param objectName Name of the object type (for 'class' column).
   * @param reader     SDSTableReader to read tables.
   * @param upd        Update timestamp in epoch millis.
   * @return Map of table name to enriched DataFrame.
   */
  def enrichWithResolver(
    modelInput: Seq[TableConfig],
    objectName: String,
    reader: SDSTableReader,
    upd: Long
  ): Map[String, DataFrame] = {

    val filterCondition = expr(s"$UPDATED_AT_TS = to_timestamp($upd / 1000)")

    modelInput.map { model =>
      val mainDf = EIUtil.safeReadRelation(
        model.table_name,
        filterCondition,
        reader
      )

      val enrichedDf = model.resolver_table match {
        case resolver if resolver != null && resolver.nonEmpty =>
          val resolverTables = resolver.split(",").map(_.trim).filter(_.nonEmpty)

          if (resolverTables.isEmpty) {
            // If resolver string is empty or just commas, treat as no resolver
            mainDf.withColumn(disambCol, lit(null).cast(StringType))
          } else {
            // Read the first resolver table and select idCol and disambCol
            var currentResolverDf = EIUtil.safeReadRelationResolver(
              resolverTables.head,
              filterCondition,
              reader
            ).select(idCol, disambCol)

            // Join with subsequent resolver tables
            resolverTables.tail.foreach { currentResolverTableName =>
              val nextResolverDf = EIUtil.safeReadRelationResolver(
                  currentResolverTableName,
                  filterCondition,
                  reader
                ).select(idCol, disambCol)
                .withColumnRenamed(disambCol, "temp_disamb_col") // Rename to avoid conflict during join

              currentResolverDf = currentResolverDf.join(
                nextResolverDf,
                currentResolverDf.col(disambCol) === nextResolverDf.col(idCol),
                "left"
              ).select(currentResolverDf.col(idCol), nextResolverDf.col("temp_disamb_col").alias(disambCol))
            }

            // Join mainDf with the final currentResolverDf
            mainDf.join(currentResolverDf, Seq(idCol), "left")
              .withColumn(
                disambCol,
                coalesce(col(disambCol), col(idCol))
              )          }

        case _ =>
          // If no resolver, add disambiguated column as relationship_id
          mainDf.withColumn(disambCol, col(idCol))
      }

      val resultDF = enrichedDf
        .withColumn(objectTypeName, lit(objectName))
        .withColumn("table_name", lit(model.table_name))

      resultDF.printSchema()
      model.table_name -> resultDF
    }.toMap
  }
} 