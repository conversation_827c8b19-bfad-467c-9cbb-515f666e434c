package ai.prevalent.entityinventory.utils

import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.entityinventory.utils.DataFrameWithColumnLogged._
import ai.prevalent.sdspecore.jobbase.LoggerBase
import org.apache.spark.sql.{Column, DataFrame}
import org.apache.spark.sql.catalyst.expressions.{Attribute, Cast, EvalMode, Expression, LambdaFunction, NamedExpression, UnresolvedNamedLambdaVariable}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.jgrapht.alg.connectivity.ConnectivityInspector
import org.jgrapht.graph.{DefaultDirectedGraph, DefaultEdge}
import org.jgrapht.traverse.{DepthFirstIterator, TopologicalOrderIterator}

import java.util
import scala.collection.JavaConverters._
import scala.collection.mutable
import scala.collection.mutable.{Map => MutableMap}
import scala.language.postfixOps

object SparkUtil extends LoggerBase {

  def unionByName(dfList: DataFrame*): DataFrame = {
    val schema: Map[String, DataType] = dfList
      .flatMap(_.schema.fields)
      .groupBy(_.name)
      .view
      .mapValues { fields =>
        fields.map(_.dataType).reduce { (dataType1, dataType2) =>
          (dataType1,dataType2) match {
            case (x:StructType,y:StructType) =>
              x.fields.foldLeft(y)((st,f) =>  if (st.names.contains(f.name)) st else st.add(f))
            case (x:ArrayType,y:DataType) => x
            case (x:DataType,y:ArrayType) => y
            case (StringType, _: NumericType) => StringType
            case (_: NumericType, StringType) => StringType
            case (BooleanType, StringType) => StringType
            case (StringType, BooleanType) => StringType
            case (x:DataType,y:NullType) => x
            case (x:DataType,y:DataType) => x
          }
        }
      }.toMap
    val newSchema =  schema.foldLeft(new StructType())((schema,field)=> schema.add(StructField(field._1,field._2,true)))
    LOGGER.info(s"Full Schema - ${newSchema}")
    try {
      val adjustedDFs = dfList
        .map(SchemaEvolutionUtil.evolveSchema(_,newSchema))
        .toList

      LOGGER.info("Schema adjusted for all df")

      adjustedDFs.reduce((x,y) => x.unionByName(y,true))
    } catch {
      case e: Exception =>
        LOGGER.error(s"Failed to perform union operation: input contained ${dfList.size} DataFrames, but they were all empty or produced empty results after schema evolution", e)
        throw e
    }
  }

  def getDependendProperty(properties: Seq[Property], fieldName: String): mutable.Set[String] = {
    val graph = createPropertyGraph(properties)
    getDependendProperty(fieldName, graph)
  }

  def getDependendProperty(fieldName: String, graph: DefaultDirectedGraph[String, EIEdge]): mutable.Set[String] = {
    val fields = new mutable.HashSet[String]()
    val bfsIterator = new DepthFirstIterator(graph, fieldName);
    while (bfsIterator.hasNext()) {
      val v = bfsIterator.next()
      fields.add(v)
    }
    fields
  }

  def createPropertyGraph(properties: Seq[Property]) ={
    val graph = new DefaultDirectedGraph[String, EIEdge](new EIEdge().getClass)
    properties.foreach(property => {
      val attributes = expr(property.colExpr).expr.references.map(_.name)
      attributes.foreach(graph.addVertex(_))
      graph.addVertex(property.colName)
      attributes.filter(_ != property.colName).foreach(graph.addEdge(property.colName, _))
    })
    graph
  }

  def propertyExpressionReplace(properties: Seq[Property], df: DataFrame): DataFrame = {
    val graph = new DefaultDirectedGraph[String, EIEdge](new EIEdge().getClass)

    def getAllReferences(expr: Expression): Set[String] = {
      val refs = new mutable.HashSet[String]

      def processExpr(exp: Expression, lambdaArgs: Set[String] = Set.empty): Unit = {
        exp match {
          case attr: Attribute if !lambdaArgs.contains(attr.name) =>
            refs.add(attr.name.split("[.]")(0))
          case lm: LambdaFunction =>
            processExpr(lm.function, lm.arguments.map(_.name).toSet)
          case v: UnresolvedNamedLambdaVariable if !lambdaArgs.contains(v.sql) =>
            refs.add(v.sql)
          case other =>
            other.children.foreach(child => processExpr(child.asInstanceOf[Expression], lambdaArgs))
        }
      }

      processExpr(expr)
      refs.toSet
    }

    properties.foreach(property => {
      val attributes = getAllReferences(expr(property.colExpr).expr)
      attributes.foreach(graph.addVertex(_))
      graph.addVertex(property.colName)
      attributes.filter(_ != property.colName).foreach(graph.addEdge(property.colName, _))
    })

    LOGGER.info(s"Edge Set - ${graph.edgeSet()}")
    val propsOrder = properties
      .map(p => (p.colName,getDependendProperty(p.colName, graph).toList))
      .toMap
    val dependencyOrder = resolveDependencyLevels(propsOrder)
    LOGGER.info(s"Dependency Order - ${dependencyOrder.mkString("Array(", ", ", ")")}")
    LOGGER.info(s"Properties Order - $propsOrder")
    dependencyOrder.foldLeft(df)((updDF, props) => {
      val propertiesOrderd = properties.filter(p => props.contains(p.colName))
      selectExpressionAndFieldsLogged(propertiesOrderd, updDF)
    })
  }

  def selectExpressionAndFieldsLogged(properties: Seq[Property], sourceDF: DataFrame): DataFrame = {
    // Use withColumnLogged for each property
    //    properties.foldLeft(sourceDF) { (df, property) =>
    //      df.withColumnLogged(property.colName, expr(property.colExpr))
    //    }

    try {
      val propCols = properties.map(p => expr(p.colExpr).as(p.colName))
      val dfCols = sourceDF
        .columns
        .diff(properties.map(_.colName)) // Exclude the columns of the filtered properties
        .intersect(sourceDF.columns) // Ensure that the remaining columns exist in the DataFrame
        .map(name => expr(s"`$name`").as(name)) //
      val allCols = propCols ++ dfCols
      sourceDF.select(allCols: _*)
    } catch {
      case e: Exception =>
        properties.foreach { prop =>
          sourceDF.withColumnLogged(prop.colName, expr(prop.colExpr))
        }
        sourceDF
    }
  }

  //  def selectExpressionAndFields(properties: Seq[Property], sourceDF: DataFrame): DataFrame = {
  //    val propCols = properties.map(p => expr(p.colExpr).as(p.colName))
  //    val dfCols = sourceDF
  //      .columns
  //      .diff(properties.map(_.colName))  // Exclude the columns of the filtered properties
  //      .intersect(sourceDF.columns)     // Ensure that the remaining columns exist in the DataFrame
  //      .map(name => expr(s"`$name`").as(name)) //
  //    val allCols = propCols ++ dfCols
  //    sourceDF.select(allCols: _*)
  //  }

  /**
   *
   * @param properties
   * @return
   */
  def propertyExpressionReplace(properties: Seq[Property]): Seq[Property] = {
    val graph = new DefaultDirectedGraph[String, DefaultEdge](new DefaultEdge().getClass)
    properties.foreach(property => {
      val attributes = getAllReferences(expr(property.colExpr).expr)
      attributes.foreach(graph.addVertex(_))
      graph.addVertex(property.colName)
      attributes.filter(_ != property.colName).foreach(graph.addEdge(property.colName, _))
    })

    LOGGER.info(s"Edge Set - ${graph.edgeSet()}")
    val sub = new TopologicalOrderIterator[String, DefaultEdge](graph)
    var propertiesOrder = List.empty[String]

    while (sub.hasNext) {
      val vertex = sub.next
      propertiesOrder = propertiesOrder :+ vertex
    }

    // reversing the topological order output ,
    // this gives the order in which the we replace the properties with expression
    propertiesOrder = propertiesOrder.reverse.intersect(properties.map(_.colName))
    LOGGER.info(s"Replacing Order - $propertiesOrder")
    val propertiesMap = properties.map(prop => (prop.colName, prop)).toMap
    val replaceExprns = MutableMap.empty[String, String]
    val newProperties = propertiesOrder.map(propertyName => {

      val property = propertiesMap(propertyName)
      val newExpr = if (property.fieldsSpec.replaceExpression.getOrElse(true)) {

        LOGGER.debug(s"Replace property $propertyName with expression $replaceExprns")
        val sdsExpr = expr(property.colExpr).expr
        sdsExpr
          .transformUp {
            case exp:Expression => expressionReplace(exp,replaceExprns)
          }
          .transformUp {
            case ca: Cast  => new SDSCast(ca.child, ca.dataType, ca.timeZoneId, ca.evalMode)
            case lm: LambdaFunction => new SDSLambdaFunction(lm.function, lm.arguments, lm.hidden)
          }
      }
      else expr(property.colExpr).expr

      val newSQl = if (property.fieldsSpec.convertEmptyToNull) s"CASE WHEN btrim(cast(${newExpr.sql} as STRING),'[\\t ]') = '' THEN null ELSE ${newExpr.sql} END" else newExpr.sql
      replaceExprns.put(propertyName,newSQl)
      property.copy(colName = propertyName, colExpr = newSQl.replace("__ei__internal__temp", "").replace("unspecifiedframe$()", ""), fieldsSpec = property.fieldsSpec)
    })
    LOGGER.info(s"New Property - $newProperties")
    newProperties
  }

  def getAllReferences(expr: Expression): Set[String] = {
    val refs = new mutable.HashSet[String]

    def processExpr(exp: Expression, lambdaArgs: Set[String] = Set.empty): Unit = {
      exp match {
        case attr: Attribute if !lambdaArgs.contains(attr.name) =>
          refs.add(attr.name.split("[.]")(0))
        case lm: LambdaFunction =>
          processExpr(lm.function, lm.arguments.map(_.name).toSet)
        case v: UnresolvedNamedLambdaVariable if !lambdaArgs.contains(v.sql) =>
          refs.add(v.sql)
        case other =>
          other.children.foreach(child => processExpr(child.asInstanceOf[Expression], lambdaArgs))
      }
    }

    processExpr(expr)
    refs.toSet
  }


  def propertyExpressionReplace(properties: mutable.LinkedHashMap[String, String], df: DataFrame): DataFrame = {
    val originalColumns = df.columns.toSet
    val missingColumns = findMissingBaseColumns(properties, originalColumns)
    val dfWithMissingCols = if (missingColumns.nonEmpty) {
      LOGGER.info(s"Adding missing self-referencing columns: ${missingColumns.mkString(", ")}")
      val missingColsExpressions = missingColumns.map(col => lit(null).as(col))
      df.select(df.columns.map(c => col(s"`$c`")) ++ missingColsExpressions.toSeq: _*)
    } else {
      df
    }

    val graph = new DefaultDirectedGraph[String, EIEdge](new EIEdge().getClass)
    properties.foreach { case (colName, colExpr) =>
      val attributes = getAllReferences(expr(colExpr).expr)
      attributes.foreach(graph.addVertex(_))
      graph.addVertex(colName)
      attributes.filter(_ != colName).foreach(graph.addEdge(colName, _))
    }

    LOGGER.info(s"Edge Set - ${graph.edgeSet()}")
    val propsOrder = properties
      .map { case (colName, _) => (colName, getDependendProperty(colName, graph).toList) }
      .toMap
    val dependencyOrder = resolveDependencyLevels(propsOrder)
    LOGGER.info(s"Dependency Order - ${dependencyOrder.mkString("Array(", ", ", ")")}")
    LOGGER.info(s"Properties Order - $propsOrder")

    dependencyOrder.foldLeft(dfWithMissingCols)((updDF, props) => {
      val propertiesOrderd = props.map(colName =>
        Property(colName, properties(colName))
      ).toSeq
      selectExpressionAndFieldsLogged(propertiesOrderd, updDF)
    })
  }


  def resolveDependencyLevels(dependencyMap: Map[String, List[String]]): List[List[String]] = {
    import scala.collection.mutable

    val allFields = dependencyMap.keySet

    // Step 1: Initialize in-degree to 0 for all keys
    val inDegree = mutable.Map[String, Int]().withDefaultValue(0)
    allFields.foreach(field => inDegree(field) = 0)

    val adjList = mutable.Map[String, List[String]]().withDefaultValue(Nil)

    // Step 2: Only count internal dependencies (ignore self-dependencies and non-key dependencies)
    for ((field, deps) <- dependencyMap) {
      for (dep <- deps if dep != field && allFields.contains(dep)) {
        adjList(dep) ::= field
        inDegree(field) += 1
      }
    }

    val result = mutable.ListBuffer[List[String]]()
    val visited = mutable.Set[String]()

    // Step 3: Start from fields with no internal dependencies
    var queue = mutable.Queue[String](allFields.filter(inDegree(_) == 0).toSeq: _*)

    while (queue.nonEmpty) {
      val currentLevel = queue.dequeueAll(_ => true).distinct
      result += currentLevel.toList
      for (field <- currentLevel) {
        visited += field
        for (dependent <- adjList(field)) {
          inDegree(dependent) -= 1
          if (inDegree(dependent) == 0 && !visited.contains(dependent)) {
            queue.enqueue(dependent)
          }
        }
      }
    }

    if (visited.size != allFields.size) {
      throw new RuntimeException("Cycle or unresolved dependencies found.")
    }

    result.toList
  }

  def getExpressionConnectedComponents(properties: Seq[Property]): Array[Array[String]] = {
    val graph = createPropertyGraph(properties)
    val connectedComponents = new ConnectivityInspector(graph)

    val sub = new TopologicalOrderIterator[String, EIEdge](graph).asScala
    var order = Array.empty[String]
    sub.foreach( v => {order = order:+v})
    order = order.reverse

    var fields = Array.empty[Array[String]]
    val kk: util.List[util.Set[String]] = connectedComponents.connectedSets()
    kk.forEach( con => {
      var ar = Array.empty[String]
      con.forEach( p => {
        ar = ar:+p})
      fields = fields:+ ar
    })
    fields
  }


  def getSRDMProperty(fields:Array[String],config:Config) ={
    val cols = config.allProperties.map(_.colName)
    val srDMprop = fields
      .filter( f => !cols.contains(f.split("[.]")(0)))
      .map( f => Property(s"""srdm__${f.toLowerCase.replaceAll("[.]","_")}""",f))
    println(s"srdm prop${srDMprop.toList}")
    srDMprop
  }

  def getSRDMColumns(config:Config,fields: Array[String]): Map[String,String] = {
    val names = config.allProperties.map(_.colName)
    val srMap = config.allProperties.flatMap(k => expr(k.colExpr).expr.references.map(_.name))
      .filter( p => !names.contains(p))
      .map( f => (s"""srdm__${f.toLowerCase.replaceAll("[.]","_")}""",f))
      .toMap
    println(s"srMap $srMap")
    fields.map( f =>{
      if(srMap.contains(f))
        (f,s"""srdm__${srMap.get(f).get}""")
      else (f,f)
    }).toMap
  }


  def expressionReplace(exp: Expression, replaceExprns: MutableMap[String, String]): Expression = {
    def transformExpression(attr: Attribute, attrName: String): Expression = {
      LOGGER.info(s"Replacing attribute $attr split $attrName with expression ${replaceExprns.get(attrName)}")
      val replaceExpr = replaceExprns(attrName)
      val replacedSql = replaceExpr ++ s"${attr.name.replaceAll(attrName, "")}"
      val sdsExpr = expr(replacedSql).expr
      sdsExpr.transformUp {
        case attr: Attribute => expr(s"${attr.name}__ei__internal__temp").expr.asInstanceOf[Attribute]
        case ca: Cast => new SDSCast(ca.child, ca.dataType, ca.timeZoneId, ca.evalMode)
        case lm: LambdaFunction => new SDSLambdaFunction(lm.function, lm.arguments, lm.hidden)
      }
    }

    exp match {
      case lm: LambdaFunction =>
        val lambdaArgs = lm.arguments.map(_.name).toSet

        val newBody = lm.function.transformUp {
          case v: UnresolvedNamedLambdaVariable
            if !lambdaArgs.contains(v.name) && replaceExprns.contains(v.name) =>
            expr(replaceExprns(v.name)).expr
          case attr: Attribute
            if !lambdaArgs.contains(attr.name) && replaceExprns.contains(attr.name.split("[.]")(0)) =>
            val attrName = attr.name.split("[.]")(0)
            transformExpression(attr, attrName)
          case exp: Expression => exp
        }

        new SDSLambdaFunction(newBody, lm.arguments, lm.hidden)

      case attr: Attribute if replaceExprns.contains(attr.name.split("[.]")(0)) =>
        val attrName = attr.name.split("[.]")(0)
        transformExpression(attr, attrName)
      case exp: Expression => exp
    }
  }

  def findMissingBaseColumns(propertiesMap: mutable.LinkedHashMap[String, String],
                             originalColumns: Set[String]): Set[String] = {
    val missingColumns = mutable.Set[String]()

    propertiesMap.foreach { case (colName, colExpr) =>
      val referencedColumns = getAllReferences(expr(colExpr).expr).toSet

      // Check if column references itself and is not in original columns
      if (referencedColumns.contains(colName) && !originalColumns.contains(colName)) {
        missingColumns.add(colName)
      }
    }

    missingColumns.toSet
  }
}

object CasePropertyExpressionReplacer extends LoggerBase {
  def caseSensitivePropertyExpressionReplace(properties: Seq[Property], identicalColumnSequences: Seq[Seq[String]]): Seq[Property] = {
    val graph = new DefaultDirectedGraph[String, DefaultEdge](new DefaultEdge().getClass)
    properties.foreach(property => {
      val attributes = expr(property.colExpr).expr.references.map(_.name.split("[.]")(0))
      attributes.foreach(graph.addVertex(_))
      graph.addVertex(property.colName)
      attributes.filter(_ != property.colName).foreach(graph.addEdge(property.colName, _))
    })

    val sub = new TopologicalOrderIterator[String, DefaultEdge](graph)
    var propertiesOrder = List.empty[String]

    while (sub.hasNext) {
      val vertex = sub.next
      propertiesOrder = propertiesOrder :+ vertex
    }

    propertiesOrder = propertiesOrder.reverse.intersect(properties.map(_.colName))
    val propertiesMap = properties.map(prop => (prop.colName, prop)).toMap
    val newProperties = propertiesOrder.map(propertyName => {
      val property = propertiesMap(propertyName)
      val newExpr = if (!property.fieldsSpec.caseSensitiveExpression) {
        val sdsExpr = expr(property.colExpr).expr
        sdsExpr
          .transformUp {
            case exp: Expression =>
              val replacedExp = caseExpressionReplace(exp, identicalColumnSequences)
              replacedExp
          }
          .transformUp {
            case ca: Cast  => new SDSCast(ca.child, ca.dataType, ca.timeZoneId, ca.evalMode)
            case lm: LambdaFunction => new SDSLambdaFunction(lm.function, lm.arguments, lm.hidden)
          }
      } else {
        val originalExpr =expr(property.colExpr).expr
        originalExpr
      }
      val newProperty = property.copy(colName = propertyName, colExpr = newExpr.sql.replace("__ei__internal__temp", "").replace("unspecifiedframe$()", ""), fieldsSpec = property.fieldsSpec)
      newProperty
    })
    newProperties
  }

  def caseExpressionReplace(exp: Expression, identicalColumnSequences: Seq[Seq[String]]): Expression = {
    exp.transform {
      case attr: Attribute if attr.references.nonEmpty && identicalColumnSequences.map(_.map(_.toLowerCase)).exists(_.contains(attr.references.head.name.toLowerCase())) =>
        val attrName = attr.references.head.name
        val sequence = identicalColumnSequences
          .find(_.exists(_.equalsIgnoreCase(attrName)))
          .getOrElse(Seq(attr.references.head.name))
        if (sequence.length > 1) {
          val replacedSql = s"COALESCE(${sequence.map(col => s"${col}__ei__internal__temp").mkString(", ")})"
          val replacedExpr = expr(replacedSql).expr
          replacedExpr
        } else {
          val replacedSql = s"${sequence.map(col => s"${col}__ei__internal__temp").mkString(", ")}"
          val replacedExpr = expr(replacedSql).expr
          replacedExpr
        }
      case exp: Expression => exp
    }
  }
}

class SDSLambdaFunction(override val function: Expression, override val arguments: Seq[NamedExpression], override val hidden: Boolean = false) extends LambdaFunction(function,arguments,hidden){
  override def sql = {
    val attrSql = if (arguments.size > 1) arguments.map(_.sql).mkString("(", ",", ")") else arguments(0).sql
    val functionStr = function.sql
    s"$attrSql -> $functionStr"
  }
  override def canEqual(that: Any): Boolean = null != that && that.isInstanceOf[SDSLambdaFunction]

  override def equals(that: Any): Boolean = that match {
    case that: SDSLambdaFunction => this.canEqual(that) && this.hashCode() == that.hashCode()
    case _ => false
  }

  override def hashCode(): Int = {
    ("SDSLambdaFunction" + this.sql).hashCode
  }
}

class SDSCast(override val child: Expression,
              override val dataType: DataType,
              override val timeZoneId: Option[String] ,
              override val evalMode: EvalMode.Value ) extends Cast(child,dataType,timeZoneId,evalMode){
  override def sql: String =  s"CAST(${child.sql} AS ${dataType.sql})"

  override def canEqual(that: Any): Boolean = null !=that && that.isInstanceOf[SDSCast]
  override def equals(that: Any): Boolean = that match {
    case that:SDSCast => this.canEqual(that) && this.hashCode() == that.hashCode()
    case _ => false
  }
  override def hashCode(): Int = {
    ("SDSCAST"+this.sql).hashCode
  }
}

object SchemaEvolutionUtil {

  def evolveSchema(df: DataFrame, newSchema: StructType): DataFrame = {
    val dfSchema = df.schema
    val newCols = newSchema.fields.map(tableField => {
      val isColExists = dfSchema.names.contains(tableField.name)
      val updCol = tableField.dataType match {
        case structType: StructType if isColExists => from_json(to_json(col(tableField.name)), structType)
        case arrayType: ArrayType if arrayType.elementType.isInstanceOf[StructType] && isColExists => from_json(to_json(col(tableField.name)), arrayType)
        case arrayType: ArrayType if isColExists && !dfSchema(tableField.name).dataType.isInstanceOf[ArrayType] => when(col(tableField.name).isNotNull,array(tableField.name)).otherwise(lit(null).cast(arrayType))
        case stringType: StringType if isColExists && !dfSchema(tableField.name).dataType.isInstanceOf[StringType] =>
          col(tableField.name).cast(StringType)
        case c if isColExists && !dfSchema(tableField.name).dataType.sql.equals(c.sql) && !c.isInstanceOf[NullType] =>
          expr(s"cast(${tableField.name} as ${c.sql})")
        case _ if isColExists => col(tableField.name)
        case _ if !isColExists => lit(null).cast(tableField.dataType)
        case _ => null
      }
      updCol.as(tableField.name)
    })
    df.select(newCols:_*)
  }

}

class EIEdge extends DefaultEdge{
  override def getSource = super.getSource
  override def getTarget = super.getTarget
}

object DataFrameWithColumnLogged {
  implicit class DataFrameOps(df: DataFrame) extends LoggerBase {
    def withColumnLogged(colName: String, colExpr: Column): DataFrame = {
      try {
        df.withColumn(colName, colExpr)
      } catch {
        case e: Exception =>
          LOGGER.error(s"Failed to add column '$colName' with expression: ${colExpr.expr.sql}")
          throw new RuntimeException(s"Failed to add column '$colName' with expression: ${colExpr.expr.sql}", e)
      }
    }
  }
}