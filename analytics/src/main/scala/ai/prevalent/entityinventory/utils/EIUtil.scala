package ai.prevalent.entityinventory.utils

import ai.prevalent.entityinventory.common.configs.{EIJobArgs, Entity, Property}
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import org.apache.spark.sql.catalyst.expressions.{Attribute, Cast, LambdaFunction}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, DecimalType, LongType, NullType, StringType, StructType}
import org.apache.spark.sql.{Column, DataFrame, SparkSession}
import scala.collection.mutable.LinkedHashMap
import ai.prevalent.entityinventory.utils.DataFrameWithColumnLogged._

object EIUtil extends  LoggerBase{
  def spark = SparkSession.active

  def readPrevDF(args:EIJobArgs, eiDF:DataFrame): DataFrame = {
    val maxUpd = eiDF.filter(expr(s"to_timestamp(${args.currentUpdateDate}/1000) > $UPDATED_AT_TS")).select(max(col(UPDATED_AT_TS)).as(UPDATED_AT_TS))
      .withColumnLogged(UPDATED_AT,unix_millis(col(UPDATED_AT_TS)))
      .map(row => row.getAs[Long](UPDATED_AT))(spark.implicits.newLongEncoder).collect()
      .lastOption

    if(maxUpd.isDefined) {
      args.prevUpdateDate = if(args.prevUpdateDate <= 0) maxUpd.get else args.prevUpdateDate
      args.parsedIntervalStartEpoch = if(args.parsedIntervalStartEpoch <=0 ) maxUpd.get+1 else args.parsedIntervalStartEpoch
    } else {
      args.prevUpdateDate = if(args.prevUpdateDate <=0 ) -1 else args.prevUpdateDate
      args.parsedIntervalStartEpoch = if(args.parsedIntervalStartEpoch <=0) args.srdmHistoricalParsedIntervalStartEpoch else args.parsedIntervalStartEpoch
    }
    LOGGER.info(s"$args")
    eiDF.filter(expr(s"$UPDATED_AT_TS = to_timestamp(${args.prevUpdateDate}/1000)"))
  }

  def getPrevConfig(dataFrame: DataFrame, jobArgs: EIJobArgs): Option[String] = {
    val config = dataFrame.filter(s"$KG_CONTENT_TYPE='config'").select("kg_config").map(row => row.getAs[String]("kg_config"))(spark.implicits.newStringEncoder).collect().lastOption
    LOGGER.info(s"Previous config found - $config")
    if(jobArgs.processDeltaProperty)
      config
    else {
      LOGGER.info("Process delta properties is set to false, not considering the prev config")
      Option.empty
    }
  }

  def prepareInvDFWithConfig(config:String, invDF:DataFrame, args: EIJobArgs, spark: SparkSession) = {
    val cDF = spark.sql(
      s"""SELECT to_timestamp(${args.currentUpdateDate}/1000) AS ${SDSProperties.schema.UPDATED_AT_TS},
         |${args.currentUpdateDate} AS ${SDSProperties.schema.UPDATED_AT},
         |'config' AS $KG_CONTENT_TYPE""".stripMargin).withColumnLogged("kg_config", lit(config)
    )
   SparkUtil.unionByName(invDF.withColumnLogged(s"$KG_CONTENT_TYPE",lit("data")), cDF)
  }

  def safeReadEntity(tableName:String, filterCondition: Column = lit(true), reader:SDSTableReader,
                     default:DataFrame = spark.emptyDataFrame
                 .withColumnLogged(UPDATED_AT,lit(null).cast(LongType))
                 .withColumnLogged(KG_CONTENT_TYPE, lit(null).cast(StringType))
                 .withColumnLogged("kg_config", lit(null).cast(StringType))
                 .withColumnLogged(P_ID,lit(null).cast(StringType))
                 .withColumnLogged(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
                 .withColumnLogged(ORIGIN,lit(null).cast(StringType))
                 .withColumnLogged(LAST_FOUND, lit(null).cast(LongType))
              ) : DataFrame ={
    safeRead(tableName = tableName, filterCondition = filterCondition, reader = reader, default = default)
  }

  def safeReadRelation(tableName: String, filterCondition: Column = lit(true), reader: SDSTableReader,
               default: DataFrame = spark.emptyDataFrame
                 .withColumnLogged(UPDATED_AT, lit(null).cast(LongType))
                 .withColumnLogged(KG_CONTENT_TYPE, lit(null).cast(StringType))
                 .withColumnLogged("kg_config", lit(null).cast(StringType))
                 .withColumnLogged("relationship_id", lit(null).cast(StringType))
                 .withColumnLogged(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
                 .withColumnLogged(LAST_FOUND, lit(null).cast(LongType))

              ): DataFrame = {
    safeRead(tableName = tableName, filterCondition = filterCondition, reader = reader, default = default)
  }

  private def safeRead(tableName:String, filterCondition:Column, reader:SDSTableReader, default:DataFrame) ={
    val tableExists = reader.isTableExists(tableName)
    if (!tableExists) {
      LOGGER.warn(s"Table '$tableName' not found. Returning empty DataFrame with some mandatory columns.")
    }
    val readDF = reader.readOrElse(tableName, default).filter(filterCondition)
    if(readDF.columns.contains(KG_CONTENT_TYPE))
      readDF
    else
      readDF.withColumnLogged(KG_CONTENT_TYPE, lit("data")).withColumnLogged("kg_config",lit(null).cast(StringType))
  }

  def safeReadResolver(tableName: String, filterCondition: Column, reader: SDSTableReader,
                       default: DataFrame = spark.emptyDataFrame
                         .withColumnLogged(UPDATED_AT, lit(null).cast(LongType))
                         .withColumnLogged("p_id", lit(null).cast(StringType))
                         .withColumnLogged("disambiguated_p_id", lit(null).cast(StringType))
                         .withColumnLogged("class", lit(null).cast(StringType))
                         .withColumnLogged(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
                      ): DataFrame = {
    if (tableName == null) {
      return default
    }
    val readDF = reader.readOrElse(tableName, default)
    readDF
      .filter(filterCondition)
  }

  def safeReadRelationResolver(tableName: String, filterCondition: Column, reader: SDSTableReader,
                               default: DataFrame = spark.emptyDataFrame
                                 .withColumn(UPDATED_AT, lit(null).cast(LongType))
                                 .withColumn("relationship_id", lit(null).cast(StringType))
                                 .withColumn("disambiguated_relationship_id", lit(null).cast(StringType))
                                 .withColumn("relationship_name", lit(null).cast(StringType))
                                 .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
                              ): DataFrame = {
    if (tableName == null) {
      return default
    }
    val readDF = reader.readOrElse(tableName, default)
    readDF
      .filter(filterCondition)
  }


  def preProcesmptyStringtoNull(df:DataFrame):DataFrame ={
    val strCols = df.schema.fields
      .filter(_.dataType.equals(StringType))
      .map(_.name)
    val strColsEmptyRemoved = strCols.map(field => expr(s"""CASE WHEN btrim($field,'\\r\\n\\t ')!='' THEN btrim($field,'\\r\\n\\t ') END""").as(field))
    val otherCols = df.columns.diff(strCols).map(col(_))
    val finalCols = otherCols ++ strColsEmptyRemoved
    df.select(finalCols:_*)
  }

  def findLatestUpdateAttrExpr(invDF:DataFrame, entityConfig: Entity) : LinkedHashMap[String, String] = {
    val exprMap = LinkedHashMap[String, String]()

    if (entityConfig.lastUpdateFields.nonEmpty) {
      entityConfig.lastUpdateFields.foreach{field =>
        val exprStr = if (!invDF.columns.contains(field)) {
          s"CAST(NULL AS STRING)"
        } else {
          invDF.schema(field).dataType match {
            case _: StructType => s"to_json(col($field))"
            case _: ArrayType => s"concat_ws('<-->', array_sort($field))"
            case _ => s"CAST($field AS STRING)"
          }
        }
        exprMap.put(s"prev_cal__$field", exprStr)
      }

      val lastUpdatedAttrExpr = entityConfig.lastUpdateFields.map { field =>
        s"""'$field',named_struct('isChangedInCurrentRun',COALESCE($PREV_ATTRS.$field,'COALESCE-dummy') != COALESCE(prev_cal__$field,'COALESCE-dummy') AND $PREV_ATTRS.$UPDATED_AT IS NOT NULL,
           'prev',CASE WHEN COALESCE($PREV_ATTRS.$field,'COALESCE-dummy') != COALESCE(prev_cal__$field,'COALESCE-dummy') AND $PREV_ATTRS.$UPDATED_AT IS NOT NULL
             THEN named_struct('value',CAST($PREV_ATTRS.$field AS STRING),'$UPDATED_AT',$PREV_ATTRS.$UPDATED_AT,'$LAST_FOUND',$PREV_ATTRS.$LAST_FOUND)
             ELSE prev__$LAST_UPDATED_ATTRS.$field.prev
           END,'last_changed',CASE WHEN COALESCE($PREV_ATTRS.$field,'COALESCE-dummy') != COALESCE(prev_cal__$field,'COALESCE-dummy') AND $PREV_ATTRS.$UPDATED_AT IS NOT NULL
             THEN named_struct('value',prev_cal__$field,'$UPDATED_AT',$UPDATED_AT,'$LAST_FOUND',$LAST_FOUND)
             ELSE prev__$LAST_UPDATED_ATTRS.$field.last_changed END)
       """
      }.mkString("named_struct(", ",", ")")

      exprMap.put(LAST_UPDATED_ATTRS, lastUpdatedAttrExpr)

      val lastUpdatedAtExpr = entityConfig.lastUpdateFields
        .map(field => s"$LAST_UPDATED_ATTRS.$field.last_changed.$LAST_FOUND")
        .mkString("greatest(", ",", s", $FIRST_FOUND)")

      exprMap.put(LAST_UPDATED_DATE, lastUpdatedAtExpr)
    }

    exprMap
  }
  def removeNullFields(outputDf: DataFrame): DataFrame ={
    val nullFields = outputDf.schema
      .filter(f => f.dataType.isInstanceOf[NullType] || (f.dataType.isInstanceOf[ArrayType] && f.dataType.asInstanceOf[ArrayType].elementType.isInstanceOf[NullType]))
      .map(_.name)
    val nullFieldsRemovedDf = outputDf.drop(nullFields: _*)
    nullFieldsRemovedDf
  }

  def updateOLAPDataTypes(olapDF:DataFrame, skipColumns:Set[String] = Set.empty): DataFrame = {
    olapDF.schema.fields.filterNot(f => skipColumns.contains(f.name))
      .foldLeft(olapDF)((df, property) => {
      property.dataType match {
        case _:StructType => df.withColumnRenamed(property.name,s"${property.name}__struct").withColumn(s"${property.name}",to_json(col(s"${property.name}__struct")))
        case ar:ArrayType if(ar.elementType.isInstanceOf[StructType]) => df.withColumnRenamed(property.name,s"${property.name}__struct").withColumn(s"${property.name}",to_json(col(s"${property.name}__struct")))
        case spe:ArrayType if(spe.elementType.isInstanceOf[StringType]) => df.withColumn(property.name, expr(s"transform(${property.name}, x -> regexp_replace(x, '[\\n\\t\\r]', ' '))"))
        case dt: DecimalType => df.withColumn(property.name, col(property.name).cast("double"))
        case _ => df
      }
    })
  }

  def preProcessPreviousInventory(prevInventory:DataFrame, entityConfig: Entity) :DataFrame ={
    LOGGER.info("Pre Processing previous inventory")
    val lastUpdatedAttrSchema = entityConfig
      .lastUpdateFields.map(field => s"`$field`: STRUCT<`prev`: STRUCT<`value`: STRING, `updated_at`: BIGINT, `last_found_date`: BIGINT>, `last_changed`: STRUCT<`value`: STRING, `updated_at`: BIGINT, `last_found_date`: BIGINT>,`isChangedInCurrentRun`:Boolean>").mkString("STRUCT<",",",">")
    val emptyDF = prevInventory.sparkSession.emptyDataFrame
      .withColumnLogged(P_ID,lit(null).cast(StringType))
      .withColumnLogged(LAST_FOUND,lit(null).cast(LongType))
      .withColumnLogged(UPDATED_AT,lit(null).cast(LongType))
      .withColumnLogged(s"prev__$LAST_UPDATED_ATTRS",expr(s"from_json(null,'$lastUpdatedAttrSchema')"))


    val schemaAdjustedDF = SparkUtil.unionByName(prevInventory.withColumnRenamed(LAST_UPDATED_ATTRS,s"prev__$LAST_UPDATED_ATTRS"), emptyDF)
    val lastUpdPrev: List[Column] = entityConfig.lastUpdateFields.map { field =>
      if (schemaAdjustedDF.columns.contains(field)) {
        if (schemaAdjustedDF.schema(field).dataType.isInstanceOf[StructType]) {
          to_json(col(field)).as(field)
        } else if (schemaAdjustedDF.schema(field).dataType.isInstanceOf[ArrayType]) {
          concat_ws("<-->", array_sort(col(field))).as(field)
        } else {
          col(field).cast(StringType).as(field)
        }
      } else {
        lit(null).cast(StringType).as(field)
      }
    }
    schemaAdjustedDF.withColumnLogged(PREV_ATTRS,struct(col(LAST_FOUND)+:col(UPDATED_AT)+:lastUpdPrev:_*))

  }

  def checkMissingColumns(df: DataFrame, columnsOrExpressions: Seq[String], contextName: String, loggerReturnStatus: String): Unit = {
    if (columnsOrExpressions.isEmpty) return
    val dfCols = df.columns.toSet
    val missingCols = columnsOrExpressions.filterNot(expr => dfCols.exists(expr.contains)).toSet
    if (missingCols.nonEmpty) {
      val message = s"Missing columns in $contextName: ${missingCols.mkString(", ")}\nAvailable columns: ${dfCols.mkString(", ")}"
      loggerReturnStatus match {
        case "pass" =>
          LOGGER.warn(message)
        case "fail" =>
          LOGGER.error(message)
          throw new IllegalArgumentException(message)
      }
    }
  }
}


object StructFieldReplaceUtils {
  def replaceStructProperties(sourceDF: DataFrame, propsList: Seq[Property]):  (LinkedHashMap[String, String], Seq[Property])={

    val structProps = getStructFields(propsList).diff(Array(LAST_UPDATED_ATTRS))
    val structPropsMap: Map[String, String] = structProps.map { prop =>
      val fieldName = s"ei_temp_${prop.replace(".", "__")}"
      (prop, fieldName)
    }.toMap
    val missingStruct = structProps.filter( prop =>{
      try {
        sourceDF.select(prop)
        false
      }
      catch {
        case e:Exception => true
      }
    })

    EILOGGER.info(s"missing struct fields ${missingStruct.toList}")
    val tempStructPropsExpr = LinkedHashMap[String, String]()

    structPropsMap.foreach { case (originalField, newFieldName) =>
      val fieldValue = if (missingStruct.contains(originalField)) "NULL" else originalField
      val exprStr = s"CASE WHEN btrim(cast($fieldValue as STRING),'[\\t ]') = '' THEN null ELSE $fieldValue END"
      tempStructPropsExpr.put(newFieldName, exprStr)
    }

    val structFieldsReplacedPropList = propsList.map { prop =>
      val sdsExpr = expr(prop.colExpr).expr
      val newExpr = sdsExpr
        .transformUp {
          case attr: Attribute if (structPropsMap.contains(attr.name)) =>
            expr(structPropsMap(attr.name)).expr
        }
        .transformUp {
          case ca: Cast => new SDSCast(ca.child, ca.dataType, ca.timeZoneId, ca.evalMode)
          case lm: LambdaFunction => new SDSLambdaFunction(lm.function, lm.arguments, lm.hidden)
        }
      prop.copy(colName = prop.colName, colExpr = newExpr.sql.replace("unspecifiedframe$()", ""), fieldsSpec = prop.fieldsSpec)
    }
    (tempStructPropsExpr, structFieldsReplacedPropList)
  }

  /**
   * returns seq of struct fields used in column expressions
   * @param allProperties
   */
  def getStructFields(propsList: Seq[Property]): Seq[String] = {
    propsList.flatMap(prop => {
      val referenceFields = expr(prop.colExpr).expr.references.map(_.name)
        .filter(_.contains("."))
      referenceFields
    }).distinct
  }

  def getNonStructFields(propsList: Seq[Property]): Seq[String] = {
    propsList.flatMap(prop => {
      val referenceFields = expr(prop.colExpr).expr.references.map(_.name)
        .filter(!_.contains("."))
      referenceFields
    }).distinct
  }

}
