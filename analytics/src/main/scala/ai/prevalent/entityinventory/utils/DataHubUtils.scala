package ai.prevalent.entityinventory.utils

import com.linkedin.schema._
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.entityinventory.lineage.ColumnLineageDefinition
import com.linkedin.common.UrnArray
import com.linkedin.common.urn.{DataPlatformUrn, DatasetUrn, Urn, UrnUtils}
import com.linkedin.common.FabricType
import com.linkedin.dataset._
import com.linkedin.schema.SchemaMetadata.PlatformSchema
import datahub.client.rest.RestEmitter
import datahub.event.MetadataChangeProposalWrapper
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.DataFrame
import com.linkedin.schema.{<PERSON>hemaField, SchemaFieldArray, SchemaFieldDataType, SchemaMetadata}
import com.linkedin.schema.{BooleanType, BytesType, DateType, NumberType, StringType, TimeType}
import com.linkedin.data.DataMap
import com.linkedin.data.template.DynamicRecordTemplate
import com.linkedin.common.Status
import com.linkedin.common.urn.{DataJobUrn, DataFlowUrn, DataPlatformUrn, DatasetUrn}
import com.linkedin.datajob.DataJobInputOutput
import com.linkedin.common.DatasetUrnArray
import com.linkedin.data.template.StringArray
import com.linkedin.data.template.StringMap

object DataHubUtils extends LoggerBase{

  private lazy val emitter:RestEmitter = {
    if(spark.conf.get("spark.datahub.rest.server","").isEmpty){
      LOGGER.warn("DataHub REST server not configured, skipping DataHub emission")
      null
    }
    else {
      RestEmitter.create(b => b.server(spark.conf.get("spark.datahub.rest.server"))
        .token(spark.conf.get("spark.datahub.rest.token", ""))
      )
    }
  }
  val spark: SparkSession = SparkSession.active



  def emitColumnLineageToDataHub(lineageDefinitions: Seq[ColumnLineageDefinition]): Unit = {
    if(emitter == null) {
      LOGGER.warn(s"DataHub REST server not configured, skipping DataHub emission for $lineageDefinitions")
      return
    }

    lineageDefinitions.groupBy(_.targetDataset)
      .foreach { case (targetDataset, lineageDefinitions) =>
        val targetPlatform = new DataPlatformUrn(lineageDefinitions.last.targetPlatform)
        val platformInstance = spark.conf.get("spark.datahub.metadata.dataset.platformInstance", "")
        val targetUrn = new DatasetUrn(
          targetPlatform,
          s"$platformInstance.${targetDataset}",
          FabricType.valueOf(spark.conf.get("spark.datahub.metadata.dataset.env"))
        )
        val lineage = fineGrainedLineage(targetUrn,lineageDefinitions)
        emitColumnLineageToDataHub(targetUrn,lineage)
      }
  }

  private def fineGrainedLineage(targetUrn: DatasetUrn, lineageDefinition:Seq[ColumnLineageDefinition]): UpstreamLineage = {
    val fgla = new FineGrainedLineageArray()
    val upstreams = new UpstreamArray();
    lineageDefinition.foreach(lineageDefinition => {
      val sourcePlatform = new DataPlatformUrn(lineageDefinition.sourcePlatform)
      val platformInstance = spark.conf.get("spark.datahub.metadata.dataset.platformInstance", "")
      val sourceUrn = new DatasetUrn(
        sourcePlatform,
        s"$platformInstance.${lineageDefinition.sourceDataset}",
        FabricType.valueOf(spark.conf.get("spark.datahub.metadata.dataset.env"))
      )

      lineageDefinition.columnMappings.foreach { mapping =>
        val fgl = new FineGrainedLineage()

        val upstreamFields = new UrnArray()
        val downstreamFields = new UrnArray()

        mapping.sourceColumns.foreach { sourceCol =>
          val fieldUrn = UrnUtils.getUrn(s"urn:li:schemaField:($sourceUrn,$sourceCol)")
          upstreamFields.add(fieldUrn)
        }

        mapping.targetColumns.foreach { targetCol =>
          val fieldUrn = UrnUtils.getUrn(s"urn:li:schemaField:($targetUrn,$targetCol)")
          downstreamFields.add(fieldUrn)
        }

        val upstream = new Upstream()
        upstream.setDataset(sourceUrn)

        upstream.setType(DatasetLineageType.TRANSFORMED)

        // Prevent duplicate upstreams
        //    if (!upstreams.exists(_.getDataset.toString == sourceUrn.toString)) {
        upstreams.add(upstream)
        //    }

        upstreamFields.sort(java.util.Comparator.comparing[Urn, String](_.toString))
        downstreamFields.sort(java.util.Comparator.comparing[Urn, String](_.toString))

        fgl.setUpstreams(upstreamFields)
        fgl.setUpstreamType(FineGrainedLineageUpstreamType.FIELD_SET)
        fgl.setDownstreams(downstreamFields)
        fgl.setTransformOperation(mapping.transformation)
        fgl.setDownstreamType(FineGrainedLineageDownstreamType.FIELD_SET)
        fgl.setConfidenceScore(1.0f) // or adjust if needed
        fgla.add(fgl)
      }
    })
    val lineage = new UpstreamLineage()
    lineage.setFineGrainedLineages(fgla)
    lineage.setUpstreams(upstreams)
    lineage

  }


  def registerHiveDatasetInDataHub(df: DataFrame, tableName: String, customProperties: Map[String, String] = Map.empty): Unit = {
    if(emitter == null) {
      LOGGER.warn("DataHub REST server not configured, skipping DataHub emission")
      return
    }

    LOGGER.info(s"Registering Hive dataset $tableName in DataHub with schema from DataFrame")
    // 1. Build the URN for the Hive dataset
    val platform = new DataPlatformUrn("hive")
    val env = FabricType.valueOf(spark.conf.get("spark.datahub.metadata.dataset.env", "PROD"))
    val platformInstance = spark.conf.get("spark.datahub.metadata.dataset.platformInstance", "")
    val datasetName = if (platformInstance.nonEmpty) s"$platformInstance.$tableName" else tableName
    val datasetUrn = new DatasetUrn(platform, datasetName, env)

    // 2. Build the schema fields from the DataFrame
    val schemaFields = new SchemaFieldArray()
    df.schema.fields.foreach { field =>
      val schemaField = new SchemaField()
      schemaField.setFieldPath(field.name)
      val dataType = new SchemaFieldDataType()
      dataType.setType(sparkTypeToDataHubType(field.dataType.typeName))
      schemaField.setType(dataType)
      schemaField.setDescription("") // Optionally set description
      schemaField.setNativeDataType(field.dataType.typeName) // <-- ADD THIS LINE
      schemaFields.add(schemaField)
    }

    // 3. Build the schema metadata aspect
    val schemaMetadata = new SchemaMetadata()
    schemaMetadata.setSchemaName(tableName)
    schemaMetadata.setPlatform(platform)
    schemaMetadata.setVersion(0)
    schemaMetadata.setFields(schemaFields)
    schemaMetadata.setHash("")
    // Workaround: set platformSchema to a dummy DataMap with a union member
    val documentSchema = df.schema.fields.map(f => s"${f.name}:${f.dataType.typeName}").mkString(", ")
    val kafkaSchemaMap = new DataMap()
    kafkaSchemaMap.put("documentSchema", documentSchema)
    kafkaSchemaMap.put("documentSchemaType", "SPARK") // or AVRO/PROTOBUF/JSON

    val platformSchemaMap = new DataMap()
    platformSchemaMap.put("com.linkedin.schema.KafkaSchema", kafkaSchemaMap)

    schemaMetadata.data().put("platformSchema", platformSchemaMap)
    schemaMetadata.setCluster(env.name())

    // 5. Emit to DataHub
    val mcpw = MetadataChangeProposalWrapper.builder()
      .entityType("dataset")
      .entityUrn(datasetUrn.toString)
      .upsert()
      .aspect(schemaMetadata)
      .build()

    val result = emitter.emit(mcpw).get()
    
    if (result.isSuccess) {
      LOGGER.info(s"Hive dataset $datasetUrn registered in DataHub with schema from DataFrame")
    } else {
      LOGGER.warn(s"Failed to register Hive dataset $datasetUrn in DataHub, ${result}")
    }
    setDataSetProperties(tableName, customProperties)

    val status = new Status()
    status.setRemoved(false)

    val mcpwStatus = MetadataChangeProposalWrapper.builder()
      .entityType("dataset")
      .entityUrn(datasetUrn.toString)
      .upsert()
      .aspect(status)
      .build()
    val resultStatus = emitter.emit(mcpwStatus).get()
    if (resultStatus.isSuccess) {
      LOGGER.info(s"Status aspect for $datasetUrn registered in DataHub (removed=false)")
    } else {
      LOGGER.warn(s"Failed to register Status aspect for $datasetUrn in DataHub: ${resultStatus}")
    }
//    connectSparkFlowOutputToDataset(tableName)
  }

  def setDataSetProperties(datasetName: String, customProperties: Map[String, String]): Unit = {
    if (emitter == null) {
      LOGGER.warn("DataHub REST server not configured, skipping DataHub emission")
      return
    }
    val datasetUrn = getDatasetUrn(datasetName)
    val dataSetProperties = new DatasetProperties()
    // 4. Add custom properties if provided
    if (customProperties.nonEmpty) {
      val stringMap = new StringMap()
      customProperties.foreach { case (k, v) => stringMap.put(k, v) }
      dataSetProperties.setCustomProperties(stringMap)
    }
    val mcpw = MetadataChangeProposalWrapper.builder()
      .entityType("dataset")
      .entityUrn(datasetUrn.toString)
      .upsert()
      .aspect(dataSetProperties)
      .build()
    val result = emitter.emit(mcpw).get()
    if (result.isSuccess) {
      LOGGER.info(s"Custom properties for $datasetUrn registered in DataHub")
    } else {
      LOGGER.warn(s"Failed to register custom properties for $datasetUrn in DataHub: ${result}")
    }
  }

  private def emitColumnLineageToDataHub(targetUrn: DatasetUrn,lineage: UpstreamLineage): Unit = {
    val mcpw = MetadataChangeProposalWrapper.builder()
      .entityType("dataset")
      .entityUrn(targetUrn.toString)
      .upsert()
      .aspect(lineage)
      .build();
    val result = emitter.emit(mcpw).get()
    if(result.isSuccess) {
      LOGGER.info(s"Column Lineage Successfully emitted for $lineage")
    } else {
      LOGGER.warn(s"Column Lineage Failed emitted for $lineage")
    }
  }

  def connectSparkFlowOutputToDataset(outputTableName: String): Unit = {
    // Get configs
    val env = spark.conf.get("spark.datahub.metadata.dataset.env", "PROD")
    val pipelineInstance = spark.conf.get("spark.datahub.metadata.pipeline.platformInstance", "")
    val datasetInstance = spark.conf.get("spark.datahub.metadata.dataset.platformInstance", "")
    val flowName = spark.conf.get("spark.datahub.flow_name", "unknown_flow")

    // DataFlow URN (for the pipeline/flow) - all Strings!
    val flowUrn = new DataFlowUrn(
      "spark",
      flowName,
      pipelineInstance
    )

    // DataJob URN (for the job/step)
    val jobUrn = new DataJobUrn(
      flowUrn,
      "main" // or use a step name if you have one
    )

    // Dataset URN (for the output dataset)
    val datasetUrn = new DatasetUrn(
      new DataPlatformUrn("hive"),
      if (datasetInstance.nonEmpty) s"$datasetInstance.$outputTableName" else outputTableName,
      com.linkedin.common.FabricType.valueOf(env)
    )

    // DataJobInputOutput aspect (for outputs)
    val io = new DataJobInputOutput()
    val outputs = new DatasetUrnArray()
    outputs.add(datasetUrn)
    io.setOutputDatasets(outputs)

    // Emit DataJobInputOutput
    val mcpwIO = MetadataChangeProposalWrapper.builder()
      .entityType("dataJob")
      .entityUrn(jobUrn.toString)
      .upsert()
      .aspect(io)
      .build()
    val resultIO = emitter.emit(mcpwIO).get()
    if (resultIO.isSuccess) {
      LOGGER.info(s"Connected DataJob $jobUrn to output dataset $datasetUrn in DataHub")
    } else {
      LOGGER.warn(s"Failed to connect DataJob $jobUrn to output dataset $datasetUrn in DataHub: $resultIO")
    }
  }

  def patchDataJobInputOutputEdges(
    jobUrn: String,
    inputDatasetUrns: Seq[String],
    outputDatasetUrns: Seq[String]
  ): Unit = {
    // Build the PATCH JSON array
    val now = System.currentTimeMillis()
    val actor = "urn:li:corpuser:UNKNOWN"
    val patchOps = (inputDatasetUrns.map { urn =>
      s"""{"op":"add","path":"/inputDatasetEdges/$urn","value":{"destinationUrn":"$urn","lastModified":{"time":$now,"actor":"$actor"},"created":{"time":$now,"actor":"$actor"}}}"""
    } ++ outputDatasetUrns.map { urn =>
      s"""{"op":"add","path":"/outputDatasetEdges/$urn","value":{"destinationUrn":"$urn","lastModified":{"time":$now,"actor":"$actor"},"created":{"time":$now,"actor":"$actor"}}}"""
    }).mkString("[", ",", "]")

    // Build the aspect as a generic JSON payload
    val aspect = new StringMap()
    aspect.put("contentType", "application/json")
    aspect.put("value", patchOps)

    val mcpw = MetadataChangeProposalWrapper.builder()
      .entityType("dataJob")
      .entityUrn(jobUrn)
      .upsert() // Use upsert if patch is not available
      .aspect(aspect)
      .build()

    val result = emitter.emit(mcpw).get()
    if (result.isSuccess) {
      LOGGER.info(s"Patched DataJobInputOutput edges for $jobUrn in DataHub")
    } else {
      LOGGER.warn(s"Failed to patch DataJobInputOutput edges for $jobUrn in DataHub: $result")
    }
  }


  /**
   * Connects a dataset (by name) to an existing Spark task (DataJob) in DataHub by emitting the DataJobInputOutput aspect.
   * @param datasetName The name of the dataset (table).
   * @param sparkTaskName The name of the Spark task (DataJob).
   * @param datasetType The type of the dataset platform (default: "hive").
   * @param flowName The name of the pipeline/flow (default: from spark.conf or "unknown_flow").
   * @param isInput Whether to connect as input (true) or output (false, default).
   */
  def connectDatasetToSparkTask(
    datasetName: String,
    datasetType: String = "hive",
    isInput: Boolean = false
  ): Unit = {
    if (emitter == null) {
      LOGGER.warn("DataHub REST server not configured, skipping DataJobInputOutput emission")
      return
    }


    val env = spark.conf.get("spark.datahub.metadata.dataset.env", "PROD")
    val pipelineInstance = spark.conf.get("spark.datahub.metadata.pipeline.platformInstance", "")
    val datasetInstance = spark.conf.get("spark.datahub.metadata.dataset.platformInstance", "")
    val sparkTaskName = if(pipelineInstance.nonEmpty) s"$pipelineInstance.${spark.conf.get("spark.datahub.flow_name", "unknown_flow")}" else spark.conf.get("spark.datahub.flow_name", "unknown_flow")

    val flowUrn = new com.linkedin.common.urn.DataFlowUrn("spark", sparkTaskName, pipelineInstance)
    val jobUrn = new com.linkedin.common.urn.DataJobUrn(flowUrn, sparkTaskName)
    val datasetUrn = new com.linkedin.common.urn.DatasetUrn(
      new com.linkedin.common.urn.DataPlatformUrn(datasetType),
      if (datasetInstance.nonEmpty) s"$datasetInstance.$datasetName" else datasetName,
      com.linkedin.common.FabricType.valueOf(env)
    )

    // DataJobInputOutput aspect
    val io = new com.linkedin.datajob.DataJobInputOutput()
    val urnArray = new com.linkedin.common.DatasetUrnArray()
    urnArray.add(datasetUrn)
    if (isInput) {
      io.setInputDatasets(urnArray)
      io.setOutputDatasets(new com.linkedin.common.DatasetUrnArray())
      LOGGER.info(s"Connecting DataJob $jobUrn to input dataset $datasetUrn (platform: $datasetType, name: $datasetName)")
    } else {
      io.setOutputDatasets(urnArray)
      io.setInputDatasets(new com.linkedin.common.DatasetUrnArray())
      LOGGER.info(s"Connecting DataJob $jobUrn to output dataset $datasetUrn (platform: $datasetType, name: $datasetName)")
    }
    val mcpwIO = MetadataChangeProposalWrapper.builder()
      .entityType("dataJob")
      .entityUrn(jobUrn.toString)
      .upsert()
      .aspect(io)
      .build()
    val resultIO = emitter.emit(mcpwIO).get()
    if (resultIO.isSuccess) {
      LOGGER.info(s"Connected DataJob $jobUrn to ${if (isInput) "input" else "output"} dataset $datasetUrn in DataHub (DataJobInputOutput aspect)")
    } else {
      LOGGER.warn(s"Failed to connect DataJob $jobUrn to ${if (isInput) "input" else "output"} dataset $datasetUrn in DataHub: $resultIO")
    }
  }

  // Helper function to map Spark types to DataHub union types
  def sparkTypeToDataHubType(sparkType: String): SchemaFieldDataType.Type = sparkType.toLowerCase match {
    case "string"    => SchemaFieldDataType.Type.create(new StringType())
    case "integer"   => SchemaFieldDataType.Type.create(new NumberType())
    case "int"       => SchemaFieldDataType.Type.create(new NumberType())
    case "long"      => SchemaFieldDataType.Type.create(new NumberType())
    case "double"    => SchemaFieldDataType.Type.create(new NumberType())
    case "float"     => SchemaFieldDataType.Type.create(new NumberType())
    case "boolean"   => SchemaFieldDataType.Type.create(new BooleanType())
    case "binary"    => SchemaFieldDataType.Type.create(new BytesType())
    case "date"      => SchemaFieldDataType.Type.create(new DateType())
    case "timestamp" => SchemaFieldDataType.Type.create(new TimeType())
    // Add more as needed
    case _            => SchemaFieldDataType.Type.create(new StringType())
  }

  def getDatasetUrn(tableName: String): DatasetUrn = {
    val platform = new DataPlatformUrn("hive")
    val env = FabricType.valueOf(spark.conf.get("spark.datahub.metadata.dataset.env", "PROD"))
    val platformInstance = spark.conf.get("spark.datahub.metadata.dataset.platformInstance", "")
    val datasetName = if (platformInstance.nonEmpty) s"$platformInstance.$tableName" else tableName
    new DatasetUrn(platform, datasetName, env)
  }

  def changeDataSetStatus(datasetName:String, isRemoved:Boolean): Boolean ={
    if (emitter == null) {
      LOGGER.warn("DataHub REST server not configured, skipping DataHub emission")
      return false
    }
    val datasetUrn = getDatasetUrn(datasetName)
    val status = new Status()
    status.setRemoved(isRemoved)

    val mcpwStatus = MetadataChangeProposalWrapper.builder()
      .entityType("dataset")
      .entityUrn(datasetUrn.toString)
      .upsert()
      .aspect(status)
      .build()
    val resultStatus = emitter.emit(mcpwStatus).get()
    if (resultStatus.isSuccess) {
      LOGGER.info(s"Status aspect for $datasetUrn registered in DataHub (removed=$isRemoved)")
    } else {
      LOGGER.warn(s"Failed to register Status aspect for $datasetUrn in DataHub: ${resultStatus}")
    }
    return resultStatus.isSuccess
  }
}
