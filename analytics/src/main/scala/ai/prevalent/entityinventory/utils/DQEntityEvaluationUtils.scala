package ai.prevalent.entityinventory.utils

import ai.prevalent.entityinventory.common.configs.TableConfig
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{KG_CONTENT_TYPE, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions.{expr, lit, col, coalesce}
import org.apache.spark.sql.types.StringType

/**
 * Utility functions for Data Quality (DQ) evaluation, including enrichment with resolver tables
 * and helper methods for column name resolution.
 */
object DQEntityEvaluationUtils extends LoggerBase {

  /**
   * Column names for entity tables.
   */
  val idCol: String = P_ID
  val disambCol: String = "disambiguated_p_id"
  val objectTypeName: String = "class"

  /**
   * Reads and enriches each table with disambiguation data if a resolver table is provided.
   *
   * For each TableConfig in modelInput:
   *   - Reads the main table filtered by update timestamp.
   *   - If a resolver table is specified, joins the main table with the resolver on the id column,
   *     adding the disambiguated column.
   *   - If no resolver table, adds the disambiguated column as null.
   *   - Adds 'class' and 'table_name' columns for context.
   *
   * @param modelInput Sequence of TableConfig entries containing table and optional resolver table.
   * @param objectName Name of the object type (for 'class' column).
   * @param reader     SDSTableReader to read tables.
   * @param upd        Update timestamp in epoch millis.
   * @return Map of table name to enriched DataFrame.
   */
  def enrichWithResolver(
    modelInput: Seq[TableConfig],
    objectName: String,
    reader: SDSTableReader,
    upd: Long
  ): Map[String, DataFrame] = {

    // Filter for the current update timestamp
    val filterCondition = expr(s"$UPDATED_AT_TS = to_timestamp($upd / 1000) AND ($KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL)")

    modelInput.map { model =>
      // Read main table data filtered by update timestamp
      val mainDf = EIUtil.safeReadEntity(
        model.table_name,
        filterCondition,
        reader
      )

      // If resolver table exists, join with disambiguation fields
      val enrichedDf = model.resolver_table match {
        case resolver if resolver != null && resolver.nonEmpty =>
          val resolverTables = resolver.split(",").map(_.trim).filter(_.nonEmpty)

          if (resolverTables.isEmpty) {
            // If resolver string is empty or just commas, treat as no resolver
            mainDf.withColumn(disambCol, lit(null).cast(StringType))
          } else {

            val resolverFilter = expr(s"$UPDATED_AT_TS = to_timestamp($upd / 1000)")
            if(resolverTables.length < 2) {
              val interDF = EIUtil.safeReadResolver(
                  resolverTables.head,
                  resolverFilter,
                  reader
                ).withColumnRenamed(disambCol, s"${disambCol}_temp")
                .select(idCol, s"${disambCol}_temp").as("inter")

              mainDf.as("main").join(interDF, expr(s"inter.$idCol = main.$idCol"), "left")
                .withColumn(disambCol, coalesce(interDF(s"${disambCol}_temp"),mainDf(idCol)))
                .select("main.*",disambCol)
            }
            else {
              val intraDF = EIUtil.safeReadResolver(
                resolverTables.head,
                resolverFilter,
                reader
              ).select(idCol, disambCol).as("intra")

              val interDF = EIUtil.safeReadResolver(
                  resolverTables.tail.head,
                  resolverFilter,
                  reader
                ).withColumnRenamed(disambCol, s"${disambCol}_temp")
                .select(idCol, s"${disambCol}_temp").as("inter")

              mainDf.as("main").join(intraDF, expr(s"intra.$idCol = main.$idCol"), "left")
                .withColumn(s"${disambCol}_intra", coalesce(intraDF(disambCol),mainDf(idCol)))
                .select("main.*",s"${disambCol}_intra").as("main")
                .join(interDF, expr(s"inter.$idCol = main.${disambCol}_intra"), "left")
                .withColumn(
                  disambCol,
                  coalesce(col(s"inter.${disambCol}_temp"), col(s"main.${disambCol}_intra"))
                ).select("main.*",disambCol)
            }

//            // Read the first resolver table and select idCol and disambCol
//            var currentResolverDf = EIUtil.safeReadResolver(
//              resolverTables.head,
//              expr(s"$UPDATED_AT_TS = to_timestamp($upd / 1000)"),
//              reader
//            ).select(idCol, disambCol)
//
//            // Join with subsequent resolver tables
//            resolverTables.tail.foreach { currentResolverTableName =>
//              val nextResolverDf = EIUtil.safeReadResolver(
//                currentResolverTableName,
//                expr(s"$UPDATED_AT_TS = to_timestamp($upd / 1000)"),
//                reader
//              ).select(idCol, disambCol)
//                .withColumnRenamed(disambCol, "temp_disamb_col") // Rename to avoid conflict during join
//
//              currentResolverDf = currentResolverDf.join(
//                nextResolverDf,
//                currentResolverDf.col(disambCol) === nextResolverDf.col(idCol),
//                "left"
//              ).select(currentResolverDf.col(idCol), nextResolverDf.col("temp_disamb_col").alias(disambCol))
//            }
//
//            // Join mainDf with the final currentResolverDf
//            mainDf.join(currentResolverDf, Seq(idCol), "left")
//              .withColumn(
//                disambCol,
//                coalesce(col(disambCol), col(idCol))
//              )
          }

        case _ =>
          // If no resolver, add disambiguated column as p_id
          mainDf.withColumn(disambCol, col(idCol))
      }

      // Add 'class' and 'table_name' columns for context
      val resultDF = enrichedDf
        .withColumn("table_name", lit(model.table_name))

      // Print schema for debugging
      resultDF.printSchema()
      model.table_name -> resultDF
    }.toMap
  }
}