package ai.prevalent.entityinventory.utils

import ai.prevalent.entityinventory.disambiguator.configs.specs.ConfigurationItem
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.apache.hadoop.fs.{FileSystem, LocatedFileStatus, Path, RemoteIterator}
import org.apache.spark.sql.SparkSession
import org.json4s.{DefaultFormats, Formats}
import scala.collection.mutable

import java.net.URI
import scala.collection.mutable.ListBuffer

object ConfigUtil {

  private def spark =  SparkSession.active

  def readAllConfigs[T](configListPath:String, configArtifactURI:String, configEndpoint:String, formats: Formats = DefaultFormats)(implicit m:Manifest[T]): mutable.Map[String,T] = {
    val uri = new URI(configListPath)
    val publisherConfigs : mutable.Map[String,T] =mutable.Map.empty
    if (uri.getScheme=="https" || uri.getScheme=="http") {
      val configurationItem:Seq[ConfigurationItem]= ConfigUtils.getConfig[Seq[ConfigurationItem]](spark, uri.toString, manifest[Seq[ConfigurationItem]], DefaultFormats)
      val eiInterConfigPath = configArtifactURI + configEndpoint
      val configurationItemNames = configurationItem.map(x=>x.name)
      configurationItemNames.foreach(item => {
        val sparkJobConfig = eiInterConfigPath+item
        val disambiguationConfig:T = ConfigUtils.getConfig(spark, sparkJobConfig, m, formats)
        publisherConfigs(item) = disambiguationConfig
      })
    }
    else {
      val fs = FileSystem.get(uri, spark.sparkContext.hadoopConfiguration)
      val remoteFiles: RemoteIterator[LocatedFileStatus] = fs.listFiles(new Path(uri), false)
      while(remoteFiles.hasNext){
        val file: LocatedFileStatus = remoteFiles.next()
        val path = file.getPath
        val disambiguationConfig:T = ConfigUtils.getConfig(spark, path.toString, m, formats)
        publisherConfigs(path.getName.split("\\.")(0))= disambiguationConfig
      }
    }
    publisherConfigs
  }
}
