package ai.prevalent.entityinventory.utils

import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.entityinventory.lineage.{ColumnLineageDefinition, ColumnMapping}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import org.apache.spark.sql.functions.expr

object LineageUtils extends LoggerBase{
  /**
   * Emits column lineage to DataHub
   * @param sourceTable
   * @param targetTable
   * @param properties
   */
  def emitColumnLineage(sourceTable:String, targetTable: String,
                        properties:List[Property]): Unit = {
    val columnLineageDefinitions: Seq[ColumnLineageDefinition] = properties
      .groupBy(_.fieldsSpec.isInventoryDerived)
      .view
      .mapValues(props =>props.map(p => ColumnMapping(expr(p.colExpr).expr.references.map(_.name).toList, List(p.colName), p.colExpr)).toList )
      .collect{
        case (true, columnMapping) => ColumnLineageDefinition(sourceDataset = targetTable, targetDataset = targetTable, columnMappings = columnMapping)
        case (false, columnMapping) => ColumnLineageDefinition(sourceDataset = sourceTable, targetDataset = targetTable, columnMappings = columnMapping)
      }.toSeq

    LOGGER.info(s"Column Lineage Definitions $columnLineageDefinitions")
    DataHubUtils.emitColumnLineageToDataHub(lineageDefinitions = columnLineageDefinitions)
  }

  def createColumnLineage(sourceTable: String, targetTable: String, input: Either[Array[String], Array[Property]]): ColumnLineageDefinition = {
    val columnMappings = input match {
      case Left(cols) => cols.map(col => ColumnMapping(List(col), List(col), col)).toList
      case Right(props) => props.map(p => ColumnMapping(expr(p.colExpr).expr.references.map(_.name).toList, List(p.colName), p.colExpr)).toList
    }
    val columnLineageDefinition = ColumnLineageDefinition(sourceDataset = sourceTable, targetDataset = targetTable, columnMappings = columnMappings)
    LOGGER.info(s"Column Lineage Definitions $columnLineageDefinition")
    columnLineageDefinition
  }
}
