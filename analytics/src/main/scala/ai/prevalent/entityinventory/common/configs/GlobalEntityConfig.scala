package ai.prevalent.entityinventory.common.configs

import ai.prevalent.entityinventory.delta.{Change, GlobalEntityConfigDelta}

case class GlobalEntityConfig(entityClass:String =null,
                  commonProperties: Array[Property] = Array.empty[Property],
                  entitySpecificProperties: Array[Property] = Array.empty[Property],
                  fieldLevelSpec:Array[Property] = Array.empty[Property],
                  lastUpdateFields:Array[String] = Array.empty) extends EIConfig[GlobalEntityConfig,GlobalEntityConfigDelta] {

  override def getConfigDelta(otherConfig: GlobalEntityConfig): GlobalEntityConfigDelta = GlobalEntityConfigDelta(prevConfig = otherConfig, newConfig = this)

  override def getConfigDelta(deltas: Seq[Change]): GlobalEntityConfigDelta = GlobalEntityConfigDelta (clientConfig = this, deltas = deltas)
}
