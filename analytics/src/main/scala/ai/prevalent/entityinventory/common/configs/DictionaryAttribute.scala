package ai.prevalent.entityinventory.common.configs

import ai.prevalent.entityinventory.delta.Change
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.write

import scala.reflect.runtime.universe._

case class DictionaryAttribute(caption:Option[String] , description:Option[String] , group:Option[String],
                               enable_hiding:Option[String], `type`:Option[String] , width:Option[String] ,
                               step_interval:Option[String] , range_selection:Option[String] , ui_visibility:Option[String],
                               candidate_key:Option[String] , data_structure:Option[String]){

  def attributeSpec: String = {
    val state = List(group, enable_hiding, `type`, width, step_interval, range_selection, ui_visibility, candidate_key, data_structure)
    write(state)(DefaultFormats)
  }

  override def equals(obj: Any): Boolean = obj match {
    case that: DictionaryAttribute =>
      (this.group == that.group) &&
        (this.enable_hiding == that.enable_hiding) &&
        (this.`type` == that.`type`) &&
        (this.width == that.width) &&
        (this.step_interval == that.step_interval) &&
        (this.range_selection == that.range_selection) &&
        (this.ui_visibility == that.ui_visibility) &&
        (this.candidate_key == that.candidate_key) &&
        (this.data_structure == that.data_structure)
    case _ => false
  }

  override def hashCode(): Int = {
    val state = Seq(group, enable_hiding, `type`, width, step_interval, range_selection, ui_visibility, candidate_key, data_structure)
    state.map {
      case Some(value) => value.hashCode
      case None        => 0
      case value       => value.hashCode
    }.foldLeft(0)((a, b) => 31 * a + b)
  }

  def canEqual(other: Any): Boolean = other.isInstanceOf[DictionaryAttribute]

  override def toString: String = write(this)(DefaultFormats)

  def getConfigDelta(previous: DictionaryAttribute, fieldName:String): List[Change] = {
    val currentMirror = runtimeMirror(this.getClass.getClassLoader)
    val previousMirror = runtimeMirror(previous.getClass.getClassLoader)

    val currentType = currentMirror.reflect(this).symbol.typeSignature
    currentType.members.collect {
      case field if field.isTerm && field.asTerm.isVal =>
        val attributeName = field.name.toString.trim.split("_").map(_.capitalize).mkString(" ")
        val currentValue = currentMirror.reflect(this).reflectField(field.asTerm).get.asInstanceOf[Option[String]]
        val previousValue = previousMirror.reflect(previous).reflectField(field.asTerm).get.asInstanceOf[Option[String]]


        if (currentValue != previousValue) {
          Some(Change(changeType = attributeName, name = fieldName, reason = Option.empty,
            message = Map(s"Old $attributeName "->previousValue.getOrElse("").toString, s"New $attributeName "->currentValue.getOrElse("").toString),
            category="UI Attribute"))
        }else Option.empty
    }.filter(_.isDefined).map(_.get).toList
  }
}


