package ai.prevalent.entityinventory.common.configs

import ai.prevalent.sdspecore.configbase.ConfigurableJobArgs
import picocli.CommandLine.Option

class EIJobArgs extends ConfigurableJobArgs {

  @Option(names = Array("--parsed-interval-start"), description = Array("Parsed Interval Start Epoch"), required = false)
  var parsedIntervalStartEpoch: Long = -1
  @Option(names = Array("--srdm-historical-parsed-interval-start"), description = Array("Parsed Interval Start Epoch"), required = false)
  var srdmHistoricalParsedIntervalStartEpoch: Long = 0

  @Option(names = Array("--parsed-interval-end"), description = Array("Parsed Interval End Epoch"), required = false)
  var parsedIntervalEndEpoch: Long = -1

  @Option(names = Array("--current-updated-date"), description = Array("Event Timestamp End Epoch"), required = true)
  var currentUpdateDate: Long = -1

  @Option(names = Array("--previous-updated-date"), description = Array("Previous Ending Epoch"), required = false)
  var prevUpdateDate: Long = -1

  @Option(names = Array("--ei-config-version"), description = Array("Entity Inventory Config Version"), required = false)
  var eIConfigVersion: String = "new"

  @Option(names = Array("--process-delta-properties"), description = Array("Handle delta properties"), required = false)
  var processDeltaProperty: Boolean = true

  @Option(names = Array("--rerun-info-json"), description = Array("Rerun Info JSON"), required = false)
  var rerunInfoJson: String = _


  override def toString = s"EIJobArgs(parsedIntervalStartEpoch=$parsedIntervalStartEpoch, srdmHistoricalParsedIntervalStartEpoch=$srdmHistoricalParsedIntervalStartEpoch, parsedIntervalEndEpoch=$parsedIntervalEndEpoch, currentUpdateDate=$currentUpdateDate, prevUpdateDate=$prevUpdateDate, eIConfigVersion=$eIConfigVersion, rerunInfoJson=$rerunInfoJson)"
}