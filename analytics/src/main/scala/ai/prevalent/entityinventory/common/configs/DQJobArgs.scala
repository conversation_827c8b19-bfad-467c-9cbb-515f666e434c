package ai.prevalent.entityinventory.common.configs

import ai.prevalent.sdspecore.configbase.ConfigurableJobArgs
import picocli.CommandLine.{Option, ITypeConverter}
import scala.{Option => SOption}
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import scala.jdk.CollectionConverters._

case class TableConfig(table_name: String, resolver_table: String = null)

class TableConfigListConverter extends ITypeConverter[java.util.List[TableConfig]] {
  override def convert(value: String): java.util.List[TableConfig] = {
    val mapper = new ObjectMapper()
    mapper.registerModule(DefaultScalaModule)

    try {
      val list = mapper.readValue(value, classOf[Array[TableConfig]]).toList
      list.asJava // RETURN java.util.List, not Scala List
    } catch {
      case e: Exception =>
        throw new IllegalArgumentException(s"Failed to parse list_of_tables JSON: ${e.getMessage}", e)
    }
  }
}

class DQJobArgs extends ConfigurableJobArgs {
  @Option(names = Array("--current-updated-date"), description = Array("Event Timestamp End Epoch"), required = true)
  var currentUpdateDate: Long = -1

  @Option(names = Array("--list-of-tables"), converter = Array(classOf[TableConfigListConverter]), required = false)
  var listOfTables: java.util.List[TableConfig] = _

  @Option(names = Array("--object-type"), description = Array("Object Type, Its either entity or relationship"), required = false)
  var objectType: String = _

  @Option(names = Array("--object-name"), description = Array("Object Name"), required = false)
  var objectName: String = _

  @Option(names = Array("--kg-job-type"), description = Array("KG Job Type for special processing"), required = false)
  var kgJobType: String = _

  def getListOfTables: Seq[TableConfig] =
    SOption(listOfTables).map(_.asScala.toSeq).getOrElse(Seq.empty)

  override def toString: String = s"DQJobArgs(currentUpdateDate=$currentUpdateDate,listOfTables=$listOfTables,objectType=$objectType,objectName=$objectName,kgJobType=$kgJobType)"
}

