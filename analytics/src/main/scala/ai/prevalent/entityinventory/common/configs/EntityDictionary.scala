package ai.prevalent.entityinventory.common.configs

import ai.prevalent.entityinventory.delta.{Change, EntityDictionaryDelta, RelationshipDictionaryDelta}

case class EntityDictionary(caption:String = "", description:String="",
                            attributes:Map[String, DictionaryAttribute] = Map.empty)
  extends EIConfig[EntityDictionary, EntityDictionaryDelta] {
  override def getConfigDelta(prevConfig: EntityDictionary): EntityDictionaryDelta = EntityDictionaryDelta(prevConfig= prevConfig, newConfig = this)
  override def getConfigDelta(deltas: Seq[Change]): EntityDictionaryDelta = EntityDictionaryDelta()
}
