package ai.prevalent.entityinventory.common.rerun

import ai.prevalent.entityinventory.delta.{Change, Delta}
import ai.prevalent.entityinventory.utils.EILOGGER
import ai.prevalent.entityinventory.common.configs.EIJobArgs
import org.json4s.DefaultFormats
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.json4s.jackson.Serialization.write
import org.json4s.jackson.JsonMethods.parse
import org.json4s._
import org.json4s.jackson.JsonMethods._

/**
 * Represents the different types of changes that can trigger a rerun
 */
sealed trait ChangeType
case object FileChange extends ChangeType
case object Jar<PERSON>hange extends ChangeType
case object ConfigChange extends ChangeType
case object NoChange extends ChangeType

/**
 * Represents the execution strategy based on the type of changes
 */
sealed trait ExecutionStrategy
case object FullRerun extends ExecutionStrategy
case object IncrementalRerun extends ExecutionStrategy
case object SkipExecution extends ExecutionStrategy

/**
 * Represents the scope of data that needs to be updated
 */
sealed trait UpdateScope
case object UpdateSnapshots extends UpdateScope
case object UpdateViews extends UpdateScope
case object UpdateBoth extends UpdateScope
case object UpdateNone extends UpdateScope

/**
 * Configuration for field-level update strategies
 */
case class FieldUpdateConfig(
                              fieldName: String,
                              updateScope: UpdateScope,
                              isInventoryDerived: Boolean = false
                            )

/**
 * Represents the rerun analysis result
 */
case class RerunAnalysis(
                          hasChanges: Boolean,
                          changeType: ChangeType,
                          executionStrategy: ExecutionStrategy,
                          updateScope: UpdateScope,
                          affectedFields: Seq[String] = Seq.empty,
                          message: String,
                          configChanges: Option[Seq[Change]] = None
                        )

/**
 * Represents the current and previous run hashes
 */
case class RunHashes(
                      fileHash: String,
                      configHash: String,
                      jarHash: String
                    )

/**
 * Represents the rerun context with all necessary information
 */
case class RerunContext(
                         currentHashes: RunHashes,
                         previousHashes: Option[RunHashes],
                         configChanges: Option[Seq[Change]] = None,
                         fieldUpdateConfigs: Seq[FieldUpdateConfig] = Seq.empty,
                         rerunInfo: Option[RerunInfo] = None
                       )

case class Hashes(fileHash: String, configHash: String, jarHash: String)
case class RerunInfo(
                      hasChanges: Option[Boolean] = None,
                      fileHashChanged: Option[Boolean] = None,
                      configHashChanged: Option[Boolean] = None,
                      jarHashChanged: Option[Boolean] = None,
                      currentHashes: Hashes,
                      previousHashes: Hashes,
                      message: Option[String] = None
                    )

/**
 * Generic trait for rerun strategy implementation
 */
trait RerunStrategy[C, D <: Delta] {

  /**
   * Analyzes the rerun context and determines the appropriate execution strategy
   */
  def analyzeRerun(context: RerunContext): RerunAnalysis

  /**
   * Determines the update scope based on config changes (layer-specific)
   */
  def determineUpdateScope(changes: Seq[Change], fieldConfigs: Seq[FieldUpdateConfig]): UpdateScope

  /**
   * Identifies affected fields based on config changes (layer-specific)
   */
  def identifyAffectedFields(changes: Seq[Change]): Seq[String]

  /**
   * Validates if the rerun strategy is applicable for the given configuration
   */
  def validateStrategy(config: C): Boolean

  /**
   * Creates field update configurations from the given configuration (layer-specific)
   */
  def createFieldUpdateConfigs(config: C): Seq[FieldUpdateConfig]
}

/**
 * Base implementation of rerun strategy with common logic
 */
abstract class BaseRerunStrategy[C, D <: Delta] extends RerunStrategy[C, D] {

  override def analyzeRerun(context: RerunContext): RerunAnalysis = {
    val changeType = determineChangeType(context)
    if (changeType == FileChange || changeType == JarChange) {
      val message = generateMessage(changeType, FullRerun, UpdateBoth, Seq.empty)

      return RerunAnalysis(
        hasChanges = true,
        changeType = changeType,
        executionStrategy = FullRerun,
        updateScope = UpdateBoth,
        affectedFields = Seq.empty,
        message = message,
        configChanges = context.configChanges
      )
    }
    val updateScope = determineUpdateScope(context.configChanges.getOrElse(Seq.empty), context.fieldUpdateConfigs)
    val executionStrategy = determineExecutionStrategy(changeType, updateScope, context)
    val affectedFields = identifyAffectedFields(context.configChanges.getOrElse(Seq.empty))

    val message = generateMessage(changeType, executionStrategy, updateScope, affectedFields)

    RerunAnalysis(
      hasChanges = changeType != NoChange,
      changeType = changeType,
      executionStrategy = executionStrategy,
      updateScope = updateScope,
      affectedFields = affectedFields,
      message = message,
      configChanges = context.configChanges
    )
  }

  /**
   * Determines the type of change based on hash comparisons (generic logic)
   */
  protected def determineChangeType(context: RerunContext): ChangeType = {

    val rerunInfoOpt = context.rerunInfo

    rerunInfoOpt match {
      case None =>
        EILOGGER.info("No rerun info found - treating as new execution")
        FileChange

      case Some(rerunInfo) =>
        (rerunInfo.fileHashChanged, rerunInfo.jarHashChanged, rerunInfo.configHashChanged) match {
          case (Some(true), _, _) =>
            EILOGGER.info("File hash changed - full rerun required")
            FileChange
          case (_, Some(true), _) =>
            EILOGGER.info("JAR hash changed - full rerun required")
            JarChange
          case (_, _, Some(true)) =>
            EILOGGER.info("Config hash changed - incremental rerun required")
            ConfigChange
          case (Some(false), Some(false), Some(false)) =>
            EILOGGER.info("No changes detected")
            NoChange
          case _ =>
            EILOGGER.info("Unclear change status - treating as new execution")
            FileChange
        }
    }
  }

  /**
   * Determines the execution strategy based on change type (generic logic)
   */
  protected def determineExecutionStrategy(changeType: ChangeType,updateScope: UpdateScope, context: RerunContext): ExecutionStrategy = {
    changeType match {
      case FileChange | JarChange =>
        EILOGGER.info("File or JAR change detected - executing full rerun")
        FullRerun

      case ConfigChange =>
        updateScope match {
          case UpdateBoth =>
            EILOGGER.info("Config change detected executing full rerun")
            FullRerun
          case UpdateViews =>
            EILOGGER.info("Config change detected - executing incremental rerun")
            IncrementalRerun
          case UpdateNone =>
            EILOGGER.info("No Config Change  - skipping rerun")
            SkipExecution
        }
//
//      case NoChange =>
//        EILOGGER.info("No changes detected - skipping execution")
//        SkipExecution
    }
  }

  /**
   * Generates a descriptive message about the rerun analysis
   */
  protected def generateMessage(
                                 changeType: ChangeType,
                                 executionStrategy: ExecutionStrategy,
                                 updateScope: UpdateScope,
                                 affectedFields: Seq[String]
                               ): String = {
    val changeDesc = changeType match {
      case FileChange => "File changes detected"
      case JarChange => "JAR changes detected"
      case ConfigChange => "Configuration changes detected"
      case NoChange => "No changes detected"
    }

    val strategyDesc = executionStrategy match {
      case FullRerun => "Full rerun required"
      case IncrementalRerun => "Incremental rerun required"
      case SkipExecution => "Execution skipped"
    }

    val scopeDesc = updateScope match {
      case UpdateBoth => "Both snapshots and views will be updated"
      case UpdateSnapshots => "Only snapshots will be updated"
      case UpdateViews => "Only views will be updated"
      case UpdateNone => "No updates required"
    }

    val fieldsDesc = if (affectedFields.nonEmpty) {
      s"Affected fields: ${affectedFields.mkString(", ")}"
    } else {
      "No specific fields affected"
    }

    s"$changeDesc - $strategyDesc - $scopeDesc - $fieldsDesc"
  }
}

/**
 * Companion object with utility methods
 */
object RerunStrategy {

  /**
   * Logs the rerun analysis results
   */
  def logAnalysis(analysis: RerunAnalysis): Unit = {
    EILOGGER.info(s"Rerun Analysis: ${analysis.message}")
    EILOGGER.info(s"Change Type: ${analysis.changeType}")
    EILOGGER.info(s"Execution Strategy: ${analysis.executionStrategy}")
    EILOGGER.info(s"Update Scope: ${analysis.updateScope}")
    if (analysis.affectedFields.nonEmpty) {
      EILOGGER.info(s"Affected Fields: ${analysis.affectedFields.mkString(", ")}")
    }
  }

  def parseRerunInfo(jsonStr: String): RerunInfo = {
    ConfigUtils.getConfigFromJSON(jsonStr)(manifest[RerunInfo])
  }

  def getContextInfo(jobArgs:EIJobArgs):RerunContext= {
    val rerunInfoOpt: Option[RerunInfo] =
      Option(jobArgs.rerunInfoJson).filter(_.nonEmpty).map(parseRerunInfo)
    val context = rerunInfoOpt.map { info =>
      RerunContext(
        currentHashes = RunHashes(info.currentHashes.fileHash, info.currentHashes.configHash, info.currentHashes.jarHash),
        previousHashes = Some(RunHashes(info.previousHashes.fileHash, info.previousHashes.configHash, info.previousHashes.jarHash)) ,
        rerunInfo = Some(info)
      )
    }.getOrElse(
      RerunContext(RunHashes("", "", ""), None, rerunInfo = None)
    )
    context
  }
}