package ai.prevalent.entityinventory.cds

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.disambiguator.Disambiguator.readEnrichModels
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils.InventoryModelSpec
import ai.prevalent.entityinventory.loader.Loader.LOGGER
import ai.prevalent.entityinventory.loader.LoaderUtils
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql.functions.expr
import org.apache.spark.sql.{DataFrame, SparkSession}

import java.io.InputStream
import java.net.URI
import java.security.MessageDigest
import java.util.Base64

case class TableSpec(path: String, dataFrame: DataFrame)
case class PreviousRunHashes(configHash: Option[String], jarHash: Option[String], fileHash: Option[String])
trait ChangeDetectionSystem {
  def getInputConfigHash(config : String): String = {
    hashCreation(config.getBytes)

  }

  def hashCreation(inputBytes : Array[Byte] ):String={
    val digest = MessageDigest.getInstance("SHA-256")
    val hashBytes = digest.digest(inputBytes)
    Base64.getEncoder.encodeToString(hashBytes)
  }
  def getJarHash(spark:SparkSession, filePath: String = ""): String = {
    val jarLocation = spark.conf.get("spark.sds.jar.location",filePath)

    try {
      val uri = new URI(jarLocation)
      val fs = FileSystem.get(uri, spark.sparkContext.hadoopConfiguration)
      val path = new Path(uri)
      val inputStream: InputStream = fs.open(path)
      val jarBytes = inputStream.readAllBytes()
      inputStream.close()
      val hashCreated = hashCreation(jarBytes)
      println(s"jar hashCreated ${hashCreated}")
      println(hashCreated)
      hashCreated
    } catch {
      case e: Exception =>
        LOGGER.info(s"Failed to read or hash JAR from $jarLocation: ${e.getMessage}")
        ""
    }
  }

  def getpreviousRunInfo(prevDataFrame: DataFrame):Option[PreviousRunHashes]= {
    try{
      val previousHashesRow = prevDataFrame.filter(s"${SDSProperties.schema.KG_CONTENT_TYPE}='config'")
        .select(s"${SDSProperties.schema.KG_CONFIG_HASH}", s"${SDSProperties.schema.KG_JAR_FILE_HASH}",
          s"${SDSProperties.schema.KG_INPUT_FILE_HASH}").collect()
      if (previousHashesRow.nonEmpty) {
        val row = previousHashesRow.head
        Some(PreviousRunHashes(configHash = Option(row.getAs[String](SDSProperties.schema.KG_CONFIG_HASH)),
          jarHash = Option(row.getAs[String](SDSProperties.schema.KG_JAR_FILE_HASH)),
          fileHash = Option(row.getAs[String](SDSProperties.schema.KG_INPUT_FILE_HASH))
        ))
      } else {
        None
      }
    } catch {
      case ex: Exception =>
        LOGGER.warn(s"Could not retrieve previous run hashes: ${ex.getMessage}")
        None
    }

  }


  /**
   * Generic method to get input files hash from TableSpec collection
   * Combines both DataFrame file hashes and full SHOW CREATE TABLE output
   */
  def getFilesHashGeneric(tableSpecs: Array[TableSpec], spark: SparkSession): String = {
    LOGGER.info(s"Starting generic file hash calculation for ${tableSpecs.length} TableSpecs")

    val allCollectedData = scala.collection.mutable.Set[String]()

    tableSpecs.foreach { tableSpec =>
      LOGGER.info(s"Processing TableSpec: ${tableSpec.path}")

      // Get files from DataFrame using input_file_name()
      val dataFrameFiles = tableSpec.dataFrame
        .withColumn("kg_file_info", expr("input_file_name()"))
        .select("kg_file_info")
        .distinct()
        .collect()
        .map(_.getString(0))
        .filter(file => file != null && file.nonEmpty)

      LOGGER.info(s"TableSpec ${tableSpec.path} - DataFrame files count (${dataFrameFiles.length})")
      allCollectedData ++= dataFrameFiles

      // Get full SHOW CREATE TABLE output for hash calculation
      try {
        val createTableResult = spark.sql(s"SHOW CREATE TABLE ${tableSpec.path}")
        val createTableSQL = createTableResult.collect().map(_.getString(0)).mkString("\n")

        LOGGER.info(s"TableSpec ${tableSpec.path} - Using full CREATE TABLE statement for hash")
        // Add the full CREATE TABLE statement to hash calculation
        allCollectedData += createTableSQL

      } catch {
        case e: Exception =>
          LOGGER.warn(s"Failed to execute SHOW CREATE TABLE for ${tableSpec.path}: ${e.getMessage}")
          LOGGER.info(s"TableSpec ${tableSpec.path} - Using table path as fallback")
          allCollectedData += tableSpec.path
      }
    }

    val finalDataList = allCollectedData.toList.sorted
    val combinedData = finalDataList.mkString("")
    val hash = hashCreation(combinedData.getBytes)
    LOGGER.info(s"GENERIC RUN - Calculated combined hash from ${finalDataList.length} data sources: $hash")
    hash
  }






  def prepareInputDataframeForCDS(jobArgs: EIJobArgs,
                                  config: Any,
                                  reader: SDSTableReader,
                                  spark: SparkSession): (Array[TableSpec]) = {

    config match {
      case loaderConfig: ai.prevalent.entityinventory.loader.configs.specs.Config =>
        prepareLoaderInputDataframeForCDS(jobArgs, loaderConfig, reader, spark)
      case _ =>
        throw new IllegalArgumentException(s"Unsupported config type: ${config.getClass}")
    }
  }

  private def prepareLoaderInputDataframeForCDS(jobArgs: EIJobArgs,
                                                config: ai.prevalent.entityinventory.loader.configs.specs.Config,
                                                reader: SDSTableReader,
                                                spark: SparkSession): Array[TableSpec] = {

    val updatedParsedIntervalStartEpoch = LoaderUtils.getUpdatedParsedIntervalStartEpoch(jobArgs, config, reader, spark)

    LOGGER.info(s"LOADER CDS - Time Range: startEpoch=${updatedParsedIntervalStartEpoch}, endEpoch=${jobArgs.parsedIntervalEndEpoch}")
    LOGGER.info(s"API RUN - Current Update Date: ${jobArgs.currentUpdateDate}")

    val filterString = s"""
      ${config.dataSource.get.dataIntervalTimestampKey} <= to_timestamp(${jobArgs.parsedIntervalEndEpoch / 1000})
      AND ${config.dataSource.get.dataIntervalTimestampKey} >= to_timestamp(${updatedParsedIntervalStartEpoch / 1000})
    """

    val inputDataframe = LoaderUtils.getInputDataframe(config, filterString, jobArgs, reader)
    val tablePath = config.dataSource.get.srdm

    val tableSpecs = Array(TableSpec(tablePath, inputDataframe))

    LOGGER.info(s"LOADER CDS - Prepared ${tableSpecs.length} TableSpecs")
    tableSpecs
  }




}
