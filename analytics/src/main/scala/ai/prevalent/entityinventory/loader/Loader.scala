package ai.prevalent.entityinventory.loader
import ai.prevalent.entityinventory.cds.ChangeDetectionSystem
import ai.prevalent.entityinventory.utils.LineageUtils
import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.loader.LoaderUtils.{finalSelectInvColumns, tableCreation, viewCreation, viewSqlCreation}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{KG_CONTENT_TYPE,PRIMARY_KEY,UPDATED_AT_TS}

import ai.prevalent.entityinventory.loader.configs.specs.{Config, ConfigSerializer, OutputTableInfoSerializer}
import ai.prevalent.entityinventory.utils.{DataHubUtils, EILOGGER, EIUtil, LineageUtils, SparkUtil}
import ai.prevalent.entityinventory.common.configs.{EIJobArgs, OutputTableInfo, Property}
import ai.prevalent.entityinventory.loader.LoaderUtils.{finalSelectInvColumns, tableCreation, viewCreation, viewSqlCreation}
import ai.prevalent.entityinventory.loader.configs.specs.{Config, ConfigSerializer, OutputTableInfoSerializer}
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql.{DataFrame, Row}
import org.apache.spark.sql.functions._
import org.json4s.Formats
import org.apache.spark.sql.types.{ArrayType, MapType, NullType, TimestampType}
import ai.prevalent.entityinventory.utils.DataFrameWithColumnLogged._




object Loader extends SDSSparkBaseConfigurable[EIJobArgs, Config] with ChangeDetectionSystem{
  override def configFormats: Formats = super.configFormats  + ConfigSerializer + OutputTableInfoSerializer

  def build(jobArgs: EIJobArgs, config: Config, reader: SDSTableReader, writer: SDSTableWriter): DataFrame = {
    val previousInRead = EIUtil.safeReadEntity(config.outputTableInfo.outputTableName, reader = reader)
    val previousInventory = EIUtil.readPrevDF(jobArgs, previousInRead)
    val prevConfig: Option[String] = EIUtil.getPrevConfig(previousInventory, jobArgs)

    val sourceDF: DataFrame = reader.read(config.dataSource.get.srdm).filter(
      col(config.dataSource.get.dataIntervalTimestampKey) <= to_timestamp(lit(jobArgs.parsedIntervalEndEpoch).divide(1000)))
    EILOGGER.jsonMiniPrint("SRDM Source Schema", sourceDF.schema)
    LOGGER.info(s"SRDM Source Count ${sourceDF.count()}")
    val (rawSrdmDf, latestInventoryExpr) = LoaderUtils.build(sourceDF, previousInventory.filter(s"$KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL"), config, prevConfig, jobArgs, reader, writer)(spark)
    val selectFinalFields: List[String] = finalSelectInvColumns(rawSrdmDf, config)
    val finalSql =viewSqlCreation(rawSrdmDf, config, latestInventoryExpr, jobArgs, selectFinalFields)
    if (config.outputTableInfo.outputWrittenMode == "viewType") {
      viewCreation(config, finalSql)
    }
    val tempViewName = config.outputTableInfo.outputTableName.replace(".", "_") + "_temp"

    val df = spark.table(tempViewName)

    if(config.outputTableInfo.outputWrittenMode == "viewType"){
      DataHubUtils.registerHiveDatasetInDataHub(df,
        config.outputTableInfo.outputTableName)

      DataHubUtils.setDataSetProperties(s"${config.outputTableInfo.outputTableName}__srdm_inv",
        Map("is_intermediate_output" -> "true"))
    }
    df
  }

  override def execute(jobArgs: EIJobArgs, config: Config): Unit = {
    config.configValidator()
    spark.conf.set("spark.sql.caseSensitive", "true")
    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark, tableProperties = Map.empty, options = Map("partitionOverwriteMode" -> "dynamic"))
    EILOGGER.jsonMiniPrint("Loader Config", config)

    val finalOutDF = build(jobArgs, config, reader, writer)

    if (config.outputTableInfo.outputWrittenMode == "tableType") {
      tableCreation(finalOutDF,config,writer)
    }
    createMiniSRDMLoader(jobArgs, reader, writer, config)
  }

  def createMiniSRDMLoader(jobArgs: EIJobArgs, reader: SDSTableReader, writer: SDSTableWriter, config: Config): Unit = {
    LOGGER.info(s"Creating mini SRDM ")
    val sourceDF: DataFrame = reader.read(config.dataSource.get.srdm)
    val properties = sourceDF
      .drop(config.dataSource.get.uniqueRecordIdentifierKey)
      .columns
      .filter(column => column != PRIMARY_KEY)
      .map { column =>
        sourceDF.schema(column).dataType match {
          case _: MapType =>
            Property(column, s"to_json($column)")
          case _ =>
            Property(column, column)
        }
      }
    val schema = spark.conf.getOption("spark.kg.mini_srdm_schema").getOrElse {
      val msg = "Missing required Spark config: spark.kg.mini_srdm_schema"
      EILOGGER.error(msg)
      throw new ai.prevalent.entityinventory.exceptions.InvalidDataException(msg)
    }
    val srdmMiniTableName = s"$schema.${config.outputTableInfo.outputTableName.split("\\.").last}"
    val srdmConfig = Config(
      primaryKey = config.primaryKey,
      entity = config.entity,
      origin = config.origin,
      dataSource = config.dataSource,
      outputTableInfo = OutputTableInfo(srdmMiniTableName, "tableType"),
      commonProperties = properties,
      temporaryProperties = config.temporaryProperties,
    )
    jobArgs.rerunInfoJson = null
    val srdmMiniTable = build(jobArgs, srdmConfig, reader, writer)
    val miniSchema = srdmMiniTable.schema
    val miniEmptyDF = spark.createDataFrame(spark.sparkContext.emptyRDD[Row], miniSchema)
//    tableCreation(srdmMiniTable,srdmConfig,writer)
    writer.overwritePartition(miniEmptyDF, srdmMiniTableName, Array(days(col(UPDATED_AT_TS))))

    DataHubUtils.registerHiveDatasetInDataHub(srdmMiniTable, srdmMiniTableName, Map("is_mini_srdm" -> "true"))
    LineageUtils.emitColumnLineage(srdmMiniTableName, config.outputTableInfo.outputTableName, config.allProperties.toList)
  }
  override def getInitParams: EIJobArgs = new EIJobArgs()

  override def getConfigManifest: Manifest[Config] = manifest[Config]
}
