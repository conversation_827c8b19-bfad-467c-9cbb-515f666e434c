package ai.prevalent.entityinventory.loader.rerun

import ai.prevalent.entityinventory.common.configs.{EIJobArgs, Property}
import ai.prevalent.entityinventory.common.rerun._
import ai.prevalent.entityinventory.delta.{Change, LoaderConfigDelta}
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.entityinventory.utils.EILOGGER
import ai.prevalent.sdspecore.utils.ConfigUtils

/**
 * Specific rerun strategy implementation for the Loader phase
 */
class LoaderRerunStrategy extends BaseRerunStrategy[Config, LoaderConfigDelta] {

  override def validateStrategy(config: Config): Boolean = {
    config != null && config.outputTableInfo != null
  }

  override def createFieldUpdateConfigs(config: Config): Seq[FieldUpdateConfig] = {
    config.allProperties(true).map { property =>
      val fieldSpec = property.fieldsSpec
      FieldUpdateConfig(
        fieldName = property.colName,
        updateScope = if (fieldSpec.isInventoryDerived) UpdateViews else UpdateBoth,
        isInventoryDerived = fieldSpec.isInventoryDerived
      )
    }
  }

  override def determineUpdateScope(changes: Seq[Change], fieldConfigs: Seq[FieldUpdateConfig]): UpdateScope = {
    if (changes.isEmpty) return UpdateNone

    EILOGGER.info(s"Analyzing ${changes.length} changes:")
    changes.foreach(change =>
      EILOGGER.info(s"  - ${change.category}: ${change.name} (${change.changeType})")
    )
    val hasCriticalChanges = changes.exists(change =>
      change.category == "Primary Key" ||
        change.category == "Filter" ||
        change.category == "Origin" ||
        change.category == "Data Source"
    )

    if (hasCriticalChanges) {
      EILOGGER.info("Critical changes detected - performing full rerun")
      return UpdateBoth
    }
    val affectedFields = identifyAffectedFields(changes)
    val affectedConfigs = fieldConfigs.filter(config => affectedFields.contains(config.fieldName))

    if (affectedConfigs.isEmpty) return UpdateNone

    val allInventoryDerived = affectedConfigs.forall(_.isInventoryDerived)
    if (allInventoryDerived) {
      EILOGGER.info("Only inventory derived fields changed - updating views only")
      UpdateViews
    } else {
      EILOGGER.info("Non-inventory derived fields changed - performing full rerun")
      UpdateBoth
    }
  }

  override def identifyAffectedFields(changes: Seq[Change]): Seq[String] = {
    changes
      .filter(change =>
        change.category == "Attribute" ||
          change.category == "Enrichment"
      )
      .map(_.name)
      .distinct
  }
}

/**
 * Companion object with utility methods for loader rerun strategy
 */
object LoaderRerunStrategy {

  def apply(): LoaderRerunStrategy = new LoaderRerunStrategy()

  /**
   * Analyzes the rerun requirements for a loader configuration
   */


  def analyzeLoaderRerun(
                          jobArgs: EIJobArgs,
                          currentConfig: Config,
                          previousConfig: Option[String]
                        ): RerunAnalysis = {
    val strategy = new LoaderRerunStrategy()

    val context = RerunStrategy.getContextInfo(jobArgs)

    val configChanges = previousConfig.map { prevStr =>
      val prevConfigObj = ConfigUtils.getConfigFromJSON(prevStr)(Loader.getConfigManifest)
      currentConfig.getConfigDelta(prevConfigObj).changes()
    }
    val fieldUpdateConfigs = strategy.createFieldUpdateConfigs(currentConfig)
    val enrichedContext = context.copy(
      configChanges = configChanges,
      fieldUpdateConfigs = fieldUpdateConfigs
    )
    val analysis = strategy.analyzeRerun(enrichedContext)
    RerunStrategy.logAnalysis(analysis)
    analysis
  }
}