package ai.prevalent.entityinventory.loader.configs.specs

import ai.prevalent.entityinventory.common.configs.{DataSource, EIConfig, Entity, FieldsSpec, OutputTableInfo, Property}
import ai.prevalent.entityinventory.delta.{Change, LoaderConfigDelta}
import ai.prevalent.entityinventory.enrichment.Enrichment
import ai.prevalent.entityinventory.exceptions.UnsupportedConfig
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.utils.EILOGGER
import ai.prevalent.entityinventory.utils.SparkUtil.getAllReferences
import org.json4s.jackson.Serialization.write
import org.apache.spark.sql.catalyst.expressions.{Expression}
import org.apache.spark.sql.catalyst.parser.{CatalystSqlParser, ParseException}
import org.json4s.JsonAST.{JField, JObject, JString}
import org.json4s.{CustomSerializer, DefaultFormats, Extraction, Formats, JNull, MappingException}



case class Config(primaryKey: String = null,
                  filterBy: String = "true",
                  entity: Entity = Entity.defaultEntityConfig,
                  origin: String = null,
                  operationalMode: String = "carryForward",
                  dataSource: Option[DataSource] = Option.empty,
                  outputTableInfo: OutputTableInfo,
                  commonProperties: Array[Property] = Array.empty[Property],
                  entitySpecificProperties: Array[Property] = Array.empty[Property],
                  sourceSpecificProperties: Array[Property] = Array.empty[Property],
                  temporaryProperties: Array[Property] = Array.empty[Property],
                  deltaProperties: Array[Property] = Array.empty[Property],
                  enrichments: Array[Enrichment] = Array.empty
                 ) extends EIConfig[Config, LoaderConfigDelta] {
  def allProperties: Array[Property] = {
    val commonFunctionAdded = commonProperties ++ corePropertiesExpression ++ Array(Property("class", s"'${entity.name}'"))
    commonFunctionAdded ++ entitySpecificProperties ++ sourceSpecificProperties ++ deltaProperties
  }

  def allProperties(includeTempProperties: Boolean): Array[Property] = if (includeTempProperties) allProperties ++ temporaryProperties else allProperties

  def corePropertiesExpression = {
    val dateFieldSpec = FieldsSpec(
      aggregateFunction = Option.empty[String],
      isInventoryDerived = false,
      persistNonNullValue = Option.empty[Boolean],
      replaceExpression = Option.empty[Boolean]
    )

    val firstFoundSpec = dateFieldSpec.copy(aggregateFunction = Some("min"))
    val lastFoundSpec = dateFieldSpec.copy(aggregateFunction = Some("max"))
    val invFieldSpec = FieldsSpec(aggregateFunction = Option.empty[String], isInventoryDerived = true, persistNonNullValue = Option.empty[Boolean], replaceExpression = Option.empty[Boolean])
    Array(
      Property(FIRST_FOUND, "event_timestamp_epoch", firstFoundSpec),
      Property(LAST_FOUND, "event_timestamp_epoch", lastFoundSpec),
      Property(LIFETIME, "CASE WHEN ((last_active_date IS NOT NULL) AND (first_seen_date IS NOT NULL)) THEN datediff(from_unixtime((last_active_date / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime((first_seen_date / 1000), 'yyyy-MM-dd HH:mm:ss')) ELSE CAST(NULL AS INT) END", invFieldSpec),
      Property(RECENCY, s"datediff(from_unixtime(($UPDATED_AT / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime(($LAST_FOUND / 1000), 'yyyy-MM-dd HH:mm:ss'))", invFieldSpec),
      Property(OBSERVED_LIFETIME, s"datediff(from_unixtime(($LAST_FOUND / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime(($FIRST_FOUND / 1000), 'yyyy-MM-dd HH:mm:ss'))", invFieldSpec),
      Property(RECENT_ACTIVITY, s"CASE WHEN (last_active_date IS NOT NULL) THEN datediff(from_unixtime(($UPDATED_AT / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime((last_active_date / 1000), 'yyyy-MM-dd HH:mm:ss')) ELSE CAST(NULL AS INT) END", invFieldSpec),
    )
  }

  def validateExpr(exprStr: String, colName: String): Unit = {
    if (colName.contains(" ")) {
      throw new UnsupportedConfig(s"Invalid column name '$colName': Column names cannot contain spaces. Use underscore (_) for separation instead. Example: '${colName.replace(" ", "_")}'")
    }
    try {
      val parsedExpr: Expression = CatalystSqlParser.parseExpression(exprStr)
    } catch {
      case e: ParseException =>
        throw new UnsupportedConfig(s"Invalid expression in column '$colName',Update colExpr '$exprStr': \n${e.getMessage}")
      case e: Exception =>
        throw new UnsupportedConfig(s"Error validating expression in column '$colName': ${e.getMessage}\nExpression: $exprStr")
    }
  }

  def validateNoDuplicateColumns(properties: Seq[Property]): Unit = {
    val colNameCounts = properties.groupBy(_.colName).map { case (name, props) => (name, props.length) }
    val duplicates = colNameCounts.filter(_._2 > 1)
    if (duplicates.nonEmpty) {
      val duplicateDetails = duplicates.map { case (colName, _) =>
        val props = properties.filter(_.colName == colName)
        s"colName '$colName' appears ${props.length} times"
      }.mkString("\n")
      throw new UnsupportedConfig(s"Duplicate column names found in configuration: $duplicateDetails. Each column name must be unique across all properties.")
    }
  }

  private def validateLastUpdatedAttrsConflicts(): Unit = {
    val lastUpdateFields = entity.lastUpdateFields.toSet
    if (lastUpdateFields.isEmpty) return

    // Find columns that directly reference last_updated_attrs
    val columnsReferencingLastUpdatedAttrs = allProperties.filter { prop =>
      prop.colExpr != null && prop.colExpr.contains("last_updated_attrs")
    }.map(_.colName).toSet

    // Build dependency map for all properties
    val dependencyMap: Map[String, List[String]] = allProperties.map { prop =>
      if (prop.colExpr != null && prop.colExpr.trim.nonEmpty) {
        try {
          val refs = getAllReferences(CatalystSqlParser.parseExpression(prop.colExpr))
          (prop.colName, refs.toList)
        } catch {
          case _: Exception => (prop.colName, List.empty[String])
        }
      } else {
        (prop.colName, List.empty[String])
      }
    }.toMap

    // Find all columns that depend on last_updated_attrs columns (transitively)
    val allDependentColumns = findTransitiveDependencies(columnsReferencingLastUpdatedAttrs, dependencyMap)

    // Check for conflicts
    val conflictingColumns = lastUpdateFields.intersect(allDependentColumns)

    if (conflictingColumns.nonEmpty) {
      val errorDetails = conflictingColumns.map { colName =>
        val dependencyChain = findDependencyChain(colName, columnsReferencingLastUpdatedAttrs, dependencyMap)
        s"  - '$colName' ${dependencyChain}"
      }.mkString("\n")

      val errorMsg = s"Invalid configuration: The following columns are in 'lastUpdateFields' but depend on 'last_updated_attrs' references, which creates circular dependencies:\n$errorDetails\n" +
        "Solution: Remove these columns from 'lastUpdateFields' or remove their dependency on 'last_updated_attrs'."

      EILOGGER.error(errorMsg)
      throw new UnsupportedConfig(s"LastUpdatedAttrsConflict: $errorMsg")
    }

    // Also check if any column directly referencing last_updated_attrs is in lastUpdateFields
    val directConflicts = columnsReferencingLastUpdatedAttrs.intersect(lastUpdateFields)
    if (directConflicts.nonEmpty) {
      val errorMsg = s"Invalid configuration: The following columns directly reference 'last_updated_attrs' but are included in 'lastUpdateFields': ${directConflicts.mkString(", ")}. " +
        "Columns that reference 'last_updated_attrs' cannot be in 'lastUpdateFields' as this creates circular dependencies."

      EILOGGER.error(errorMsg)
      throw new UnsupportedConfig(s"LastUpdatedAttrsDirectConflict: $errorMsg")
    }
  }

  private def findTransitiveDependencies(initialSet: Set[String], dependencyMap: Map[String, List[String]]): Set[String] = {
    import scala.collection.mutable

    val result = mutable.Set[String]()
    val queue = mutable.Queue[String]()
    val visited = mutable.Set[String]()

    // Start with columns that directly reference last_updated_attrs
    initialSet.foreach { col =>
      result += col
      queue.enqueue(col)
    }

    while (queue.nonEmpty) {
      val current = queue.dequeue()
      if (!visited.contains(current)) {
        visited += current

        // Find all columns that depend on the current column
        dependencyMap.foreach { case (colName, dependencies) =>
          if (dependencies.contains(current) && !result.contains(colName)) {
            result += colName
            queue.enqueue(colName)
          }
        }
      }
    }

    result.toSet
  }

  private def findDependencyChain(targetColumn: String, lastUpdatedAttrsColumns: Set[String], dependencyMap: Map[String, List[String]]): String = {
    import scala.collection.mutable

    def dfs(current: String, path: List[String], visited: Set[String]): Option[List[String]] = {
      if (visited.contains(current)) return None

      val newPath = current :: path
      val dependencies = dependencyMap.getOrElse(current, List.empty)

      // Check if any dependency is a last_updated_attrs column
      val lastUpdatedAttrsDep = dependencies.find(lastUpdatedAttrsColumns.contains)
      if (lastUpdatedAttrsDep.isDefined) {
        return Some((lastUpdatedAttrsDep.get :: newPath).reverse)
      }

      // Continue searching through dependencies
      for (dep <- dependencies) {
        val result = dfs(dep, newPath, visited + current)
        if (result.isDefined) return result
      }

      None
    }

    dfs(targetColumn, List.empty, Set.empty) match {
      case Some(chain) => s"depends on: ${chain.mkString(" -> ")}"
      case None => "has indirect dependency on last_updated_attrs"
    }
  }
  def configValidator(): Unit = {
    val properties = allProperties
    val invalidCombinations = Seq(
      InvalidCombination("isInventoryDerived + aggregate", p => p.fieldsSpec.isInventoryDerived, p => p.fieldsSpec.aggregateFunction.isDefined, "Cannot combine aggregate spec with isInventoryDerived"),
      InvalidCombination("isInventoryDerived + persistence", p => p.fieldsSpec.isInventoryDerived, p => p.fieldsSpec.persistNonNullValue.isDefined, "Cannot combine persistence spec with isInventoryDerived"),
      InvalidCombination("aggregate + persistence", p => p.fieldsSpec.aggregateFunction.isDefined, p => p.fieldsSpec.persistNonNullValue.isDefined, "Cannot combine aggregate spec with persistence spec")
    )
    properties.foreach { prop =>
      invalidCombinations.foreach { combo =>
        if (combo.condition1(prop) && combo.condition2(prop)) {
          val errorMsg = s"Invalid spec for column '${prop.colName}': ${combo.errorMsg} (has ${combo.name})"
          EILOGGER.error(errorMsg)
          throw new UnsupportedConfig(s"InvalidSpecCombination: ${errorMsg}")
        }
      }
    }

    val tempFieldsWithWarning = temporaryProperties.filter(prop => {
      if (prop.fieldsSpec.persistNonNullValue.isDefined || prop.fieldsSpec.replaceExpression.isDefined || prop.fieldsSpec.aggregateFunction.isDefined)
        true
      else false
    })
    if (!tempFieldsWithWarning.isEmpty) {
      EILOGGER.error(s"Inapplicable fieldspecs for ${tempFieldsWithWarning.mkString(",")}")
      throw new UnsupportedConfig("InvalidSpec: temporary props only support isInventoryDerived,caseSensitiveExpression and convertEmptyToNull field spec")
    }
    validateNoDuplicateColumns(allProperties)
    validateLastUpdatedAttrsConflicts()
    val defaultProperties = Array(Property("primaryKey", primaryKey), Property("filterBy", filterBy), Property("origin", origin))
    (defaultProperties ++ allProperties(true)).foreach { prop =>
      if (prop.colExpr != null && prop.colExpr.trim.nonEmpty) {
        validateExpr(prop.colExpr, prop.colName)
        val explodePattern = """(?i)(?<!\w)explode\s*\(""".r
        if (explodePattern.findFirstIn(prop.colExpr).isDefined) {
          val errorMsg = s"Error in column '${prop.colName}': Using 'explode' function is not allowed. Please use 'explode_outer' instead."
          EILOGGER.error(errorMsg)
          throw new UnsupportedConfig(errorMsg)
        }
      }
    }
    allProperties

  }


  private case class InvalidCombination(name: String, condition1: Property => Boolean, condition2: Property => Boolean, errorMsg: String)


  override def toString: String = write(this)(Loader.configFormats)

  override def getConfigDelta(otherConfig: Config): LoaderConfigDelta = LoaderConfigDelta(otherConfig, this)

  override def getConfigDelta(deltas: Seq[Change]): LoaderConfigDelta = LoaderConfigDelta(this, deltas)


}

object ConfigSerializer extends CustomSerializer[Config](format => (
  {
    // Deserialization: From JSON to Config object
    case jsonObj: JObject =>
      // Define formats to be used for extraction, which includes the serializer for OutputTableInfo
      implicit val formats: Formats = DefaultFormats + OutputTableInfoSerializer
      // Transform the JSON by renaming the "outputTable" field to "outputTableInfo"
      val transformedJson = jsonObj.transformField {
        case JField("outputTable", value) => JField("outputTableInfo", value)
      }
      // Extract the Config object from the transformed JSON
      transformedJson.extract[Config]
  },
  {
    // Serialization: From Config object to JSON
    case config: Config =>
      implicit val formats: Formats = DefaultFormats + OutputTableInfoSerializer
      // Decompose the Config object into a JObject
      val decomposedJson = Extraction.decompose(config).asInstanceOf[JObject]
      // Transform the JObject by renaming "outputTableInfo" back to "outputTable"
      decomposedJson.transformField {
        case JField("outputTableInfo", value) => JField("outputTable", value)
      }
  }
))

/**
 * Custom serializer for the OutputTableInfo case class.
 * This allows the field to be specified as either a simple string or a full JSON object.
 */
object OutputTableInfoSerializer extends CustomSerializer[OutputTableInfo](format => (
  {
    // Deserialization: Converts JSON into an OutputTableInfo object.
    case JString(tableName) =>
      OutputTableInfo(outputTableName = tableName, outputWrittenMode = "viewType")
    case obj: JObject =>
      implicit val formats = DefaultFormats
      val tableName = (obj \ "outputTableName").extract[String]
      val writeMode = (obj \ "outputWrittenMode").extractOpt[String].getOrElse("viewType")
      OutputTableInfo(tableName, writeMode)
    case JNull =>
      throw new MappingException("outputTableInfo cannot be null.")
  },
  {
    // Serialization: Converts an OutputTableInfo object back into a standard JSON object.
    case outputInfo: OutputTableInfo =>
      JObject(
        JField("outputTableName", JString(outputInfo.outputTableName)),
        JField("outputWrittenMode", JString(outputInfo.outputWrittenMode))
      )
  }
))


