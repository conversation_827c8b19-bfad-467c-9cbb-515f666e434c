package ai.prevalent.entityinventory.loader

import ai.prevalent.entityinventory.cds.TableSpec
import ai.prevalent.entityinventory.common.configs.{EIJobArgs, Entity, Property}
import ai.prevalent.entityinventory.common.rerun.{IncrementalRerun, RerunStrategy, UpdateViews}
import ai.prevalent.entityinventory.delta.LoaderConfigDelta
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils.DATA_SOURCE_SUBSET_NAME
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.enrichment.Enrichment
import ai.prevalent.entityinventory.exceptions.UnsupportedConfig
import ai.prevalent.entityinventory.loader.Loader.{getInputConfigHash, spark}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.loader.rerun.LoaderRerunStrategy
import ai.prevalent.entityinventory.utils.EIUtil.{findLatestUpdateAttrExpr, removeNullFields, safeReadEntity}
import ai.prevalent.entityinventory.utils.SparkUtil.{findMissingBaseColumns, getAllReferences, propertyExpressionReplace, resolveDependencyLevels}
import ai.prevalent.entityinventory.utils.StructFieldReplaceUtils.{getNonStructFields, replaceStructProperties}
import ai.prevalent.entityinventory.utils.{CasePropertyExpressionReplacer, EILOGGER, EIUtil, SDSCast, SDSLambdaFunction, SparkUtil}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableWriter}
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, LongType, StringType, TimestampType}
import org.apache.spark.sql.{DataFrame, SparkSession, functions}
import ai.prevalent.entityinventory.utils.DataFrameWithColumnLogged._

import scala.collection.mutable
import scala.collection.mutable.{HashSet, LinkedHashMap, ListBuffer}
import scala.util.control.Exception.allCatch

object LoaderUtils extends LoggerBase {

  /**
   * Add core fields to the source df
   *
   * @param sourceDF
   * @param startDateEpoch
   * @param endDateEpoch
   * @param config
   * @return
   */

  def addRunCoreProperties(sourceDF: DataFrame, endDateEpoch: Long, config: Config): DataFrame = {
    sourceDF.withColumnLogged(SDSProperties.schema.UPDATED_AT, expr(s"$endDateEpoch"))
      .withColumnLogged(SDSProperties.schema.ORIGIN, expr(config.origin))
  }

  def applyTempTransform(sourceDF: DataFrame, config: Config,
                         isInventoryDerived: Boolean): DataFrame = {
    val temporaryProperties = config.temporaryProperties
      .filter(_.fieldsSpec.isInventoryDerived == isInventoryDerived)
    EILOGGER.info(s"""Adding temporary variables for ${if (isInventoryDerived) "Inventory fields" else "SDM fields"}, fields -  ${temporaryProperties.map(_.colName).toList}""".stripMargin)
    val nonStructFields = getNonStructFields(temporaryProperties)
    val missingFields = nonStructFields.diff(sourceDF.columns)
    EILOGGER.warn(s"""Missing fields for ${if (isInventoryDerived) "Inventory fields" else "SDM fields"} - ${missingFields} """)
    val missingFieldsExpressions = missingFields.map(field => lit(null).as(field))
    val nonStructFieldsAddedDf = sourceDF.select(
      sourceDF.columns.map(c => col(s"`$c`")) ++ missingFieldsExpressions: _*
    )
   temporaryProperties.foldLeft(nonStructFieldsAddedDf) { (accumulatedDf, prop) =>
      val (tempStructFieldsExpr, structFieldsReplacedPropList) = replaceStructProperties(accumulatedDf, Seq(prop))
      val dfWithStructs = tempStructFieldsExpr.foldLeft(accumulatedDf) {
        (currentDf, structCol) =>
          currentDf.withColumnLogged(structCol._1, expr(structCol._2))
      }
      dfWithStructs.withColumnLogged(prop.colName, expr(structFieldsReplacedPropList.head.colExpr))
    }
  }


  /**
   * Added temporary fields to source dataframe
   *
   * @param sourceDF
   * @param config
   * @param isInventoryDerived
   * @return
   */

  def applyTempTransformOfInventoryDerived(sourceDF: DataFrame, config: Config,
                         isInventoryDerived: Boolean): mutable.LinkedHashMap[String,String] = {
    val temporaryProperties = config.temporaryProperties
      .filter(_.fieldsSpec.isInventoryDerived == isInventoryDerived)
    EILOGGER.info(s"""Adding temporary variables for ${if (isInventoryDerived) "Inventory fields" else "SDM fields"}, fields -  ${temporaryProperties.map(_.colName).toList}""".stripMargin)
    val nonStructFields = getNonStructFields(temporaryProperties)
    val missingFields = nonStructFields.diff(sourceDF.columns)
    EILOGGER.warn(s"""Missing fields for ${if (isInventoryDerived) "Inventory fields" else "SDM fields"} - ${missingFields} """)
    val missingFieldsExpressions = LinkedHashMap[String, String]()
    missingFields.foreach { field =>
      missingFieldsExpressions.put(field, "NULL")
    }
    val nonStructFieldsAddedDf = sourceDF.select(
      sourceDF.columns.map(c => col(s"`$c`")) ++ (missingFields.map(field => lit(null).as(field))): _*
    )
    val tempStructMissingExpressions = LinkedHashMap[String, String]()
    val resultDf = temporaryProperties.foldLeft(nonStructFieldsAddedDf) { (accumulatedDf, prop) =>
      val (tempStructFieldsExpr, structFieldsReplacedPropList) =
        replaceStructProperties(accumulatedDf, Seq(prop))
      tempStructFieldsExpr.foreach { case (colName, colExpr) =>
        tempStructMissingExpressions.put(colName, colExpr)
      }
      tempStructMissingExpressions.put(prop.colName, structFieldsReplacedPropList.head.colExpr)
      val dfWithStructs = tempStructFieldsExpr.foldLeft(accumulatedDf) {
        (currentDf, structCol) =>
          currentDf.withColumnLogged(structCol._1, expr(structCol._2))
      }
      dfWithStructs.withColumnLogged(prop.colName, expr(structFieldsReplacedPropList.head.colExpr))
    }
    mergeWithReorder(missingFieldsExpressions, tempStructMissingExpressions)
    missingFieldsExpressions
  }

  /**
   * Map inventory fields with their corresponding expression to the dataframe according to the config
   *
   * @param stage1DF
   * @param config
   * @param properties
   * @param startDateEpoch
   * @param endDateEpoch
   * @param isInventoryDerived
   * @return transformed df
   */


  def applyTransformations(stage1DF: DataFrame, config: Config, properties: Seq[Property],
                           endDateEpoch: Long, isInventoryDerived: Boolean): DataFrame = {
    val sourceWithCoreFieldsDF = if (!isInventoryDerived) addRunCoreProperties(stage1DF, endDateEpoch, config) else stage1DF
    val filteredProperties = properties
      .filter(_.fieldsSpec.isInventoryDerived == isInventoryDerived)
    LOGGER.info(s"""Adding transformation for ${if (isInventoryDerived) "Inventory fields" else "SDM fields"}, fields -  ${filteredProperties.map(_.colName).toList}""".stripMargin)
    val transformedDF = applyPropertyTransformations(sourceWithCoreFieldsDF, filteredProperties)
    transformedDF
  }

  def applyPropertyTransformations(
                                    sourceDF: DataFrame,
                                    properties: Seq[Property]
                                  ): DataFrame = {
    val propertiesReplaced = SparkUtil.propertyExpressionReplace(properties)
    val propertiesExpr = propertiesReplaced.map(prop => expr(prop.colExpr).as(prop.colName))
    val dfColumns = sourceDF
      .columns
      .diff(properties.map(_.colName)) // Exclude the columns of the filtered properties
      .intersect(sourceDF.columns) // Ensure that the remaining columns exist in the DataFrame
      .map(name => expr(s"`$name`").as(name)) // Map each column to a column expression
      val requiredCols = dfColumns ++ propertiesExpr
      try {
        sourceDF.select(requiredCols: _*)
      } catch {
        case e: Exception =>
          propertiesReplaced.foreach { prop =>
            sourceDF.withColumnLogged(prop.colName, expr(prop.colExpr))
          }
          sourceDF
      }

  }


  /**
   * Add salting to primary key according to skew factor
   *
   * @param transformedDf
   * @param primaryKey
   * @param skewFactor
   * @return salted dataframe
   */
  def saltedDataframe(transformedDf: DataFrame, primaryKey: String, skewFactor: String): DataFrame = {
    transformedDf
      .withColumnLogged("salt_id", (lit(skewFactor) * rand()).cast("int"))
      .withColumnLogged(s"salted_${primaryKey}", functions.concat(col(primaryKey), lit("_"), col("salt_id")))
  }

  /**
   * Builds latest inventory according to persist/update spec, last_found and uuid params. Aggregation for fields is also calculated here
   *
   * @param inDF
   * @param primaryKeyColumn
   * @param config
   * @param spark
   * @return latest inv dataframe
   */
  def buildLatestInventory(inDF: DataFrame, primaryKeyColumn: String, config: Config)(spark: SparkSession): DataFrame = {
    val aggrFunValuesProps: Array[Property] = (config.sourceSpecificProperties ++ config.entitySpecificProperties ++
      config.commonProperties ++ config.corePropertiesExpression ++ config.deltaProperties)
      .filter(_.fieldsSpec.aggregateFunction.isDefined)
    val invExtraColumns = inDF.columns
      .diff(config.allProperties.filter(!_.fieldsSpec.isInventoryDerived).map(_.colName))
      .diff((Array(primaryKeyColumn, config.dataSource.get.uniqueRecordIdentifierKey)))
      .map(name => Property(colName = name))
    val finalColumns: Array[Property] = invExtraColumns ++ config.allProperties.filter(!_.fieldsSpec.isInventoryDerived)
    val aggCols = finalColumns.diff(aggrFunValuesProps).map(prop => {
      val structCol = struct(LAST_FOUND, config.dataSource.get.uniqueRecordIdentifierKey, prop.colName)
      val updCol =
        if (prop.fieldsSpec.persistNonNullValue.getOrElse(true)) {
          when(col(prop.colName).isNotNull, structCol)
        } else if (config.deltaProperties.isEmpty) {
          when(!array_contains(coalesce(col("persistenceDeltaOffFields"), array()), prop.colName), structCol)
        } else {
          structCol
        }
      max(updCol).as(prop.colName)
    }) ++ Array(config.dataSource.get.uniqueRecordIdentifierKey).map(name => max(name).as(name))

    val aggregrateFunCols = aggrFunValuesProps.map(prop => {
      val colName = prop.colName
      val aggregateFunc = prop.fieldsSpec.aggregateFunction.get
      expr(s"$aggregateFunc($colName)").as(colName)
    })

    val finalAggCols = aggregrateFunCols ++ aggCols
    val aggStructColSelect = finalColumns.diff(aggrFunValuesProps)
      .map(prop => col(prop.colName).getField(prop.colName).as(prop.colName))
    val finalSelectCols = aggStructColSelect ++ Array(config.dataSource.get.uniqueRecordIdentifierKey, primaryKeyColumn).map(name => col(name).as(name)) ++ (aggrFunValuesProps).map(prop => col(prop.colName))
    val aggDF = inDF.groupBy(primaryKeyColumn).agg(finalAggCols.head, finalAggCols.tail: _*)
      .select(finalSelectCols: _*)
    aggrFunValuesProps.map(_.colName).foldLeft(aggDF)((df, field) => {
      aggDF.schema(field).dataType match {
        case a: ArrayType => if(a.elementType.isInstanceOf[ArrayType]) df.withColumnLogged(field, array_distinct(filter(flatten(col(field)), ar => ar.isNotNull))) else df
        case _ => df
      }
    })
  }

  def getSparkConf(confKey: String)(spark: SparkSession): Option[String] = allCatch.opt(spark.conf.get(confKey))

  /**
   * Fields used in common field calculation expression but are missing in dataframe are added with null value.
   *
   * @param df
   * @param config
   * @return
   */
  def addMinimumFields(df: DataFrame, config: Config, isInventoryDerived: Boolean): mutable.LinkedHashMap[String, String] = {
    val commonProperties = config.allProperties.filter(_.fieldsSpec.isInventoryDerived == isInventoryDerived)
    val identicalColumnSequences: Seq[Seq[String]] = extractIdenticalColumnSequences(df)
    val caseInSensitiveReplacedProperties = CasePropertyExpressionReplacer.caseSensitivePropertyExpressionReplace(commonProperties, identicalColumnSequences)
    EILOGGER.jsonMiniPrint("Properties from addMinimumFields function", commonProperties)
    EILOGGER.jsonMiniPrint("Case sensitive Replacer added properties from addMinimumFields function", caseInSensitiveReplacedProperties)
    val commonCols: Array[String] = caseInSensitiveReplacedProperties
      .flatMap(prop => expr(prop.colExpr).expr.references.map(_.name.split("[.]")(0)))
      .distinct
      .diff(df.columns)
      .toArray
    val missingColsMap = mutable.LinkedHashMap[String, String]()
    commonCols.diff(df.columns).foreach { field =>
      missingColsMap.put(field, "NULL")}
    missingColsMap
  }

  /**
   * Adds sdm derieved fields to the inv dataframe
   *
   * @param sourceDF
   * @param config
   * @param jobArgs
   * @return
   */
  def generateSDMDerivedFields(sourceDF: DataFrame, config: Config, jobArgs: EIJobArgs, reader: SDSTableReader): DataFrame = {
    val updatedSource =sourceDF.withColumnLogged(UPDATED_AT_TS, expr(s"to_timestamp(${jobArgs.currentUpdateDate}/1000)"))
    val enrichedDF = Enrichment.applyEnrichments(updatedSource, config.enrichments, reader)
    val identicalColumnSequences: Seq[Seq[String]] = extractIdenticalColumnSequences(enrichedDF)
    val filteredProperties = config.allProperties
      .filter(!_.fieldsSpec.isInventoryDerived)
    val caseInSensitiveReplacedProperties = CasePropertyExpressionReplacer.caseSensitivePropertyExpressionReplace(filteredProperties, identicalColumnSequences)
    EILOGGER.jsonMiniPrint("Case InSensitive Replaced Properties", caseInSensitiveReplacedProperties)

      val emptyRemovedDF = EIUtil.preProcesmptyStringtoNull(enrichedDF)
      val (tempStructFieldsAddedDFExpr, structFieldsReplacedPropList) = replaceStructProperties(emptyRemovedDF, caseInSensitiveReplacedProperties)
      val tempStructFieldsAddedDF = propertyExpressionReplace(tempStructFieldsAddedDFExpr,emptyRemovedDF)
      val primaryKeyAddedDF = generatePrimaryKey(tempStructFieldsAddedDF, config)
      if (config.dataSource.get.dataEventTimestampKey != EVENT_TIMESTAMP_TS && !primaryKeyAddedDF.schema(config.dataSource.get.dataEventTimestampKey).dataType.isInstanceOf[TimestampType]) {
        EILOGGER.error(s"Invalid spec for: ${config.dataSource.get.dataEventTimestampKey}")
        throw new UnsupportedConfig(s"Column ${config.dataSource.get.dataEventTimestampKey} must be of TimestampType")
      }

      val timestampKeyAddedDF = primaryKeyAddedDF
        .withColumnLogged("event_timestamp_epoch", when(lit(config.dataSource.get.dataEventTimestampKey) === lit(EVENT_TIMESTAMP_TS), expr("cast(event_timestamp_epoch as bigint)")
        ).otherwise(expr(s"unix_millis(${config.dataSource.get.dataEventTimestampKey})")))
        .filter(col(config.dataSource.get.dataEventTimestampKey) <= to_timestamp(lit(jobArgs.currentUpdateDate).divide(1000)))
        .filter(config.filterBy)
        .filter(s"(CASE WHEN btrim(${config.primaryKey},'[\r\n\t\f ]')!='' THEN btrim(${config.primaryKey},'[\r\n\t\f ]') END) IS NOT NULL")
      //      .repartition(col(PRIMARY_KEY))
      if (!timestampKeyAddedDF.columns.contains(config.dataSource.get.uniqueRecordIdentifierKey)) {
        throw new UnsupportedConfig(
          s"The UUID Column `${config.dataSource.get.uniqueRecordIdentifierKey}` is not present in the input source. " +
            s"Add it in temporary variables and map it in `uniqueRecordIdentifierKey` under `datasource` in the loader config."
        )
      }
      val transformedSourceDF = applyTransformations(timestampKeyAddedDF, config, structFieldsReplacedPropList, jobArgs.currentUpdateDate, isInventoryDerived = false)
      val enrichmentColumnsSeq: Array[String] = config.enrichments
        .flatMap { enrichment =>
          val columns = enrichment.lookupInfo.enrichmentColumns ++ Array(ORIGIN_LOOKUP_ENRICH)
          if (columns.nonEmpty) {
            LOGGER.info(s"Adding enrichment columns from table '${enrichment.lookupInfo.tableName}': ${columns.mkString(", ")}")
            columns.intersect(transformedSourceDF.columns)
          } else {
            LOGGER.info(s"No enrichment columns to add for table '${enrichment.lookupInfo.tableName}'.")
            Array.empty[String]
          }
        }
      val sdmTransformFields = (Array(ORIGIN, PRIMARY_KEY, UPDATED_AT, FIRST_FOUND,
        LAST_FOUND, config.dataSource.get.uniqueRecordIdentifierKey) ++ config.allProperties.filter(!_.fieldsSpec.isInventoryDerived).map(_.colName) ++ enrichmentColumnsSeq).distinct
      transformedSourceDF.select(sdmTransformFields.map(col(_)): _*)
    }

    def generatePrimaryKey(df: DataFrame, config: Config): DataFrame = {
      val tempDf = applyTempTransform(df, config, isInventoryDerived = false)
      val addMinimumFieldsExpr = addMinimumFields(tempDf, config, isInventoryDerived = false)
      addMinimumFieldsExpr.put(SDSProperties.schema.PRIMARY_KEY,config.primaryKey)
      var resultDf = tempDf
      addMinimumFieldsExpr.foreach { case (colName, exprValue) =>
        resultDf = resultDf.withColumnLogged(colName, expr(exprValue))
      }

      resultDf
    }

    def extractIdenticalColumnSequences(df: DataFrame): Seq[Seq[String]] = {
      val possibleFields = ListBuffer[String]()
      val processedCols = HashSet[String]()
      val columnSequences = ListBuffer[Seq[String]]()

      def traverseSchema(path: Seq[String], schema: org.apache.spark.sql.types.DataType): Unit = {
        schema match {
          case structType: org.apache.spark.sql.types.StructType =>
            structType.fields.foreach { field =>
              val fullPath = path :+ field.name
              possibleFields += fullPath.mkString(".")
              traverseSchema(fullPath, field.dataType)
            }
          case _ =>
        }
      }
      df.schema.fields.foreach { field =>
        val path = Seq(field.name)
        possibleFields += path.head
        traverseSchema(path, field.dataType)
      }
      possibleFields.groupBy(_.toLowerCase).values.foreach { identicalColumns =>
        columnSequences += identicalColumns.toSeq
        processedCols ++= identicalColumns.map(_.toLowerCase)
      }

      columnSequences.toSeq
    }

    def addMissingFields(invDf: DataFrame, properties: Array[Property]): DataFrame = {
      val missingCols: Array[String] = properties.map(prop => expr(prop.colExpr).expr)
        .map(_.references.map(_.name))
        .flatten.filter(!_.contains(".")).distinct
        .diff(invDf.columns)
      missingCols
        .foldLeft(invDf)((df, field) => df.withColumn(field, lit(null).cast(StringType)))
    }
    def generatePID(config: Config): mutable.LinkedHashMap[String, String] = {
      mutable.LinkedHashMap("primary_key_attribute" ->  s"""r"${config.primaryKey}"""",
        "p_id" -> SDSProperties.GUID_EXPR)
    }

    def  tableCreation(latestSrdmTransformedDf: DataFrame,config: Config,writer: SDSTableWriter)={
      writer.overwritePartition(latestSrdmTransformedDf, config.outputTableInfo.outputTableName, Array(days(col(UPDATED_AT_TS)),col(KG_CONTENT_TYPE)))
    }
  def generateViewSQL(propertiesMap: mutable.LinkedHashMap[String, String],
                      baseTableName: String,
                      originalColumns: Set[String],
                      filterCondition: Option[String] = None,
                     ): String = {

    val dependencyMap: Map[String, List[String]] = propertiesMap.map{ case (colName, colExpr) =>
      (colName, getAllReferences(expr(colExpr).expr).toList)
    }.toMap


    val levels = resolveDependencyLevels(dependencyMap)

    val missingColumns = findMissingBaseColumns(propertiesMap, originalColumns)

    // Track which columns are available at each level
    var availableColumns = originalColumns ++ missingColumns
    val baseCols = originalColumns.toSeq.sorted.map(col => s"`$col`")
    var currentSQL = if (missingColumns.nonEmpty) {
      val allBaseCols = baseCols ++
        missingColumns.toSeq.sorted.map(col => s"NULL AS `$col`")
      val baseQuery = s"SELECT ${allBaseCols.mkString(", ")} FROM $baseTableName"

      if (filterCondition.isEmpty) {
        baseQuery
      } else {
        s"$baseQuery WHERE ${filterCondition.get}"
      }
    } else {
      if (filterCondition.isEmpty) {
        s"SELECT ${baseCols.mkString(", ")} FROM $baseTableName"
      } else {
        s"SELECT ${baseCols.mkString(", ")} FROM $baseTableName WHERE ${filterCondition.get}"
      }
    }
    // Process each dependency level as a separate nested SELECT
    for ((level, levelIndex) <- levels.zipWithIndex if level.nonEmpty) {
      val derivedCols = level.map { colName =>
        val colExpr = propertiesMap(colName)
        s"$colExpr AS `$colName`"
      }

      // For columns being replaced, we need to exclude them from * and add the new version
      val replacedCols = level.toSet
      val keptCols = availableColumns.diff(replacedCols)

      val selectClause = if (keptCols.nonEmpty) {
        // Explicitly select non-replaced columns + new derived columns
        (keptCols.toSeq.sorted.map(col => s"`$col`") ++ derivedCols).mkString(", ")
      } else {
        // Only derived columns
        derivedCols.mkString(", ")
      }

      currentSQL = s"SELECT $selectClause FROM (\n$currentSQL\n) AS level_$levelIndex"

      // Update available columns for next level
      availableColumns = keptCols ++ replacedCols
    }

    currentSQL

  }

  def viewSqlCreation(latestSrdmTransformedDf: DataFrame,config: Config,selectExprMap:LinkedHashMap[String,String],jobArgs: EIJobArgs,finalSelectCols: List[String]) = {

    val recencyFilter = if (config.operationalMode == "dailyReset") Some(s"$RECENCY=0") else None
    val validSelectExprMap = selectExprMap.filter { case (key, value) => !key.matches("^\\d+$")}
    val validOriginalColumns = latestSrdmTransformedDf.columns.toSet.filter { col =>
      !col.matches("^\\d+$")
    }
    val baseTableName = if (config.outputTableInfo.outputWrittenMode == "tableType") {
      val tempViewName = s"${config.outputTableInfo.outputTableName.replace(".", "_")}__srdm_inv_temp"
      latestSrdmTransformedDf.createOrReplaceTempView(tempViewName)
      tempViewName
    } else {
      // Use existing table name for viewType mode
      s"${config.outputTableInfo.outputTableName}__srdm_inv"
    }
    val intermediateSql = generateViewSQL(
      propertiesMap = validSelectExprMap,
      baseTableName = baseTableName,
      originalColumns = validOriginalColumns
    )


    val finalSQL = s"""
                      |WITH computed_data AS (
                      |$intermediateSql
                      |)
                      |SELECT ${finalSelectCols.mkString(", ")}
                      |FROM computed_data
                      |""".stripMargin

    println("finalSQL*********************************")
    println(finalSQL)
    val whereClause = recencyFilter.map(f => s"WHERE $f").getOrElse("")
    val rawDf = spark.sql(finalSQL)
    val df = removeNullFields(rawDf)
    val cleanedColumns = df.columns.toList.filter(col => !col.matches("^\\d+$"))
    val configRowSelectCols = cleanedColumns.map { colName=>
      if (Set(UPDATED_AT_TS, KG_CONFIG, KG_CONTENT_TYPE,KG_CONFIG_HASH,KG_JAR_FILE_HASH,KG_INPUT_FILE_HASH,UPDATED_AT).contains(colName))
        colName
      else
        s"NULL AS $colName"
    }
    val viewSQL = s"""
                     |WITH computed_data AS (
                     |$intermediateSql
                     |),
                     |transformed_data AS (
                     |  SELECT ${cleanedColumns.mkString(", ")}
                     |  FROM computed_data
                     |  WHERE $KG_CONTENT_TYPE = 'data'
                     |),
                     |config_data AS (
                     |  SELECT ${configRowSelectCols.mkString(", ")}
                     |  FROM computed_data
                     |  WHERE $KG_CONTENT_TYPE = 'config'
                     |)
                     |SELECT * FROM transformed_data
                     |$whereClause
                     |UNION ALL
                     |SELECT * FROM config_data
                     |""".stripMargin

    val tempViewName = config.outputTableInfo.outputTableName.replace(".", "_") + "_temp"
    spark.sql(s"CREATE OR REPLACE TEMPORARY VIEW $tempViewName AS $viewSQL")
    viewSQL
    }

  def viewCreation(config: Config, viewSQL: String)={
    val schema = config.outputTableInfo.outputTableName.split("\\.")(0)
    spark.sql(s"CREATE SCHEMA IF NOT EXISTS $schema")
    spark.sql(s"DROP VIEW IF EXISTS ${config.outputTableInfo.outputTableName}")
    spark.sql(s"CREATE VIEW ${config.outputTableInfo.outputTableName} AS $viewSQL")
  }


  def finalSelectInvColumns(selectDf: DataFrame, config:Config):List[String]={

      val voidRemovedSrdmColumns = config.allProperties.filter(!_.fieldsSpec.isInventoryDerived).map(_.colName)
        .filter(selectDf.columns.contains(_))
      val configInvProperties = config.allProperties.filter(_.fieldsSpec.isInventoryDerived).map(_.colName)
      val configProperties = voidRemovedSrdmColumns ++ configInvProperties
      val invCoreCols = Array(ORIGIN, PRIMARY_KEY, P_ID, UPDATED_AT, FIRST_FOUND,
        LAST_FOUND, LIFETIME, RECENCY, OBSERVED_LIFETIME, RECENT_ACTIVITY, LAST_UPDATED_ATTRS, LAST_UPDATED_DATE, UPDATED_AT_TS,
        KG_CONTENT_TYPE,KG_CONFIG,DATA_SOURCE_SUBSET_NAME,FRAGMENTS,KG_INPUT_FILE_HASH,KG_JAR_FILE_HASH,KG_CONFIG_HASH)
      val otherCoreCols = Array("first_seen_date","last_active_date")
      val enrichmentColumnsSeq: Array[String] = config.enrichments
        .flatMap { enrichment =>
          val columns = enrichment.lookupInfo.enrichmentColumns ++ Array(ORIGIN_LOOKUP_ENRICH)
          if (columns.nonEmpty) {
            LOGGER.info(s"Adding enrichment columns from table '${enrichment.lookupInfo.tableName}': ${columns.mkString(", ")}")
            columns.intersect(selectDf.columns)
          } else {
            LOGGER.info(s"No enrichment columns to add for table '${enrichment.lookupInfo.tableName}'.")
            Array.empty[String]
          }
        }
      val finalCols = (configProperties ++ invCoreCols ++ enrichmentColumnsSeq ++ otherCoreCols).distinct.toList
      finalCols
    }
  def mergeWithReorder(map: LinkedHashMap[String, String], updates: LinkedHashMap[String, String]): Unit = {
      updates.foreach { case (k, v) =>
        if (map.contains(k)) map.remove(k)
        map.put(k, v)
      }
    }

    /**
     * Generate inv derived fields from the existing fields in the inv
     *
     * @param latestInventoryDf
     * @param config
     * @param jobArgs
     * @return
     *
     */
    def generateInventoryDerivedFields(latestInventoryDf: DataFrame, config: Config, jobArgs: EIJobArgs): mutable.LinkedHashMap[String, String] = {
      LOGGER.info("Adding transformation for Inventory fields")
      val mergedExprMap: LinkedHashMap[String, String] = LinkedHashMap()
      val pidAddedDFExpr = generatePID(config)
      mergedExprMap ++= pidAddedDFExpr
      val minimumFieldsExpr = addMinimumFields(latestInventoryDf, config, isInventoryDerived = true)
      minimumFieldsExpr.remove(LAST_UPDATED_ATTRS)
      mergedExprMap ++= minimumFieldsExpr
      val applyTempTransformExpr = applyTempTransformOfInventoryDerived(latestInventoryDf, config, isInventoryDerived = true)
      mergedExprMap ++= applyTempTransformExpr
      val invProperties = SparkUtil.propertyExpressionReplace(config.allProperties.filter(_.fieldsSpec.isInventoryDerived))
      val inventoryFieldsWithoutLastUpd = invProperties
        .filter(prop => {
          !expr(prop.colExpr).expr.references.flatMap(_.references.map(_.name.split("[.]")(0))).toArray.contains(LAST_UPDATED_ATTRS)
        })
      val intermediateReplaceDf = propertyExpressionReplace(mergedExprMap,latestInventoryDf)
      val (structFieldsReplacedPropExpr,structFieldsReplacedPropListWithoutLastUpd) = replaceStructProperties(
        intermediateReplaceDf,
        inventoryFieldsWithoutLastUpd)
      mergedExprMap ++= structFieldsReplacedPropExpr
      val filteredProperties = structFieldsReplacedPropListWithoutLastUpd
        .filter(_.fieldsSpec.isInventoryDerived == true)
      LOGGER.info(s"""Adding transformation for Inventory fields, fields -  ${filteredProperties.map(_.colName).toList}""".stripMargin)
      val propertiesReplaced = SparkUtil.propertyExpressionReplace(filteredProperties)
      propertiesReplaced.foreach { prop =>
        mergedExprMap.put(prop.colName, prop.colExpr)
      }
      val existingColumns = latestInventoryDf.columns.toSet
      val existingExpressions = mergedExprMap.keySet
      val allExistingColumns = existingColumns ++ existingExpressions
      Array("first_seen_date", "last_active_date").foreach { colName =>
        if (!allExistingColumns.contains(colName) ||
          mergedExprMap.get(colName).exists(value =>
            value == null || value.toString.toUpperCase == "NULL" || value.toString.trim.isEmpty)) {
          mergedExprMap.put(colName, "CAST(NULL AS STRING)")
        }
      }
      val intermediateDf = propertyExpressionReplace(mergedExprMap, latestInventoryDf)
      mergedExprMap ++= findLatestUpdateAttrExpr(intermediateDf,config.entity)
      val inventoryFieldsWithLastUpd = invProperties
        .filter(prop => {
          expr(prop.colExpr).expr.references.flatMap(_.references.map(_.name.split("[.]")(0))).toArray.contains(LAST_UPDATED_ATTRS)
        })
      val intermediateLastUpdReplaceDf = propertyExpressionReplace(mergedExprMap, latestInventoryDf)
      val (lastupdDFAddedStruct, structFieldsReplacedPropListWithLastUpd) = replaceStructProperties(intermediateLastUpdReplaceDf, inventoryFieldsWithLastUpd)
      mergedExprMap ++= lastupdDFAddedStruct
      val filteredPropertiesLastUpd = structFieldsReplacedPropListWithLastUpd
        .filter(_.fieldsSpec.isInventoryDerived == true)
      LOGGER.info(s"""Adding transformation for Last updated Inventory fields, fields -  ${filteredPropertiesLastUpd.map(_.colName).toList}""".stripMargin)
      val propertiesReplacedLpd = SparkUtil.propertyExpressionReplace(filteredPropertiesLastUpd)
      propertiesReplacedLpd.foreach { prop =>
        mergedExprMap.put(prop.colName, prop.colExpr)
      }
      val additionalExprs: LinkedHashMap[String, String] = LinkedHashMap(
        "fragments" -> "1",
        DATA_SOURCE_SUBSET_NAME -> ORIGIN
      )
      mergedExprMap ++= additionalExprs
      mergedExprMap
    }
  def build(sourceDF: DataFrame, previousInventoryDF: DataFrame, config: Config, prevConfig: Option[String], jobArgs: EIJobArgs, reader: SDSTableReader, writer: SDSTableWriter)(spark: SparkSession): (DataFrame,LinkedHashMap[String,String]) = {

    val hasChanges = Option(jobArgs.rerunInfoJson)
      .filter(_.nonEmpty)
      .map(RerunStrategy.parseRerunInfo)
      .exists(_.hasChanges.getOrElse(false))

    val isViewOnlyUpdate = if (hasChanges) {
      EILOGGER.info("Changes detected - analyzing rerun strategy")
      val previousDF=EIUtil.safeReadEntity(config.outputTableInfo.outputTableName, expr(s"$UPDATED_AT_TS = to_timestamp(${jobArgs.currentUpdateDate}/1000)"), reader)
      val previousRunConfig=EIUtil.fetchPrevConfig(previousDF)
      val rerunAnalysis = LoaderRerunStrategy.analyzeLoaderRerun(
        jobArgs = jobArgs,
        currentConfig = config,
        previousConfig = previousRunConfig
      )
      rerunAnalysis.updateScope == UpdateViews && rerunAnalysis.executionStrategy == IncrementalRerun
    } else {
      EILOGGER.info("No rerun info provided or no changes detected - proceeding with normal flow")
      false
    }
    val (finalLatestSrdmInvDf, inventoryDerivedExpr) = if (isViewOnlyUpdate) {
      EILOGGER.info("Performing view-only update - skipping full rebuild")

      val existingInventoryBase = reader.read(s"${config.outputTableInfo.outputTableName}__srdm_inv").filter(col(UPDATED_AT_TS) === to_timestamp(lit(jobArgs.currentUpdateDate).divide(1000)))

      val updateContentTypeDF = existingInventoryBase.filter(s"${KG_CONTENT_TYPE}='config'").
        withColumnLogged(s"${KG_CONFIG_HASH}",lit(getInputConfigHash(config.toString)))
      writer.overwritePartition(updateContentTypeDF, s"${config.outputTableInfo.outputTableName}__srdm_inv", Array(days(col(UPDATED_AT_TS)), col(KG_CONTENT_TYPE)))
      LOGGER.info(s"Successfully written the base table for updating the ${KG_CONTENT_TYPE},${config.outputTableInfo.outputTableName}__srdm_inv")
      val inventoryDerivedExpr = generateInventoryDerivedFields(existingInventoryBase, config, jobArgs)

      (existingInventoryBase, inventoryDerivedExpr)

    } else {
      EILOGGER.info("Performing full rebuild")
      val sdmTransformSelectedDF = buildSRDMInventorTransoformation(sourceDF, previousInventoryDF, config, prevConfig, jobArgs, reader)
      val skewFactor = getSparkConf(SDSProperties.sds.spark.conf.SALTING)(spark)
      val latestInventoryDf = if (skewFactor.isDefined) {
        val saltedDF = saltedDataframe(sdmTransformSelectedDF, SDSProperties.schema.PRIMARY_KEY, skewFactor.get)
        val saltedDFWithNewKey = buildLatestInventory(saltedDF, s"salted_${SDSProperties.schema.PRIMARY_KEY}", config)(spark)
        buildLatestInventory(saltedDFWithNewKey, SDSProperties.schema.PRIMARY_KEY, config)(spark)
          .drop(s"salted_${SDSProperties.schema.PRIMARY_KEY}", "salt_id")
      } else {
        buildLatestInventory(sdmTransformSelectedDF, SDSProperties.schema.PRIMARY_KEY, config)(spark)
      }
      val coreRunFieldsAddedInv = addRunCoreProperties(latestInventoryDf, jobArgs.currentUpdateDate, config)
      val tableSpec = Array(TableSpec(config.dataSource.get.srdm, sdmTransformSelectedDF))
      val latestSrdmInvDf = EIUtil.prepareInvDFWithConfig(config = config.toString, invDF = coreRunFieldsAddedInv.withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)")), args = jobArgs, spark = Loader.spark,tableSpec)
      val finalLatestSrdmInvDf = removeNullFields(latestSrdmInvDf)
      val srdmInvDf = if (config.outputTableInfo.outputWrittenMode == "viewType") {
        writer.overwritePartition(finalLatestSrdmInvDf, s"${config.outputTableInfo.outputTableName}__srdm_inv", Array(days(col(UPDATED_AT_TS)),col(KG_CONTENT_TYPE)))
        LOGGER.info(s"Successfully written the base table ${config.outputTableInfo.outputTableName}__srdm_inv")
        reader.read(s"${config.outputTableInfo.outputTableName}__srdm_inv").filter(col(UPDATED_AT_TS) === to_timestamp(lit(jobArgs.currentUpdateDate).divide(1000)))
      } else {
        finalLatestSrdmInvDf
      }
      val inventoryDerivedExpr=generateInventoryDerivedFields(srdmInvDf, config, jobArgs)
      (srdmInvDf,inventoryDerivedExpr)
    }
    (finalLatestSrdmInvDf, inventoryDerivedExpr)

  }

  def getConfigDelta(config: Config, prevConfigStr: Option[String]): Option[Config] = {
    if (prevConfigStr.isDefined) {
      val prevConfig: Config = ConfigUtils.getConfigFromJSON(prevConfigStr.get, Loader.configFormats)(Loader.getConfigManifest)
      val delta = LoaderConfigDelta(prevConfig, config)
      val srdmMappedProp = config.allProperties(true).filter(!_.fieldsSpec.isInventoryDerived)
      val srdmChangedFields = delta.properties.filter(_.category.eq("Attribute")).map(_.name).intersect(srdmMappedProp.map(_.colName))
      val srdmChangedProps = srdmMappedProp.filter(p => srdmChangedFields.contains(p.colName))
      val srdmRefProp = srdmChangedFields.flatMap(field => {
        val dependentFields = SparkUtil.getDependendProperty(srdmMappedProp , field)
        val prop=srdmMappedProp.filter(p => dependentFields.contains(p.colName))
        println(s"Field '$field' is referenced by: ${prop.map(_.colName).mkString(", ")}")
        prop
      }).distinct
      val srdmDerivedProp: Array[Property] = (srdmChangedProps ++ srdmRefProp).distinct
      val primaryKeyProperty = Property(PRIMARY_KEY, config.primaryKey)
      val filterProperty=Property("filter",config.filterBy)
      val primaryKeyTempProps = SparkUtil.getDependendProperty(config.temporaryProperties :+ primaryKeyProperty, PRIMARY_KEY)
      val primaryKeyProperties = config.temporaryProperties.filter(p => primaryKeyTempProps.contains(p.colName))
      val filterByKeyTempProps = SparkUtil.getDependendProperty(config.temporaryProperties :+ filterProperty, "filter")
      val filterByKeyProperties = config.temporaryProperties.filter(p => filterByKeyTempProps.contains(p.colName))
      val tempPropDelta = config.temporaryProperties.intersect(srdmDerivedProp)
      val finalTemPropDeltasWoOrder = primaryKeyProperties ++ tempPropDelta ++ filterByKeyProperties

      val finalTemPropDelta = config.temporaryProperties.intersect(finalTemPropDeltasWoOrder ++ srdmRefProp)
      val finalSrdcmChangedProps = srdmDerivedProp.diff(finalTemPropDelta)

      val deltaConfig = config.copy(deltaProperties = finalSrdcmChangedProps, sourceSpecificProperties = Array.empty,
        entitySpecificProperties = Array.empty, commonProperties = Array.empty, temporaryProperties = finalTemPropDelta)
      EILOGGER.jsonMiniPrint("Delta Conf",deltaConfig)
      Some(deltaConfig)
    }
    else Option.empty
  }

  def buildSRDMInventorTransoformation(sourceDF: DataFrame, previousInventoryDF: DataFrame,
                                       config: Config, prevConfig: Option[String],
                                       jobArgs: EIJobArgs, reader: SDSTableReader) = {
    val configDelta = getConfigDelta(config, prevConfig)
    val deltaDF = sourceDF.filter(col(config.dataSource.get.dataIntervalTimestampKey) >= to_timestamp(lit(jobArgs.parsedIntervalStartEpoch).divide(1000)))
    val srdmInv = generateSDMDerivedFields(deltaDF, config, jobArgs, reader)
    val unionSRDMWithPrevInv = (srdm: DataFrame, inv: DataFrame) => {
      val processdInv = EIUtil.preProcessPreviousInventory(inv, config.entity)
        .drop(config.allProperties.filter(_.fieldsSpec.isInventoryDerived).map(_.colName): _*)
      SparkUtil.unionByName(srdm, processdInv)
    }
    if (configDelta.isDefined && !configDelta.get.deltaProperties.isEmpty) {
      LOGGER.info(s" Config Delta - ${configDelta.get.toString}")
      val deltaCols = configDelta.get.deltaProperties.map(_.colName).toSet
      val persistenceDeltaOffFields = (config.commonProperties ++ config.entitySpecificProperties ++ config.sourceSpecificProperties)
        .filter(prop => !prop.fieldsSpec.persistNonNullValue.getOrElse(true) && !deltaCols.contains(prop.colName))
        .map(_.colName)
      LOGGER.info(s"Persistence Delta Off Fields - ${persistenceDeltaOffFields.mkString("Array(", ", ", ")")}")
      val historicalSRDM = sourceDF.filter(col(config.dataSource.get.dataIntervalTimestampKey) >= to_timestamp(lit(jobArgs.srdmHistoricalParsedIntervalStartEpoch).divide(1000)))
      val historicalSRDMInv = generateSDMDerivedFields(historicalSRDM, configDelta.get, jobArgs, reader).withColumnLogged("persistenceDeltaOffFields", lit(persistenceDeltaOffFields))
      val srdmInvWithDelta = SparkUtil.unionByName(srdmInv, buildLatestInventory(historicalSRDMInv, SDSProperties.schema.PRIMARY_KEY, configDelta.get)(spark))
      val previousInventoryDFWithoutDeltas = previousInventoryDF.drop(configDelta.get.deltaProperties.map(_.colName): _*)
      unionSRDMWithPrevInv(srdmInvWithDelta, previousInventoryDFWithoutDeltas)
    }
    else unionSRDMWithPrevInv(srdmInv, previousInventoryDF).withColumnLogged("persistenceDeltaOffFields", array())

  }

  def getUpdatedParsedIntervalStartEpoch(jobArgs: EIJobArgs, config: Config, reader: SDSTableReader,spark: SparkSession): Long = {
    val previousInRead = safeReadEntity(config.outputTableInfo.outputTableName, reader = reader)
    val maxUpd = previousInRead.filter(expr(s"to_timestamp(${jobArgs.currentUpdateDate}/1000) > $UPDATED_AT_TS"))
      .select(max(col(UPDATED_AT_TS)).as(UPDATED_AT_TS))
      .withColumn(UPDATED_AT, unix_millis(col(UPDATED_AT_TS)))
      .map(row => row.getAs[Long](UPDATED_AT))(spark.implicits.newLongEncoder)
      .collect()
      .lastOption

    EILOGGER.info(s"${maxUpd} ******************")
    if (maxUpd.isDefined) {
      if (jobArgs.parsedIntervalStartEpoch <= 0) maxUpd.get else jobArgs.parsedIntervalStartEpoch
    } else {
      if (jobArgs.parsedIntervalStartEpoch <= 0) jobArgs.srdmHistoricalParsedIntervalStartEpoch else jobArgs.parsedIntervalStartEpoch
    }
  }

  def getInputDataframe(config: Config, filterString: String, jobArgs: EIJobArgs,reader: SDSTableReader): DataFrame = {
    LOGGER.info(s"Getting input dataframe for table: ${config.dataSource.get.srdm}")
    val sourceDF: DataFrame = reader.read(config.dataSource.get.srdm)
    val filteredDF = sourceDF.filter(expr(filterString))
    val dataEventTimestampKey = config.dataSource.get.dataEventTimestampKey
    val tempPropertyForEventKey = config.temporaryProperties.find(_.colName == dataEventTimestampKey)
    val finalDF = if (tempPropertyForEventKey.isDefined) {
      LOGGER.info(s"Using temporary property for event key: $dataEventTimestampKey")
      val tempExpr = tempPropertyForEventKey.get.colExpr
      val tempTransformedDF = filteredDF.withColumn(dataEventTimestampKey, expr(tempExpr))
      tempTransformedDF.filter(col(dataEventTimestampKey) <= to_timestamp(lit(jobArgs.currentUpdateDate).divide(1000)))
    } else {
      LOGGER.info(s"Using data interval timestamp key: ${config.dataSource.get.dataEventTimestampKey}")
      filteredDF.filter(col(config.dataSource.get.dataEventTimestampKey) <= to_timestamp(lit(jobArgs.currentUpdateDate).divide(1000)))
    }

    finalDF
  }
}
