package ai.prevalent.entityinventory.loader.configs

object SDSProperties {
  final val GUID_EXPR =
    """
      |SHA2(
      | NULLIF(
      |   CONCAT_WS(
      |    '||',
      |    IFNULL(NULLIF(TRIM(CAST(primary_key AS STRING)), ''), '^^'),
      |    IFNULL(NULLIF(TRIM(CAST(origin AS STRING)), ''), '^^'),
      |    IFNULL(NULLIF(TRIM(CAST(class AS STRING)), ''), '^^'),
      |    IFNULL(NULLIF(TRIM(CAST(primary_key_attribute AS STRING)), ''), '^^')
      |   ),
      | '^^||^^'
      | ),
      |256
      |)
      |""".stripMargin

  final val CORE_FIELDS_DATE = Map(
    "first_found_date" -> "event_timestamp_epoch",
    "last_found_date" -> "event_timestamp_epoch"
  )


  object schema {
    final val ORIGIN = "origin"
    final val UUID = "uuid"
    final val DELIM = "__"
    final val PRIMARY_KEY = "primary_key"
    final val LAST_UPDATED_ATTRS = "last_updated_attrs"
    final val LAST_UPDATED_DATE = "last_updated_date"
    final val PREV_ATTRS = "prev_attrs"
    final val EVENT_TIMESTAMP_EPOCH = "event_timestamp_epoch"
    final val INGESTED_TIMESTAMP_EPOCH = "ingested_timestamp_epoch"
    final val PARSED_INTERVAL_TIMESTAMP_TS = "parsed_interval_timestamp_ts"
    final val UPDATED_AT = "updated_at"
    final val KG_CONTENT_TYPE = "kg_content_type"
    final val UPDATED_AT_TS = "updated_at_ts"
    final val FIRST_FOUND = "first_found_date"
    final val ORIGIN_ENTITY_ENRICH = "origin_entity_enrich"
    final val LAST_FOUND = "last_found_date"
    final val LIFETIME = "lifetime"
    final val RECENCY = "recency"
    final val OBSERVED_LIFETIME = "observed_lifetime"
    final val RECENT_ACTIVITY = "recent_activity"
    final val EVENT_TIMESTAMP_TS = "event_timestamp_ts"
    final val ORIGIN_LOOKUP_ENRICH = "origin_lookup_enrich"
    final val KG_CONFIG = "kg_config"
    final val KG_INPUT_FILE_HASH = "kg_input_file_hash"
    final val KG_JAR_FILE_HASH= "kg_jar_file_hash"
    final val KG_CONFIG_HASH = "kg_config_hash"
    final val FRAGMENTS = "fragments"

  }

  final val FILL_NA = ""

  object sds {
    object spark {
      object conf {
        final val SALTING = "spark.sds.salting"
      }
    }
  }

}
