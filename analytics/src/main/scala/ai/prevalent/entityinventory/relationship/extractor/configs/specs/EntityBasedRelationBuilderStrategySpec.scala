package ai.prevalent.entityinventory.relationship.extractor.configs.specs

import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UUID
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.StringType

class EntityBasedRelationBuilderStrategySpec(baseEntity: String) extends RelationBlockBuilder{

  case class BlockEntities(base: String, moving: String)

  override def buildBlock(sourceDF: DataFrame): DataFrame = {
    val blockEntities = if(baseEntity=="targetEntity") {
      BlockEntities("target_p_id", "source_p_id")
    } else {
      BlockEntities("source_p_id","target_p_id")
    }

    val winSpec = Window
      .partitionBy(blockEntities.base)
      .orderBy(asc("event_timestamp_epoch"),asc(schema.UUID))

    sourceDF
      .withColumn("pk__moving", col(blockEntities.moving))
      .withColumn("row_number", col(schema.UUID))
      .withColumn("block_id1", when((lag(col(blockEntities.moving),1).over(winSpec).isNull).or(col(blockEntities.moving)=!= lag(col(blockEntities.moving),1).over(winSpec)), col("row_number")))
      .withColumn("block_id",when(col("block_id1").isNotNull,col("block_id1")).otherwise(lag(col("block_id1"),1,defaultValue = null,ignoreNulls=true).over(winSpec)).cast(StringType))
      .drop("row_number","block_id1")
  }

  override def createMiniSDM(sourceDF: DataFrame,config: Config, optionalAttributes: Array[Attribute]): DataFrame = {
    val extractedFields = config.extractJoinFields(config.inputSourceInfo, sourceDF)
    val optAttrReferences = optionalAttributes.map(attr => expr(attr.exp).expr)
      .flatMap(_.references.map(_.name.split("[.]")(0)))

    val miniSDMColumns = (optAttrReferences ++ extractedFields ++ Array("event_timestamp_epoch","source_p_id","target_p_id",UUID))
      .distinct
      .intersect(sourceDF.columns)
    sourceDF.select(miniSDMColumns.map(col(_)):_*).distinct()
  }
}
