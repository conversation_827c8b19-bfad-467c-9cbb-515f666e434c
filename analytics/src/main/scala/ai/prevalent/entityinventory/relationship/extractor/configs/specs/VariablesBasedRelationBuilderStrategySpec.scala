package ai.prevalent.entityinventory.relationship.extractor.configs.specs

import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._


class VariablesBasedRelationBuilderStrategySpec( val blockVariables: Array[String]) extends RelationBlockBuilder{

  override def buildBlock(sourceDF: DataFrame): DataFrame = {
    sourceDF
      .withColumn("block_id", concat_ws("#", blockVariables.map(col):_*))
      .filter("block_id IS NOT NULL")
  }

  override def createMiniSDM(sourceDF: DataFrame, config: Config, optionalAttributes: Array[Attribute]): DataFrame = {

    val extractedFields = config.extractJoinFields(config.inputSourceInfo, sourceDF)
    val optionalAttributesRef = (
      optionalAttributes
        .filter(_.occurrence.ne("COLLECT"))
        .map(attr => expr(attr.exp).expr)
        .flatMap(_.references.map(_.name.split("[.]")(0)))
        .distinct
        .intersect(sourceDF.columns)
        .diff(blockVariables)
        .diff(Array("event_timestamp_epoch"))
        ++ extractedFields
      ).distinct


    val optonalAttributesCollectRef = optionalAttributes.filter(_.occurrence.equalsIgnoreCase("COLLECT")).map(attr => attr.exp)

    val optionalAttributesAgg = optionalAttributesRef.flatMap(name => {
        val structCol = when(col(name).isNotNull,struct("event_timestamp_epoch",name))
        Array(
          max(structCol).as(s"max__$name"),
          min(structCol).as(s"min__$name"),
        )
      })
    val optionalAttributesCollect = optonalAttributesCollectRef.map(f => {
      if (sourceDF.columns.contains(s"collected__${f}"))
        collect_set(array_union(coalesce(col(s"collected__${f}"),array()),coalesce(array(col(f)),array()))).as(s"collected__${f}")
      else
        collect_set(array(col(f))).as(s"collected__${f}")
    })

    val eventTimeMax = max("event_timestamp_epoch").as("max__event_timestamp_epoch")
    val eventTimeMin = min("event_timestamp_epoch").as("min__event_timestamp_epoch")
    val uuidMaxTimeMin = min(schema.UUID).as(schema.UUID)

    val grpdDF = sourceDF
      .groupBy(blockVariables.map(col(_)):_*)
      .agg(eventTimeMin, optionalAttributesAgg++optionalAttributesCollect:+eventTimeMax:+uuidMaxTimeMin:_*)
      .withColumn("event_timestamp_epoch",explode(array("min__event_timestamp_epoch","max__event_timestamp_epoch")))
    val optrAttrAddedDF = optionalAttributesRef.foldLeft(grpdDF)((df,name) =>{
      df.withColumn(name, expr(s"CASE WHEN min__event_timestamp_epoch=event_timestamp_epoch THEN min__$name.$name ELSE max__$name.$name END"))
        .drop(s"min__$name",s"max__$name")
    })
    val optAttrCollectDF = optonalAttributesCollectRef.foldLeft(optrAttrAddedDF)((df,name) =>{
      df.withColumn(s"collected__${name}",expr(s"array_distinct(filter(flatten(collected__${name}), x -> x is not null))").as(s"collected__${name}"))
    })

    optAttrCollectDF.drop("min__event_timestamp_epoch","max__event_timestamp_epoch")
  }

}