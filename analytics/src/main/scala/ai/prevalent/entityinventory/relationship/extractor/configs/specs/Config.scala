package ai.prevalent.entityinventory.relationship.extractor.configs.specs

import ai.prevalent.entityinventory.common.configs.{EIConfig, EIJobArgs, Property}
import ai.prevalent.entityinventory.delta.{Change, RelationshipConfigDelta}
import ai.prevalent.entityinventory.enrichment.Enrichment
import ai.prevalent.entityinventory.exceptions.InvalidRelationException
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{EVENT_TIMESTAMP_TS, ORIGIN, PARSED_INTERVAL_TIMESTAMP_TS, UPDATED_AT, UUID}
import ai.prevalent.entityinventory.loader.configs.specs.{Config => LoaderConfig}
import ai.prevalent.entityinventory.relationship.extractor.Extractor
import ai.prevalent.sdspecore.utils.SecretUtil.LOGGER
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.StringType
import org.json4s.{DefaultFormats, _}
import org.json4s.jackson.JsonMethods._
import org.json4s.jackson.Serialization.write

case class InputSourceInfo (sdmPath:String=null,
                            dataIntervalTimestampKey: String=PARSED_INTERVAL_TIMESTAMP_TS,
                            dataEventTimestampKey: String=EVENT_TIMESTAMP_TS,
                            uniqueRecordIdentifierKey:String=UUID,
                            sourceMappingInfo:MappingProperties=null,
                            targetMappingInfo:MappingProperties=null,
                            origin: String=null,
                            filter: String = "true",
                            enrichments: Array[Enrichment] = Array.empty,
                            temporaryProperties: Array[Property] = Array.empty){
  override def toString: String = pretty(render(parse(write(this)(DefaultFormats))))
}

case class MappingProperties(
                           configPath:List[String]=List.empty,
                           tableName: String=null,
                           joinCondition:String=null
                            )
case class OutputInfo(
                       outputTable:String=null,
                       prevMiniSDM: String =null
                     )

case class InputSource (dataFrame: DataFrame,
                        dataEventTimestampKey: String,
                        tempProperties: Array[String]=Array.empty,
                        sourceLoader:List[LoaderConfig],
                        targetLoader:List[LoaderConfig] ,
                        joinCondition:Array[String]=Array.empty)

case class Attribute(name: String=null, exp: String=null, occurrence: String = "LAST")

case class Config(name: String = null,
                  inverseRelationshipName: String = null,
                  inputSourceInfo: List[InputSourceInfo] = List.empty,
                  output: OutputInfo =null,
                  intraSourcePath: String = null,
                  interSourcePath: String = null,
                  interTargetPath: String = null,
                  optionalAttributes: Array[Attribute] = Array.empty,
                  valueBasedRelationBuilderStrategySpec: Option[ValueBasedRelationBuilderStrategySpec],
                  entityBasedRelationBuilderStrategySpec: Option[EntityBasedRelationBuilderStrategySpec],
                  variablesBasedRelationBuilderStrategySpec: Option[VariablesBasedRelationBuilderStrategySpec]) extends EIConfig[Config,RelationshipConfigDelta] {

  override def toString: String = write(this)(Extractor.configFormats)

  def optionalAttributesProcessed = optionalAttributes:+ Attribute(ORIGIN,ORIGIN,"COLLECT")

  def updateMeta(df: DataFrame, updateAt: Long): DataFrame = {
    val mandatoryFields = Array(
      "source_p_id",
      "target_p_id",
      "relationship_name",
      "inverse_relationship_name",
      "updated_at",
      "first_found_date",
      "last_found_date",
      "relationship_first_seen_date",
      "relationship_last_seen_date",
      "relationship_id",
      "lifetime",
      "recency",
      "source_entity_class",
      "target_entity_class",
      "start_epoch",
      "end_epoch"
    )
    val optionalFields = optionalAttributesProcessed.map(_.name)

    val requiredFields = mandatoryFields ++ optionalFields
    df
      .withColumn("relationship_name", lit(name))
      .withColumn("inverse_relationship_name", lit(inverseRelationshipName))
      .withColumn("lifetime", datediff(from_unixtime(col("relationship_last_seen_date") / 1000), from_unixtime(col("relationship_first_seen_date") / 1000)))
      .withColumn("recency", datediff(from_unixtime(col("updated_at") / 1000), from_unixtime(col("relationship_last_seen_date") / 1000)))
      .select(requiredFields.distinct.map(col): _*)
      .withColumn("relationship_origin",col(ORIGIN))
      .withColumn("lifetime_relationship", col("lifetime"))
      .withColumn("recency_relationship",col("recency"))
  }


  def execute(inputSources: List[InputSource], prevMinSDM: DataFrame,config: Config ,resolvers: DisambiguationResolvers,
              jobArgs: EIJobArgs, prevConfigStr: Option[String]): RelationBuildInfo = {

    val blockedRelationDF = if (entityBasedRelationBuilderStrategySpec.isDefined) {
      LOGGER.info(s"Relationship built using Entity Based Relation Builder Strategy.")
      entityBasedRelationBuilderStrategySpec.get.build(inputSources,prevMinSDM,resolvers,optionalAttributesProcessed,jobArgs.currentUpdateDate, this, prevConfigStr)
    } else if (variablesBasedRelationBuilderStrategySpec.isDefined) {
      LOGGER.info(s"Relationship built using Variable Based Relation Builder Strategy.")
      variablesBasedRelationBuilderStrategySpec.get.build(inputSources,prevMinSDM,resolvers,optionalAttributesProcessed,jobArgs.currentUpdateDate, this, prevConfigStr)
    } else {
      throw new InvalidRelationException("No or invalid relation builder strategy")
    }
    LOGGER.info(s"Blocked Relation Schema:,$blockedRelationDF")

    val updAddedDF  = blockedRelationDF.relationDF.withColumn(UPDATED_AT,lit(jobArgs.currentUpdateDate))
    val derivedAttributesDF = deriveoptionalAttributesProcessed(updAddedDF,  config.output.outputTable.split("[.]")(1))

    val relationDF = updateMeta(derivedAttributesDF, jobArgs.currentUpdateDate)
    LOGGER.info(s"Relationship schema,${relationDF.schema}")
    RelationBuildInfo(relationDF, blockedRelationDF.miniSDM.withColumn(UPDATED_AT,lit(jobArgs.currentUpdateDate)))
  }

  def addMissingFields(relationDf: DataFrame, properties: Array[Attribute]): DataFrame = {
    val missingCols: Array[String] = properties.map(prop => expr(prop.exp).expr).flatMap(_.references.map(_.name.split("[.]")(0))).distinct
      .diff(relationDf.columns)
    LOGGER.info(s"Missing columns are: ${missingCols.mkString(", ")}")
    missingCols
      .foldLeft(relationDf)((df, field) => df.withColumn(field, lit(null).cast(StringType)))
  }

  def deriveoptionalAttributesProcessed(blockBuiltDF: DataFrame, outputPath:String): DataFrame = {
    val blockBuiltDFWithMissingFields = addMissingFields(blockBuiltDF, optionalAttributesProcessed)
    val entityColumns = Array("source_p_id","target_p_id","source_entity_class","target_entity_class",UPDATED_AT)
    val startEpochExpr = min(col("event_timestamp_epoch")).as("first_found_date")
    val endEpochExpr = max(col("event_timestamp_epoch")).as("last_found_date")

    val attrAddedDF = optionalAttributesProcessed
      .foldLeft(blockBuiltDFWithMissingFields)((df, attr) => df.withColumn(attr.name, expr(attr.exp)))
    val attrAggCols = optionalAttributesProcessed.map(attr => {
      val structCol = when(col(attr.name).isNotNull, struct("event_timestamp_epoch",UUID, attr.name))
      attr.occurrence match {
        case "LAST" => max(structCol).as(attr.name)
        case "FIRST" => min(structCol).as(attr.name)
        case "COLLECT" => if (attrAddedDF.columns.contains(s"collected__${attr.name}"))
          collect_set(array_union(coalesce(col(s"collected__${attr.name}"),array()),coalesce(array(col(attr.name)),array()))).as(attr.name)
        else
          collect_set(array(col(attr.name))).as(attr.name)
        case _ => {
          LOGGER.info(s"Unsupported occurrence attribute for ${attr.name}, applying LAST")
          max(structCol).as(attr.name)
        }
      }
    })
    val finalAggExpr = attrAggCols ++ entityColumns.map(name => max(name).as(name))
    val aggDF = attrAddedDF.groupBy("block_id").agg(startEpochExpr,finalAggExpr:+endEpochExpr :_*)

    val attrFinalSelectCols =  optionalAttributesProcessed.map(attr =>{
      attr.occurrence match {
        case "LAST" => col(attr.name).getField(attr.name).as(attr.name)
        case "FIRST" => col(attr.name).getField(attr.name).as(attr.name)
        case "COLLECT" => expr(s"array_sort(array_distinct(filter(flatten(${attr.name}), x -> x is not null)))").as(attr.name)
        case _ => col(attr.name).getField(attr.name).as(attr.name)
      }
    })

    val commonCols = entityColumns ++ Array("first_found_date", "last_found_date","block_id")

    val finalSelectCol = attrFinalSelectCols ++ commonCols.map(name => col(name).as(name))
    val attrAggDF = aggDF.select( finalSelectCol:_*)
    val stage2DF = if(!attrAggDF.columns.contains("relationship_first_seen_date")){
      attrAggDF.withColumn("relationship_first_seen_date", col("first_found_date"))
    } else{
      attrAggDF
    }

    val stage3DF = if (!stage2DF.columns.contains("relationship_last_seen_date")) {
      stage2DF.withColumn("relationship_last_seen_date", col("last_found_date"))
    } else {
      stage2DF
    }
    stage3DF
      .withColumn("start_epoch", col("relationship_first_seen_date"))
      .withColumn("end_epoch", col("relationship_last_seen_date"))
      .withColumn("relationship_id",sha2(concat_ws("#",lit(outputPath),col("block_id")), 256))
  }

  override def getConfigDelta(otherConfig: Config): RelationshipConfigDelta = RelationshipConfigDelta(otherConfig,this)

  override def getConfigDelta(deltas: Seq[Change]): RelationshipConfigDelta = RelationshipConfigDelta(clientConfig = this, deltas)

  def extractJoinFields(inputSources: List[InputSourceInfo], sourceDF: DataFrame): Array[String] = {
    val joinConditions = inputSources.flatMap { input =>
        Seq(Option(input.sourceMappingInfo).flatMap(m => Option(m.joinCondition)), Option(input.targetMappingInfo).flatMap(m => Option(m.joinCondition))).flatten
      }
    joinConditions.flatMap(condition => expr(condition).expr.references.map(_.name).filter(_.startsWith("s.")).map(_.stripPrefix("s.")))
      .distinct.filter(sourceDF.columns.contains).toArray
  }
}

object MappingPropertiesSerializer extends CustomSerializer[MappingProperties](format => (
  {
    case JArray(elements) =>
      MappingProperties(
        configPath = elements.map {
          case JString(path) => path
          case _ => throw new MappingException("Invalid mapping format")
        }.toList
      )
    case obj: JObject =>
      implicit val formats = DefaultFormats
      Extraction.extract[MappingProperties](obj)
  },
  {
    case mapping: MappingProperties =>
      Extraction.decompose(mapping)(DefaultFormats)
  }
))