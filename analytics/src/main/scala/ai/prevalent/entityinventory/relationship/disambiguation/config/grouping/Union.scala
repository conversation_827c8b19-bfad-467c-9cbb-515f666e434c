package ai.prevalent.entityinventory.relationship.disambiguation.config.grouping

import ai.prevalent.entityinventory.delta.{Change}
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils.DISAMBIGUATION_GROUP_ID
import ai.prevalent.entityinventory.relationship.disambiguation.config.{Config, DisambiguationGrouping, DisambiguationGroupingDelta}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.monotonically_increasing_id

case class Union()  extends DisambiguationGrouping{

  override def generateGroup(nonResolvedDF:DataFrame,config:Config) :DataFrame ={
    val groupedDF=nonResolvedDF.withColumn(DISAMBIGUATION_GROUP_ID,monotonically_increasing_id()).checkpoint(true)
    groupedDF.select(DISAMBIGUATION_GROUP_ID,"relationship_id")
  }
    override def getConfigDelta(otherConfig: DisambiguationGrouping): DisambiguationGroupingDelta = {
    otherConfig match {
      case other: Union => DisambiguationGroupingDelta()
      case _ =>  DisambiguationGroupingDelta(
        groupingTypeChange = Some(Change("Disambiguation Grouping", f"Disambiguation Grouping Type Changed to ${otherConfig.getClass.getSimpleName}", Option.empty, Map("Old Grouping Type" -> "Union", "New Grouping Type" -> this.getClass.getSimpleName), "Attribute"))
      )
    }
  }

  override def getConfigDelta(deltas: Seq[Change]): DisambiguationGroupingDelta = null
}
