package ai.prevalent.entityinventory.relationship.entityenrich

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.entityinventory.relationship.entityenrich.EntityEnrich.spark
import ai.prevalent.entityinventory.relationship.entityenrich.configs.{Config}
import org.apache.spark.sql.{DataFrame, Row}
import org.apache.spark.sql.catalyst.expressions.Alias
import org.apache.spark.sql.functions._
import ai.prevalent.entityinventory.utils.EILOGGER
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import org.apache.spark.sql.types.{DataTypes, StringType, StructField, StructType}

object EntityEnrichUtil {
  def build(config: Config, entityDF: DataFrame,params: EIJobArgs): DataFrame = {
    if (config.countEnriches.isEmpty) {
      println("No count enriches found in config, returning original entityDF")
      return entityDF
    }
    val entityClass = entityDF.select("class").first().getString(0)
    println("Creating relation count enrich DataFrame")
    val relEnrichCounts = createRelCountEnrichDF(config,entityClass,params)
    entityDF.as("s").join(relEnrichCounts.as("e"),Array(P_ID),"left").checkpoint(true)
  }



  def missingFieldExprHandler(config: Config, relDF: DataFrame): Config = {
    val allColumns = relDF.columns
    EILOGGER.jsonMiniPrint("allColumns", allColumns)

    def isExpressionValid(expression: String): Boolean = {
      val referencedColumns = expr(expression).expr.references.map(_.name.split("[.]")(0))
      referencedColumns.forall(allColumns.contains)
    }

    val updatedCountEnriches = config.countEnriches.filter(enrich =>
      isExpressionValid(enrich.filter)
    )
    config.copy(countEnriches = updatedCountEnriches)
  }

  def createRelCountEnrichDF(config: Config,entityClass: String, params: EIJobArgs): DataFrame = {
    val reader = SDSTableReaderFactory.getDefault(spark)
    val enrichesByTable = config.countEnriches.groupBy(_.relationshipTableName)
    EILOGGER.jsonMiniPrint("enrichesByTable", enrichesByTable)
    val allDFs =enrichesByTable.flatMap { case (tableName, enriches) =>
      val relDF = reader.readOrElse(tableName,spark.emptyDataFrame.withColumn(UPDATED_AT_TS,lit(null).cast(DataTypes.TimestampType))).filter(s"$UPDATED_AT_TS = to_timestamp(${params.currentUpdateDate}/1000)")
      if (relDF.isEmpty) {
        EILOGGER.warn(s"Table $tableName returns Not Found or Is Empty,Therefore skipping...")
        None
      } else {
      val validEnriches = missingFieldExprHandler(Config(countEnriches = enriches,inheritedPropertyEnrichment = Seq.empty ), relDF).countEnriches
      EILOGGER.jsonMiniPrint("Valid Enriches",validEnriches)
      if (validEnriches.nonEmpty) {
        val sourceRelClass = relDF.select("source_entity_class").first().getString(0)
        val groupByField = if (sourceRelClass == entityClass) "source_p_id" else "target_p_id"
        val updDF=relDF.withColumn(P_ID,col(groupByField))
        val expressions = validEnriches.flatMap(enrich =>
          Option(enrich.colName).map(name => sum(when(expr(enrich.filter), 1).otherwise(0)).as(name)))
        Some(updDF.groupBy(P_ID).agg(expressions.head, expressions.tail: _*)
          )
      } else None
      }
    }.toSeq
    if (allDFs.isEmpty) {
      EILOGGER.info("No valid enriches found, returning empty DataFrame")
      spark.createDataFrame(spark.sparkContext.emptyRDD[Row], StructType(Seq(StructField(P_ID, StringType))))
    } else {
      allDFs.reduce(_.join(_, Seq(P_ID), "full_outer"))
    }
  }

}
