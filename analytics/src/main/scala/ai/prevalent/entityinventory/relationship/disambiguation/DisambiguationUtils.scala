package ai.prevalent.entityinventory.relationship.disambiguation

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils.DISAMBIGUATION_GROUP_ID
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.{PID_ARRAY, PRECEDENCE, RELATION_ID}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.relationship.disambiguation.config.{Config, RelDisambiguation, RelationshipModelDF, Strategy}
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.entityinventory.utils.LineageUtils.createColumnLineage
import ai.prevalent.entityinventory.utils.{DataHubUtils, EILOGGER, EIUtil, SchemaEvolutionUtil, SparkUtil}
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession, functions}

object DisambiguationUtils {

  val RELATIONSHIP_CORE_COLS = Array(PRECEDENCE, "relationship_id", DISAMBIGUATION_GROUP_ID, PID_ARRAY)

  val DEFAULT_ROLLUP_COLUMNS = Array(ORIGIN,"relationship_origin")
  val MIN_FIELDS = Array(FIRST_FOUND,"relationship_first_seen_date")
  val MAX_FIELDS = Array(LAST_FOUND,"relationship_last_seen_date")

  def minimumFields(disambiguationConf: RelDisambiguation): Array[String] = {
    val strategy = disambiguationConf.strategy.getOrElse(Strategy.empty)
    val fieldLevelConfidenceFields = strategy
      .fieldLevelConfidenceMatrix.getOrElse(Array.empty)
    val fieldLevelConfFields = fieldLevelConfidenceFields
      .map(conf => conf.field)
    val singleSourceFields = strategy.singleSource.map(_.field).toArray
    val aggregateFields = strategy.aggregation.map(_.field).toArray

    val valueConfidence = strategy.valueConfidence.getOrElse(Array.empty)
    val valueBasedFields = valueConfidence
      .map(detail => detail.field)
    val rollingUpFields: Array[String] = (strategy.rollingUpFields ++ DEFAULT_ROLLUP_COLUMNS).distinct

    aggregateFields ++ singleSourceFields ++ fieldLevelConfFields ++ valueBasedFields ++ rollingUpFields ++ RELATIONSHIP_CORE_COLS
  }

  def normalizeInventoryModels(relationshipModelSpecs: Array[RelationshipModelDF], spark: SparkSession ,
                               config: RelDisambiguation): Array[RelationshipModelDF] = {
    EILOGGER.info("Normalizing relationship models")
    val allRelSchema = relationshipModelSpecs.flatMap(_.df.schema).map(f => (f.name, f.dataType)).toMap

    relationshipModelSpecs.map(spec => {
      val emptyRemovedCols = spec.df.schema.fields
        .map(field => {
          if (field.dataType == StringType)
            when(trim(col(field.name)) === lit(""), lit(null).cast("string")).otherwise(col(field.name)).as(field.name)
          else col(field.name).as(field.name)
        })

      val emptyRemovedDF = spec.df.select(emptyRemovedCols: _*)
      val fieldsNeeded = minimumFields(config).diff(emptyRemovedDF.columns)
      val minimumSchema = fieldsNeeded
        .foldLeft(new StructType())((schema, field) => {
          schema.add(StructField(field, allRelSchema.getOrElse(field, StringType), true))
        })
      val minDF = spark.createDataFrame(spark.sparkContext.emptyRDD[Row], minimumSchema)
      val normalizedDF = SchemaEvolutionUtil.evolveSchema(emptyRemovedDF, SparkUtil.unionByName(minDF,emptyRemovedDF).schema)
      RelationshipModelDF(normalizedDF, spec.name,true,spec.path)
    })
  }

  def createEmptyOutputDF(spark: SparkSession,params: EIJobArgs): OutputDF = {
    val mandatoryFieldsInterSource = Array(
      "relationship_id","source_p_id","target_p_id","source_entity_class","target_entity_class","relationship_name","inverse_relationship_name"
    )
    val mandatoryFieldsNonResolved = Array(
      "relationship_name","data_source_name"
    )
    val mandatoryFieldsResolver = Array(
      "relationship_name","data_source_name"
    )
    def createEmptyRelationDataFrame(spark: SparkSession, mandatoryFields: Array[String]): DataFrame = {
      val emptyDF = spark.emptyDataFrame
                    .withColumn(UPDATED_AT, lit(params.currentUpdateDate))
                    .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
      mandatoryFields.foldLeft(emptyDF)((df, colName) => df.withColumn(colName, lit(null).cast(StringType)))
    }
    val interSourceDF = createEmptyRelationDataFrame(spark, mandatoryFieldsInterSource)
    val nonResolvedDF = createEmptyRelationDataFrame(spark, mandatoryFieldsNonResolved)
    val resolverDF = createEmptyRelationDataFrame(spark, mandatoryFieldsResolver)

    OutputDF(interSourceDF, nonResolvedDF, resolverDF)
  }

  def build(config: Config,params:EIJobArgs,reader:SDSTableReader, spark: SparkSession):OutputDF = {

    val latestFilterExpr = expr(s"$UPDATED_AT_TS == to_timestamp(${params.currentUpdateDate}/1000)")
    val relTables = config.relationshipModels.map(rel => {
      RelationshipModelDF(EIUtil.safeReadRelation(rel.tableName, latestFilterExpr, reader)
        .withColumn("rel_source_name", lit(rel.name)).filter(rel.filter), rel.name,reader.isTableExists(rel.tableName),rel.tableName)
    })
    if (relTables.forall(_.df.isEmpty) & relTables.exists(_.isTableExists)) {
      EILOGGER.warn("All input DataFrames are empty")
      createEmptyOutputDF(spark,params)
    }else {
      val normalizedInvModelSpecList = normalizeInventoryModels(relTables.filter(!_.df.isEmpty).toArray, spark, config.disambiguation)
      val individualColumnDefinitions = normalizedInvModelSpecList.map { spec =>
        createColumnLineage(sourceTable = spec.path, targetTable = config.output.disambiguatedModelLocation, Left(spec.df.columns))
      }
      val relDF = SparkUtil.unionByName(normalizedInvModelSpecList.map(_.df): _*)

      val confDF = confidenceMatrix(normalizedInvModelSpecList,config)
      val groupedDF = config.disambiguation.disambiguationGrouping.generateGroup(relDF,config)
      val combinedDF = joinGroupedDF(groupedDF,confDF)

      val dismbDF = confidenceEntityExtraction(combinedDF,config)
        .withColumn(UPDATED_AT, lit(params.currentUpdateDate))
        .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
      // if(dismbDF.sparkSession.conf.getOption("spark.checkpoint.dir").isDefined) dismbDF.checkpoint(true) else dismbDF

      val pattern = "([/]|^)([^/]++)[/]?$".r
      val dataSourceName = pattern.findFirstMatchIn(config.output.disambiguatedModelLocation).get.group(2)
      val interSourceDF = modelWithDerivedProperties(dismbDF, config)
        .withColumn("relationship_fragments", size(col(PID_ARRAY)))
        .drop(PID_ARRAY)
      val resolver = dismbDF.withColumn("temp", explode_outer(col(PID_ARRAY)))
        .withColumnRenamed("relationship_id", "disambiguated_relationship_id")
        .withColumnRenamed(ORIGIN, "disambiguated_origin")
        .withColumn("relationship_id", col(s"temp.relationship_id"))
        .withColumn(ORIGIN, col(s"temp.$ORIGIN"))
        .select("disambiguated_relationship_id", "disambiguated_origin", "relationship_id", ORIGIN, "relationship_name", UPDATED_AT,UPDATED_AT_TS)
        .withColumn("data_source_name", lit(dataSourceName))

      val nonResolved = relDF.withColumn("data_source_name", lit(dataSourceName))
      val derivedColumnDefinitions = Array(createColumnLineage(sourceTable = config.output.disambiguatedModelLocation, targetTable = config.output.disambiguatedModelLocation, Right(config.derivedProperties)))
      DataHubUtils.emitColumnLineageToDataHub(lineageDefinitions = (individualColumnDefinitions++derivedColumnDefinitions).toSeq)
      OutputDF(interSourceDF,nonResolved,resolver)
  }
  }

  def modelWithDerivedProperties(disambigDf: DataFrame, config: Config): DataFrame = {
    config.derivedProperties
      .foldLeft(disambigDf)((df, property) => {
        df.withColumn(property.colName, expr(property.colExpr))
      })
  }
  def confidenceMatrix(invModelSpecList: Seq[RelationshipModelDF],
                       config: Config): DataFrame = {
    val strategy = config.disambiguation.strategy.getOrElse(Strategy.empty)
    val fieldLevelConfidenceFields =
      strategy.fieldLevelConfidenceMatrix.getOrElse(Array.empty)
    val valueConfidences = strategy.valueConfidence.getOrElse(Array.empty)
    val rollUpFields = (strategy.rollingUpFields ++ DEFAULT_ROLLUP_COLUMNS).distinct
    val aggregateFields = strategy.aggregation.map(_.field)

    val confDFList = invModelSpecList
      .map(inv => {
        val confMap = config.disambiguation.confidenceMap
        val precedence = confMap.getOrElse(inv.name, 0)
        val invModel =
          inv.df.withColumn(PRECEDENCE, lit(precedence)).withColumn(s"exclude__$PRECEDENCE", expr(s"$PRECEDENCE-1-${confMap.size}"))
        val fieldLevelConfidenceDf = fieldLevelConfidenceFields.foldLeft(invModel)(
          (df, fieldConf) => {

            val fieldConfidenceMap = if (fieldConf.restrictToConfidenceMatrix) {
              fieldConf.confidenceMap
            }
            else {
              val fieldConfidence = fieldConf.confidenceMatrix
              val fieldsDiff = config.disambiguation.confidenceMatrix.getOrElse(Array.empty[String]).diff(fieldConfidence)
              val finalConfidenceMatrix = fieldConfidence ++ fieldsDiff
              val finalConfidenceMap = finalConfidenceMatrix
                .zip(Array.range(1, finalConfidenceMatrix.length + 1).reverse)
                .toMap
              finalConfidenceMap
            }
            EILOGGER.info(s"Field precedence for ${fieldConf.field} is ${fieldConfidenceMap}")
            val structCol = if (inv.df.schema(fieldConf.field).dataType.isInstanceOf[StringType]) {
              when(!col(fieldConf.field).isin(config.disambiguation.excludeValues: _*), struct("temp_precedence", LAST_FOUND, "relationship_id", fieldConf.field))
                .otherwise(expr(s"named_struct('temp_precedence',exclude__temp_precedence,'$LAST_FOUND',$LAST_FOUND,'relationship_id',relationship_id,'${fieldConf.field}',${fieldConf.field})"))
            } else
              struct("temp_precedence", LAST_FOUND, "relationship_id", fieldConf.field)

            val persistNullCondition = if (fieldConf.persistNonNullValue.getOrElse(true)) {
              when(col(fieldConf.field).isNotNull, structCol).otherwise(expr(s"named_struct('temp_precedence',exclude__temp_precedence - ${config.disambiguation.confidenceMap.size},'$LAST_FOUND',$LAST_FOUND,'relationship_id',relationship_id,'${fieldConf.field}',${fieldConf.field})"))
            }
            else {
              struct("temp_precedence", LAST_FOUND, "relationship_id", fieldConf.field)
            }
            val restrictToConfidenceMatrixCondition = if (fieldConf.restrictToConfidenceMatrix) {
              when(!col("temp_precedence").equalTo(0), persistNullCondition).otherwise(lit(null).cast(s"struct<temp_precedence:integer,$LAST_FOUND:long,relationship_id:string,${fieldConf.field}:string>"))
            } else {
              persistNullCondition
            }

            df.withColumn("temp_precedence", lit(fieldConfidenceMap.getOrElse(inv.name, 0)))
              .withColumn("exclude__temp_precedence", expr(s"temp_precedence-1-${fieldConfidenceMap.size}"))
              .withColumn(fieldConf.field, restrictToConfidenceMatrixCondition)
          }
        )
        fieldLevelConfidenceDf
      })

    val unionDF = SparkUtil.unionByName(confDFList: _*)

    val invPropertiesCol = unionDF.columns
      .diff(RELATIONSHIP_CORE_COLS)
      .diff(rollUpFields)
      .diff(valueConfidences.map(_.field))
      .diff(fieldLevelConfidenceFields.map(_.field))
      .diff(MAX_FIELDS)
      .diff(MIN_FIELDS)
      .diff(aggregateFields)
      .map(field => {
        val excludeStructCol = when(col(field).isin(config.disambiguation.excludeValues: _*), expr(s"named_struct('$PRECEDENCE',exclude__$PRECEDENCE,'$LAST_FOUND',$LAST_FOUND,'relationship_id',relationship_id,'${field}',${field})"))
          .when(col(field).isNull, expr(s"named_struct('$PRECEDENCE',exclude__$PRECEDENCE - ${config.disambiguation.confidenceMap.size},'$LAST_FOUND',$LAST_FOUND,'relationship_id',relationship_id,'${field}',${field})"))
          .otherwise(struct(PRECEDENCE, LAST_FOUND, "relationship_id", field))

        val structCol =
          if (unionDF.schema(field).dataType.isInstanceOf[StringType]) {
            excludeStructCol
          } else
            when(col(field).isNotNull, struct(PRECEDENCE, LAST_FOUND, "relationship_id", field))
        structCol.as(field)
      })

    val valuePrecedenceColumns = valueConfidences
      .map(fieldDetail => {
        val precedenceExpr = fieldDetail.confidenceMap
          .map(confidenceValue => s"WHEN ${fieldDetail.field} = '${confidenceValue._1}' THEN ${confidenceValue._2}")
          .mkString("CASE ", " ", s" ELSE ${Int.MinValue} END")

        val precedenceValueStruct = expr(s"named_struct('$PRECEDENCE',$precedenceExpr,'${fieldDetail.field}',${fieldDetail.field})")
        precedenceValueStruct.as(fieldDetail.field)
      })

    val rollUpFieldsColumns = rollUpFields.map(field => struct(PRECEDENCE, field).as(field))

    val finalColumns = RELATIONSHIP_CORE_COLS.diff(Array(DISAMBIGUATION_GROUP_ID)).map(col(_)) ++ fieldLevelConfidenceFields.map(f => col(f.field).as(f.field)) ++
      valuePrecedenceColumns ++ invPropertiesCol ++ aggregateFields.map(col(_)) ++ rollUpFieldsColumns ++ MIN_FIELDS.map(col(_)) ++ MAX_FIELDS.map(col(_))
    unionDF.withColumn(PID_ARRAY, struct("relationship_id", ORIGIN))
      .select(finalColumns: _*)
  }


  def joinGroupedDF(groupedDF: DataFrame, confidenceDF: DataFrame): DataFrame = {
    val confDFSelectFields = confidenceDF.columns.diff(groupedDF.columns)
    EILOGGER.jsonMiniPrint("confDFSelectFields", confDFSelectFields)
    val finalSelect = confDFSelectFields.map(field => confidenceDF(field).as(s"conf_$field")) ++ groupedDF.columns.map(field => groupedDF(field).as(s"grouped_$field"))
    val resultDF = confidenceDF
      .as("conf")
      .join(groupedDF.as("grouped"), "relationship_id")
      .select(finalSelect: _*)
    resultDF.columns.foldLeft(resultDF) { (df, colName) =>
      df.withColumnRenamed(colName, colName.replaceAll("^(conf|grouped)_", ""))
    }
  }

  def confidenceEntityExtraction(invDF: DataFrame, config: Config): DataFrame = {
    val strategy = config.disambiguation.strategy.getOrElse(Strategy.empty)
    val rollingUpFields: Array[String] = (strategy.rollingUpFields ++ DEFAULT_ROLLUP_COLUMNS).distinct
    val aggregationFields = strategy.aggregation
    val maxFields = invDF.schema.fields.filter(k => !k.dataType.isInstanceOf[StructType])
      .map(_.name)
      .diff(RELATIONSHIP_CORE_COLS)
      .diff(MIN_FIELDS)
      .diff(MAX_FIELDS)
      .diff(aggregationFields.map(_.field)) ++ MAX_FIELDS
    val precedenceFields = invDF.columns.diff(RELATIONSHIP_CORE_COLS)
      .diff(rollingUpFields)
      .diff(aggregationFields.map(_.field))
      .diff(maxFields)
      .diff(MIN_FIELDS)

    EILOGGER.jsonMiniPrint("Rolling up fields", rollingUpFields)
    EILOGGER.info(s"Strategy- ${strategy}, ${config.disambiguation.strategy.isDefined}")

    val pidAggExpr = max(struct(PRECEDENCE, LAST_FOUND, "relationship_id")).as("relationship_id")
    val pidArrayAggExpr = collect_set(col(PID_ARRAY)).as(PID_ARRAY)
    val maxAgg = maxFields.map(n => max(n).as(n))
    val minAgg = MIN_FIELDS.map(n => min(n).as(n))
    val aggregationFieldAggExpr = aggregationFields.map(f => expr(s"${f.function}(${f.field})").as(f.field))

    val rollupFieldAggExprs = rollingUpFields.map(field => collect_set(field).as(field))
    val precedenceAggExprs = precedenceFields.map(f => max(f).as(f))

    val finalAggExprs = Array(pidAggExpr) ++
      rollupFieldAggExprs ++ precedenceAggExprs ++ aggregationFieldAggExpr ++ maxAgg ++ minAgg
    val aggDF = invDF.groupBy(DISAMBIGUATION_GROUP_ID).agg(pidArrayAggExpr, finalAggExprs: _*)

    val pidFinalExpr = col("relationship_id").getField("relationship_id").as("relationship_id")
    val maxFinalAGG = maxFields.map(n => col(n))
    val minFinalAGG = MIN_FIELDS.map(n => col(n))
    val pidArrayFinalExpr = col(PID_ARRAY)
    val aggregationFieldFinalExpr = aggregationFields.map(f => col(f.field))

    val rollUpFieldsFinalExpr = rollingUpFields.map(f => {
      val dType = invDF.schema(f).dataType.asInstanceOf[StructType](f).dataType
      if (dType.isInstanceOf[ArrayType]) {
        when(array_distinct(col(f).getField(f)).notEqual(array(lit(null))), array_distinct(flatten(filter(sort_array(col(f), false).getField(f), ar => ar.isNotNull)))).otherwise(lit(null).cast(dType)).as(f)
      } else {
        when(array_distinct(col(f).getField(f)).notEqual(array(lit(null))), array_distinct(filter(sort_array(col(f), false).getField(f), ar => ar.isNotNull))).otherwise(lit(null).cast(s"array<${dType.typeName}>")).as(f)
      }
    })

    val precedenceFieldsFinalExpr = precedenceFields.map(f => col(f).getField(f).as(f))

    val postColumns = Array(pidFinalExpr, pidArrayFinalExpr) ++
      rollUpFieldsFinalExpr ++ precedenceFieldsFinalExpr ++ aggregationFieldFinalExpr ++ maxFinalAGG ++ minFinalAGG

    val postDF = aggDF.select(postColumns: _*)
    findLifeTimeAndRecency(postDF)
    val lifetimeRecencyDF = findLifeTimeAndRecency(postDF)
    if(lifetimeRecencyDF.sparkSession.conf.getOption("spark.checkpoint.dir").isDefined) lifetimeRecencyDF.checkpoint(true) else lifetimeRecencyDF
  }

  def findLifeTimeAndRecency(df: DataFrame): DataFrame = {
    df.withColumn("start_epoch", col("relationship_first_seen_date"))
      .withColumn("end_epoch", col("relationship_last_seen_date"))
      .withColumn("lifetime", datediff(from_unixtime(col("relationship_last_seen_date") / 1000), from_unixtime(col("relationship_first_seen_date") / 1000)))
      .withColumn("recency", datediff(from_unixtime(col("updated_at") / 1000), from_unixtime(col("relationship_last_seen_date") / 1000)))
      .withColumn("lifetime_relationship", col("lifetime"))
      .withColumn("recency_relationship", col("recency"))
  }

  def enrichRelationGraphDetails(resolverDF: DataFrame,relationshipName: String,nonResolvedDF: DataFrame): (DataFrame, DataFrame) = {
    val nullRemovedNonResolvedDf = removeNullFields(
      nonResolvedDF.as("i").join(resolverDF.as("resolver"), RELATION_ID)
        .withColumn("disambiguated_relationship_id", when(col("resolver.disambiguated_relationship_id").isNotNull, col("resolver.disambiguated_relationship_id")).otherwise(col("i.relationship_id")))
        .select("i.*", "disambiguated_relationship_id"))
      .withColumn("graph_id", sha2(functions.concat(col(RELATION_ID), lit("Fragment"), col("updated_at_ts")), 256))


    val nullRemovedResolverDf = removeNullFields(resolverDF)
      .withColumn("relationship_name", expr(s"'${relationshipName} Has Fragment'"))
      .withColumn("inverse_relationship_name", expr(s"'Resolved To ${relationshipName}'"))
      .withColumn("graph_id", sha2(functions.concat(col(RELATION_ID), col("disambiguated_relationship_id"), col("updated_at_ts")), 256))
      .withColumn("source_graph_id", sha2(functions.concat(col("disambiguated_relationship_id"), col("updated_at_ts")), 256))
      .withColumn("target_graph_id", sha2(functions.concat(col(RELATION_ID), lit("Fragment"), col("updated_at_ts")), 256))

    (nullRemovedResolverDf, nullRemovedNonResolvedDf)
  }

}
