package ai.prevalent.entityinventory.enrichment

import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.ORIGIN_LOOKUP_ENRICH
import ai.prevalent.entityinventory.common.configs.{EIConfig, Property}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.entityinventory.utils.{EILOGGER, EIUtil}
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{array, array_union, col, expr, lit, to_timestamp, when}
import ai.prevalent.entityinventory.delta.{Change, EnrichmentDelta}
import ai.prevalent.entityinventory.utils.EIUtil.{LOGGER, checkMissingColumns, spark}

case class LookupInfo(tableName: String,name: Option[String],filter: String = "true", preTransform: Array[Property] = Array.empty, enrichmentColumns: Array[String])

case class Enrichment(lookupInfo: LookupInfo, sourcePreTransform: Array[Property] = Array.empty, joinType: String = "LEFT",
                      joinCondition: String) extends EIConfig[Enrichment, EnrichmentDelta]{
  def enrich(source: DataFrame,  reader: SDSTableReader): DataFrame = {
    import source.sparkSession.implicits._
    checkMissingColumns(source, sourcePreTransform.map(_.colExpr), "sourcePreTransform","fail")
    val sourceTransformed = transform(source, sourcePreTransform)
    val tableExists = reader.isTableExists(lookupInfo.tableName)
    if (!tableExists) {
      LOGGER.warn(s"Table '${lookupInfo.tableName}' not found. Returning empty DataFrame")
    }
    var lookupDF = reader.readOrElse(lookupInfo.tableName, source.sparkSession.emptyDataFrame)
    lookupDF = EIUtil.preProcesmptyStringtoNull(lookupDF)
    if (lookupDF.isEmpty && joinType.toUpperCase == "LEFT") {
      sourceTransformed
    } else if (lookupDF.isEmpty && joinType.toUpperCase == "INNER") {
      sourceTransformed.limit(1).filter("false")
    } else {
      val filteredLookupDF = lookupDF.filter(lookupInfo.filter)
      checkMissingColumns(filteredLookupDF, lookupInfo.preTransform.map(_.colExpr), "lookupInfo.preTransform","fail")
      val lookupTransformed = transform(filteredLookupDF, lookupInfo.preTransform)
      checkMissingColumns(lookupTransformed, lookupInfo.enrichmentColumns, "enrichmentColumns","fail")

      val lookupDf=lookupInfo.name match {
        case Some(lookupName) =>
          lookupTransformed.withColumn(ORIGIN_LOOKUP_ENRICH, array(lit(lookupName)))
        case None =>
          lookupTransformed.withColumn(ORIGIN_LOOKUP_ENRICH, array(lit(null)))
      }
      val originLookupEnrichCol = if (sourceTransformed.columns.contains(ORIGIN_LOOKUP_ENRICH)) {
        when(col(s"s.$ORIGIN_LOOKUP_ENRICH").isNotNull && col(s"e.$ORIGIN_LOOKUP_ENRICH").isNotNull,
          array_union(col(s"s.$ORIGIN_LOOKUP_ENRICH"), col(s"e.$ORIGIN_LOOKUP_ENRICH")))
          .otherwise(col(s"e.$ORIGIN_LOOKUP_ENRICH"))
          .alias(ORIGIN_LOOKUP_ENRICH)
      } else {
        col(s"e.$ORIGIN_LOOKUP_ENRICH").alias(ORIGIN_LOOKUP_ENRICH)
      }
      val sourceColumns = sourceTransformed.columns.filter(_ != ORIGIN_LOOKUP_ENRICH).map(colName => col(s"s.$colName"))
      val finalCols = lookupInfo.enrichmentColumns.map(f => col(s"e.$f")) ++ sourceColumns  ++ Seq(originLookupEnrichCol)
      val isUpdatedAtTsPresent = lookupDf.columns.contains(UPDATED_AT_TS)
      val joinExpr = s"($joinCondition)" + (if (isUpdatedAtTsPresent) s" AND e.$UPDATED_AT_TS = s.$UPDATED_AT_TS" else "")
      sourceTransformed.as("s")
        .join(lookupDf.as("e"), expr(joinExpr), joinType)
        .select(finalCols: _*)
    }
  }

  def transform(df: DataFrame, transform: Array[Property]): DataFrame = {
      transform.foldLeft(df)((dfp, prop) => {
        dfp.withColumn(prop.colName, expr(prop.colExpr))
      })
  }

  override def getConfigDelta(otherConfig: Enrichment): EnrichmentDelta = ???

  override def getConfigDelta(deltas: Seq[Change]): EnrichmentDelta = ???
}

object Enrichment {
  def applyEnrichments(source: DataFrame, enrichments: Seq[Enrichment], reader: SDSTableReader) : DataFrame ={
    source.printSchema()
    enrichments.foldLeft(source)((df,enr) => {
      enr.enrich(df,reader)
    })
  }
}