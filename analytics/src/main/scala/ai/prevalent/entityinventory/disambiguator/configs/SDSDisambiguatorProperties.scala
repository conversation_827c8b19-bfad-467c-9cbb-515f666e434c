package ai.prevalent.entityinventory.disambiguator.configs

object SDSDisambiguatorProperties {
  final val P_ID_EXPR =
    """
      |SHA2(
      | NULLIF(
      |   CONCAT_WS(
      |    '||',
      |   IFNULL(NULLIF(TRIM(CAST(primary_key AS STRING)), ''), '^^'),
      |   IFNULL(NULLIF(TRIM(CAST(origin AS STRING)), ''), '^^'),
      |   IFNULL(NULLIF(TRIM(CAST(class AS STRING)), ''), '^^'),
      |   ),
      | '^^||^^'
      | ),
      |256
      |)
      |""".stripMargin

  object schema {
    final val P_ID = "p_id"
    final val RELATION_ID = "relationship_id"
    final val PRECEDENCE = "inter_source_temp_precedence"
    final val CANDIDATE_KEY = "inter_source_temp_candidate_key"
    final val PID_ARRAY = "inter_source_temp_pids"
    final val ORIGIN = "origin"
    final val DELIM="__"
    final val ORIGIN_PID = "origin_pid"
  }

  object sds {
    object spark {
      object conf {
        final val MODEL_BASE_PATH = "spark.sds.modelbasepath"
      }
    }
  }
}
