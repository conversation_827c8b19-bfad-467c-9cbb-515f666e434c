package ai.prevalent.entityinventory.parquetconverter

import ai.prevalent.entityinventory.parquetconverter.configs.ParquetConverterArgs
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.sdspecore.sparkbase.SDSSparkBase
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import ai.prevalent.sdspecore.udf.SDSUDFBase
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions.{col, concat_ws, expr, regexp_replace, to_json}
import org.apache.spark.sql.types.{ArrayType, DecimalType, NullType, StringType, StructType}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, DecimalType, StringType, StructType}

object ParquetConverter extends SDSSparkBase[ParquetConverterArgs] {

  override def execute(params: ParquetConverterArgs): Unit = {
    val reader = SDSTableReaderFactory.getDefault(spark)

    val inputDF = reader.read(params.icebergTableName).filter(params.filterExpression)
    val complexHandledDf = complexFieldHandle(inputDF)
    val outputDf=emptyStringToNullHandle(complexHandledDf)
    val outDf = removeNullFields(outputDf)
    outDf.write.mode(params.writeMode).parquet(params.outputPath)
  }

  /** Override this method in concrete class to specify job arguments class
   *
   * @example
   * override def getInitParams:JobArgs = new JobArgs
   */
  override def getInitParams: ParquetConverterArgs = new ParquetConverterArgs()

  def complexFieldHandle(modeDF:DataFrame):DataFrame ={
    modeDF.schema.fields.foldLeft(modeDF)((df, property) => {
      property.dataType match {
        case or: ArrayType if property.name == "origin" => df.withColumn(property.name, concat_ws("<-->", col(property.name)))
        case relOr: StringType if property.name == "origin_relationship" => df.withColumn(property.name, regexp_replace(col("origin_relationship"),",","<-->"))
        case ar:ArrayType if !(ar.elementType.isInstanceOf[ArrayType] || ar.elementType.isInstanceOf[StructType]) =>
          df.withColumn(property.name,concat_ws("<-->",col(property.name)))
        case _:ArrayType => df.withColumn(property.name,to_json(col(property.name)))
        case _:StructType => df.withColumn(property.name,to_json(col(property.name)))
        case dt: DecimalType => df.withColumn(property.name, col(property.name).cast("double"))
        case _ => df
      }
    })
  }

  def emptyStringToNullHandle(inputDf :DataFrame):DataFrame={
    inputDf.schema.fields.foldLeft(inputDf)((df, property) => {
      property.dataType match {
        case _: StringType =>
          df.withColumn(property.name, expr(s"CASE WHEN btrim(cast(${property.name} as STRING), '[\\t\\r\\n ]') = '' THEN null ELSE ${property.name} END"))
        case _ => df
      }
    })
  }

}