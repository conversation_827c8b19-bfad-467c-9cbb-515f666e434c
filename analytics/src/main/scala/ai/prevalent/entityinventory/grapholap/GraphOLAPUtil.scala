package ai.prevalent.entityinventory.grapholap

import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableWriter}
import org.apache.spark.sql.catalyst.expressions.Expression

object GraphOLAPUtil extends  LoggerBase{
   def createReplicaTables(tables: Iterable[String], replicaSchema:String, writer:SDSTableWriter, filters: Seq[Expression]): Unit = {
     tables.foreach( publisherTable => {
       val tableName = publisherTable.split("[.]")(publisherTable.split("[.]").length-1)
       LOGGER.info(s"Creating replica for $replicaSchema.$tableName from base $publisherTable with filter $filters")
       writer.createReplica(sourceTable = publisherTable, targetTable = s"$replicaSchema.$tableName", filters)
     })
   }
}
