package ai.prevalent.entityinventory.grapholap

import ai.prevalent.entityinventory.disambiguator.Disambiguator
import ai.prevalent.entityinventory.grapholap.configs.GraphOlapJobArgs
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.entityinventory.utils.ConfigUtil
import ai.prevalent.sdspecore.sparkbase.SDSSparkBase
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReaderFactory, SDSTableWriterFactory}
import org.apache.spark.sql.functions._

import java.time.{Instant, ZoneId}


object GraphFragmentOlap extends SDSSparkBase [GraphOlapJobArgs] {

  type DisambConfig = ai.prevalent.entityinventory.disambiguator.configs.specs.Config

  override def getInitParams: GraphOlapJobArgs = new GraphOlapJobArgs()
  
  override def execute(jobArgs: GraphOlapJobArgs): Unit = {
    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark)
    val zone = ZoneId.of(spark.conf.get("spark.sql.session.timeZone", "UTC"))
    val startDateTime = Instant.ofEpochMilli(jobArgs.endEpoch).atZone(zone)
    val snapShotFilter = Array(startDateTime).map(time => expr(s"$UPDATED_AT_TS='$time'").expr)

    LOGGER.info(s"snapshot filter ${snapShotFilter.toList}")

    val disambOutInfo = ConfigUtil.readAllConfigs[DisambConfig](configListPath = spark.conf.get("spark.sds.inter.writebackBasePath"),
        configArtifactURI = spark.conf.get("spark.sds.restapi.configArtifactoryUri",""),
        configEndpoint = spark.conf.get("spark.sds.restapi.eiSparkConfigsBasePath",""),
        formats = Disambiguator.configFormats
      ).values.map(_.output)
      .filter(_.isFragmentOLAPTable)

    val knowledgeGraphOlapSchema = spark.conf.get("spark.sds.kg.olap.fragment.schema")
    GraphOLAPUtil.createReplicaTables(disambOutInfo.map(_.fragmentLocation.getOrElse("")), knowledgeGraphOlapSchema, writer, snapShotFilter)
    GraphOLAPUtil.createReplicaTables(disambOutInfo.map(_.resolverLocation), knowledgeGraphOlapSchema, writer, snapShotFilter)
  }
}
