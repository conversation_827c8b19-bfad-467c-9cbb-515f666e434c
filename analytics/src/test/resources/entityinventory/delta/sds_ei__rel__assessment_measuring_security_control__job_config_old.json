{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_regulatory_compliance_assessments__assessments_measuring_security_controls", "name": "model1"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "excludeValues": [], "strategy": {"rollingUpFields": [], "fieldLevelConfidenceMatrixStrategy": [], "valueConfidenceStrategy": [], "aggregationStrategy": []}, "confidenceMatrix": []}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__assessment_measuring_security_control", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_rel_disambiguation_resolver", "filter": "true"}}