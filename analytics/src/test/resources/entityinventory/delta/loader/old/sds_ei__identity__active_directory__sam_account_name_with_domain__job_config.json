{"primaryKey": "sam_account_name_with_domain_temp", "origin": "'Microsoft Active Directory'", "temporaryProperties": [{"colName": "domain_temp", "colExpr": "regexp_extract(distinguishedName,'DC=([^,]++)')"}, {"colName": "sam_account_name_temp", "colExpr": "RTRIM('$',sAMAccountName)"}, {"colName": "sam_account_name_with_domain_temp", "colExpr": "CASE WHEN sam_account_name_temp IS NULL OR domain_temp IS NULL THEN NULL ELSE CONCAT_WS('\\\\',domain_temp,sam_account_name_temp) END"}, {"colName": "temp_manager", "colExpr": "CASE WHEN manager!='[]' THEN manager ELSE NULL END"}, {"colName": "temp_account_expires", "colExpr": "CASE WHEN accountExpires!='[]' THEN accountExpires ELSE NULL END"}], "commonProperties": [{"colName": "type", "colExpr": "'AD Domain'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(ad_created_date,first_found_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(ad_last_password_change_date, ad_last_sync_date, login_last_date,ad_created_date,ad_account_disabled_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_provider", "colExpr": "'AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_name", "colExpr": "LOWER(ad_sam_account_name_with_domain)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_type", "colExpr": "CASE WHEN ad_sam_account_type IS NULL THEN NULL WHEN ad_sam_account_type='NORMAL_USER_ACCOUNT' and REGEXP_LIKE(lower(ad_distinguished_name),'(?i)service[ ]?account|sccm|scom') THEN 'Service Account' WHEN lower(ad_sam_account_type) LIKE '%user%' THEN 'User Account' ELSE 'Other' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "manager", "colExpr": "CASE WHEN regexp_replace(regexp_extract(temp_manager, 'CN=(.*)'), ',\\\\w.*', '') != '' THEN regexp_replace(regexp_extract(temp_manager, 'CN=(.*)'), ',\\\\w.*', '') ELSE NULL END"}, {"colName": "login_last_date", "colExpr": "CASE WHEN (lastLogon != '[]' AND lastLogon  != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastLogon))) ELSE NULL END"}, {"colName": "account_expires_date", "colExpr": "CASE WHEN DATE(temp_account_expires) IS NOT NULL THEN CAST(DATE_FORMAT(temp_account_expires, 'yyyy-MM-dd') AS STRING)  ELSE REGEXP_REPLACE(temp_account_expires, '[(|)]', '') END"}, {"colName": "account_status", "colExpr": "ad_operational_status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "user_principal_name", "colExpr": "LOWER(regexp_replace(userPrincipalName,'^[_]',''))"}], "sourceSpecificProperties": [{"colName": "ad_operational_status", "colExpr": "CASE WHEN ad_uac is NULL THEN NULL WHEN LOWER(ad_uac) LIKE '%disable%' THEN 'Disabled' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ad_distinguished_name", "colExpr": "<PERSON><PERSON><PERSON>"}, {"colName": "ad_created_date", "colExpr": "CASE WHEN (whenCreated != '[]' AND whenCreated != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(whenCreated))) ELSE NULL END"}, {"colName": "ad_last_sync_date", "colExpr": "CASE WHEN (lastLogonTimestamp != '[]' AND lastLogonTimestamp  != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastLogonTimestamp))) ELSE NULL END"}, {"colName": "ad_last_password_change_date", "colExpr": "CASE WHEN (pwdLastSet != '[]' AND pwdLastSet != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(pwdLastSet))) ELSE NULL END"}, {"colName": "ad_sam_account_type", "colExpr": "sAMAccountType"}, {"colName": "ad_member_of", "colExpr": "CAST(memberOf AS STRING)"}, {"colName": "ad_domain", "colExpr": "domain_temp"}, {"colName": "ad_sam_account_name_with_domain", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ad_sam_account_name", "colExpr": "LOWER(sam_account_name_temp)"}, {"colName": "ad_uac", "colExpr": "CAST(userAccountControl AS STRING)"}, {"colName": "ad_account_disabled_date", "colExpr": "CASE WHEN LOWER(ad_uac) LIKE '%disable%' THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "min", "persistNonNullValue": false}}], "dataSource": {"name": "MS Active Directory", "feedName": "MS Active Directory", "srdm": "srdm.microsoft__active_directory"}, "outputTableInfo": {"outputTableName": "sds_ei__identity__active_directory__sam_account_name_with_domain", "outputWrittenMode": "tableType"}, "entityConfig": {"name": "Identity", "lastUpdateFields": ["type"]}}