{"transformSpec": {"type": "AttributeWriteBack", "postSchemas": "new_schema", "tableInfo": {"tableName": "new_table", "tableType": "entity", "inputTableIdentifierRegex": null, "enrichTableIdentifierRegex": null, "commonColumns": ["p_id", "updated_at_partition", "updated_at_ts"], "uniqCol": "p_id"}}, "derivedProperties": [], "outputTableInfo": {"partitionColumns": ["updated_at_partition"], "outputTableName": "new_output_table", "outputFilter": "true"}, "isOLAPTable": true}