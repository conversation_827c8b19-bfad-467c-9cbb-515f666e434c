{"primaryKey": "sam_account_name_with_domain_temp", "origin": "'MS Active Directory'", "temporaryProperties": [{"colName": "domain_temp", "colExpr": "regexp_extract(distinguishedName,'DC=([^,]++)')"}, {"colName": "sam_account_name_temp", "colExpr": "RTRIM('$',sAMAccountName)"}, {"colName": "sam_account_name_with_domain_temp", "colExpr": "CASE WHEN sam_account_name_temp IS NULL OR domain_temp IS NULL THEN NULL ELSE CONCAT_WS('\\\\',domain_temp,sam_account_name_temp) END"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN (DistinguishedName like ('%OU=PAI users%')) AND (not(lower(SamAccountName) like '%test%' OR lower(SamAccountName) like '%demo%')) THEN 'Human' ELSE 'Non-Human' END"}, {"colName": "first_seen_date", "colExpr": "LEAST(ad_created_date,first_found_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(last_password_change_date,login_last_date,ad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "operational_status", "colExpr": "CASE WHEN lower(TRIM(Enabled)) = 'false' THEN 'Disabled' WHEN lower(TRIM(Enabled)) = 'true' THEN 'Active' ELSE NULL END"}, {"colName": "identity_provider", "colExpr": "'Active Directory'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_format", "colExpr": "'SAM Account Name'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "user_principal_name", "colExpr": "lower(UserPrincipalName)"}, {"colName": "identity_display_name", "colExpr": "DisplayName"}, {"colName": "login_last_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(LastLogonDate, 'M/d/yyyy h:mm:ss a')))"}, {"colName": "last_password_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(PasswordLastSet, 'M/d/yyyy h:mm:ss a')))"}, {"colName": "account_never_expire", "colExpr": "CASE WHEN accountExpires = 0 THEN True ELSE False END"}, {"colName": "password_not_required", "colExpr": "CAST(PasswordNotRequired AS BOOLEAN)"}, {"colName": "password_never_expire", "colExpr": "CAST(PasswordNeverExpires AS BOOLEAN)"}, {"colName": "identity_primary_key", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_configured_time", "colExpr": "5", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_count_flag", "colExpr": "case when badPwdCount=bad_password_configured_time then True else False end", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "ad_distinguished_name", "colExpr": "<PERSON><PERSON><PERSON>"}, {"colName": "ad_domain", "colExpr": "domain_temp"}, {"colName": "ad_sam_account_name_with_domain", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ad_sam_account_name", "colExpr": "LOWER(sam_account_name_temp)"}, {"colName": "ad_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(whenCreated, 'M/d/yyyy h:mm:ss a')))"}, {"colName": "ad_sam_account_type", "colExpr": "sAMAccountType"}], "dataSource": {"name": "MS Active Directory", "feedName": "MS Active Directory", "srdm": "srdm.microsoft__active_directory"}, "outputTableInfo": {"outputTableName": "ei_his.sds_ei__identity__active_directory__sam_account_name_with_domain", "outputWrittenMode": "tableType"}, "entityConfig": {"name": "Identity", "lastUpdateFields": ["type"]}}