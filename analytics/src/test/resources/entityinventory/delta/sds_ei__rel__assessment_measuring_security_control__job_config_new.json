{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_regulatory_compliance_assessments__assessments_measuring_security_controls", "name": "model1", "filter": "true"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__assessments_measuring_security_controls", "name": "model2", "filter": "true"}], "disambiguation": {"disambiguationGrouping": {"type": "Union"}, "excludeValues": ["N/A", "unknown"], "strategy": {"rollingUpFields": ["assessment_name"], "fieldLevelConfidenceMatrixStrategy": ["strategy1"], "valueConfidenceStrategy": ["strategy2"], "aggregationStrategy": ["strategy3"]}, "confidenceMatrix": [{"field": "confidence_score", "threshold": 0.8}]}, "derivedProperties": [{"colName": "assessment_summary", "colExpr": "concat(assessment_name, '-', origin)", "fieldsSpec": {"isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__assessment_measuring_security_control", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_rel_disambiguation_resolver", "filter": "status = 'active'"}}