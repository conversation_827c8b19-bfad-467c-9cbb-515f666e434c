{"transformSpec": {"type": "AttributeWriteBack", "postSchemas": "old_schema", "tableInfo": {"tableName": "old_table", "tableType": "entity", "inputTableIdentifierRegex": null, "enrichTableIdentifierRegex": null, "commonColumns": ["p_id", "updated_at_partition", "updated_at_ts"], "uniqCol": "p_id"}}, "derivedProperties": [], "outputTableInfo": {"partitionColumns": ["updated_at_partition"], "outputTableName": "old_output_table", "outputFilter": "true"}, "isOLAPTable": true}