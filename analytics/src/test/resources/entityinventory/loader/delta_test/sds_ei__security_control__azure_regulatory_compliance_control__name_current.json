{"primaryKey": "lower(name)", "origin": "'MS Azure Security Resources'", "filterBy": "lower(properties.state) IN ('passed','failed','skipped') AND lower(type) IN ('microsoft.security/regulatorycompliancestandards/regulatorycompliancecontrols')", "temporaryProperties": [{"colName": "temp_statuses", "colExpr": "collect_set(properties.description) over (partition by lower(name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000)) ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_id", "colExpr": "properties.description"}, {"colName": "temp_standard", "colExpr": "upper(REGEXP_EXTRACT(temp_id, 'regulatoryComplianceStandards/([^/]+)/'))"}, {"colName": "temp_test", "colExpr": "'test'"}], "commonProperties": [{"colName": "type", "colExpr": "'MS Azure Regulatory Compliance Control'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "test_id", "colExpr": "temp_test", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "id", "colExpr": "upper(name)"}, {"colName": "title", "colExpr": "properties.description"}, {"colName": "status", "colExpr": "case when array_contains(temp_statuses, 'Passed') or array_contains(temp_statuses, 'Failed') then 'Enabled' else 'Disabled' end"}, {"colName": "standard_name", "colExpr": "collect_set(temp_standard) OVER (partition by lower(name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "associated_standards", "colExpr": "transform(standard_name,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "srdm_ite.microsoft_azure__security_resources"}, "outputTableInfo": {"outputTableName": "kg1_ite.sds_ei__security_control__azure_regulatory_compliance_control__name", "outputWrittenMode": "tableType"}, "entity": {"name": "Security Control", "fieldSpec": {"persistNonNullValue": true}, "lastUpdateFields": ["type", "first_seen_date"]}}