{"primaryKey": "lower(name)", "filterBy": "lower(properties.state) IN ('passed','failed','skipped') AND lower(type) IN ('microsoft.security/regulatorycompliancestandards/regulatorycompliancecontrols')", "entity": {"fieldSpec": {"persistNonNullValue": true, "isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}, "name": "Security Control", "lastUpdateFields": ["type", "first_seen_date"], "commonProperties": [], "entitySpecificProperties": [], "sourceSpecificProperties": []}, "origin": "'MS Azure Security Resources'", "operationalMode": "carryForward", "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "srdm_ite.microsoft_azure__security_resources", "dataIntervalTimestampKey": "parsed_interval_timestamp_ts", "dataEventTimestampKey": "event_timestamp_ts", "uniqueRecordIdentifierKey": "uuid"}, "outputTableInfo": {"outputTableName": "kg1_ite.sds_ei__security_control__azure_regulatory_compliance_control__name", "outputWrittenMode": "tableType"}, "commonProperties": [], "entitySpecificProperties": [], "sourceSpecificProperties": [], "temporaryProperties": [{"colName": "temp_statuses", "colExpr": "collect_set(properties.description) over (partition by lower(name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000)) ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "fieldsSpec": {"isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}, {"colName": "temp_id", "colExpr": "properties.description", "fieldsSpec": {"isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}, {"colName": "temp_standard", "colExpr": "upper(REGEXP_EXTRACT(temp_id, 'regulatoryComplianceStandards/([^/]+)/'))", "fieldsSpec": {"isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}, {"colName": "temp_test", "colExpr": "'test'", "fieldsSpec": {"isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}], "deltaProperties": [{"colName": "test_id", "colExpr": "temp_test", "fieldsSpec": {"persistNonNullValue": false, "isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}, {"colName": "status", "colExpr": "case when array_contains(temp_statuses, 'Passed') or array_contains(temp_statuses, 'Failed') then 'Enabled' else 'Disabled' end", "fieldsSpec": {"isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}, {"colName": "standard_name", "colExpr": "collect_set(temp_standard) OVER (partition by lower(name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "fieldsSpec": {"isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}], "enrichments": []}