{"countEnriches": [{"colName": "vulnerability_count", "relationshipTableName": "ei_rel.vulnerabilities", "filter": "severity IN ('HIGH', 'CRITICAL')"}, {"colName": "compliance_findings", "relationshipTableName": "ei_rel.compliance_findings", "filter": "status = 'FAILED'"}], "derivedProperties": [{"colName": "total_risk_score", "colExpr": "vulnerability_count * 15 + compliance_findings * 10"}, {"colName": "risk_level", "colExpr": "CASE WHEN total_risk_score > 100 THEN 'HIGH' ELSE 'LOW' END"}]}