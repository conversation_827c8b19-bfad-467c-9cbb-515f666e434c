{"description": "Person entity dictionary v2", "attributes": {"email": {"type": "string", "description": "Primary and secondary email addresses", "caption": null, "group": null, "enable_hiding": null, "width": null, "step_interval": null, "range_selection": null, "ui_visibility": null, "candidate_key": null, "data_structure": null}, "employee_id": {"type": "string", "description": "Unique employee identifier", "caption": null, "group": null, "enable_hiding": null, "width": null, "step_interval": null, "range_selection": null, "ui_visibility": null, "candidate_key": null, "data_structure": null}, "first_name": {"type": "string", "description": "Given name of the person", "caption": null, "group": null, "enable_hiding": null, "width": null, "step_interval": null, "range_selection": null, "ui_visibility": null, "candidate_key": null, "data_structure": null}, "display_name": {"type": "string", "description": "Display name for UI", "caption": null, "group": null, "enable_hiding": null, "width": null, "step_interval": null, "range_selection": null, "ui_visibility": null, "candidate_key": null, "data_structure": null}}}