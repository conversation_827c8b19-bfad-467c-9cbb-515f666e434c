{"inventoryModelInput": [{"path": "/data2", "name": "source2"}, {"path": "/data1", "name": "source1"}], "disambiguation": {"candidateKeys": ["azure_id"], "confidenceMatrix": ["source1", "source2", "source3"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "os", "confidenceMatrix": ["source2", "source1", "source3"]}], "valueConfidence": [{"field": "status", "confidenceMatrix": ["Active", "InActive"]}], "rollingUpFields": ["origin"]}}, "output": {"disambiguatedModelLocation": "", "resolverLocation": ""}, "entityConfig": {"name": "Host", "defaultEntitySpec": {}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn"]}}