{"inventoryModelInput": [{"path": "/dafa", "name": "source1"}, {"path": "/data2", "name": "source2"}], "disambiguation": {"candidateKeys": ["azure_id"], "confidenceMatrix": ["source1", "source2", "source3"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "os", "confidenceMatrix": ["source2", "source1", "source3"]}], "valueConfidence": [{"field": "status", "confidenceMatrix": ["Active", "InActive"]}], "rollingUpFields": ["origin"]}}, "output": {"disambiguatedModelLocation": "", "resolverLocation": ""}, "entity": {"name": "Host", "defaultEntitySpec": {}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn"]}}