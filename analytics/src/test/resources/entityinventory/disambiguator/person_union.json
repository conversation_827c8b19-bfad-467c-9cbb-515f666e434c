{"inventoryModelInput": [{"path": "ei.sds_ei__person__source1", "name": "source1", "isEnrichSource": false, "removeFields": []}, {"path": "ei.sds_ei__person__source2", "name": "source2", "isEnrichSource": true}], "disambiguation": {"disambiguationType": "Union", "excludeValues": ["Unknown", "N/A"]}, "output": {"disambiguatedModelLocation": "ei.person_union", "filter": "status = 'ACTIVE'"}, "entity": {"name": "Person", "entityClass": "Person", "commonProperties": [], "entitySpecificProperties": [], "sourceSpecificProperties": []}, "derivedProperties": [{"colName": "display_name", "colExpr": "coalesce(full_name, username)"}]}