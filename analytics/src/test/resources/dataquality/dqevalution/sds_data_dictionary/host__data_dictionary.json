{"id": 30038, "name": "host__data_dictionary", "config_item_type": "data_dictionary_config", "config_item_sub_type": null, "config_item_level": "client", "config_deploy_type": "airflow_shared_fs", "version": "2-0-0-20250528-1212058612-KG", "client_revision": 5, "solution_name": "ei", "entity_name": "host", "is_available": true, "is_deleted": false, "is_validated": true, "display_name": "Host", "entity_name_label": "Host", "config_value": {"caption": "Host", "description": "An independent compute instance where we have visibility or management of the operating system.", "attributes": {"activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 30 days.", "enum_t": {"0": {"caption": "Active"}, "1": {"caption": "Inactive"}}, "group": "common", "type": "string", "derived_field": true, "enable_hiding": false, "dashboard_identifier": {"EI": {}, "Host": {}}}, "type": {"caption": "Type", "description": "The specific role of the entity based on its functionality within an organisation.\n\nPossible distinct values includes Server,Workstation,Network,Printer,Hypervisor,Mobile and Other.If Type information is not available, this field is marked as No Data.", "group": "common", "enable_hiding": false, "type": "string", "category": "General Information", "dq_enable": false, "dq_acceptable_values": ["Hypervisor", "Other", "Workstation", "Server", "Network", "Printer", "Mobile"]}, "origin": {"caption": "Origin", "description": "Data source(s) from which the entity has been extracted.\nFor example AWS, Qualys etc.", "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "category": "General Information", "dashboard_identifier": {"EI": {}, "Host": {}}}, "last_scan": {"caption": "Last Scan", "description": "Last Scan date", "group": "common", "enable_hiding": false, "type": "timestamp", "data_structure": "list", "category": "General Information", "dashboard_identifier": {"EI": {}, "Host": {}}}}}}