{"name": "Cloud Compute Resource Belongs To Cloud Account", "origin": "AWS", "inverseRelationshipName": "Cloud Account Has Cloud Compute Resource", "intraSourcePath": "rel_test.sds_ei_intra_source_resolver", "interSourcePath": "rel_test.sds_ei_inter_source_resolver", "inputSourceInfo": [{"sdmPath": "sdm.aws__ec2_describe_instances", "origin": "AWS", "sourceMappingInfo": ["sds_ei__cloud_compute__aws_ec2_instance__instanceid"], "targetMappingInfo": {"tableName": "rel_test.sds_ei__cloud_account", "joinCondition": "s.ownerid=e.primary_key"}}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "rel_test.sds_ei__rel__aws_ec2_instance__cloud_compute_resource_belongs_to_cloud_account", "prevMiniSDM": "rel_test.sds_ei__rel_mini_sdm__aws_ec2_instance__cloud_compute_resource_belongs_to_cloud_account"}, "relationship": {"rel_name": "Cloud Compute Resource Belongs To Cloud Account", "name": "AWS", "feedName": "EC2 Instance"}}