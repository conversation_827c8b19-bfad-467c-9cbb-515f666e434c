package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.{DictionaryAttribute, EntityDictionary}
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.sql.SparkSession
import org.scalatest.BeforeAndAfterAll
import org.scalatest.flatspec.AnyFlatSpec

class EntityDictionaryDeltaSpec extends AnyFlatSpec with BeforeAndAfterAll with DataFrameSuiteBase {
  var prevConfig: EntityDictionary = _
  var newConfig: EntityDictionary = _

  override def beforeAll(): Unit = {
    super.beforeAll()
    val oldPath = "file:" + getClass.getResource("/entityinventory/dictionary/person_dictionary_old.json").getPath
    val newPath = "file:" + getClass.getResource("/entityinventory/dictionary/person_dictionary_new.json").getPath
    prevConfig = ConfigUtils.getConfig(spark, oldPath, manifest[EntityDictionary])
    newConfig = ConfigUtils.getConfig(spark, newPath, manifest[EntityDictionary])
  }

  "EntityDictionaryDelta" should "detect description changes" in {
    val delta = EntityDictionaryDelta(prevConfig, newConfig)
    assert(delta.description.isDefined)
    assert(delta.description.get.category == "Entity")
  }

  it should "detect attribute changes" in {
    val delta = EntityDictionaryDelta(prevConfig, newConfig)
    assert(delta.attrChanges.nonEmpty, "Expected changes but got none")

    // Print all changes for debugging
    delta.attrChanges.foreach(c => println(s"Change: ${c.name} - ${c.message}"))

    // Check for description changes
    val descriptionChange = delta.attrChanges.exists(c => {
      val message = c.message.asInstanceOf[Map[String, String]]
      c.name == "email" && 
      message.exists { case (k, v) => k.trim == "Old Description" && v.trim == "Primary email address" } &&
      message.exists { case (k, v) => k.trim == "New Description" && v.trim == "Primary and secondary email addresses" }
    })
    assert(descriptionChange, "Email description change not found")

    // Check for removed attributes
    val removedAttr = delta.attrChanges.exists(c => 
      c.name == "last_name" && 
      c.message.isInstanceOf[Map[_, _]] &&
      c.message.asInstanceOf[Map[String, String]].contains("Description") &&
      c.message.asInstanceOf[Map[String, String]]("Description") == "Last name of the person"
    )
    assert(removedAttr, "Last name removal not found")

    // Check for new attributes
    val newAttr = delta.attrChanges.exists(c => 
      c.name == "display_name" && 
      c.message.isInstanceOf[Map[_, _]] &&
      c.message.asInstanceOf[Map[String, String]].contains("Description") &&
      c.message.asInstanceOf[Map[String, String]]("Description") == "Display name for UI"
    )
    assert(newAttr, "Display name addition not found")
  }

  it should "handle attribute type consistency" in {
    val oldEmail = prevConfig.attributes.get("email").get
    val newEmail = newConfig.attributes.get("email").get
    
    assert(oldEmail.`type`.get == newEmail.`type`.get)
    assert(oldEmail.description.get != newEmail.description.get)
  }

  it should "handle empty configs gracefully" in {
    val emptyConfig = EntityDictionary(
      description = "Empty config",
      attributes = Map.empty[String, DictionaryAttribute]
    )

    val delta = EntityDictionaryDelta(emptyConfig, newConfig)
    assert(delta.attrChanges.nonEmpty)
  }

  it should "detect no changes when configs are identical" in {
    val attr1 = DictionaryAttribute(
      caption = None,
      description = Some("desc1"),
      group = None,
      enable_hiding = None,
      `type` = Some("type1"),
      width = None,
      step_interval = None,
      range_selection = None,
      ui_visibility = None,
      candidate_key = None,
      data_structure = None
    )

    val prevConfig = EntityDictionary(
      description = "Description",
      attributes = Map("attr1" -> attr1)
    )

    val newConfig = EntityDictionary(
      description = "Description",
      attributes = Map("attr1" -> attr1)
    )

    val delta = EntityDictionaryDelta(prevConfig, newConfig)
    assert(delta.description.isEmpty)
    assert(delta.attrChanges.isEmpty)
  }
}
