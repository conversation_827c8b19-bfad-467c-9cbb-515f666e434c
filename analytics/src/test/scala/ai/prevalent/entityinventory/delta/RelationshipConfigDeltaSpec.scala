package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.relationship.extractor.Extractor
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.scalatest.BeforeAndAfterAll
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.scalatest.flatspec.AnyFlatSpec
import ai.prevalent.entityinventory.relationship.extractor.configs.specs.Config

class RelationshipConfigDeltaSpec extends AnyFlatSpec with BeforeAndAfterAll with DataFrameSuiteBase {
  var oldConfig: Config = _
  var newConfig: Config = _

  override def beforeAll(): Unit = {
    super.beforeAll()
    val oldPath = "file:" + getClass.getResource("/relationship_config_old.json").getPath
    val newPath = "file:" + getClass.getResource("/relationship_config_new.json").getPath
    oldConfig = ConfigUtils.getConfig(spark, oldPath, manifest[Config], Extractor.configFormats)
    newConfig = ConfigUtils.getConfig(spark, newPath, manifest[Config], Extractor.configFormats)
  }

  "RelationshipConfigDelta" should "detect attribute and input source info changes" in {
    val delta = RelationshipConfigDelta(oldConfig, newConfig)
    assert(delta.properties.isEmpty && delta.inputSourceInfoChanges.isEmpty)
  }

  it should "detect no changes for identical configs" in {
    val delta = RelationshipConfigDelta(oldConfig, oldConfig)
    assert(delta.properties.isEmpty && delta.inputSourceInfoChanges.isEmpty)
  }
} 