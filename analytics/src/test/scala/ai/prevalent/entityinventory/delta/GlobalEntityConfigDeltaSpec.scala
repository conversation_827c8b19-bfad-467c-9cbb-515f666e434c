package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.GlobalEntityConfig
import org.scalatest.flatspec.AnyFlatSpec

class GlobalEntityConfigDeltaSpec extends AnyFlatSpec {
  "GlobalEntityConfigDelta" should "detect property and field spec changes" in {
    val oldConfig = GlobalEntityConfig()
    val newConfig = GlobalEntityConfig()
    val delta = GlobalEntityConfigDelta(oldConfig, newConfig)
    assert(delta.properties.isEmpty && delta.fieldSpec.isEmpty)
  }

  it should "detect no changes for identical configs" in {
    val config = GlobalEntityConfig()
    val delta = GlobalEntityConfigDelta(config, config)
    assert(delta.properties.isEmpty && delta.fieldSpec.isEmpty)
  }
} 