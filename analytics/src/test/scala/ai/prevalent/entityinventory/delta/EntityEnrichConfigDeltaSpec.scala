package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.entityinventory.relationship.entityenrich.configs.{Config, CountEnrich}
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.sql.SparkSession
import org.scalatest.BeforeAndAfterAll
import org.scalatest.flatspec.AnyFlatSpec

class EntityEnrichConfigDeltaSpec extends AnyFlatSpec with BeforeAndAfterAll with DataFrameSuiteBase {
  var prevConfig: Config = _
  var newConfig: Config = _

  override def beforeAll(): Unit = {
    super.beforeAll()
    val oldPath = "file:" + getClass.getResource("/entityinventory/enrich/entity_enrich_old.json").getPath
    val newPath = "file:" + getClass.getResource("/entityinventory/enrich/entity_enrich_new.json").getPath
    prevConfig = ConfigUtils.getConfig(spark, oldPath, manifest[Config])
    newConfig = ConfigUtils.getConfig(spark, newPath, manifest[Config])
  }

  "EntityEnrichConfigDelta" should "detect changes in count enriches" in {
    val changes = EntityEnrichConfigDelta.checkEnrichChanges(prevConfig, newConfig)

    assert(changes.nonEmpty)

    // Filter change
    assert(changes.exists(c =>
      c.name == "ei_rel.vulnerabilities" &&
        c.message.contains("Old Enrich Filter") &&
        c.message.contains("New Enrich Filter") &&
        c.category == "Enrich"
    ))

    // Removed
    assert(changes.exists(c =>
      c.name == "ei_rel.security_findings" &&
        c.message.contains("Removed Enrich")
    ))

    // Added
    assert(changes.exists(c =>
      c.name == "ei_rel.compliance_findings" &&
        c.message.contains("New Enrich")
    ))
  }

  it should "detect changes in derived properties" in {
    val changes = EntityEnrichConfigDelta.checkPropertyChanges(prevConfig.derivedProperties, newConfig.derivedProperties)

    assert(changes.nonEmpty)

    assert(changes.exists(c =>
      c.name == "total_risk_score" &&
        c.message.contains("Old Expression") &&
        c.message.contains("New Expression")
    ))

    assert(changes.exists(c =>
      c.name == "risk_level" &&
        c.message.contains("Expression")
    ))
  }

  it should "handle empty configs correctly" in {
    val emptyConfig = new Config {
      override val countEnriches: List[CountEnrich] = List.empty
      override val derivedProperties: Array[Property] = Array.empty
    }
    val changes = EntityEnrichConfigDelta.checkEnrichChanges(emptyConfig, emptyConfig)
    assert(changes.isEmpty)
  }

  it should "create correct EntityEnrichConfigDelta object" in {
    val delta = EntityEnrichConfigDelta(prevConfig, newConfig)
    assert(delta.countEnrichChanges.nonEmpty)
    assert(delta.properties.nonEmpty)
  }
}
