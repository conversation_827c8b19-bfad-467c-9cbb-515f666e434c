package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.{DictionaryAttribute, RelationshipDictionary}
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.sql.SparkSession
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.BeforeAndAfterAll
import org.scalatest.Assertions.fail

class RelationshipDictionaryDeltaSpec extends AnyFlatSpec with BeforeAndAfterAll with DataFrameSuiteBase {
  var prevConfig: RelationshipDictionary = _
  var newConfig: RelationshipDictionary = _

  override def beforeAll(): Unit = {
    super.beforeAll()
    val oldPath = "file:" + getClass.getResource("/entityinventory/dictionary/employment_dictionary_old.json").getPath
    val newPath = "file:" + getClass.getResource("/entityinventory/dictionary/employment_dictionary_new.json").getPath
    prevConfig = ConfigUtils.getConfig(spark, oldPath, manifest[RelationshipDictionary])
    newConfig = ConfigUtils.getConfig(spark, newPath, manifest[RelationshipDictionary])
  }

  "RelationshipDictionaryDelta" should "detect description changes" in {
    val delta = RelationshipDictionaryDelta(prevConfig, newConfig)
    assert(delta.description.isDefined)
    assert(delta.description.get.category == "Relationship")
  }

  it should "detect attribute changes" in {
    val delta = RelationshipDictionaryDelta(prevConfig, newConfig)

    // Debug output
    delta.attrChanges.foreach(c => println(s"Detected change message: ${c.message}"))

    assert(delta.attrChanges.nonEmpty)

    // Helper function to check if any value in the map contains a substring (case-insensitive)
    def messageContains(c: ai.prevalent.entityinventory.delta.Change, substring: String): Boolean = {
      c.message.values.exists(_.toLowerCase.contains(substring.toLowerCase))
    }

    // Check for entity attribute description change (department)
    assert(delta.attrChanges.exists(c =>
      messageContains(c, "department") &&
        messageContains(c, "department and team name")
    ))

    // Check for removed attributes (title)
    assert(delta.attrChanges.exists(c =>
      messageContains(c, "title") &&
        c.category == "Attribute"
    ))

    // Check for new attributes (level)
    assert(delta.attrChanges.exists(c =>
      messageContains(c, "level") &&
        c.category == "Attribute"
    ))

  }


  it should "detect attribute type changes" in {
    val oldAttr = prevConfig.entity_attributes.getOrElse("department", org.scalatest.Assertions.fail("Old department not found"))
    val newAttr = newConfig.entity_attributes.getOrElse("department", org.scalatest.Assertions.fail("New department not found"))
    println("Old entity attributes keys: " + prevConfig.entity_attributes.keys.mkString(", "))
    println("Old relationship attributes keys: " + prevConfig.relationship_attributes.keys.mkString(", "))
    println("New entity attributes keys: " + newConfig.entity_attributes.keys.mkString(", "))
    println("New relationship attributes keys: " + newConfig.relationship_attributes.keys.mkString(", "))
    assert(oldAttr.description != newAttr.description)
  }

  it should "handle missing attributes gracefully" in {
    val emptyConfig = RelationshipDictionary(
      description = "Empty config",
      entity_attributes = Map.empty[String, DictionaryAttribute],
      relationship_attributes = Map.empty[String, DictionaryAttribute]
    )

    val delta = RelationshipDictionaryDelta(emptyConfig, newConfig)
    assert(delta.attrChanges.nonEmpty)
    assert(delta.attrChanges.forall(_.category == "Attribute"))
  }
}
