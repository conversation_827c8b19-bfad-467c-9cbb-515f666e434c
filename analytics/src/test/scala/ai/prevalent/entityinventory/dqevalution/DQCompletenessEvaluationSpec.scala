package ai.prevalent.entityinventory.dqevalution

import ai.prevalent.entityinventory.common.configs.{DQ<PERSON>ob<PERSON>rgs, DictionaryAttribute, TableConfig}
import ai.prevalent.entityinventory.utils.{DQEntityEvaluationUtils, DQRelationshipEvaluationUtils}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.apache.spark.sql.{DataFrame, Row}
import org.apache.spark.sql.functions.{col, expr, lit}
import org.apache.spark.sql.types.{ArrayType, DoubleType, IntegerType, LongType, StringType, StructField, StructType, TimestampType}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

import java.sql.Timestamp
import java.io.File
import scala.reflect.io.Directory

class DQCompletenessEvaluationSpec extends AnyFlatSpec with BeforeAndAfter with DataFrameSuiteBase with IcebergSparkTestWrapper with LoggerBase {

  import spark.implicits._

  var writer: SDSTableWriter = _
  var reader: SDSTableReader = _
  var dataDictPath = "file:" + getClass.getResource("/dataquality/dqevalution/sds_data_dictionary/host__data_dictionary.json").getPath
  var confPath = "file:" + getClass.getResource("/dataquality/dqevalution/data_quality/sds_ei__entity_data_quality.json").getPath
  var enrichedDFs: Map[String, DataFrame] = _
  val table_name = "ei.sds_ei__host__qualys_host_list__host_id"

  var args = Array(
    "--config-path", f"$confPath;$dataDictPath",
    "--list-of-tables", """[{"table_name":"ei.sds_ei__host__qualys_host_list__host_id", "resolver_table": "ei.sds_ei_intra_source_resolver,ei.sds_ei__resolver__host" }]""",
    "--current-updated-date", "1747051199999",
    "--objectName", "host"
  )
  var jobArgs: DQJobArgs = _

  override def conf: SparkConf = super.conf.set("spark.sql.iceberg.merge-schema", "true")
    .set("spark.sql.iceberg.set-all-nullable-field", "true")
    .set("spark.sql.iceberg.check-ordering", "false").set("spark.sql.session.timeZone", "UTC")
    .set("spark.sds.iceberg.read.schema-evolution.enabled", "true")
    .set("spark.sds.iceberg.read.schema-evolution.conflict-strategy", "DROP_EXISTING_COLUMN")
    .set("spark.sds.kg.dq.schema", "ei")

  override def warehousePath: String = {
    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
    getClass().getResource("/iceberg_warehouse").getPath
  }

  before {
    writer = SDSTableWriterFactory.get(SDSIcebergConnect.name, spark)
    reader = SDSTableReaderFactory.get(SDSIcebergConnect.name, spark)
    jobArgs = DQCompletenessEvaluation.getParseCmdArgs(args)

    spark.sql(s"""CREATE SCHEMA IF NOT EXISTS iceberg_catalog.ei""")
    val source1 = Seq(
      ("Host", "p1", "Workstation", Seq("Router"), Some(1747051149999L), Some(1), "1747051149999", "data"),
      ("Host", "p2", "  !", Seq("Firewall"), Some(1747051149999L), Some(1), "1747051149999", "data"),
      ("Host", "p3", "Server", Seq("  !"), Some(1747051149999L), None, "1747051149999", "data"),
      ("Host", "p4", null, Seq("MDM"), Some(0L), Some(-2147483648), "1747051149999", "data"),
      ("Host", "p5", "Mobile", Seq("Uncategorized"), None, Some(1), "1747051149999", "data"),
      ("Host", "p6", null, Seq(null.asInstanceOf[String]), Some(1747051149999L), Some(1), "1747051149999", "data")
    ).toDF("class", "p_id", "type", "asset_purpose", "last_scan", "host_id", "UPDATED_AT", "kg_content_type")
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
    writer.overwritePartition(source1, table_name, Array(col("updated_at_ts")))
    val source2 = Seq(
      ("p1", "d1", "1747051149999"),
      ("p2", "d2", "1747051149999"),
      ("p3", "d3", "1747051149999"),
      ("p4", "d4", "1747051149999"),
      ("p5", "d5", "1747051149999"),
    ).toDF("p_id", "disambiguated_p_id", UPDATED_AT)
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
    writer.overwritePartition(source2, "ei.sds_ei_intra_source_resolver", Array(col("updated_at_ts")))

    val source3 = Seq(
      ("d1", "fd1", "1747051149999"),
      ("d2", "fd2", "1747051149999"),
      ("d3", "fd3", "1747051149999"),
      ("d4", "fd4", "1747051149999"),
      ("d5", "fd5", "1747051149999"),
    ).toDF("p_id", "disambiguated_p_id", UPDATED_AT)
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
    writer.overwritePartition(source3, "ei.sds_ei__resolver__host", Array(col("updated_at_ts")))
  }

  "it" should "return proper Seq of dataframe along with resolver" in {
    // This test checks that enrichWithResolver returns a DataFrame with the correct join between the main and resolver tables.
    // - The expected DataFrame should have the same rows as the main table, with disambiguated_p_id from the resolver table (or null if not found).
    // - The class and table_name columns are set as per the enrichWithResolver logic.
    // - If the resolver logic or table_name/class logic changes, update the expectedDis DataFrame accordingly.
    // Define schema
    val schema = StructType(Seq(
      StructField("p_id", StringType, nullable = true),
      StructField("type", StringType, nullable = true),
      StructField("asset_purpose", ArrayType(StringType, containsNull = true), nullable = true),
      StructField("last_scan", LongType, nullable = true),
      StructField("host_id", IntegerType, nullable = true),
      StructField("UPDATED_AT", StringType, nullable = true),
      StructField("updated_at_ts", TimestampType, nullable = true),
      StructField("kg_content_type", StringType, nullable = true),
      StructField("disambiguated_p_id", StringType, nullable = true),
      StructField("class", StringType, nullable = true),
      StructField("table_name", StringType, nullable = false)
    ))

    val data = Seq(
      Row("p1", "Workstation", Seq("Router"), 1747051149999L, 1, "1747051149999", Timestamp.valueOf("2025-05-12 06:29:00"), "data", "fd1", "Host", table_name),
      Row("p2", "  !", Seq("Firewall"), 1747051149999L, 1, "1747051149999", Timestamp.valueOf("2025-05-12 06:29:00"), "data", "fd2", "Host", table_name),
      Row("p3", "Server", Seq("  !"), 1747051149999L, null, "1747051149999", Timestamp.valueOf("2025-05-12 06:29:00"), "data", "fd3", "Host", table_name),
      Row("p4", null, Seq("MDM"), 0L, -2147483648, "1747051149999", Timestamp.valueOf("2025-05-12 06:29:00"), "data", "fd4", "Host", table_name),
      Row("p5", "Mobile", Seq("Uncategorized"), null, 1, "1747051149999", Timestamp.valueOf("2025-05-12 06:29:00"), "data", "fd5", "Host", table_name),
      Row("p6", null, Seq(null.asInstanceOf[String]), 1747051149999L, 1, "1747051149999", Timestamp.valueOf("2025-05-12 06:29:00"), "data", "p6", "Host", table_name)
    )

    // Create DataFrame
    val expectedDis = spark.createDataFrame(
      spark.sparkContext.parallelize(data),
      schema
    )
    val config = DQCompletenessEvaluation.getConfig(spark, jobArgs)

    enrichedDFs = DQEntityEvaluationUtils.enrichWithResolver(
      modelInput = Seq(TableConfig(
        table_name = table_name,
        resolver_table = "ei.sds_ei_intra_source_resolver,ei.sds_ei__resolver__host"
      )),
      objectName = "host",
      reader = reader,
      upd = 1747051149999L
    )
    println("****************************************************************************")

    assertDataFrameDataEquals(
      enrichedDFs.last._2.drop(col("updated_at_ts")),
      expectedDis.drop(col("updated_at_ts"))
    )
  }

  "it" should "provide valid Completeness score" in {
    // This test checks that evaluateCompleteness returns the correct completeness scores for each row.
    // - For 'type':
    //     - null => 0
    //     - NA identifier ("  !") => null
    //     - otherwise => 1
    // - For 'asset_purpose' (Array[String]):
    //     - null or [null] => 0
    //     - [NA identifier] => null
    //     - otherwise => 1
    // - cqs_total is the sum of the two scores (treating null as 0).
    // - number_cq_attributes is the count of non-null scores (i.e., not null or NA identifier).
    // - If the scoring logic or NA identifier changes, update the expectedDis DataFrame accordingly.
    val schema = StructType(Seq(
      StructField("class", StringType, nullable = true),
      StructField("table_name", StringType, nullable = false),
      StructField("updated_at_ts", TimestampType, nullable = true),
      StructField("p_id", StringType, nullable = true),
      StructField("disambiguated_p_id", StringType, nullable = true),
      StructField("completeness_attr_scores", StructType(Seq(
        StructField("p_id", IntegerType, nullable = true),
        StructField("type", IntegerType, nullable = true),
        StructField("asset_purpose", IntegerType, nullable = true),
        StructField("last_scan", IntegerType, nullable = true),
        StructField("host_id", IntegerType, nullable = true),
        StructField(UPDATED_AT, IntegerType, nullable = true),
        StructField("disambiguated_p_id", IntegerType, nullable = true),
        StructField("table_name", IntegerType, nullable = true)
      )), nullable = false),
      StructField("completeness_quality_score", DoubleType, nullable = true),
      StructField("completeness_quality_score_category", StringType, nullable = true)
    ))

    val data = Seq(
      Row("Host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p1", "fd1", Row(1, 1, 1, 1, 1, 1, 1, 1), 100.0, "High"),
      Row("Host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p2", "fd2", Row(1, null, 1, 1, 1, 1, 1, 1), 100.0, "High"),
      Row("Host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p3", "fd3", Row(1, 1, null, 1, 0, 1, 1, 1), 85.7143, "High"),
      Row("Host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p4", "fd4", Row(1, 0, 1, null, null, 1, 1, 1), 83.3333, "High"),
      Row("Host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p5", "fd5", Row(1, 1, 1, 0, 1, 1, 1, 1), 87.5, "High"),
      Row("Host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p6", "p6", Row(1, 0, 0, 1, 1, 1, 1, 1), 75.0, "High")
    )
    // Create DataFrame
    val expectedDis = spark.createDataFrame(
      spark.sparkContext.parallelize(data),
      schema
    )
    val config = DQCompletenessEvaluation.getConfig(spark, jobArgs)
    val dqConfig = config._1.config_value
    val dataDict: Map[String, DictionaryAttribute] = config._2.config_value.attributes
    val (idCol, disambCol, objectTypeName) = (
      DQEntityEvaluationUtils.idCol,
      DQEntityEvaluationUtils.disambCol,
      DQEntityEvaluationUtils.objectTypeName
    )

    val resultDF = DQCompletenessEvaluation.evaluateCompleteness(
      enrichedDFs, objectType = "entity", dataDict,
      dqConfig, idCol, disambCol, objectTypeName
    )

    println("************************************************************88")
    resultDF.printSchema()
    resultDF.show(truncate=false)
    expectedDis.printSchema()
    expectedDis.show(truncate=false)
    assertDataFrameDataEquals(
      expectedDis.drop(col(UPDATED_AT_TS)),
      resultDF.drop(col(UPDATED_AT_TS), col(UPDATED_AT))
    )
  }
}