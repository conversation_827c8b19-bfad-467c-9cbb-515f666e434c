package ai.prevalent.entityinventory.dqevalution

import ai.prevalent.entityinventory.common.configs.DQJobArgs
import ai.prevalent.entityinventory.dqevalution.DQDimensionCompaction.getMergedDimensionTables
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{FloatType, IntegerType, StringType, StructField, StructType, TimestampType, DoubleType}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

import java.io.File
import java.sql.Timestamp
import scala.reflect.io.Directory

class DQDimensionCompactionSpec extends AnyFlatSpec with BeforeAndAfter with DataFrameSuiteBase with IcebergSparkTestWrapper with LoggerBase {

  import spark.implicits._

  var writer: SDSTableWriter = _
  var reader: SDSTableReader = _
  var confPath = "file:" + getClass.getResource("/dataquality/dqevalution/data_quality/sds_ei__entity_data_quality.json").getPath

  var args = Array(
    "--config-path", confPath,
    "--list-of-tables", """[{"table_name":"ei.sds_ei__host__qualys_host_list__host_id"}]""",
    "--current-updated-date", "1747031340000",
    "--object-name", "host"
  )
  var jobArgs: DQJobArgs = _
  val table_name = "ei.sds_ei__host__qualys_host_list__host_id"

  override def conf: SparkConf = super.conf.set("spark.sql.iceberg.merge-schema", "true")
    .set("spark.sql.iceberg.set-all-nullable-field", "true")
    .set("spark.sql.iceberg.check-ordering", "false").set("spark.sql.session.timeZone", "UTC")
    .set("spark.sds.iceberg.read.schema-evolution.enabled", "true")
    .set("spark.sds.iceberg.read.schema-evolution.conflict-strategy", "DROP_EXISTING_COLUMN")
    .set("spark.sds.kg.dq.schema", "ei")

  override def warehousePath: String = {
    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
    getClass().getResource("/iceberg_warehouse").getPath
  }

  before {
    writer = SDSTableWriterFactory.get(SDSIcebergConnect.name, spark)
    reader = SDSTableReaderFactory.get(SDSIcebergConnect.name, spark)
    jobArgs = DQCompletenessEvaluation.getParseCmdArgs(args)

    spark.sql(s"""CREATE SCHEMA IF NOT EXISTS iceberg_catalog.ei""")
    val cqsSchema = StructType(Seq(
      StructField("class", StringType, nullable = false),
      StructField("table_name", StringType, nullable = false),
      StructField("updated_at_ts", TimestampType, nullable = true),
      StructField("p_id", StringType, nullable = true),
      StructField("disambiguated_p_id", StringType, nullable = true),
      StructField("completeness_attr_scores", StructType(Seq(
        StructField("type", IntegerType, nullable = true),
        StructField("asset_purpose", IntegerType, nullable = true)
      )), nullable = false),
      StructField("completeness_quality_score", FloatType, nullable = false)
    ))

    val cqsData = Seq(
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p1", "d1", Row(1, 1), 100.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p2", "d2", Row(null, 1), 100.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p3", "d3", Row(1, null), 100.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p4", "d4", Row(0, 1), 50.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p5", "d5", Row(1, 1), 100.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p6", null, Row(0, 0), 0.0f)
    )

    // Create DataFrame
    val cqsDF = spark.createDataFrame(
      spark.sparkContext.parallelize(cqsData),
      cqsSchema
    )

    writer.overwritePartition(cqsDF, "ei.sds_ei__host__dq_completeness", Array(days(col(UPDATED_AT_TS)), col("class"), col("table_name")))

    val aqsSchema = StructType(Seq(
      StructField("class", StringType, nullable = false),
      StructField("table_name", StringType, nullable = false),
      StructField("updated_at_ts", TimestampType, nullable = true),
      StructField("p_id", StringType, nullable = true),
      StructField("disambiguated_p_id", StringType, nullable = true),
      StructField("accuracy_quality_score", FloatType, nullable = false)
    ))

    val aqsData = Seq(
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p1", "d1", 100.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p2", "d2", 100.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p3", "d3", 100.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p4", "d4", 50.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p5", "d5", 100.0f),
      Row("host", table_name, Timestamp.valueOf("2025-05-12 11:59:00"), "p6", null, 0.0f)
    )

    // Create DataFrame
    val aqsDF = spark.createDataFrame(
      spark.sparkContext.parallelize(aqsData),
      aqsSchema
    )
    writer.overwritePartition(aqsDF, "ei.sds_ei__host__dq_accuracy", Array(days(col(UPDATED_AT_TS)), col("class"), col("table_name")))
  }

  "DQDimensionCompaction" should "join dimension tables and write output" in {
    val dqDimensions = Seq("completeness","accuracy")
    val mergedDF: DataFrame = getMergedDimensionTables(
      spark=spark,
      objectType="entity", objectName="host",
      dqDimensions=dqDimensions, jobArgs=jobArgs,
      reader=reader, dqSchema="ei"
    )

    // Create expected DataFrame
    val expectedSchema = StructType(Seq(
      StructField("class", StringType, nullable = true),
      StructField("table_name", StringType, nullable = true),
      StructField("p_id", StringType, nullable = true),
      StructField("updated_at_ts", TimestampType, nullable = true),
      StructField("disambiguated_p_id", StringType, nullable = true),
      StructField("completeness_attr_scores", StructType(Seq(
        StructField("type", IntegerType, nullable = true),
        StructField("asset_purpose", IntegerType, nullable = true)
      )), nullable = true),
      StructField("completeness_quality_score", FloatType, nullable = true),
      StructField("accuracy_quality_score", FloatType, nullable = true),
      StructField("aggregated_quality_score", DoubleType, nullable = true)
    ))

    val expectedData = Seq(
      Row("host", table_name, "p1", Timestamp.valueOf("2025-05-12 11:59:00"), "d1", Row(1, 1), 100.0f, 100.0f, 100.0),
      Row("host", table_name, "p2", Timestamp.valueOf("2025-05-12 11:59:00"), "d2", Row(null, 1), 100.0f, 100.0f, 100.0),
      Row("host", table_name, "p3", Timestamp.valueOf("2025-05-12 11:59:00"), "d3", Row(1, null), 100.0f, 100.0f, 100.0),
      Row("host", table_name, "p4", Timestamp.valueOf("2025-05-12 11:59:00"), "d4", Row(0, 1), 50.0f, 50.0f, 50.0),
      Row("host", table_name, "p5", Timestamp.valueOf("2025-05-12 11:59:00"), "d5", Row(1, 1), 100.0f, 100.0f, 100.0),
      Row("host", table_name, "p6", Timestamp.valueOf("2025-05-12 11:59:00"), null, Row(0, 0), 0.0f, 0.0f, 0.0)
    )

//    val expectedDF = spark.createDataFrame(
//      spark.sparkContext.parallelize(expectedData),
//      expectedSchema
//    )

    // Assert DataFrames are equal (ignoring row order)
//    assertDataFrameEquals(expectedDF, mergedDF)
  }
} 