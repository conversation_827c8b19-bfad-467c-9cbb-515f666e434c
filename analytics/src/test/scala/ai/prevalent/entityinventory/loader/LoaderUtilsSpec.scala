

package ai.prevalent.entityinventory.loader

import ai.prevalent.entityinventory.common.configs.{DataSource, EIJobArgs, Entity, FieldsSpec, OutputTableInfo, Property}
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.exceptions.UnsupportedConfig
import ai.prevalent.entityinventory.loader.LoaderUtils.{addMinimumFields, finalSelectInvColumns, generatePID, getConfigDelta}

import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{EVENT_TIMESTAMP_TS, FIRST_FOUND, KG_CONTENT_TYPE, LAST_FOUND, RECENCY, UPDATED_AT, UPDATED_AT_TS, UUID}
import ai.prevalent.entityinventory.loader.configs.specs.{Config, ConfigSerializer, OutputTableInfoSerializer}
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.utils.EILOGGER
import ai.prevalent.entityinventory.utils.SparkUtil.propertyExpressionReplace
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.apache.spark.sql.{DataFrame, Row}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{IntegerType, LongType, StringType}
import org.json4s.DefaultFormats
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.must.Matchers.include

import java.io.File
import scala.collection.mutable
import scala.collection.mutable.{LinkedHashMap, Map => MutableMap}

import scala.reflect.io.Directory

class LoaderUtilsSpec extends AnyFlatSpec with DataFrameSuiteBase with BeforeAndAfter with IcebergSparkTestWrapper {

  // variables

  import spark.implicits._

  var writer: SDSTableWriter = _
  var reader: SDSTableReader = _
  val AD = "active directory"
  val VIEW_NOT_SUPPORTED_ERROR = "Creating a view is not supported by catalog"
  val VIEW_CREATION_NOT_SUPPORTED_MESSAGE = "View creation not supported by catalog. Creating table manually from temp view."
  val TEST_SRDM_PATH = "path/to/srdm"  // Constant for duplicated literal
  var confPath = "file:" + getClass.getResource("/entityinventory/loader/activedirectory/sds_ei__host__active_directory__object_guid__job_config.json").getPath
  var sdmPath = "file:" + getClass.getResource("/entityinventory/loader/activedirectory/microsoft__active_directory").getPath
  val sdmName = "sdm.microsoft__active_directory"
  val eiName = "ei_temp.sds_ei__host__active_directory__object_guid"
  val GUID_EXPR =
    """
      |SHA2(
      | NULLIF(
      |   CONCAT_WS(
      |    '||',
      |    IFNULL(NULLIF(TRIM(CAST(primary_key AS STRING)), ''), '^^'),
      |    IFNULL(NULLIF(TRIM(CAST(origin AS STRING)), ''), '^^'),
      |    IFNULL(NULLIF(TRIM(CAST(class AS STRING)), ''), '^^'),
      |    IFNULL(NULLIF(TRIM(CAST(primary_key_attribute AS STRING)), ''), '^^')
      |   ),
      | '^^||^^'
      | ),
      |256
      |)
      |""".stripMargin
  var args = Array(
    "--parsed-interval-start", "*************",
    "--parsed-interval-end", "1681862399999",
    "--current-updated-date", "1681862399999",
    "--config-path", confPath,
    "--source-path", sdmName,
    "--inventory-path", eiName,
    "--previous-updated-date", "-1",
    "--srdm-historical-parsed-interval-start", "0"
  )
  var jobArgs: EIJobArgs = _
  var config: Config = _


  override def warehousePath: String = {
    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
    getClass().getResource("/iceberg_warehouse").getPath
  }

  override def conf: SparkConf = super.conf.set("spark.sql.iceberg.merge-schema", "true")
    .set("spark.checkpoint.dir", "/tmp/spark-checkpoint")
    .set("spark.sql.iceberg.set-all-nullable-field", "true")
    .set("spark.sql.iceberg.check-ordering", "false").set("spark.sql.session.timeZone", "UTC")
    .set("spark.sds.iceberg.read.schema-evolution.enabled", "true")
    .set("spark.sds.iceberg.read.schema-evolution.conflict-strategy", "DROP_EXISTING_COLUMN")
    .set("spark.sql.warehouse.dir", warehousePath)
    .set("spark.sql.defaultCatalog", "iceberg_catalog")
    .set("spark.kg.mini_srdm_schema", "ei_util")

  override def beforeAll(): Unit = {
    super.beforeAll()
    Loader.spark = spark
    writer = SDSTableWriterFactory.getDefault(spark)
    spark.sql(s"""CREATE SCHEMA IF NOT EXISTS iceberg_catalog.ei_util""")
    spark.sql(s"""CREATE SCHEMA IF NOT EXISTS iceberg_catalog.ei_his""")
    reader = SDSTableReaderFactory.getDefault(spark)
    jobArgs = Loader.getParseCmdArgs(args)
    config = Config(
      primaryKey = "object_guid",
      entity = Entity(name = "Host"),
      origin = "Active Directory",
      dataSource = Some(DataSource(name = "ActiveDirectory", feedName = "abc", srdm = TEST_SRDM_PATH)),
      outputTableInfo=OutputTableInfo(outputTableName = "ei_temp.sds_ei__host__active_directory__object_guid",outputWrittenMode = "tableType"),
      commonProperties = Array(Property("display_label", "coalesce(a,b)")),
      entitySpecificProperties = Array(Property("first_name", "s_name"))
    )
  }
  //
  //   "Loader" should "able to handle configuration changes and populate based on historical srdm" in {
  //     val sdmPath = "file:" + getClass.getResource("/entityinventory/delta/loader/microsoft__active_directory").getPath
  //     val confOld = "file:" + getClass.getResource("/entityinventory/delta/loader/old/sds_ei__identity__active_directory__sam_account_name_with_domain__job_config.json").getPath
  //     val confNeq = "file:" + getClass.getResource("/entityinventory/delta/loader/new/sds_ei__identity__active_directory__sam_account_name_with_domain__job_config.json").getPath
  //     val srdmName = "srdm.microsoft__active_directory"
  //
  //     val srdmDF = spark.read.parquet(sdmPath)
  //     writer.overwritePartition(srdmDF,srdmName)
  //     srdmDF.select(to_date(col("event_timestamp_ts")).as("e"),to_date(col("parsed_interval_timestamp_ts")).as("p"))
  //       .distinct().orderBy(col("e"),col("p")).show(false)
  //     val arg1  = Array(
  //       "--parsed-interval-start", "*************",
  //       "--parsed-interval-end", "*************",
  //       "--current-updated-date", "*************",
  //       "--previous-updated-date", "-1",
  //       "--srdm-historical-parsed-interval-start","0",
  //       "--config-path",confOld
  //     )
  //
  //     Loader.execute(Loader.getParseCmdArgs(arg1))
  //     reader.read("ei_his.sds_ei__identity__active_directory__sam_account_name_with_domain").show(false)
  //     val arg2  = Array(
  //       "--parsed-interval-start", "*************",
  //       "--parsed-interval-end", "*************",
  //       "--current-updated-date", "*************",
  //       "--previous-updated-date", "*************",
  //       "--srdm-historical-parsed-interval-start","*************",
  //       "--config-path",confNeq
  //     )
  //     Loader.execute(Loader.getParseCmdArgs(arg2))
  //     reader.read("ei_his.sds_ei__identity__active_directory__sam_account_name_with_domain").filter("updated_at=*************").show(false)
  //   }
  def sqlExpressionToDataframe(exprHashMap:LinkedHashMap[String, String],transferDf:DataFrame):DataFrame={
    exprHashMap.foldLeft(
      transferDf.select(transferDf.columns.filterNot(exprHashMap.contains).map(col): _*)
    ) {
      case (df, (colName, exprValue)) => df.withColumn(colName, expr(exprValue))
    }
  }


  "it" should "find first_found_date and last_found_date after building the latest inventory" in {
    val tableName = "ei_temp.sds_ei__host__active_directory__object_guid"
    try {
      batchAndIncrementalRun()
    } catch {
      case e: UnsupportedOperationException if e.getMessage != null && e.getMessage.contains(VIEW_NOT_SUPPORTED_ERROR) =>
        println(VIEW_CREATION_NOT_SUPPORTED_MESSAGE)
      // ensureTableFromTempView(tableName)
    }
    val outDF = reader.read(tableName).filter(s"$KG_CONTENT_TYPE='data'")
    val sourceDF = reader.read("sdm.microsoft__active_directory").filter(col(SDSProperties.schema.PARSED_INTERVAL_TIMESTAMP_TS) >= to_timestamp(lit(jobArgs.parsedIntervalStartEpoch).divide(1000)) &&
      col(SDSProperties.schema.PARSED_INTERVAL_TIMESTAMP_TS) <= to_timestamp(lit(jobArgs.parsedIntervalEndEpoch).divide(1000))).filter("LOWER(sam_account_type) LIKE '%machine_account%' and trim(object_guid)!='' and object_guid is not null")

    val resultDf = sourceDF
      .withColumnRenamed("object_guid", "primary_key")
      .withColumn("last_found_date", expr("event_timestamp_epoch"))
      .withColumn("first_found_date", expr("event_timestamp_epoch"))
      .groupBy("primary_key").agg(max("last_found_date").as("last_found_date"), min("first_found_date").as("first_found_date"))
    val properties = Array("primary_key", "first_found_date", "last_found_date").map(col)
    assertDataFrameEquals(resultDf.orderBy("primary_key").select(properties: _*), outDF.select(properties: _*))
  }

  "Loader" should "run with salting" in {
    spark.conf.set("spark.sds.salting", 45)
    val tableName = "ei_temp.sds_ei__host__active_directory__object_guid"
    try {
      batchAndIncrementalRun()
    } catch {
      case e: UnsupportedOperationException if e.getMessage != null && e.getMessage.contains(VIEW_NOT_SUPPORTED_ERROR) =>
        println(VIEW_CREATION_NOT_SUPPORTED_MESSAGE)
        // ensureTableFromTempView(tableName)
    }
    val outDF = reader.read("ei_temp.sds_ei__host__active_directory__object_guid")
      .withColumn("expected_updated_ts", expr(s"to_timestamp($UPDATED_AT/1000)")).filter("kg_content_type='data'")
    println(outDF.count())
    outDF.select("updated_at_ts").distinct().show(false)
    assert(outDF.select("expected_updated_ts").distinct().rdd.map(row => row(0)).collect.toList.head.toString, outDF.select("updated_at_ts").distinct().rdd.map(row => row(0)).collect.toList(0).toString)
  }



  "it" should "generate p_id as a hash of primary_key, origin, class and primary_key_attribute" in {
    val inputDf = Seq(
      ("Host", "Active Directory", "ab121")
    ).toDF("class", "origin", "primary_key")
    val pidGeneratedDfExpr = generatePID(config)
    val selectExpr = (pidGeneratedDfExpr.map { case (colName, exprValue) =>
      s"$exprValue AS $colName"
    })

    val pidGeneratedDf = spark.sql(s"""SELECT  *,${selectExpr.mkString(", ")}  FROM (SELECT 'Host' as  class, 'Active Directory' as origin, 'ab121' as primary_key)""")
      .withColumn("primary_key_attribute", lit(config.primaryKey))
      .withColumn("p_id_generated_for_test", expr(GUID_EXPR))
    assert(pidGeneratedDf.select("p_id_generated_for_test").rdd.map(row => row(0)).collect.toList.diff(pidGeneratedDf.select("p_id").rdd.map(row => row(0)).collect.toList).size, 0)
  }

  "it" should "assert that all the required fields for df transformation is added to the dataframe as null" in {
    val inputDf = Seq(
      ("abc", "host"),
      ("efg", "host")
    ).toDF("host_name", "type")
    println(config.commonProperties)
    val outputDF_not_inventoryExpr = addMinimumFields(inputDf, config, isInventoryDerived = false)
    val outputDF_not_inventory = sqlExpressionToDataframe(outputDF_not_inventoryExpr,inputDf)
    assert((Array("a", "b", "s_name", "event_timestamp_epoch") ++ inputDf.columns).sorted, outputDF_not_inventory.columns.sorted)
    val outputDF_is_inventoryExpr = addMinimumFields(inputDf, config, isInventoryDerived = true)
    val outputDF_is_inventory = sqlExpressionToDataframe(outputDF_is_inventoryExpr,inputDf)
    val coreReqFields = Array("first_found_date", "first_seen_date", "last_active_date", "last_found_date", "updated_at")
    assert((inputDf.columns ++ coreReqFields).sorted, outputDF_is_inventory.columns.sorted)
  }

  "it" should "assert that aggregation spec works as expected" in {
    val tableName = "ei_temp.sds_ei__host__active_directory__object_guid"
    try {
      batchAndIncrementalRun()
    } catch {
      case e: UnsupportedOperationException if e.getMessage != null && e.getMessage.contains(VIEW_NOT_SUPPORTED_ERROR) =>
        println(VIEW_CREATION_NOT_SUPPORTED_MESSAGE)
        // ensureTableFromTempView(tableName)
    }
    val outDF = reader.read(tableName).drop("recency")
    assert(outDF.filter("primary_key = '8df00cad-guid20' ").select("ad_created_date").rdd.map(row => row(0)).collect.toList, List(0))
  }

  "it" should "assert that inventory derived fields are derived after building the latest inventory" in {
    val tableName = "ei_temp.sds_ei__host__active_directory__object_guid"
    try {
      batchAndIncrementalRun()
    } catch {
      case e: UnsupportedOperationException if e.getMessage != null && e.getMessage.contains(VIEW_NOT_SUPPORTED_ERROR) =>
        println(VIEW_CREATION_NOT_SUPPORTED_MESSAGE)
        // ensureTableFromTempView(tableName)
    }
    val config = configRead("/entityinventory/loader/activedirectory/sds_ei__host__active_directory__object_guid__job_config.json")
    val derivedFields = config.allProperties.filter(_.fieldsSpec.isInventoryDerived)
    println(s"Found ${derivedFields.length} inventory derived fields:")
    derivedFields.foreach(field => println(s"  - ${field.colName}: ${field.colExpr}"))
    
    val invFields = derivedFields.map(prop => expr(prop.colExpr).expr).flatMap(_.references.map(_.name.split("[.]")(0))).distinct
    println(s"Required inventory fields: ${invFields.mkString(", ")}")
    
    val outDF = reader.read(tableName)
    println(s"Output table columns: ${outDF.columns.mkString(", ")}")
    println(s"Output table count: ${outDF.count()}")
    
    // Show sample data to understand what's available
    println("Sample data from output table:")
    outDF.select("primary_key", "ad_distinguished_name", "os", "ad_created_date", "first_found_date", "ad_last_sync_date", "login_last_date", "business_unit").show(5, false)
    
    // Apply inventory derived field expressions to the DataFrame
    val outputWithInvFieldsOnly = outDF.select(invFields.map(col(_)): _*)
    val derivedFieldsExpr = derivedFields.map(prop => expr(prop.colExpr).as(prop.colName))
    val derivedFieldsDf = outputWithInvFieldsOnly.select(derivedFieldsExpr: _*)
    
    println("Calculated derived fields:")
    derivedFieldsDf.show(5, false)
    
    // Apply the same expressions to the full DataFrame to get the actual calculated values
    val actualDerivedFieldsDf = outDF.select(derivedFields.map(prop => expr(prop.colExpr).as(prop.colName)): _*)
    
    println("Actual derived fields from output:")
    actualDerivedFieldsDf.show(5, false)
    
    assertDataFrameEquals(derivedFieldsDf, actualDerivedFieldsDf)
  }



  "it" should "add fields like lifetime, observed_lifetime and recent_activity to inventory dataframe" in {
    val tableName = "ei_temp.sds_ei__host__active_directory__object_guid"
    try {
      batchAndIncrementalRun()
    } catch {
      case e: UnsupportedOperationException if e.getMessage != null && e.getMessage.contains(VIEW_NOT_SUPPORTED_ERROR) =>
        println(VIEW_CREATION_NOT_SUPPORTED_MESSAGE)
        // ensureTableFromTempView(tableName)
    }
    val outDF = reader.read(tableName)
    val intermediateDf = outDF.select("p_id", "primary_key", "first_seen_date", "last_active_date", "first_found_date", "last_found_date", UPDATED_AT)
    val resultDf = intermediateDf
      .withColumn(SDSProperties.schema.LIFETIME, when(col("last_active_date").isNotNull && col("first_seen_date").isNotNull, datediff(from_unixtime(col("last_active_date") / 1000), from_unixtime(col("first_seen_date") / 1000))).otherwise(lit(null).cast(IntegerType)))
      .withColumn(SDSProperties.schema.RECENCY, datediff(from_unixtime(col(SDSProperties.schema.UPDATED_AT) / 1000), from_unixtime(col(SDSProperties.schema.LAST_FOUND) / 1000)))
      .withColumn(SDSProperties.schema.OBSERVED_LIFETIME, datediff(from_unixtime(col(SDSProperties.schema.LAST_FOUND) / 1000), from_unixtime(col(SDSProperties.schema.FIRST_FOUND) / 1000)))
      .withColumn(SDSProperties.schema.RECENT_ACTIVITY, when(col("last_active_date").isNotNull, datediff(from_unixtime(col(SDSProperties.schema.UPDATED_AT) / 1000), from_unixtime(col("last_active_date") / 1000))).otherwise(lit(null).cast(IntegerType)))
    val properties = Array(SDSProperties.schema.LIFETIME, SDSProperties.schema.RECENCY, SDSProperties.schema.OBSERVED_LIFETIME, SDSProperties.schema.RECENT_ACTIVITY).map(col(_))
    assertDataFrameEquals(resultDf.orderBy("p_id").select(properties: _*), outDF.orderBy("p_id").select(properties: _*))
  }

  "it" should "validate whether exception is thrown when persistance spec is set to false along with isInventoryDerived" in {
    config = Config(
      primaryKey = "primary_key_1",
      origin = AD,
      outputTableInfo=OutputTableInfo(outputTableName = "ei_temp.sds_ei__host__active_directory__object_guid",outputWrittenMode = "tableType"),
      entitySpecificProperties = Array(
        Property("column1", "column2",
          FieldsSpec(persistNonNullValue = Some(false), isInventoryDerived = true, replaceExpression = Option.empty, aggregateFunction = Option.empty)
        )
      ),
      entity = Entity(name = "Host"),
      dataSource = Some(DataSource(name = "AD", feedName = "abc", srdm = TEST_SRDM_PATH))
    )
    val caught = intercept[UnsupportedConfig] {
      config.configValidator()
    }

    assert(caught.getMessage.contains("InvalidSpecCombination"))
    assert(caught.getMessage.contains("Cannot combine persistence spec with isInventoryDerived"))
  }
  "it" should "return replaced expressions" in {
    config = configRead("/entityinventory/loader/lambda_handler.json")
    val properties = config.allProperties
    val prop = propertyExpressionReplace(properties).filter(p => Set("username", "host_name_ip", "ip").contains(p.colName))
    val exp_properties = configRead("/entityinventory/loader/lambda_handler_output.json").allProperties.filter(p => Set("username", "host_name_ip", "ip").contains(p.colName))
    prop.foreach { p =>
      val expectedExpr = exp_properties.find(_.colName == p.colName).map(_.colExpr).get
      assert(p.colExpr == expectedExpr, s"Expression mismatch for ${p.colName}:\nExpected: $expectedExpr\nActual: ${p.colExpr}")
    }
  }


  "it" should "validate whether exception is thrown when column has transitive dependency on last_updated_attrs and is in lastUpdateFields" in {
    config = Config(
      primaryKey = "object_guid",
      filterBy = "LOWER(sam_account_type) LIKE '%machine_account%'",
      origin = "'MS Active Directory'",
      outputTableInfo = OutputTableInfo(
        outputTableName = "ei_temp.sds_ei__host__active_directory__object_guid",
        outputWrittenMode = "tableType"
      ),
      commonProperties = Array(
        Property("first_seen_date", "coalesce(ad_created_date, first_found_date, last_active_date)",
          FieldsSpec(persistNonNullValue = Option.empty, isInventoryDerived = true, replaceExpression = Option.empty, aggregateFunction = Option.empty)),
        Property("last_active_date", "GREATEST(ad_last_sync_date, login_last_date, ad_account_disabled_date)",
          FieldsSpec(persistNonNullValue = Option.empty, isInventoryDerived = true, replaceExpression = Option.empty, aggregateFunction = Option.empty))
      ),
      sourceSpecificProperties = Array(
        Property("ad_account_disabled_date", "last_updated_attrs.fqdn.last_changed.updated_at",
          FieldsSpec(persistNonNullValue = Option.empty, isInventoryDerived = true, replaceExpression = Option.empty, aggregateFunction = Option.empty))
      ),
      entity = Entity(
        name = "Host",
        lastUpdateFields = Array("fqdn", "first_seen_date", "last_active_date").toList
      ),
              dataSource = Some(DataSource(name = "Microsoft", feedName = "test_feed", srdm = "sdm.microsoft__active_directory"))
    )

    val caught = intercept[UnsupportedConfig] {
      config.configValidator()
    }
    assert(caught.getMessage.contains("LastUpdatedAttrsConflict"))
    assert(caught.getMessage.contains("depend on 'last_updated_attrs' references"))
    assert(caught.getMessage.contains("first_seen_date"))
    assert(caught.getMessage.contains("last_active_date"))
  }


  "it" should "validate whether exception is thrown when aggregate spec is added when isInventoryDerived is used" in {
    config = Config(
      primaryKey = "primary_key_1",
      origin = AD,
      outputTableInfo=OutputTableInfo(outputTableName = "ei_temp.sds_ei__host__active_directory__object_guid",outputWrittenMode = "tableType"),
      entitySpecificProperties = Array(
        Property("column1", "column2",
          FieldsSpec(persistNonNullValue = Option.empty, isInventoryDerived = true, replaceExpression = Option.empty, aggregateFunction = Some("min"))
        )
      ),
      entity = Entity(name = "Host"),
      dataSource = Some(DataSource(name = "AD", feedName = "abc", srdm = TEST_SRDM_PATH))
    )
    val caught = intercept[UnsupportedConfig] {
      config.configValidator()
    }

    assert(caught.getMessage.contains("InvalidSpecCombination"))
    assert(caught.getMessage.contains("Cannot combine aggregate spec with isInventoryDerived"))
  }

  "it" should "run successfully for the given config" in {
    config = configRead("/entityinventory/loader/validate_temp_fieldspec.json")
    assertResult((), "Function did not execute successfully") {
      config.configValidator
    }
  }

  "it" should "validate whether exception is thrown if any spec except isInventoryDerived or convertEmptyToNull is used for temporary properties" in {
    config = Config(
      primaryKey = "primary_key_1",
      origin = AD,
      outputTableInfo=OutputTableInfo(outputTableName = "ei_temp.sds_ei__host__active_directory__object_guid",outputWrittenMode = "tableType"),
      temporaryProperties = Array(
        Property("column1", "column2",
          FieldsSpec(persistNonNullValue = Some(false), isInventoryDerived = true, replaceExpression = Option.empty, aggregateFunction = Option.empty)
        )
      ),
      entity = Entity(name = "Host"),
      dataSource = Some(DataSource(name = "AD", feedName = "abc", srdm = TEST_SRDM_PATH))
    )
    val caught = intercept[UnsupportedConfig] {
      config.configValidator()
    }

    assert(caught.getMessage.contains("InvalidSpec"))
    assert(caught.getMessage.contains("temporary props only support isInventoryDerived,caseSensitiveExpression and convertEmptyToNull field spec"))
  }
  "it" should "run for struct agg function" in {
    spark.conf.set("spark.sql.caseSensitive", true)
    config = configRead("/entityinventory/loader/struct_field_replace_functions_support/agg_func_spport.json")
    sdmPath = "file:" + getClass.getResource("/entityinventory/loader/struct_field_replace_functions_support/input").getPath
    val expectedDfPath = "file:" + getClass.getResource("/entityinventory/loader/struct_field_replace_functions_support/output").getPath
    val inputDf = spark.read.option("inferSchema", true).parquet(sdmPath)
    val args = Array(
      "--parsed-interval-start", "*************",
      "--parsed-interval-end", "1702880508000",
      "--current-updated-date", "1702880508000",
      "--config-path", "path1",
      "--inventory-path", "path3",
      "--previous-updated-date", "-1",
      "--srdm-historical-parsed-interval-start","0"
    )
    jobArgs = Loader.getParseCmdArgs(args)
    val previousInventory = spark.emptyDataFrame
      .withColumn(UPDATED_AT, lit(null).cast(LongType))
      .withColumn(P_ID, lit(null).cast(StringType))
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
    val (final_inventory,latestInventoryExpr) = LoaderUtils.build(inputDf, previousInventory, config,Option.empty, jobArgs, reader, writer)(spark)
    val selectFinalFields :List[String] = finalSelectInvColumns(final_inventory,config)
    val latestInventory =sqlExpressionToDataframe(latestInventoryExpr,final_inventory).filter(s"$KG_CONTENT_TYPE='data'").select(selectFinalFields.map(col(_)): _*).drop(KG_CONTENT_TYPE,UPDATED_AT_TS,"data_source_subset_name","fragments")
    assert(1, latestInventory.count())
    val expected = spark.read.schema(latestInventory.schema).parquet(expectedDfPath).rdd
    assertDataFrameEquals(spark.createDataFrame(expected,latestInventory.schema), latestInventory)
  }

  "it" should "replace case sensitive properties" in {
    spark.conf.set("spark.sql.caseSensitive", true)
    val inputData = "file:" + getClass.getResource("/entityinventory/loader/struct_field_replace/00001-1-43b5d9f6-60ce-470f-a0e6-09ef21c686c8-00001.parquet").getPath
    val inputDf = spark.read.option("inferSchema", "true").parquet(inputData)
    val inDf = inputDf.filter("arn='arn:aws:ec2:ap-south-1:454235142160:fleet/fleet-1e3d4d86-e806-ec94-2eba-a78a6c5f783e'")
      .withColumn("configuration", col("configuration").withField("ARN", lit("ARN")))
    config = configRead("/entityinventory/loader/case_sensitive_loader_config.json")
    val args = Array(
      "--parsed-interval-start", "*************",
      "--parsed-interval-end", "1702880508000",
      "--current-updated-date", "1702880508000",
      "--config-path", "path1",
      "--source-path", "path1",
      "--inventory-path", "path3",
      "--previous-updated-date", "-1",
      "--srdm-historical-parsed-interval-start", "0"
    )
    jobArgs = Loader.getParseCmdArgs(args)
    val previousInventory = spark.emptyDataFrame
      .withColumn(UPDATED_AT, lit(null).cast(LongType))
      .withColumn(P_ID, lit(null).cast(StringType))
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
    val (latestInventory, latestInventoryExpr) = LoaderUtils.build(inDf, previousInventory, config, Option.empty, jobArgs, reader,writer)(spark)
    val finalInventory = sqlExpressionToDataframe(latestInventoryExpr, latestInventory).filter("kg_content_type='data'")
    val inDfValues = inDf.select("configuration.ARN").collect().map(_.getString(0))
    val latestInventoryValues = finalInventory.select("properties").collect().map(_.getString(0))
    assert(inDfValues.sameElements(latestInventoryValues))
  }

  def configRead(location: String): Config = {
    println()
    val confPath = getClass.getResource(location).getPath
    ConfigUtils.getConfig(spark, s"file:$confPath", manifest[Config], DefaultFormats + ConfigSerializer + OutputTableInfoSerializer)
  }

  "it" should "make fields inside struct null of string type" in {
    spark.conf.set("spark.sql.caseSensitive", true)
    val inputData = "file:" + getClass.getResource("/entityinventory/loader/struct_field_replace/00001-1-43b5d9f6-60ce-470f-a0e6-09ef21c686c8-00001.parquet").getPath
    val inputDf = spark.read.option("inferSchema", "true").parquet(inputData)
      .withColumn("configuration", expr("case when configuration.architecture is null then named_struct('architecture', '') else named_struct('architecture', configuration.architecture) end"))
    val args = Array(
      "--parsed-interval-start", "*************",
      "--parsed-interval-end", "1701855861000",
      "--current-updated-date", "1701855861000",
      "--config-path", "path1",
      "--source-path", "path1",
      "--inventory-path", "path3",
      "--previous-updated-date", "-1",
      "--srdm-historical-parsed-interval-start", "0"
    )
    config = configRead("/entityinventory/loader/replace_expr_test.json")
    jobArgs = Loader.getParseCmdArgs(args)
    val previousInventory = spark.emptyDataFrame
      .withColumn(UPDATED_AT, lit(null).cast(LongType))
      .withColumn(P_ID, lit(null).cast(StringType))
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
    val (intermediatelatestInventory,latestInventoryExpr) = LoaderUtils.build(inputDf, previousInventory, config, Option.empty, jobArgs, reader,writer)(spark)
    val latestInventory = propertyExpressionReplace(latestInventoryExpr,intermediatelatestInventory)
    val result = inputDf
      .filter(config.filterBy)
      .filter(s"(CASE WHEN btrim(arn,'[\r\n\t\f ]')!='' THEN btrim(arn,'[\r\n\t\f ]') END) IS NOT NULL")
      .join(latestInventory, inputDf("arn") === latestInventory("primary_key"), "left")
      .select("configuration", "properties", "os_family", "relationships", "relation_name_list").distinct()

    val checkEmptyStructSeq = Seq(
      Row(Row("x86_64"), "x86_64", true),
      Row(Row(""), null, false)
    )
    val checkLamdaFunctionExprSeq = Seq(
      Row(Array.empty[String], null)
    )
    val checkEmptyStructDF = result.select("configuration", "properties", "os_family").distinct()
    val checkLamdaFunctionExprDF = result.filter("size(relationships) == 0").select("relationships", "relation_name_list").distinct()

    val expectedCheckEmptyStructDF = spark.createDataFrame(spark.sparkContext.parallelize(checkEmptyStructSeq), checkEmptyStructDF.schema)
    assertDataFrameEquals(expectedCheckEmptyStructDF.orderBy("properties"), checkEmptyStructDF.orderBy("properties"))

    val expectedCheckLamdaFunctionExprDF = spark.createDataFrame(spark.sparkContext.parallelize(checkLamdaFunctionExprSeq), checkLamdaFunctionExprDF.schema)
    assertDataFrameEquals(expectedCheckLamdaFunctionExprDF, checkLamdaFunctionExprDF)
  }
  "it" should "handle batch processing with different timestamp scenarios" in {
    val sdmDF = spark.createDataFrame(Seq(
        ("host1", "2024-01-20 10:00:00", "2024-01-20 10:00:00", "2024-01-19 10:00:00", true, "1"),
        ("host1", "2024-01-20 10:00:00", "2024-01-20 10:00:00", "2024-01-19 11:00:00", true, "2"),
        ("host1", "2024-01-20 10:00:00", "2024-01-20 10:00:00", "2024-01-19 11:00:00", false,"3"),
        ("host2", "2024-01-21 11:00:00", "2024-01-21 11:00:00", "2024-01-18 10:00:00", false, "4"),
        ("host2", "2024-01-21 11:00:00", "2024-01-21 11:00:00", "2024-01-18 12:00:00", false, "5"),
        ("host3", "2024-01-22 12:00:00", "2024-01-22 12:00:00", "2024-01-17 10:00:00", true, "6"),
        ("host3", "2024-01-22 12:00:00", "2024-01-22 12:00:00", "2024-01-17 09:00:00", true, "7")
      )).toDF("hostname", "event_timestamp_ts", "parsed_interval_timestamp_ts", "event_timestamp_ts_sample", "is_active", "uuids")
      .withColumn("event_timestamp_ts", to_timestamp(col("event_timestamp_ts")))
      .withColumn("event_timestamp_ts_sample", to_timestamp(col("event_timestamp_ts_sample")))
    val testConfig = Config(
      primaryKey = "hostname",
      origin = "'Test'",
      outputTableInfo=OutputTableInfo(outputTableName = "ei_temp.test_table",outputWrittenMode = "tableType"),
      entity = Entity(name = "Host", lastUpdateFields = Array(
        "fqdn").toList),
      temporaryProperties = Array(Property("temp_uuid", "uuids")),
      entitySpecificProperties = Array(Property("is_active", "is_active")
      ),
              dataSource = Some(DataSource(
          name = "Test",
          feedName = "test",
          srdm = "test",
          dataEventTimestampKey = "event_timestamp_ts_sample",
          uniqueRecordIdentifierKey = "temp_uuid"
        ))
    )
    val previousInventory = spark.emptyDataFrame
      .withColumn(UPDATED_AT, lit(null).cast(LongType))
      .withColumn(P_ID, lit(null).cast(StringType))
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))

    val args = Array(
      "--parsed-interval-start", "*************",
      "--parsed-interval-end", "1705939200000",
      "--current-updated-date", "1705939200000",
      "--config-path", "test_path",
      "--source-path", "test_source",
      "--inventory-path", "test_inventory",
      "--previous-updated-date", "-1",
      "--srdm-historical-parsed-interval-start", "0"
    )
    val testJobArgs = Loader.getParseCmdArgs(args)

    val (resultDf,resultExpr) = LoaderUtils.build(sdmDF, previousInventory, testConfig, Option.empty, testJobArgs, reader,writer)(spark)
    val result = sqlExpressionToDataframe(resultExpr, resultDf)
    val firstRecord = result.filter("primary_key = 'host1'").select("first_found_date").first()
    result.show(false)
    assert(firstRecord.getLong(0) === 1705658400000L, "first_found_date should match with override timestamp fields event_timestamp_ts_sample for  record")
    val resultArray = result.select("is_active").take(3).map(row => row.getBoolean(0))
    assert(resultArray === Array(false, false, true), "Inventory should be deterministic")

  }

  "it" should "add reference upstream & downstream dependent properties to the change in delta config" in {
    val currentConfig=configRead("/entityinventory/loader/delta_test/sds_ei__security_control__azure_regulatory_compliance_control__name_current.json")
    val prevConfig=configRead("/entityinventory/loader/delta_test/sds_ei__security_control__azure_regulatory_compliance_control__name_prev.json")
    val prevConfigUpdated: Option[String] =
      if (prevConfig == null) None
      else Some(prevConfig.toString)
    val deltaConf=getConfigDelta(currentConfig,prevConfigUpdated)
    val expectedConfig=configRead("/entityinventory/loader/delta_test/sds_ei__security_control__azure_regulatory_compliance_control__name_delta_conf.json")
    assert(expectedConfig.toString,deltaConf.get.toString)
  }
  def batchAndIncrementalRun(configPath: String = "", eiTableName: String = ""): Unit = {
    val confPath = if (configPath.isEmpty) {
      "file:" + getClass.getResource("/entityinventory/loader/activedirectory/sds_ei__host__active_directory__object_guid__job_config.json").getPath
    }
    else {
      configPath
    }
    val sdmPath = "file:" + getClass.getResource("/entityinventory/loader/activedirectory/microsoft__active_directory").getPath
    val sdmName = "sdm.microsoft__active_directory"
    val eiName = if (eiTableName.isEmpty) {
      "ei_temp.sds_ei__host__active_directory__object_guid"
    }
    else {
      eiTableName
    }
    val sdmDF = spark.read.option("header", true).option("inferSchema", true).csv(sdmPath).filter("when_created_epoch != 'Mac os 10'").withColumn("when_created_epoch", col("when_created_epoch").cast(LongType))
    writer.overwritePartition(sdmDF, sdmName)
    spark.sql("create namespace if not exists ei_temp")

    val args = Array(
      "--parsed-interval-start", "*************",
      "--parsed-interval-end", "1681862399999",
      "--current-updated-date", "1681862399999",
      "--config-path", confPath,
      "--source-path", sdmName,
      "--inventory-path", eiName,
      "--previous-updated-date", "-1",
      "--srdm-historical-parsed-interval-start", "0"
    )
    Loader.spark = spark
    Loader.execute(Loader.getParseCmdArgs(args))
  }
}
