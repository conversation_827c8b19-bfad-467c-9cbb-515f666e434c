package ai.prevalent.entityinventory.loader

import ai.prevalent.entityinventory.common.configs._
import ai.prevalent.entityinventory.common.rerun.{Config<PERSON><PERSON><PERSON>, FileChange, FullRerun, IncrementalRerun, NoChange, RerunContext, RerunStrategy, RunHashes, SkipExecution, UpdateBoth, UpdateNone, UpdateViews}
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.entityinventory.loader.rerun.LoaderRerunStrategy
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

import scala.reflect.io.Directory
import java.io.File

class LoaderRerunStrategySpec extends AnyFlatSpec with DataFrameSuiteBase with BeforeAndAfter with IcebergSparkTestWrapper {

  import spark.implicits._

  var jobArgs: EIJobArgs = _

  override def warehousePath: String = {
    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
    getClass().getResource("/iceberg_warehouse").getPath
  }

  override def conf: SparkConf = super.conf.set("spark.sql.iceberg.merge-schema", "true")
    .set("spark.sql.iceberg.set-all-nullable-field", "true")
    .set("spark.sql.iceberg.check-ordering", "false").set("spark.sql.session.timeZone", "UTC")
    .set("spark.sds.iceberg.read.schema-evolution.enabled", "true")
    .set("spark.sds.iceberg.read.schema-evolution.conflict-strategy", "DROP_EXISTING_COLUMN")
    .set("spark.sds.hive.read.catalog","spark_catalog")

  override def beforeAll(): Unit = {
    super.beforeAll()
    Loader.spark = spark
    spark.sql(s"""CREATE SCHEMA IF NOT EXISTS iceberg_catalog.ei_util""")
    spark.sql(s"""CREATE SCHEMA IF NOT EXISTS iceberg_catalog.ei_his""")

    // Create minimal job args for testing
    val args = Array(
      "--parsed-interval-start", "0000000000000",
      "--parsed-interval-end", "1681862399999",
      "--current-updated-date", "1681862399999",
      "--config-path", "test_path",
      "--source-path", "test_source",
      "--inventory-path", "test_inventory",
      "--previous-updated-date", "-1",
      "--srdm-historical-parsed-interval-start", "0"
    )
    jobArgs = Loader.getParseCmdArgs(args)
  }

  // Helper method to create base config
  def createBaseConfig(primaryKey: String = "hostname", origin: String = "'Test'") = Config(
    primaryKey = primaryKey,
    origin = origin,
    outputTableInfo = OutputTableInfo(outputTableName = "ei_temp.test_table", outputWrittenMode = "tableType"),
    entity = Entity(name = "Host", lastUpdateFields = Array("fqdn").toList),
    temporaryProperties = Array(Property("temp_uuid", "uuids")),
    entitySpecificProperties = Array(Property("is_active", "is_active")),
    dataSource = Some(DataSource(
      name = "Test",
      feedName = "test",
      srdm = "test",
      dataEventTimestampKey = "event_timestamp_ts_sample",
      uniqueRecordIdentifierKey = "temp_uuid"
    ))
  )

  // Helper method to create config with inventory-derived field
  def createConfigWithInventoryDerivedField = Config(
    primaryKey = "hostname",
    origin = "'Test'",
    outputTableInfo = OutputTableInfo(outputTableName = "ei_temp.test_table", outputWrittenMode = "tableType"),
    entity = Entity(name = "Host", lastUpdateFields = Array("fqdn").toList),
    temporaryProperties = Array(Property("temp_uuid", "uuids")),
    entitySpecificProperties = Array(
      Property("is_active", "is_active"),
      Property("inventory_derived_field", "some_expression",
        FieldsSpec(persistNonNullValue = Some(false), isInventoryDerived = true, replaceExpression = Some(false), aggregateFunction = Option.empty[String]))
    ),
    dataSource = Some(DataSource(
      name = "Test",
      feedName = "test",
      srdm = "test",
      dataEventTimestampKey = "event_timestamp_ts_sample",
      uniqueRecordIdentifierKey = "temp_uuid"
    ))
  )
  "LoaderRerunStrategy" should "parse rerun info JSON and detect config changes for inventory-derived fields" in {
    val rerunInfoJson = """{
    "hasChanges": true,
    "fileHashChanged": false,
    "configHashChanged": true,
    "jarHashChanged": false,
    "currentHashes": {
        "fileHash": "c",
        "configHash": "new_config_hash",
        "jarHash": "b"
    },
    "previousHashes": {
        "fileHash": "c",
        "configHash": "old_config_hash",
        "jarHash": "b"
    },
    "message": "Configuration changes detected"
  }"""

    jobArgs.rerunInfoJson = rerunInfoJson

    val oldConfig = createConfigWithInventoryDerivedField
    val newConfig = oldConfig.copy(
      entitySpecificProperties = Array(
        Property("is_active", "is_active"),
        Property("inventory_derived_field", "new_expression",
          FieldsSpec(persistNonNullValue = Some(false), isInventoryDerived = true, replaceExpression = Some(false), aggregateFunction = Option.empty[String]))
      )
    )

    val rerunAnalysis = LoaderRerunStrategy.analyzeLoaderRerun(
      jobArgs = jobArgs,
      currentConfig = newConfig,
      previousConfig = Some(oldConfig.toString)
    )

    println(rerunAnalysis.toString)

    assert(rerunAnalysis.updateScope == UpdateViews)
    assert(rerunAnalysis.executionStrategy == IncrementalRerun)
    assert(rerunAnalysis.hasChanges)
    assert(rerunAnalysis.changeType == ConfigChange)
    assert(rerunAnalysis.affectedFields.contains("inventory_derived_field"))
  }

  "LoaderRerunStrategy" should "parse rerun info JSON and detect file hash changes" in {
    val rerunInfoJson = """{
    "hasChanges": true,
    "fileHashChanged": true,
    "configHashChanged": false,
    "jarHashChanged": false,
    "currentHashes": {
        "fileHash": "new_file_hash",
        "configHash": "a",
        "jarHash": "b"
    },
    "previousHashes": {
        "fileHash": "old_file_hash",
        "configHash": "a",
        "jarHash": "b"
    },
    "message": "File changes detected"
  }"""

    jobArgs.rerunInfoJson = rerunInfoJson

    val config = createBaseConfig()
    val rerunAnalysis = LoaderRerunStrategy.analyzeLoaderRerun(
      jobArgs = jobArgs,
      currentConfig = config,
      previousConfig = None
    )

    assert(rerunAnalysis.updateScope == UpdateBoth)
    assert(rerunAnalysis.executionStrategy == FullRerun)
    assert(rerunAnalysis.hasChanges)
    assert(rerunAnalysis.changeType == FileChange)
  }

  "LoaderRerunStrategy" should "parse rerun info JSON and detect config changes for non-inventory-derived fields" in {
    val rerunInfoJson =
      """{
    "hasChanges": true,
    "fileHashChanged": false,
    "configHashChanged": true,
    "jarHashChanged": false,
    "currentHashes": {
        "fileHash": "c",
        "configHash": "new_config_hash",
        "jarHash": "b"
    },
    "previousHashes": {
        "fileHash": "c",
        "configHash": "old_config_hash",
        "jarHash": "b"
    },
    "message": "Configuration changes detected"
  }"""

    jobArgs.rerunInfoJson = rerunInfoJson

    val oldConfig = createBaseConfig()
    val newConfig = oldConfig.copy(
      entitySpecificProperties = Array(
        Property("is_active", "is_active"),
        Property("new_field", "new_expression") // Non-inventory-derived
      )
    )

    val rerunAnalysis = LoaderRerunStrategy.analyzeLoaderRerun(
      jobArgs = jobArgs,
      currentConfig = newConfig,
      previousConfig = Some(oldConfig.toString)
    )

    assert(rerunAnalysis.updateScope == UpdateBoth)
    assert(rerunAnalysis.executionStrategy == FullRerun)
    assert(rerunAnalysis.hasChanges)
    assert(rerunAnalysis.changeType == ConfigChange)
    assert(rerunAnalysis.affectedFields.contains("new_field"))
  }

  }