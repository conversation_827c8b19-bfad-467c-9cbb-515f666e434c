relationship_name,source_entity,target_entity
account_associated_with_identity,Account,Identity
application_has_vulnerability_finding,Application,Vulnerability
application_running_on_host,Application,Host
assessment_associated_with_cloud_account,Assessment,Cloud Account
assessment_associated_with_finding,Assessment,Finding
assessment_measuring_security_control,Assessment,Security Control
bucket_belongs_to_storage_account,Storage,Storage
cloud_account_associated_findings,Cloud Account,Finding
cloud_account_associated_with_assessment,Cloud Account,Assessment
cloud_account_associated_with_compliance_standard,Cloud Account,Compliance Standard
cloud_account_associated_with_security_control,Cloud Account,Security Control
cloud_account_has_cluster_resource,Cloud Account,Cluster
cloud_account_has_container_resource,Cloud Account,Container
cloud_account_has_finding,Cloud Account,Finding
cloud_account_has_host,Cloud Account,Host
cloud_account_has_storage_resource,Cloud Account,Storage
cluster_has_finding,Cluster,Finding
cluster_resource_belongs_to_cloud_account,Cluster,Cloud Account
compliance_standard_associated_with_cloud_account,Compliance Standard,Cloud Account
compliance_standard_measured_by_security_control,Compliance Standard,Security Control
compute_instance_group_belongs_to_kubernetes_cluster,Cluster,Cluster
compute_instance_group_belongs_to_mapreduce_cluster,Cluster,Cluster
compute_instance_group_has_virtual_machine,Cluster,Host
container_belongs_to_container_group,Container,Cluster
container_belongs_to_container_service,Container,Cluster
container_group_has_container,Cluster,Container
container_has_finding,Container,Finding
container_has_vulnerability_finding,Container,Vulnerability
container_resource_belongs_to_cloud_account,Container,Cloud Account
container_service_has_container,Cluster,Container
file_system_service_belongs_to_storage_account,Storage,Storage
finding_associated_with_cloud_account,Finding,Cloud Account
finding_associated_with_cluster,Finding,Cluster
finding_associated_with_container,Finding,Container
finding_associated_with_host,Finding,Host
finding_associated_with_network,Finding,Network
finding_associated_with_network_interface,Finding,Network Interface
finding_associated_with_network_services,Finding,Network Services
finding_associated_with_storage,Finding,Storage
host_belongs_to_cloud_account,Host,Cloud Account
host_has_finding,Host,Finding
host_has_identity,Host,Identity
host_has_vulnerability_finding,Host,Vulnerability
host_hosting_application,Host,Application
host_owned_by_person,Host,Person
identity_associated_with_host,Identity,Host
identity_associated_with_person,Identity,Person
identity_has_account,Identity,Account
instance_has_network_interface,Network,Network
internet_gateway_has_virtual_network,Network,Network
kubernetes_cluster_has_compute_instance_group,Cluster,Cluster
load_balancer_associates_to_subnet,Network,Network
mapreduce_cluster_has_compute_instance_group,Cluster,Cluster
network_has_finding,Network,Finding
network_interface_associates_to_instance,Network,Network
network_interface_has_finding,Network Interface,Finding
network_interface_has_public_ip,Network,Network
network_interface_has_security_group,Network,Network
network_interface_has_subnet,Network,Network
network_interface_has_virtual_network,Network,Network
network_service_has_finding,Network Services,Finding
person_has_identity,Person,Identity
person_owns_host,Person,Host
public_ip_associates_with_network_interface,Network,Network
queue_service_belongs_to_storage_account,Storage,Storage
security_control_associated_with_cloud_account,Security Control,Cloud Account
security_control_measured_by_assessment,Security Control,Assessment
security_control_measuring_compliance_standard,Security Control,Compliance Standard
security_group_associates_to_network_interface,Network,Network
storage_account_has_bucket,Storage,Storage
storage_account_has_file_system_service,Storage,Storage
storage_account_has_queue_service,Storage,Storage
storage_account_has_table_service,Storage,Storage
storage_has_finding,Storage,Finding
storage_resource_belongs_to_cloud_account,Storage,Cloud Account
subnet_associates_to_load_balancer,Network,Network
subnet_associates_to_network_interface,Network,Network
subnet_associates_to_virtual_network,Network,Network
table_service_belongs_to_storage_account,Storage,Storage
virtual_machine_belongs_to_compute_instance_group,Host,Cluster
virtual_machine_has_volume,Host,Storage
virtual_network_associates_to_internet_gateway,Network,Network
virtual_network_associates_to_network_interface,Network,Network
virtual_network_has_subnet,Network,Network
volume_associates_to_virtual_machine,Storage,Host
vulnerability_finding_on_application,Vulnerability,Application
vulnerability_finding_on_container,Vulnerability,Container
vulnerability_finding_on_host,Vulnerability,Host
