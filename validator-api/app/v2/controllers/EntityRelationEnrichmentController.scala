package v2.controllers



import akka.actor.ActorSystem
import play.api.Configuration
import play.api.mvc._
import v2.services.{ChangeDetectionService, DependencyManagerService, LineageService, ResultsService}

import scala.concurrent.ExecutionContext
import javax.inject.Inject
import v2.common.Modules

class EntityRelationEnrichmentController @Inject()(
                                                      cc: ControllerComponents,
                                                      dependencyManagerService:
                                                      DependencyManagerService,
                                                      appConfig: Configuration,
                                                      resultsService: ResultsService,
                                                      lineageService: LineageService,
                                                      changeDetectionService: ChangeDetectionService,
                                                      actorSystem: ActorSystem
                                                    )
extends BaseExecutionController(cc, dependencyManagerService, appConfig, resultsService, lineageService,changeDetectionService,actorSystem) {


  override def getModuleName: String = {
    Modules.ENTITY_ENRICHMENT
  }
}