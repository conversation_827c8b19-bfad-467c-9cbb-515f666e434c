package v2.controllers
import play.api.mvc._
import javax.inject.Inject
import ai.prevalent.entityinventory.loader.configs.specs.Config
import common.InvalidJsonError
import org.json4s.jackson.Serialization.write
import play.api.Configuration
import play.api.libs.json.{JsValue, Json}
import v2.services.{ChangeDetectionService, DependencyManagerService, LineageService, ResultsService}

import akka.actor.ActorSystem
import v2.common.{Modules}
import v2.utils.{ResultSetUtils}



class LoaderController @Inject()(
                                  cc: ControllerComponents,
                                  dependencyManagerService: DependencyManagerService,
                                  appConfig: Configuration,
                                  resultsService: ResultsService,
                                  lineageService: LineageService,
                                  resultSetUtils: ResultSetUtils,
                                 changeDetectionService: ChangeDetectionService,
                                  actorSystem: ActorSystem,
                                )
  extends BaseExecutionController(cc, dependencyManagerService, appConfig, resultsService, lineageService,changeDetectionService,actorSystem) {




  override def getModuleName: String = Modules.LOADER
  private def isSnakeCase(str: String): Boolean =  "^[a-z]+(_[a-z]+)*$".r.matches(str)



  def validateSchema: Action[JsValue] = Action(parse.json) { request =>
    val config: Config = try {
      val body = request.body.toString()
      org.json4s.jackson.JsonMethods.parse(body).extract[Config]
    } catch {
      case ex: Exception => throw InvalidJsonError(ex.getMessage)

    }
    val lastUpdateFields = config.entity.lastUpdateFields.map{str=> str->isSnakeCase(str)}

    val commonSQLValidation = dependencyManagerService.validateSQL(config.commonProperties, "commonProperties")
    val entitySQLValidation = dependencyManagerService.validateSQL(config.entitySpecificProperties, "entitySpecificProperties")
    val sourceSQLValidation = dependencyManagerService.validateSQL(config.sourceSpecificProperties,"sourceSpecificProperties")
    val temporarySQLValidation = dependencyManagerService.validateSQL(config.temporaryProperties,"temporaryProperties")

    val allSQLValidationErrors = commonSQLValidation ++ entitySQLValidation ++ sourceSQLValidation ++ temporarySQLValidation

    val allSnakeCaseValid = lastUpdateFields.forall(_._2)
    if (allSQLValidationErrors.isEmpty && allSnakeCaseValid) {
      Ok(Json.obj("status" -> "Valid JSON Object", "output" -> Json.parse(write(config))))
    } else {
      Ok(Json.obj(
        "status" -> "Invalid JSON Object",
        "message" -> Json.obj(
          "invalidSQL" -> allSQLValidationErrors
        )
      ))
    }
  }

}
