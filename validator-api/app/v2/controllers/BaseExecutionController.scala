package v2.controllers

import ai.prevalent.entityinventory.cds.{PreviousRunHashes, TableSpec}
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory}
import akka.actor.ActorSystem
import org.json4s.{DefaultFormats, Formats}
import play.api.libs.json.{JsValue, Json}
import play.api.mvc._
import v2.services.{ChangeDetectionService, DependencyManagerService, LineageService, ResultsService}

import scala.concurrent.ExecutionContext
import javax.inject.{Inject, Singleton}
import scala.concurrent.Future
import common.InvalidSQLError
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions.expr
import play.api.Logger
import v1.utils.LiveContext
import v2.common.{ExpressionRequest, ExpressionResponse, Modules, ResolutionLevel, cdsRequest}
import v2.models.{ExecutionModule, ExecutionResult, HashComparisonResult, QueryParams}
import v2.utils.{ConfigRevisionsUtils, ResultSetUtils}
import play.api.Configuration

@Singleton
case class ModuleVariables @Inject()(validatorModule: String, configName: String, lineageData: Future[JsValue],
                                     schemaName: String,configApiUrl:String, publisherSchemaName: String,eiEnrichSchemaName:String,eiFragmentSchemaName:String)

abstract class BaseExecutionController @Inject()(
                                                  cc: ControllerComponents,
                                                  dependencyManagerService: DependencyManagerService,
                                                  appConfig: Configuration,
                                                  resultsService: ResultsService,
                                                  lineageService: LineageService,
                                                  changeDetectionService: ChangeDetectionService,
                                                  actorSystem: ActorSystem
                                                )extends AbstractController(cc) {


  def getModuleName: String
  protected def initializeSparkSession(): Unit = {

    LOGGER.info("Initializing Spark session")
    try {
      LiveContext.buildSessionFromEnvSpec()
      LOGGER.info("Validator::: Session Created")
    } catch {
      case ex: Exception =>
        LOGGER.error("Validator::: Error while creating spark session")
        LOGGER.error(ex.getMessage)
        throw new RuntimeException("Failed to initialize Spark session", ex)
    }
  }
  implicit val formats: Formats = DefaultFormats
  private val LOGGER = Logger(getClass)

  implicit val ec: ExecutionContext = actorSystem.dispatchers.lookup("play.spark.dispatcher")

  def run(queryParams: QueryParams): Action[JsValue] = Action(parse.json).async { implicit request =>

    val moduleName = getModuleName
    val requestBody: JsValue = request.body
    val config: JsValue = (requestBody \ "config_value").as[JsValue]
    val configName: String = (requestBody \ "name").as[String]

    resultsService.getResultAsJson(configName) match {
      case Some(value: ExecutionResult) =>

        val completedOrErrorStatuses = Set(ExecutionModule.COMPLETED, ExecutionModule.ERROR)

        if (completedOrErrorStatuses.contains(value.status)) {
          resultsService.clearResult(configName)
        }

        if(value.status == ExecutionModule.ERROR){
          Future.successful(BadRequest(Json.toJson(value)))
        }
        else{
          LOGGER.info("Validator::: PROGRESS or COMPLETED ")
          Future.successful(Ok(Json.toJson(value)))
        }

      case None =>
        Future {
          try {
            initializeSparkSession()
          } catch {
            case ex: Exception =>
              Future.successful(BadRequest("No Active Spark session"))
          }

          LOGGER.info("Validator::: Request Body")
          LOGGER.info(requestBody.toString())
//          val configContextUrl = s"${sys.env.getOrElse("CONFIG_API_URL", null)}/context"
          val configContextData = lineageService.getConfigContext()
          var configApiUrl = (configContextData \ "config_value" \ "EI_SPARK_CONFIGS_BASE_PATH").as[String]
          val schemaName: String = (configContextData \ "config_value" \ "EI_SCHEMA_NAME").as[String]
          val publisherSchemaName: String = (configContextData \ "config_value" \ "EI_PUBLISH_SCHEMA_NAME").as[String]
          val eiEnrichSchemaName: String = (configContextData \ "config_value" \ "EI_SCHEMA_NAME").as[String]
          val eiFragmentSchemaName:String = (configContextData \ "config_value" \ "KG_FRAGMENT_SCHEMA").as[String]
          configApiUrl = s"${sys.env.getOrElse("DOMAIN_NAME", null)}" + configApiUrl

          val lineageData = lineageService.getLineageData(configName, moduleName)
          val configRevisionsUtils = new ConfigRevisionsUtils(appConfig, lineageService)
          configRevisionsUtils.revalidateConfig(configName, lineageData,queryParams.disambiguation_resolution_level.getOrElse(Seq(ResolutionLevel.ENTITY_RESOLUTION)).head)
          LOGGER.info(s"Validator::: LineageData: $lineageData ")
          val runModuleVariable = ModuleVariables(moduleName, configName, lineageData, schemaName,configApiUrl,publisherSchemaName,eiEnrichSchemaName,eiFragmentSchemaName)
          dependencyManagerService.runModule(queryParams, config, runModuleVariable)
          val response = ExecutionResult(configName = configName,executionFlow = List(s"${runModuleVariable.configName} In-progress"))
          Ok(Json.toJson(response))
        }.recover {
          case ex: Exception =>
            LOGGER.error("Error while running module", ex)
            InternalServerError(Json.obj("error" -> ex.getMessage))
        }

    }
  }

  def validateExpression: Action[ExpressionRequest] = Action(parse.json[ExpressionRequest]  ).async { implicit request =>

    try {
      val body = request.body.expression
      val expression = expr(body).expr
      expression.childrenResolved
      Future.successful(Ok(Json.toJson(ExpressionResponse(output = body))))
    } catch {
      case ex: Exception =>
        throw InvalidSQLError(ex.getMessage)

    }
  }

  def currentHash: Action[AnyContent] = Action {
    implicit request =>{
      val s = resultsService.getCurrentSearches
      Ok(Json.toJson(s))
    }
  }

  def allHash: Action[AnyContent] = Action {
    implicit request =>{
      val s = resultsService.getAllItems
      Ok(s)
    }
  }

  def clearHash: Action[AnyContent] = Action{
    implicit  request => {
      resultsService.clearAllResult()
      Ok("Cleared all results")
    }
  }

  def setDummyHash(): Action[JsValue] = Action(parse.json){
    implicit  request => {
      val requestBody: JsValue = request.body
      val name= (requestBody \ "name").as[String]
      val status= (requestBody \ "status").as[String]
      resultsService.storeResult(configName = name,status = status)
      Ok(Json.toJson(resultsService.getResultAsJson(name)))
    }
  }

//  def getFilesHash(dataFrame: List[DataFrame]): String = {
//    changeDetectionService.getInputFilesHash(getModuleName, dataFrame)
//  }

  def getFilesHash(tableSpecs: Array[TableSpec]): String = {
    changeDetectionService.getInputFilesHash(getModuleName, tableSpecs)
  }

  def getJarHashValue(filePath: String = ""): String = {
    changeDetectionService.getJarHash(getModuleName, filePath)
  }

  def getConfigHashValue(config: String): String = {
    changeDetectionService.getInputConfigHash(getModuleName, config)
  }

  def getPreviousInfo(dataFrame: DataFrame): Option[PreviousRunHashes] = {
    changeDetectionService.getPreviousInfoHash(getModuleName, dataFrame)
  }

  def compareHashes(fileHash: String, configHash: String, jarHash: String, previousHashes: Option[PreviousRunHashes]): HashComparisonResult= {
    changeDetectionService.compareHashes(fileHash, configHash, jarHash, previousHashes)
  }

  def prepareInputDataframeForCDS(jobArgs: ai.prevalent.entityinventory.common.configs.EIJobArgs,
                                  config: Any,
                                  reader: SDSTableReader,
                                  spark: SparkSession): Array[TableSpec] = {
    changeDetectionService.prepareInputDataframeForCDS(getModuleName, jobArgs, config, reader, spark)
  }

 def processCDS: Action[cdsRequest] = Action(parse.json[cdsRequest]).async { implicit request =>

    val moduleName = getModuleName
    implicit val formats: Formats = changeDetectionService.getModuleFormats(moduleName)

    try {
      initializeSparkSession()
      val jobArgs = request.body.EIJobArgs

      // Get module-specific config
      //val configJsValue = lineageService.getConfig(jobArgs.configPath)
      val moduleConfig = changeDetectionService.getModuleConfig(jobArgs.configPath, moduleName)

      val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)

      // Prepare input tables (generic across modules)
      val inputTables = prepareInputDataframeForCDS(jobArgs, moduleConfig, reader, LiveContext.spark)

      // Calculate hashes
      val fileHash = getFilesHash(inputTables)
      val configHash = getConfigHashValue(moduleConfig.toString)
      val jarHash = getJarHashValue(request.body.JarLocation)

      // Get output dataframe with module-specific logic
      val outputInfo = changeDetectionService.getModuleOutputInfo(moduleName, jobArgs, moduleConfig)
      val outputDataframe = changeDetectionService.getDataframe(
        outputInfo.tableName,
        outputInfo.filterString,
        reader
      )

      val previousRunInfo = getPreviousInfo(outputDataframe)
      val compareResult = compareHashes(configHash, jarHash, fileHash, previousRunInfo)


      Future.successful(Ok(Json.toJson(compareResult)))
    } catch {
      case ex: Exception =>
        LOGGER.error("Error in processCDS", ex)
        Future.successful(InternalServerError(Json.obj("error" -> ex.getMessage)))
    }
  }








}
