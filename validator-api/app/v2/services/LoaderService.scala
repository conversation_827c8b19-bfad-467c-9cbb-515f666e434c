package v2.services

import ai.prevalent.entityinventory.common.configs.{EIJobArgs, OutputTableInfo}
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.specs.{Config => LoaderConfig}
import ai.prevalent.entityinventory.utils.SparkUtil
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReaderFactory, SDSTableWriterFactory}
import ai.prevalent.entityinventory.loader.Loader.spark
import common.{InvalidJsonError, TableNotFoundError}
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.col
import org.json4s.Formats
import play.api.Logger
import play.api.libs.json.{JsValue, Json}
import v2.utils.{IcebergUtils, LoaderControllerUtils, ResultSetUtils}
import v2.utils.LoaderControllerUtils.{checkTableExist, getTimeStampDetails}

import scala.concurrent.{ExecutionContext, Future}
import javax.inject.Inject
import v2.models.{ExecutionModule, ModuleServiceBase, QueryParams}
import v1.utils.LiveContext
import v2.common.{CacheUtil, Modules, ResolutionLevel}
import akka.actor.ActorSystem
class LoaderService @Inject()(
                               resultsService: ResultsService,
                               cacheUtils: CacheUtil,
                               icebergUtils: IcebergUtils,
                               resultSetUtils: ResultSetUtils,
                               actorSystem: ActorSystem
                             )(implicit ec: ExecutionContext =actorSystem.dispatchers.lookup("play.spark.dispatcher")) extends ModuleServiceBase {

  implicit val formats: Formats = Loader.configFormats
  private val LOGGER = Logger(getClass)

  def execute(configName: String, subModuleName: String, params: QueryParams, configJson:JsValue = null, schemaName: String="ei_validator"
              , configApiUrl:String ="/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/"): Future[Unit] = {

      LOGGER.info(s"Executing Loader submodule: $subModuleName")

      val loaderJobArgs = new EIJobArgs()
      val disambiguation_resolution_level =
        params.disambiguation_resolution_level.getOrElse(Seq(ResolutionLevel.ENTITY_RESOLUTION)).head
      val configData = resultSetUtils.getConfig(
        configJson = configJson,
        configApiUrl = configApiUrl,
        subModuleName = subModuleName,
        schemaName = schemaName,
        Modules.LOADER
      )
      try {
        val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)
        val writer = SDSTableWriterFactory.getDefault(LiveContext.spark, tableProperties = Map.empty, options = Map("partitionOverwriteMode" -> "dynamic"))
        val selectedCols = params.selectedCols
        val distinct = params.distinct
        val onlyInventoryConfigFields = params.onlyInventoryConfigFields
        val limit = params.limit

        val config: LoaderConfig = resultSetUtils.getConfig(
          updatedJson = configData,
          manifest = manifest[LoaderConfig],
          formats = formats
        )

        config.configValidator()
        val srdm = config.dataSource.get.srdm

        Loader.spark = LiveContext.spark
        checkTableExist(srdm, LiveContext.spark)

        val timeStampDetails =
          getTimeStampDetails(srdm, LiveContext.spark, disambiguation_resolution_level)

        if (timeStampDetails._2) {
          loaderJobArgs.parsedIntervalEndEpoch = timeStampDetails._1.parsedIntervalEndEpoch
          loaderJobArgs.parsedIntervalStartEpoch = timeStampDetails._1.parsedIntervalStartEpoch
          loaderJobArgs.prevUpdateDate = timeStampDetails._1.previousEndEpoch
          loaderJobArgs.currentUpdateDate = timeStampDetails._1.eventTimestampEndEpoch

          val paramsPriority =
            if (selectedCols.getOrElse(Seq.empty).nonEmpty &&
              onlyInventoryConfigFields.getOrElse(Seq("true")).head.toBoolean)
              "selectedCols"
            else
              "onlyInventoryConfigFields"

          var fields: Array[String] = if (paramsPriority == "selectedCols") {
            val fieldsFrom = SparkUtil.getDependendProperty(config.allProperties, selectedCols.get.head).toList.reverse
            LOGGER.info(s"Validator Fields $fieldsFrom")
            fieldsFrom.toArray
          } else {
            LoaderControllerUtils.getPropertyColNames(config.commonProperties ++
              config.entitySpecificProperties ++ config.sourceSpecificProperties) ++ Array("primary_key")
          }

          var updConfig = if (paramsPriority == "selectedCols") {
            val srDMprop = SparkUtil.getSRDMProperty(fields, config)
            fields ++= srDMprop.map(_.colName)
            config.copy(sourceSpecificProperties = config.sourceSpecificProperties ++ srDMprop)
          } else {
            config
          }

          val cachedStatusOpt = cacheUtils.getCache(
            configData,
            subModuleName,
            loaderJobArgs.currentUpdateDate,
            resolutionLevel = disambiguation_resolution_level
          )
          cachedStatusOpt match {
            case Some(ExecutionModule.COMPLETED) =>
              LOGGER.info(s"Validator::: Inside Completed $subModuleName")
              if (configJson != null) {
                val resultSet = icebergUtils.reader(LiveContext.spark, config.outputTableInfo.outputTableName, loaderJobArgs.currentUpdateDate, disambiguation_level = disambiguation_resolution_level)
                val finalFields = fields.intersect(resultSet.columns)
                val fullSet = resultSet.select(finalFields.map(col): _*)
                resultSetUtils.processResultSet(
                  fullSet,
                  distinct,
                  limit,
                  configJson,
                  configName,
                  subModuleName,
                  Modules.LOADER,
                  selectedCols,
                  onlyInventoryConfigFields
                )
              } else {
                resultsService.storeResult(
                  configName = configName,
                  executionFlow = List(s"${Modules.LOADER}: $subModuleName Completed (loaded from cache)")
                )
              }

            case Some(ExecutionModule.IN_PROGRESS) =>
              LOGGER.info(s"Validator::: Inside In-progress $subModuleName")
              resultsService.storeResult(
                configName = configName,
                executionFlow = List(s"${Modules.LOADER}: $subModuleName in-progressing")
              )

            case None =>
              LOGGER.info(s"Validator::: Inside Start $subModuleName")
              cacheUtils.setCache(Json.toJson(configData), subModuleName, loaderJobArgs.currentUpdateDate, ExecutionModule.IN_PROGRESS, resolutionLevel = disambiguation_resolution_level)
              updConfig.configValidator()
              updConfig = disambiguation_resolution_level match {
                case ResolutionLevel.CANDIDATE_KEY_RESOLUTION => updConfig.copy(outputTableInfo = updConfig.outputTableInfo.copy(outputWrittenMode = "tableType"))
                case _ => updConfig
              }
              LiveContext.spark.sql(s"DROP VIEW IF EXISTS ${config.outputTableInfo.outputTableName}")
              LiveContext.spark.sql(s"DROP TABLE IF EXISTS ${config.outputTableInfo.outputTableName + "__srdm_inv"}")
              val resultSet = Loader.build(loaderJobArgs, updConfig, reader,writer)
              if (updConfig.outputTableInfo.outputWrittenMode =="tableType"){
                icebergUtils.writer(LiveContext.spark, resultSet, config.outputTableInfo.outputTableName, restrictOutput = true, output_table_limit = params.output_table_limit, disambiguation_level = disambiguation_resolution_level)
              }

              icebergUtils.writer(LiveContext.spark, resultSet, config.outputTableInfo.outputTableName, restrictOutput = true, output_table_limit = params.output_table_limit, disambiguation_level = disambiguation_resolution_level)
              val finalFields = fields.intersect(resultSet.columns)
              val fullSet = resultSet.select(finalFields.map(col): _*)
              resultSetUtils.processResultSet(
                fullSet,
                distinct,
                limit,
                configJson,
                configName,
                subModuleName,
                Modules.LOADER,
                selectedCols,
                onlyInventoryConfigFields
              )

              cacheUtils.setCache(Json.toJson(configData), subModuleName, loaderJobArgs.currentUpdateDate, ExecutionModule.COMPLETED, resolutionLevel = disambiguation_resolution_level)
          }
        } else {
          val status = if (configJson == null) ExecutionModule.IN_PROGRESS else ExecutionModule.ERROR
          resultsService.storeResult(
            configName,
            status,
            List(s"Error while execution of loader: $subModuleName, No SRDM data")
          )
        }

        Future.successful() // return Unit
      } catch {
        case ex: Exception =>
          LOGGER.error("Exception while processing request", ex)
          resultsService.storeResult(
            configName,
            ExecutionModule.ERROR,
            List(s"Error while execution of loader: $subModuleName, ${ex.getMessage}")
          )
          cacheUtils.deleteCache(Json.toJson(configData), subModuleName, loaderJobArgs.currentUpdateDate, resolutionLevel = disambiguation_resolution_level)
          throw ex
      }
    }

}