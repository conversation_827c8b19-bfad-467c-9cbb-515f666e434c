package v2.services

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.publisher.Publisher
import ai.prevalent.entityinventory.publisher.configs.{Config => PubisherConfig}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.entityinventory.publisher.Publisher.{modelWithDerivedProperties, spark}
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import v1.utils.LiveContext
import common.InvalidJsonError
import org.apache.spark.sql.functions.{col, days}
import org.json4s.Formats
import play.api.{Configuration, Logger}
import play.api.libs.json.{JsValue, Json}
import v2.common.{CacheUtil, Modules}
import v2.models.{ExecutionModule, ModuleServiceBase, QueryParams}
import v2.utils.TimestampUtils.getTimeStampDetails
import v2.utils.{IcebergUtils, ResultSetUtils}

import javax.inject.Inject
import scala.concurrent.Future

class PublisherService @Inject()( resultsService: ResultsService,
                                 cacheUtils: CacheUtil, icebergUtils: IcebergUtils, resultSetUtils: ResultSetUtils,configuration: Configuration) extends ModuleServiceBase {

  private val LOGGER = Logger(getClass)
  implicit val formats: Formats = Publisher.configFormats

  def execute(configName: String, subModuleName: String, params: QueryParams, configJson: JsValue = null, schemaName: String = "ei_validator",configApiUrl:String = "/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/"): Future[Unit] = {
    Future.successful(())
  }

  def executeWithPubSchema(configName: String, subModuleName: String, params: QueryParams, configJson:JsValue = null, schemaName: String="ei_validator",
                           configApiUrl:String ="/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/", publishSchemaName: String=""): Future[Unit] = {

    LOGGER.info(s"Executing Publisher submodule: $subModuleName")
    var configData: JsValue = null
    val publisherArgs = new EIJobArgs()
    val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)

    try {
      LiveContext.spark.sparkContext.setCheckpointDir(LiveContext.spark.conf.get("spark.checkpoint.dir"))
      val distinct = params.distinct
      val limit = params.limit

      configData = resultSetUtils.getConfig(configJson=configJson,configApiUrl=configApiUrl,subModuleName=subModuleName,schemaName = schemaName,Modules.PUBLISHER,publisherSchema = publishSchemaName)
      val config:PubisherConfig= resultSetUtils.getConfig(updatedJson = configData,manifest = manifest[PubisherConfig],formats = formats)

      Publisher.spark = LiveContext.spark
      val timeStampDetails = getTimeStampDetails(LiveContext.spark)
      publisherArgs.currentUpdateDate = timeStampDetails.eventTimestampEndEpoch

      val cachedStatusOpt = cacheUtils.getCache(Json.toJson(configData),subModuleName, publisherArgs.currentUpdateDate)

      cachedStatusOpt match {
        case Some(cachedStatus) =>
          cachedStatus match {
            case ExecutionModule.COMPLETED =>
              LOGGER.info(s"Validator::: Inside Completed $subModuleName")
              if (configJson != null) {
                val resultSet = icebergUtils.reader(LiveContext.spark, config.outputTableInfo.outputTableName, publisherArgs.currentUpdateDate, configuration.get[String]("publisherSchema"))

                resultSetUtils.processResultSet(
                  resultSet,
                  distinct,
                  limit,
                  configJson,
                  configName,
                  subModuleName,
                  Modules.PUBLISHER
                )
              }
              else{
                resultsService.storeResult(
                  configName = configName,
                  executionFlow = List(s"${Modules.PUBLISHER}: $subModuleName Completed (loaded from cache)")
                )
              }

              Future.successful(println("Executing logic for COMPLETED"))

            case ExecutionModule.IN_PROGRESS =>
              LOGGER.info(s"Validator::: Inside In-progress $subModuleName")
              resultsService.storeResult(
                configName = configName,
                executionFlow = List(s"${Modules.PUBLISHER}: $subModuleName in-progressing")
              )
              Future.successful(println("Executing logic for IN_PROGRESS"))
          }

        case None =>
          LOGGER.info(s"Validator::: Inside Start $subModuleName")

          val (transformedModels,tblProps) = config.transformSpec.transform(reader, publisherArgs.currentUpdateDate, spark, config)
          val derivedFieldsDF = modelWithDerivedProperties(transformedModels, config)
            .filter(config.outputTableInfo.outputFilter)
          val partitionColumns = config.outputTableInfo.partitionColumns.getOrElse(Array.empty[String])

          val outDf = removeNullFields(derivedFieldsDF)
          icebergUtils.writer(LiveContext.spark,outDf, config.outputTableInfo.outputTableName,
            Array(days(col(UPDATED_AT_TS))) ++ partitionColumns.map(col),tabSchema = configuration.get[String]("publisherSchema"),tableProperties = tblProps
          )
          if(!config.isOLAPTable){
            val properties = Array("graph.vertex.name","graph.edge.name","graph.edge.source.name","graph.edge.target.name")
              .map( p => s"'$p'").mkString(",")
            spark.sql(s"""ALTER TABLE ${config.outputTableInfo.outputTableName} UNSET TBLPROPERTIES IF EXISTS  ( $properties )""")
          }

          resultSetUtils.processResultSet(
            outDf,
            distinct,
            limit,
            configJson,
            configName,
            subModuleName,
            Modules.PUBLISHER
          )

          cacheUtils.setCache(Json.toJson(configData),subModuleName, publisherArgs.currentUpdateDate, ExecutionModule.COMPLETED)
          Future.successful(println("None"))
      }
    }
    catch {
      case ex: Exception =>
        LOGGER.error("Exception while processing request", ex)
        resultsService.storeResult(
          configName = configName,
          status = ExecutionModule.ERROR,
          executionFlow = List(s"Error while execution of Publisher disambiguation : $subModuleName, ${ex.getMessage}")
        )
        cacheUtils.deleteCache(Json.toJson(configData),subModuleName, publisherArgs.currentUpdateDate)
        Future.successful(())

        throw InvalidJsonError(ex.getMessage)
    }

  }
}