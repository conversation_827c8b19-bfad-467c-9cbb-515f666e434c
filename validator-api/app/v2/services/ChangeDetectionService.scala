package v2.services

import ai.prevalent.entityinventory.cds.{ChangeDetectionSystem, PreviousRunHashes, TableSpec}
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions.expr
import org.json4s.{DefaultFormats, Formats}
import play.api.libs.json.{JsValue}
import play.api.{Configuration, Logger}
import v1.utils.LiveContext
import v2.common.Modules
import v2.utils.{ResultSetUtils}
import v2.models.HashComparisonResult
import ai.prevalent.sdspecore.utils.ConfigUtils

import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}

class ChangeDetectionService @Inject()(implicit ec: ExecutionContext, AppConfig: Configuration, resultSetUtils: ResultSetUtils) {

  def getChangeDetectionSystem(moduleName: String): ChangeDetectionSystem = {
    moduleName match {
      case Modules.LOADER => Loader
      case _ => throw new IllegalArgumentException(s"Unknown module: $moduleName")
    }
  }
  def getInputFilesHash(moduleName: String, inputTableInfo: Array[TableSpec]): String = {

      getChangeDetectionSystem(moduleName).getFilesHashGeneric(inputTableInfo,LiveContext.spark)
    }
  def getJarHash(moduleName: String, filePath: String = ""): String = {
    getChangeDetectionSystem(moduleName).getJarHash(LiveContext.spark,filePath)
  }

  def getInputConfigHash(moduleName: String, config: String): String = {
    getChangeDetectionSystem(moduleName).getInputConfigHash(config)
  }

  def getPreviousInfoHash(moduleName: String, dataFrame: DataFrame): Option[PreviousRunHashes]  = {
    getChangeDetectionSystem(moduleName).getpreviousRunInfo(dataFrame)

  }

  def getDataframe(tableName:String,exprString:String,reader:SDSTableReader):DataFrame={

    reader.read(tableName).filter(expr(exprString))

  }

  def compareHashes(
                     currentConfigHash: String,
                     currentJarHash: String,
                     currentFileHash: String,
                     previousHashes: Option[PreviousRunHashes]
                   ): HashComparisonResult = {

    val currentHashesMap = Map(
      "fileHash" -> currentFileHash,
      "configHash" -> currentConfigHash,
      "jarHash" -> currentJarHash
    )

    previousHashes match {
      case Some(prev) =>
        val fileHashChanged = currentFileHash != prev.fileHash.getOrElse("")
        val configHashChanged = currentConfigHash != prev.configHash.getOrElse("")
        val jarHashChanged = currentJarHash != prev.jarHash.getOrElse("")
        val hasChanges = fileHashChanged || configHashChanged || jarHashChanged

        val previousHashesMap = Map(
          "fileHash" -> prev.fileHash,
          "configHash" -> prev.configHash,
          "jarHash" -> prev.jarHash
        )

        val message = if (hasChanges) {
          val changedComponents = Seq(
            if (fileHashChanged) Some("input files") else None,
            if (configHashChanged) Some("configuration") else None,
            if (jarHashChanged) Some("jar files") else None
          ).flatten
          s"Changes detected in: ${changedComponents.mkString(", ")}"
        } else {
          "No changes detected - all hashes match previous run"
        }

        HashComparisonResult(
          hasChanges = hasChanges,
          fileHashChanged = fileHashChanged,
          configHashChanged = configHashChanged,
          jarHashChanged = jarHashChanged,
          currentHashes = currentHashesMap,
          previousHashes = Some(previousHashesMap),
          message = message
        )

      case None =>
        HashComparisonResult(
          hasChanges = true,
          fileHashChanged = true,
          configHashChanged = true,
          jarHashChanged = true,
          currentHashes = currentHashesMap,
          previousHashes = None,
          message = "No previous run found - treating as new execution"
        )
    }
  }

  def prepareInputDataframeForCDS(moduleName: String,
                                  jobArgs: ai.prevalent.entityinventory.common.configs.EIJobArgs,
                                  config: Any,
                                  reader: SDSTableReader,
                                  spark: SparkSession): Array[TableSpec] = {
    getChangeDetectionSystem(moduleName).prepareInputDataframeForCDS(jobArgs, config, reader, spark)
  }

 def getModuleFormats(moduleName: String): Formats = {
    moduleName match {
      case Modules.LOADER => Loader.configFormats
      case _ => DefaultFormats
    }
  }

 def getModuleConfig(configPath: String, moduleName: String): Any = {
    moduleName match {
      case Modules.LOADER =>
        ConfigUtils.getConfig(LiveContext.spark,configPath, manifest[Config], getModuleFormats(moduleName))
      case _ => throw new IllegalArgumentException(s"Unsupported module: $moduleName")
    }
  }

  case class ModuleOutputInfo(tableName: String, filterString: String)

  def getModuleOutputInfo(moduleName: String, jobArgs: EIJobArgs, config: Any): ModuleOutputInfo = {
    moduleName match {
      case Modules.LOADER =>
        val loaderConfig = config.asInstanceOf[Config]
        val filterString = s"updated_at_ts = to_timestamp(${jobArgs.currentUpdateDate}/1000) AND kg_content_type = 'config'"
        ModuleOutputInfo(loaderConfig.outputTableInfo.outputTableName, filterString)

      case _ => throw new IllegalArgumentException(s"Unsupported module: $moduleName")
    }
  }






}