package v2.utils

import play.api.libs.json.{JsArray, JsObject, JsString, JsValue, Json}

import scala.util.{Failure, Success, Try}
import ai.prevalent.sdspecore.utils.PidUtils.LOGGER


object ConfigUtils {

  def getEnvVariableAsMap(envVarName: String): Either[String, Map[String, String]] = {
    sys.env.get(envVarName) match {
      case Some(jsonString) =>
        Try(Json.parse(jsonString)) match {
          case Success(jsValue) =>
            Right(jsValue.as[Map[String, String]])
          case Failure(exception) =>
            Left(s"Error parsing JSON: ${exception.getMessage}")
        }
      case None =>
        Left(s"Environment variable $envVarName not found")
    }
  }

  def replaceExactMatches(jsValue: JsValue, oldValue: String, newValue: String): JsValue = jsValue match {
    case JsObject(fields) =>
      val updatedFields = fields.view.mapValues {
        case JsString(value) if value == oldValue =>
          JsString(newValue) // Replace exact match
        case JsString(value) if value.contains(oldValue) =>
          JsString(value.replaceFirst(s"^$oldValue\\.", s"$newValue.")) // Replace partial match
        case obj: JsObject =>
          replaceExactMatches(obj, oldValue, newValue)
        case arr: JsArray =>
          JsArray(arr.value.map(replaceExactMatches(_, oldValue, newValue)))
        case other => other
      }.toMap
      JsObject(updatedFields)
    case other => other
  }

  def replaceAllOccurrences(jsValue: JsValue, oldValue: String, newValue: String): JsValue = jsValue match {
    case JsString(value) =>
      LOGGER.info(s"Replacing $oldValue with $newValue")
      // Replace only when oldValue is used as a schema prefix (followed by a dot)
      val pattern = ("""(?<=^|[^\w])""" + java.util.regex.Pattern.quote(oldValue) + """\.""").r
      JsString(pattern.replaceAllIn(value, m => newValue + "."))
    case JsObject(fields) =>
      JsObject(fields.map { case (k, v) => k -> replaceAllOccurrences(v, oldValue, newValue) })
    case JsArray(elements) =>
      JsArray(elements.map(replaceAllOccurrences(_, oldValue, newValue)))
    case other => other
  }

}