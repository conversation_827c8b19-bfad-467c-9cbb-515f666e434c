package v2.models

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>}

case class HashComparisonResult(
                                 hasChanges: Boolean,
                                 fileHashChanged: Boolean,
                                 configHashChanged: Boolean,
                                 jarHashChanged: <PERSON>olean,
                                 currentHashes: Map[String, String],
                                 previousHashes: Option[Map[String, Option[String]]],
                                 message: String
                               )
object HashComparisonResult {

  implicit val writes: Writes[HashComparisonResult] = Json.writes[HashComparisonResult]

}
