package v1.utils

import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import org.apache.spark.sql.SparkSession
import play.api.Configuration

import scala.jdk.CollectionConverters._
import play.api.Logger

trait LiveContext {

  val logger: Logger = Logger(this.getClass())

  var spark: SparkSession = _

  private def build(spec: Map[String, String]): Unit = this.synchronized {
    if(spark==null){
      val sessionBuilder = spec.foldLeft(SparkSession.builder())((df,c) => {
        df.config(c._1,c._2)
      })
      spark = sessionBuilder.getOrCreate()
      spark.range(100).count()
    }
  }

  private def update(spec: Map[String, String]): Unit = {
    spec.foreach(conf => {
      try {
        spark.conf.set(conf._1, conf._2)
      } catch {
        case ex: Exception =>
          logger.error(ex.getMessage)
      }

    })
  }

  def close(): Unit = {
    spark.stop()
    spark = null
  }

  def create(spec: Map[String, String]): Unit = this.synchronized {
    this.build(spec)
    //    this.update(spec)
  }

  def info(): Map[String, String] = {
    if (spark != null) this.spark.conf.getAll else Map.empty[String, String]
  }

  def buildSessionFromEnvSpec(): Unit =  this.synchronized {
    val result = ConfigUtils.getEnvVariableAsMap("SPARK_CONFIGS")
    result match {
      case Right(map) =>
        this.create(map)
        logger.info(s"Successfully parsed: $map")
      case Left(error) =>
        logger.error(error)
    }

  }


  private def getAllConfigKeys(configuration: Configuration): Map[String, String] = {
    val configEntries = configuration.underlying.entrySet()
    configEntries.asScala.filter(conf => conf.getKey.startsWith("spark")).map { entry =>
      val key = entry.getKey
      val value = entry.getValue.unwrapped().toString
      key -> value
    }.toMap
  }
}

object LiveContext extends LiveContext {
}