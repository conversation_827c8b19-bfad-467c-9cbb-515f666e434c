ARG JAVA_BASE_IMAGE="prevalentai/spark:4-1-0-R-3.5.5-2.13-iceberg-v1-9-bookworm-12.10-20250428-slim"

FROM ${JAVA_BASE_IMAGE}

RUN mkdir -p /opt/spark/sds-ei-validator/

COPY ./target/scala-2.13/validator_2.13-*.jar /opt/spark/sds-ei-validator/validator_2.13.jar
COPY ../configs/data_model_delta/ /opt/spark/sds-ei-validator/data_model_delta
USER root

WORKDIR /opt/spark/sds-ei-validator/

RUN chown -R spark:root /opt/spark/sds-ei-validator/

RUN find /opt/spark/jars/ -type f -name '*log4j*.jar' -exec rm -f '{}' \; \
    && find /opt/spark/jars/ -type f -name '*guava*.jar' -exec rm -f '{}' \;


USER spark
