

unmanagedBase := baseDirectory.value / "lib"

ThisBuild / name := "sds-ei-validator"
ThisBuild / scalaVersion := "2.13.9"
ThisBuild / version := sys.env.getOrElse("buildVersion", "1.0.1")
ThisBuild / artifactName := { (sv: ScalaVersion, module: ModuleID, artifact: Artifact) =>
  artifact.name + "_" + sv.binary + "-" + module.revision + "." + artifact.extension
}

lazy val analyticsRepo = ProjectRef(file("../analytics"), "sds-ei-analytics")
assembly / assemblyJarName := s"${name.value}_${scalaBinaryVersion.value}-${version.value}.${artifact.value.extension}"

Test / parallelExecution := false
Test / fork := true
javaOptions ++= Seq("-Xms18000M", "-Xmx18000M",
  "--add-exports java.base/java.lang=ALL-UNNAMED",
  "--add-exports java.base/java.lang.invoke=ALL-UNNAMED",
  "--add-exports java.base/java.lang.reflect=ALL-UNNAMED",
  "--add-exports java.base/java.io=ALL-UNNAMED",
  "--add-exports java.base/java.net=ALL-UNNAMED",
  "--add-exports java.base/java.nio=ALL-UNNAMED",

  "--add-exports java.base/java.util=ALL-UNNAMED",
  "--add-exports java.base/java.util.concurrent=ALL-UNNAMED",
  "--add-exports java.base/java.util.concurrent.atomic=ALL-UNNAMED",
  "--add-exports java.base/sun.nio.ch=ALL-UNNAMED",
  "--add-exports java.base/sun.nio.cs=ALL-UNNAMED",
  "--add-exports java.base/sun.security.action=ALL-UNNAMED",
  "--add-exports java.base/sun.util.calendar=ALL-UNNAMED"
)
Test / javaOptions += s"-Dconfig.file=${sourceDirectory.value}/test/resources/application.conf"


lazy val coreDependency = ProjectRef(file("../core"), "core")

PlayKeys.devSettings += "play.server.http.idleTimeout" -> "360000ms"

lazy val validator = Project(id = "config-validator", base = file("."))
  .enablePlugins(PlayScala)
  .settings(
    name := """validator""",
    dependencyOverrides += "org.scala-lang.modules" %% "scala-parser-combinators" % "2.3.0",
    dependencyOverrides += "com.fasterxml.jackson.module" %% "jackson-module-scala" % "2.17.2",
    dependencyOverrides += "com.fasterxml.jackson.core" % "jackson-databind" % "2.17.2",
    dependencyOverrides += "io.netty" % "netty-codec-http2" % "4.1.118.Final",
    dependencyOverrides += "io.netty" % "netty-codec-http" % "4.1.118.Final",
    dependencyOverrides += "com.typesafe.akka" %% "akka-actor" % "2.8.1",
    dependencyOverrides += "com.typesafe.akka" %% "akka-stream" % "2.8.1",
    dependencyOverrides += "com.typesafe.akka" %% "akka-actor-typed" % "2.8.1",
    dependencyOverrides += "com.typesafe.akka" %% "akka-slf4j" % "2.8.1",
    dependencyOverrides += "com.typesafe.akka" %% "akka-serialization-jackson" % "2.8.1",
    dependencyOverrides += "org.apache.commons" %% "commons-compress" % "1.27.1",
    dependencyOverrides += "org.springframework" %% "spring-core" % "6.1.14",
      libraryDependencies ++= Seq(
      guice,
      ws,
      "org.postgresql" % "postgresql" % "42.7.3",
      "com.github.jsqlparser" % "jsqlparser" % "4.6",
      "org.scala-lang.modules" %% "scala-parallel-collections" % "1.0.0",
      "redis.clients" % "jedis" % "5.1.3",
      "net.codingwell" %% "scala-guice" % "6.0.0",
        "com.google.guava" % "guava" % "32.0.0-jre",
      "io.lemonlabs" %% "scala-uri" % "4.0.3",
      "net.logstash.logback" % "logstash-logback-encoder" % "6.3",
        "org.apache.spark" %% "spark-core" % "3.5.5" % "provided",
        "org.apache.spark" %% "spark-sql" % "3.5.5" % "provided",
        "org.apache.spark" %% "spark-graphx" % "3.5.5" % "provided",
      "com.github.tomakehurst" % "wiremock" % "1.33" % Test,
        "software.amazon.awssdk" % "url-connection-client" % "2.31.43",
      "com.holdenkarau" %% "spark-testing-base" % "3.5.1_1.5.3" % Test,
      "com.typesafe.play" %% "play-guice" % "2.9.5" % Test,
      "org.mockito" %% "mockito-scala-scalatest" % "1.17.30" % Test,
      "org.scalatestplus.play" %% "scalatestplus-play" % "6.0.0" % Test,
      play.sbt.PlayImport.cacheApi,
      "net.debasishg" %% "redisclient" % "3.42",
      "redis.clients" % "jedis" % "5.1.3",
      "com.github.karelcemus" %% "play-redis" % "3.0.0",
      "org.pac4j" %% "play-pac4j" % "12.0.1-PLAY2.9" exclude("com.google.code.findbugs", "findbugs-annotations") ,
      "org.pac4j" % "pac4j-oidc" % "6.1.3",
      "org.pac4j" % "pac4j-http" % "6.1.3",
      "com.nimbusds" % "oauth2-oidc-sdk" % "11.22.2"

    ),
    dependencyOverrides += "org.apache.commons" %% "commons-compress" % "1.27.1",
    dependencyOverrides += "org.springframework" %% "spring-core" % "6.1.14",
  ).dependsOn(analyticsRepo)

javaOptions in Test ++= Seq(
  "-Djava.net.preferIPv6Addresses=false",
  "-XX:+IgnoreUnrecognizedVMOptions",
  "--add-opens=java.base/java.lang=ALL-UNNAMED",
  "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
  "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
  "--add-opens=java.base/java.io=ALL-UNNAMED",
  "--add-opens=java.base/java.net=ALL-UNNAMED",
  "--add-opens=java.base/java.nio=ALL-UNNAMED",
  "--add-opens=java.base/java.util=ALL-UNNAMED",
  "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
  "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED",
  "--add-opens=java.base/jdk.internal.ref=ALL-UNNAMED",
  "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
  "--add-opens=java.base/sun.nio.cs=ALL-UNNAMED",
  "--add-opens=java.base/sun.security.action=ALL-UNNAMED",
  "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED",
  "--add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED",
  "-Djdk.reflect.useDirectMethodHandle=false",
  "-Xms1G",
  "-Xmx4G"
)
assembly / assemblyMergeStrategy := {
  case x if Assembly.isConfigFile(x) =>
    MergeStrategy.concat
  case PathList(ps@_*) if Assembly.isReadme(ps.last) || Assembly.isLicenseFile(ps.last) =>
    MergeStrategy.rename
  case PathList("META-INF", "versions", "9", "module-info.class") => MergeStrategy.discard
  case "module-info.class" => MergeStrategy.discard
  case PathList("META-INF", xs@_*) =>
    xs map {
      _.toLowerCase
    } match {
      case "manifest.mf" :: Nil | "index.list" :: Nil | "dependencies" :: Nil =>
        MergeStrategy.discard
      case ps@x :: xs if ps.last.endsWith(".sf") || ps.last.endsWith(".dsa") =>
        MergeStrategy.discard
      case "plexus" :: xs =>
        MergeStrategy.discard
      case "services" :: xs =>
        MergeStrategy.filterDistinctLines
      case "spring.schemas" :: Nil | "spring.handlers" :: Nil =>
        MergeStrategy.filterDistinctLines
      case _ => MergeStrategy.first
    }
  case _ => MergeStrategy.first
}